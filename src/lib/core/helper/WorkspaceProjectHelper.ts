import type { ComponentInternalInstance } from 'vue'
import { getCurrentInstance } from 'vue'
import type WebUserClass from '@/lib/service/WebUserClass'

export const defaultProject: ProjectId = Number(window.defaultProject || config.app.defaultProject || -1)

export type ProjectId = number
export type Workspace<T = 'ANY' | 'EMPTY'> = T | Exclude<string, | T>

export const projectWorkspaceMap2 = new Map<ProjectId, Workspace>()

let WebUser: WebUserClass | null = null

export function setWebUser(wu: WebUserClass) {
    WebUser = wu
}

export const getWorkspaceProjectDefinedInInstance = (instance: ComponentInternalInstance): ProjectId | null => {
    const projectId = instance.$context?.workspace || instance.data?.workspaceProject || instance.props?.workspaceProject

    if (!projectId) {
        return null
    }

    return Number(projectId)
}

export const getOldWorkspaceProjectDefinedInInstance = (instance: ComponentInternalInstance): ProjectId | null => {
    const projectId = instance.data?.workspaceProject || instance.props?.workspaceProject

    if (!projectId) {
        return null
    }

    return Number(projectId)
}

const getWorkspaceProjectForInstance = (instance: ComponentInternalInstance | null): number => {
    if (!instance) {
        return defaultProject
    }

    return getWorkspaceProjectDefinedInInstance(instance) || getWorkspaceProjectForInstance(instance.parent)
}

// @ts-ignore
export const getOldWorkspaceProjectForInstance = (instance: ComponentInternalInstance | null) => {
    if (!instance) {
        return defaultProject
    }

    return getOldWorkspaceProjectDefinedInInstance(instance) || getOldWorkspaceProjectForInstance(instance.parent)
}

export function getWorkspaceProject(instance: ComponentInternalInstance | null = null) {
    return getWorkspaceProjectForInstance(instance || getCurrentInstance())
}

export function resetWorkspaceMap() {
    projectWorkspaceMap2.clear()
}

export function setWorkspaceMap(project: ProjectId, workspace: Workspace) {
    projectWorkspaceMap2.set(project, workspace)
}

export function getDefaultWorkspace(): Workspace {
    return getWorkspaceByProject(defaultProject)
}

export function getWorkspaceByProject(project: ProjectId): Workspace {
    if (project === -1) {
        return 'ANY'
    }

    return projectWorkspaceMap2.get(Number(project)) || 'EMPTY'
}

export function getWorkspace(instance: ComponentInternalInstance | null = null) {
    return getWorkspaceByProject(getWorkspaceProject(instance))
}

export const getWorkspaceProjects = (workspaceKey: string): any[] => {
    return WebUser?.availableWorkspaceProjects[workspaceKey]?.projects ?? []
}

export const getProjectFullName = (projectId: ProjectId): string | undefined => {
    const workspaces = Object.values(WebUser?.availableWorkspaceProjects ?? {}) as any[]

    for (const workspace of workspaces) {
        const project = workspace.projects.find((project: { id: number }) => project.id === projectId)

        if (!project) {
            continue
        }

        return project.project_abbr === workspace.project_abbr ? project.project_abbr : `${project.project_abbr} (${workspace.project_abbr})`
    }
}

export const getProjectInfo = (projectId: ProjectId): {
    id: number,
    project_abbr: string,
} | undefined => {
    const workspaces = Object.values(WebUser?.availableWorkspaceProjects ?? {}) as any[]

    for (const workspace of workspaces) {
        const project = workspace.projects.find((project: { id: number }) => project.id === projectId)

        if (!project) {
            continue
        }

        return project
    }
}

export const projectColors = {
    'TBC': '#9d1d5a',
    'ABT': '#470865',
    'BCF': '#aa8453',
    'ABF': '#3B4D42',
    'BCS': '#aa8453',
    'LFS': '#3B4D42',
}

export const projectByCompanyColors = {
    'TBC': '#9d1d5a',
    'ABT': '#9d1d5a',
    'BCF': '#aa8453',
    'ABF': '#aa8453',
    'BCS': '#aa8453',
    'LFS': '#aa8453',
}

export const getProjectColor = (projectId: ProjectId): string | undefined => {
    const project = getProjectInfo(projectId)

    if (!project) {
        return
    }

    // @ts-ignore
    return projectColors[project.project_abbr]
}

export const getCompanyColorByProject = (projectId: ProjectId): string | undefined => {
    const project = getProjectInfo(projectId)

    if (!project) {
        return
    }

    // @ts-ignore
    return projectByCompanyColors[project.project_abbr]
}
