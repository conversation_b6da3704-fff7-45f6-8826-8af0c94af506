import UnMountModel from '@/lib/mixin/UnMount/UnMountModel'
import ReactiveHelper from '@/lib/core/helper/ReactiveHelper'
import { computed } from 'vue'
import MilePriceProgramModel from '@/api/models/MileProgram/MilePriceProgramModel'
import FormHelper from '@/lib/core/helper/FormHelper'
import InputField from '@/lib/FormField/InputField'
import SelectField from '@/lib/FormField/SelectField'
import AirlineModel from '@/api/models/Airline/AirlineModel'
import ArrayField from '@/lib/FormField/ArrayField'
import ConsolidatorAreaModel from '@/api/models/Consolidator/ConsolidatorAreaModel'
import InputNumberField from '@/lib/FormField/InputNumberField'
import CheckBoxField from '@/lib/FormField/CheckBoxField'
import { getTicketColorClass } from '@/lib/core/helper/ColorClassHelper'
import SaleTypeTag from '@/lib/SearchTag/Sale/SaleTypeTag'
import { SegmentType } from '~/api/models/PriceQuote/PriceQuote'
import TicketModel from '@/api/models/Ticket/TicketModel'
import TransactionService from '@/lib/service/TransactionService'

export default class PriceQuoteHelper extends UnMountModel {
    milePriceProgramList = MilePriceProgramModel.contextInstance().getAllList()
    mode = 'clone'
    segmentTypes = []
    ticketTypes = []

    $info = {
        mainForm: {},
        multiTktForm: {},
        oneTktForm: {},
        summary: {
            grandTotal: 0,
            airlineChargeAmount: 0,
            inhouseChargeAmount: 0,
            profit: 0,
            markUpAlert: false,
        },
        oneTkt: true,
        multiCity: false,
        is_award: false,
        openFromSale: false,
        sale_id: null,
        lead_id: null,
        initialRawSegments: null,
        selected: null,
        segmentsGroup: {},
        options: [],
        firstSegmentTypePreticketOptions: [],
        segmentTypePreticketOptions: [],
        updateTkt: null,
        columnsTkt: [],
        oldValues: {},
    }

    $columns = []
    $preTicketOneSell = {}

    constructor(options) {
        super()

        if (options?.mode) {
            this.mode = options.mode
        }
        this.$info = ReactiveHelper.create(this.$info)
        this.$info.mainForm = this.constructor.mainForm()
        this.$info.oneTktForm = this.constructor.preTicketForm(this.$info.mainForm)
        this.$info.multiTktForm = this.constructor.preTicketForm(this.$info.mainForm)
    }

    /**
     * @returns {FormHelper}
     */
    get form() {
        return this.$info.mainForm
    }

    /**
     * @returns {FormHelper}
     */
    get preTicketForm() {
        if (this.oneTkt) {
            return this.$info.oneTktForm
        }

        return this.$info.multiTktForm
    }

    get initialRawSegments() {
        return this.$info.initialRawSegments
    }

    get lead_id() {
        return this.$info.lead_id
    }

    get isSale() {
        return !!this.$info.sale_id
    }

    get sale_id() {
        return this.$info.sale_id
    }

    get isAward() {
        return this.$info.is_award
    }

    get multiCity() {
        return this.$info.multiCity
    }

    set multiCity(v) {
        this.$info.multiCity = v
    }

    get options() {
        return this.$info.options
    }

    get firstSegmentTypePreticketOptions() {
        return this.$info.firstSegmentTypePreticketOptions
    }

    get segmentTypePreticketOptions() {
        return this.$info.segmentTypePreticketOptions
    }

    get openFromSale() {
        return this.$info.openFromSale
    }

    get selected() {
        return this.$info.selected
    }

    set selected(v) {
        this.$info.selected = v
    }

    get outBoundList() {
        return this.form.field.segments.items.filter((item, index) => {
            return index < this.selected
        })
    }

    get inBoundList() {
        return this.form.field.segments.items.filter((item, index) => index >= this.selected)
    }

    get columns() {
        return this.$columns
    }

    set columns(v) {
        this.$columns = v
    }

    get columnsTkt() {
        return this.$info.columnsTkt
    }

    set columnsTkt(v) {
        this.$info.columnsTkt = v
    }

    get segmentsGroup() {
        return this.$info.segmentsGroup
    }

    set segmentsGroup(v) {
        this.$info.segmentsGroup = v
    }

    get formData() {
        return this.form.data
    }

    get preTicketFormData() {
        return this.preTicketForm.data
    }

    get oneTkt() {
        return this.$info.oneTkt
    }

    set oneTkt(v) {
        this.$info.oneTkt = v
        this.checkPaxOnSwitchTktType()
    }

    get adult_count() {
        return this.form.field.adult_count
    }

    get child_count() {
        return this.form.field.child_count
    }

    get infant_count() {
        return this.form.field.infant_count
    }

    get isCanSave() {
        const segmentsGroupedPreticketNumbers = []

        if (this.segmentsGroup.public) {
            this.segmentsGroup.public.forEach((segment) => {
                segmentsGroupedPreticketNumbers.push(segment.pre_ticket_number)
            })
        }

        if (this.segmentsGroup.inhouse) {
            this.segmentsGroup.inhouse.forEach((segment) => {
                segmentsGroupedPreticketNumbers.push(segment.pre_ticket_number)
            })
        }

        return this.form.validate() && this.preTicketForm.validate()
    }

    static mainForm() {
        return new FormHelper({
            lead_id: new InputField(true, 'Lead'),
            raw_segments: new InputField(true, 'Raw Segments'),
            validating_carrier_id: new SelectField(true, 'Carrier', {
                items: AirlineModel.contextInstance().getAllList(),
                emptyLabel: '-----------',
                labelAdapter: item => {
                    return `${item.system_name} - ${item.label}`
                },
            }),
            currency_id: new InputField(false, 'Currency', { value: 0 }),
            active_till: new InputField(false, 'Active Till', { value: 0 }),
            adult_count: new InputField(true, 'Adult Count', { value: 1 }),
            child_count: new InputField(true, 'Child Count', { value: 0 }),
            infant_count: new InputField(true, 'Infant Count', { value: 0 }),
            itinerary_type: new InputField(true, 'Itinerary Type'),
            client_remark: new InputField(false, 'Client Remark', { placeholder: 'Client remark...' }),
            agent_remark: new InputField(false, 'Agent Remark', { placeholder: 'Agent remark...' }),
            saleType: new SelectField(true, 'Sale Type', {
                placeholder: 'Sale type',
                value: 'sale',
                items: new SaleTypeTag().generateOptions(),
                emptyLabel: '-----------',
                labelAdapter: item => item.label,
            }),
            check_payment_ps: new InputField(true, 'CK Ps', { value: TransactionService.defaultCheckPaymentPs() }),
            tour_fare_type: new SelectField(false, 'Tour fare', {
                emptyLabel: '------',
                placeholder: 'Tour Fare',
                items: [
                    { value: 'tour_fare', label: 'Tour Fare' },
                    { value: 'tour_fare_ck', label: 'Tour Fare Ck' },
                ],
            }),

            segments: new ArrayField({
                typePreticketNumber: new InputField(false, 'Preticket & Segment type'),
                type: new SelectField(false, '', {
                    placeholder: 'Segment Type',
                    items: [],
                    emptyLabel: '-----------',
                    labelAdapter: item => item.title,
                }),
                flight_id: new InputField(true, 'Flight ID'),
                id: new InputField(true, 'ID'),

                is_return: new CheckBoxField('Is Return', 0),
                line_raw: new InputField(true, 'Line Raw'),
                number: new InputField(true, 'Number', { value: 0 }),
                part_number: new InputField(true, 'Part Number', { value: 0 }),
                pre_ticket_id: new InputField(true, 'Pre Ticket ID'),
                pre_ticket_number: new InputField(true, 'Pre Ticket Number', { value: 0 }),
                additional: new InputField(false, 'Additional'),
                flight_class: new InputField(true, 'Flight Class'),
                baggage_quantity: new InputField(false, 'Baggage Quantity'),
                baggage_weight: new InputField(true, 'Baggage Weight'),
            }),
        })
    }

    static preTicketForm(mainForm) {
        return new FormHelper({
            preTickets: new ArrayField({
                id: new InputField(true, 'ID'),
                ticket_type: new SelectField(false, 'Product type', {
                    placeholder: 'Tkt. type',
                    items: TicketModel.contextInstance().getTicketTypeOptionsData(),
                    emptyLabel: '------',
                }),
                pre_ticket_number: new InputField(true, 'Pre Ticket Number', { value: 0 }),
                miles_count: new InputField(true, 'Miles Count', {
                    value: 0,
                    isActive: (input) => input.parent.item.sell_type.value === 'award',
                }),
                award_tax_amount: new InputNumberField(true, 'Award Tax Amount', {
                    value: 0,
                    isActive: (input) => input.parent.item.sell_type.value === 'award',
                }),
                consolidator_area_id: new SelectField(true, 'Consolidator', {
                    placeholder: 'Consolidator',
                    items: ConsolidatorAreaModel.contextInstance().getNotAwardListData(),
                    emptyLabel: '------',
                    labelAdapter: item => item.label,
                    isActive: (input) => {
                        return input.parent.item.sell_type.value !== 'award' && mainForm.field.segments.items.findIndex(segment => {
                            return +segment.pre_ticket_number.value === +input.parent.item.pre_ticket_number.value
                        }) !== -1
                    },
                }),
                mile_price_program_id: new SelectField(true, 'Mile Price Program ID', {
                    isActive: (input) => input.parent.item.sell_type.value === 'award',
                    items: MilePriceProgramModel.contextInstance().getAllList(),
                }),
                sell_type: new InputField(true, 'Sell Type'),
                prices: new ArrayField({
                    fare_amount: new InputNumberField(true, 'Fare', {
                        value: 0,
                        isActive: (input) => input.parent.list.parent.item.sell_type.value !== 'award',
                    }),
                    net_price: new InputNumberField(true, 'Net', {
                        value: 0,
                        isActive: (input) => input.parent.list.parent.item.sell_type.value !== 'award',
                    }),
                    passenger_type: new InputField(true, 'Passenger'),
                    sell_amount: new InputNumberField(false, 'Sell', { value: 0 }),
                    commission_ps: new InputNumberField(false, 'Upgrade', { value: 0 }),
                    commission: new InputNumberField(false, 'Upgrade', { value: 0 }),
                    is_award_upgrade: new CheckBoxField(false, 0),
                    upgrade: new InputNumberField(false, 'Upgrade', { value: 0 }),
                    upgrade_miles_count: new InputNumberField(false, 'Upgrade miles award', { value: 0 }),
                    upgrade_tax_amount: new InputNumberField(false, 'Upgrade tax award', { value: 0 }),
                    upgrade_mile_price_program_id: new SelectField(false, 'Upgrade Mile Price Program ID', {
                        items: MilePriceProgramModel.contextInstance().getAllList(),
                    }),
                    // tax_amount: new InputField(true, 'Tax', {isActive: (input) => input.parent.list.parent.item.sell_type.value !== 'award'})
                    mark_up: new InputNumberField(false, 'Mark Up', { value: 0 }),
                }, [], data => {
                    return data.passenger_type
                }),

            }),
        })
    }

    $onUnmount() {
    }

    $checkFirstSegments() {
        if (this.multiCity) {
            return
        }

        if (this.outBoundList.length) {
            const segment = this.outBoundList[0]

            if (!segment.type.value || segment.type.value === SegmentType.Upgrade) {
                return
            }
            segment.type.silentValue = null
            const index = Number(segment.pre_ticket_number.value) - 1
            segment.typePreticketNumber.silentValue = segment.type.value ? `${index}--${segment.type.value}` : `${index}--`
        }
    }

    $init(model) {
        const defaultFormData = this.$info.mainForm.data
        defaultFormData.lead_id = model?.lead_id
        this.$info.mainForm.setData(
            defaultFormData,
        )

        if (model?.lead_id) {
            this.$info.lead_id = model?.lead_id
        }

        if (model?.sale_id) {
            this.$info.sale_id = model?.sale_id
        }

        this.form.field.segments.event.addListener('change', (args) => {
            const field = args.field
            const data = args.data
            const segment = this.form.field.segments.get(args.identity - 1, true)

            if (field === 'typePreticketNumber') {
                const [pre_ticket_number, type] = segment.typePreticketNumber.value.split('--')
                segment.pre_ticket_number.silentValue = Number(pre_ticket_number) + 1// +$el.target.value + 1
                segment.type.silentValue = type
            }

            if (['type', 'pre_ticket_number'].includes(field)) {
                const index = Number(segment.pre_ticket_number.value) - 1
                segment.typePreticketNumber.silentValue = segment.type.value ? `${index}--${segment.type.value}` : `${index}--`
            }

            this.$checkFirstSegments()
        })
    }

    sellType(pre_ticket_number = null) {
        if (this.oneTkt && !pre_ticket_number) {
            return this.preTicketForm.field.preTickets.get(0, true)?.sell_type
        } else {
            const index = this.preTicketForm.field.preTickets.items.findIndex(x => x.pre_ticket_number.value === pre_ticket_number)

            return this.preTicketForm.field.preTickets.get(index, true)?.sell_type
        }
    }

    preTicket(pre_ticket_number = null) {
        if (this.oneTkt && !pre_ticket_number) {
            return this.preTicketForm.field.preTickets.get(0, true)
        } else {
            const index = this.preTicketForm.field.preTickets.items.findIndex(x => x.pre_ticket_number.value === pre_ticket_number)

            return this.preTicketForm.field.preTickets.get(index, true)
        }
    }

    minAdultCount() {
        if (+this.child_count.value === 0) {
            return 1
        }

        return 0
    }

    minChildCount() {
        if (+this.adult_count.value === 0) {
            return 1
        }

        return 0
    }

    maxInfantCount() {
        return +this.adult_count.value + +this.child_count.value
    }

    slicePriceData(type) {
        let indexPrice = null

        this.$info.oneTktForm.field.preTickets.items.forEach((item) => {
            indexPrice = item.prices?.items.findIndex(item => item.passenger_type.value === type)

            if (indexPrice !== -1) {
                item.prices.items.splice(indexPrice, 1)
            }
        })

        this.$info.multiTktForm.field.preTickets.items.forEach((item) => {
            indexPrice = item.prices?.items.findIndex(item => item.passenger_type.value === type)

            if (indexPrice !== -1) {
                item.prices.items.splice(indexPrice, 1)
            }
        })
    }

    addPriceData(type = '') {
        this.$info.oneTktForm.field.preTickets.items.forEach((item) => {
            item?.prices.addItem({
                passenger_type: type,
            }, true)
        })

        this.$info.multiTktForm.field.preTickets.items.forEach((item) => {
            item?.prices.addItem({
                passenger_type: type,
            }, true)
        })
    }

    checkPaxOnSwitchTktType() {
        if (Number.fromString(this.adult_count.value) === 0) {
            this.slicePriceData('adult')
        } else {
            this.addPriceData('adult')
        }

        if (Number.fromString(this.child_count.value) === 0) {
            this.slicePriceData('child')
        } else {
            this.addPriceData('child')
        }

        if (Number.fromString(this.infant_count.value) === 0) {
            this.slicePriceData('infant')
        } else {
            this.addPriceData('infant')
        }
    }

    handlePax() {
        const handlePax = (...v) => {
            if (this.adult_count.value < this.minAdultCount() || this.adult_count === '') {
                this.adult_count.value = this.minAdultCount()
            }

            if (+this.adult_count.value > 99) {
                this.adult_count.value = 99
            }

            if (+this.child_count.value < this.minChildCount() || this.child_count.value === '') {
                this.child_count.value = this.minChildCount()
            }

            if (+this.child_count.value > 99) {
                this.child_count.value = 99
            }

            if (+this.infant_count.value > this.maxInfantCount()) {
                this.infant_count.value = this.maxInfantCount()
            }

            if (+this.infant_count.value < 0 || this.infant_count.value === '') {
                this.infant_count.value = 0
            }

            this.checkPaxOnSwitchTktType()
        }

        this.adult_count.event.addListener('change', handlePax)
        this.child_count.event.addListener('change', handlePax)
        this.infant_count.event.addListener('change', handlePax)
    }

    changedPax() {
        this.$info.oneTktForm.field.preTickets.items.map(function(item) {
            item.miles_count.value = 0
            item.award_tax_amount.value = 0
            item.consolidator_area_id.value = 12
            item.mile_price_program_id.value = 2

            item.prices.items.map(function(price) {
                price.fare_amount.value = 0
                price.net_price.value = 0
                price.sell_amount.value = 0
                price.commission_ps.value = 0
                price.commission.value = 0
                price.upgrade.value = 0
                price.is_award_upgrade.value = 0
                price.upgrade_miles_count.value = 0
                price.upgrade_tax_amount.value = 0
                price.upgrade_mile_price_program_id.value = 0

                return price
            })

            return item
        })

        this.$info.multiTktForm.field.preTickets.items.map(function(item) {
            item.miles_count.value = 0
            item.award_tax_amount.value = 0
            item.consolidator_area_id.value = 12
            item.mile_price_program_id.value = 2

            item.prices.items.map(function(price) {
                price.fare_amount.value = 0
                price.net_price.value = 0
                price.sell_amount.value = 0
                price.commission_ps.value = 0
                price.commission.value = 0
                price.upgrade.value = 0
                price.is_award_upgrade.value = 0
                price.upgrade_miles_count.value = 0
                price.upgrade_tax_amount.value = 0
                price.upgrade_mile_price_program_id.value = 0

                return price
            })

            return item
        })
    }

    updateSegmentsGroup() {
        this.segmentsGroup = this.formData?.segments?.reduce((r, a) => {
            let sell_type = null

            if (this.oneTkt) {
                sell_type = this.preTicketFormData.preTickets[0]?.sell_type
            } else {
                const index = this.preTicketFormData.preTickets.findIndex(x => x.pre_ticket_number === a.pre_ticket_number)
                sell_type = this.preTicketFormData.preTickets[index]?.sell_type
            }

            if (r[sell_type]) {
                const unique = r[sell_type].findIndex(item => item.pre_ticket_number === a.pre_ticket_number) === -1

                if (unique) {
                    r[sell_type] = [...r[sell_type] || [], a]
                }
            } else {
                r[sell_type] = [...r[sell_type] || [], a]
            }

            r[sell_type].sort((a, b) => {
                if (a.pre_ticket_number < b.pre_ticket_number) {
                    return -1
                } else if (a.pre_ticket_number > b.pre_ticket_number) {
                    return 1
                }

                return 0
            })

            return r
        }, {})
    }

    initPreTickets() {
        if (this.$info.oneTktForm.field.preTickets.items.length > 1) {
            this.$info.oneTktForm.field.preTickets.items.splice(1)
        }

        this.$info.oneTktForm.field.preTickets.get(0, true).pre_ticket_number.value = 1

        if (this.mode === 'clone') {
            this.$info.oneTktForm.field.preTickets.get(0, true).id.value = 'new_0'
        }

        this.$info.mainForm.field.segments.items.forEach(segment => {
            const index = this.$info.multiTktForm.field.preTickets.items.findIndex(x => x.id.value === segment.pre_ticket_id.value)

            if (+index !== -1 && !this.$info.multiTktForm.field.preTickets.get(index, true)?.pre_ticket_number.value) {
                this.$info.multiTktForm.field.preTickets.get(index, true).pre_ticket_number.value = segment.pre_ticket_number.value
            }
        })

        const firstObj = this.$info.multiTktForm.field.preTickets.get(0, true)
        this.$info.options.forEach((item, pre_ticket_number) => {
            const index = this.$info.multiTktForm.field.preTickets.items.findIndex(x => x.pre_ticket_number.value === item.label)

            if (+index === -1) {
                this.$info.multiTktForm.field.preTickets.addItem({
                    id: `new_${item.value}`,
                    pre_ticket_number: pre_ticket_number + 1,
                    miles_count: firstObj.miles_count.$copy(),
                    award_tax_amount: firstObj.award_tax_amount.$copy(),
                    consolidator_area_id: firstObj.consolidator_area_id.$copy(),
                    mile_price_program_id: firstObj.mile_price_program_id.$copy(),
                    sell_type: firstObj.sell_type.value,
                    prices: firstObj.prices.$getData(),
                })
            }
        })

        this.updateSegmentsGroup()
    }

    getTitle(type) {
        let text = ''

        switch (type) {
        case 'adult': {
            text = 'ADT'
            break
        }
        case 'child': {
            text = 'CHD'
            break
        }
        case 'infant': {
            text = 'INF'
            break
        }
        }

        return text
    }

    $changeAllTktToCk() {
        let has_award = false

        this.columnsTkt.forEach(item => {
            const index = this.$info.multiTktForm.field.preTickets.items.findIndex(x => x.pre_ticket_number.value === item.segments[0]?.pre_ticket_number)
            const sell_type = this.oneTkt ? this.$info.oneTktForm.field.preTickets.get(0, true)?.sell_type.value : this.$info.multiTktForm.field.preTickets.get(index, true)?.sell_type.value

            if (sell_type === 'award') {
                has_award = true
            }
        })

        this.$info.is_award = has_award
    }

    $initAfterParseOrClone(apiResponse, model, parse = true) {
        apiResponse.result.preTickets.sort((a, b) => a.id.localeCompare(b.id))
        this.$info.multiCity = apiResponse.result.itinerary_type === 'multiCity'
        this.$info.oneTkt = apiResponse.result.preTickets.length === 1

        this.$info.mainForm.setData(apiResponse.result)
        this.$info.oneTktForm.setData(apiResponse.result)
        this.$info.multiTktForm.setData(apiResponse.result)

        this.$info.options = []

        this.$info.initialRawSegments = this.form.field.raw_segments.value

        this.form.field.segments.items.forEach((segment, index) => {
            segment.type.items = this.segmentTypes

            // COMPOSE OPTIONS
            this.$info.options.push({
                label: +index + 1,
                value: index,
            })

            // Compose type segment options

            this.$info.segmentTypePreticketOptions.push({
                title: `${Number(index) + 1}`,
                value: `${index}--`,
            })

            this.$info.firstSegmentTypePreticketOptions.push({
                title: `${Number(index) + 1}`,
                value: `${index}--`,
            })

            const first_valid = this.segmentTypes.filter(option => {
                return [SegmentType.Upgrade].includes(option.value)
            })
            for (const type of first_valid) {
                this.$info.firstSegmentTypePreticketOptions.push({
                    title: `${Number(index) + 1} ${type.subtitle}`,
                    value: `${index}--${type.value}`,
                })
            }

            for (const type of this.segmentTypes) {
                this.$info.segmentTypePreticketOptions.push({
                    title: `${Number(index) + 1} ${type.subtitle}`,
                    value: `${index}--${type.value}`,
                })
            }

            segment.typePreticketNumber.value = segment.type.value ? `${segment.pre_ticket_number.value - 1}--${segment.type.value}` : `${segment.pre_ticket_number.value - 1}--`
        })

        this.initPreTickets()
        this.handlePax()

        if (!parse) {
            this.$changeAllTktToCk()
        }

        if (model?.sale_id) {
            this.$info.openFromSale = true
            this.changedPax()

            setTimeout(() => {
                this.$info.oneTkt = false
            }, 100)
        }
    }

    $updateSegment(segment, $event) {
        const numberToUpdate = segment.number.value
        this.form.field.segments.items.forEach((segmentForm, index) => {
            if (segmentForm.number.value === numberToUpdate) {
                segmentForm.typePreticketNumber.value = $event
            }
        })

        if (this.form.field.itinerary_type.value === 'multiCity') {
            this.$prepareMultiCity(true)
        }

        this.$prepareTkt()
    }

    $updateColumnsSegment(segment, $event) {
        const numberToUpdate = segment.number.value

        for (const column of this.$columns) {
            for (const columnSegment of column.segments) {
                if (columnSegment.number.value === numberToUpdate) {
                    columnSegment.typePreticketNumber.value = $event
                    const [pre_ticket_number, type] = columnSegment.typePreticketNumber.value.split('--')
                    segment.pre_ticket_number.silentValue = Number(pre_ticket_number) + 1// +$el.target.value + 1
                    segment.type.silentValue = type
                }
            }
        }
        this.updateSegmentsGroup()
    }

    $getPreTicketColor(segment) {
        const pre_ticket_number = segment.pre_ticket_number?.value ?? segment.pre_ticket_number
        const index = this.$info.multiTktForm.field.preTickets.items.findIndex(x => x.pre_ticket_number.value === pre_ticket_number)
        const sell_type = this.oneTkt ? this.$info.oneTktForm.field.preTickets.get(0, true)?.sell_type.value : this.$info.multiTktForm.field.preTickets.get(index, true)?.sell_type.value

        let indexSeg = 0

        if (this?.segmentsGroup[sell_type]) {
            indexSeg = this?.segmentsGroup[sell_type].findIndex(item => {
                return +item.pre_ticket_number === +pre_ticket_number
            })
        }

        return getTicketColorClass(sell_type, indexSeg, 'bg')
    }

    $prepareOneCity() {
        this.selected = this.form.field?.segments.items.length
        let prevKey = 0

        for (let i = 0; i < this.form.field?.segments.items.length; i++) {
            const item = this.form.field?.segments.get(i, true)

            if (item.part_number.value === null) {
                item.part_number.value = 0
                continue
            }

            if (item.part_number.value !== prevKey) {
                prevKey = item.part_number.value
                this.selected = i
            }
        }

        setTimeout(() => {
            if (this.inBoundList.length === 0) {
                this.form.field.itinerary_type.value = 'oneWay'
            } else {
                this.form.field.itinerary_type.value = 'roundTrip'
            }
        }, 100)
    }

    $updateInbound(val) {
        this.selected = val + 1

        this.form.field.segments.items.forEach((segment, index) => {
            segment.part_number.value = index >= this.selected ? 1 : 0
        })
    }

    $prepareMultiCity(update = false) {
        let prevKey = 0
        let newArr = []

        this.form.field.itinerary_type.value = 'multiCity'

        this.$columns = this.form.field?.segments?.items.reduce((arr, item) => {
            if (Number(item.part_number.value) !== Number(prevKey) && Number(item.part_number.value) !== 0) {
                prevKey++
                newArr = []
            }

            newArr.push({ ...item })
            arr[prevKey] = arr[prevKey] || []
            arr[prevKey].segments = arr[prevKey].segments || []
            arr[prevKey].segments = newArr

            return arr
        }, [])

        if (!update) {
            this.$columns.filter((item, index) => {
                return !!item
            })
        }
    }

    $exchangePrepareMultiCity(update = false, pnrList = []) {
        this.form.field.itinerary_type.value = 'multiCity'

        this.$columns = pnrList.map((pnr) => {
            return { segments: [], pnr }
        })

        this.form.field?.segments?.items.map((item) => {
            if (this.$columns[item.part_number.value] !== undefined) {
                this.$columns[item.part_number.value].segments.push(item)
            } else {
                this.$columns[0].segments.push(item)
            }
        })

        if (!update) {
            this.$columns.filter((item, index) => {
                return !!item
            })
        }
    }

    $addEmptyPartNumber() {
        if (this.$columns.length < this.form.field.segments.items.length) {
            this.$columns.push({ segments: []})
        }
    }

    $prepareTkt() {
        this.updateSegmentsGroup()

        this.columnsTkt = computed(() => {
            let prevKey = 0
            let preTicketNumber = 0
            let newArr = []

            const items = this.form.field.segments.$getData()
            items.sort((a, b) => {
                if (a.pre_ticket_number < b.pre_ticket_number) {
                    return -1
                } else if (a.pre_ticket_number > b.pre_ticket_number) {
                    return 1
                }

                return 0
            })

            const tkt = items.reduce((arr, item, index) => {
                if (item.pre_ticket_number !== preTicketNumber) {
                    prevKey++
                    preTicketNumber = item.pre_ticket_number
                    newArr = []
                }

                newArr.push({ ...item })
                arr[prevKey - 1] = arr[prevKey - 1] || []
                arr[prevKey - 1].segments = arr[prevKey - 1].segments || []
                arr[prevKey - 1].segments = newArr

                return arr
            }, [])

            return tkt.filter((item, index) => {
                return !!item
            })
        })
    }

    $clearOneTktUpgrade() {
        this.$info.oneTktForm.field.preTickets.items.forEach(preTicket => {
            preTicket.prices.items.forEach(price => {
                price.upgrade.silentValue = 0
                price.is_award_upgrade.silentValue = 0
                price.upgrade_miles_count.silentValue = 0
                price.upgrade_tax_amount.silentValue = 0
                price.upgrade_mile_price_program_id.silentValue = 0
            })
        })
    }

    $clearMultiTktUpgrade(index = 0) {
        this.$info.multiTktForm.field.preTickets.get(index, true).prices.items.forEach(price => {
            price.upgrade.silentValue = 0
            price.is_award_upgrade.silentValue = 0
            price.upgrade_miles_count.silentValue = 0
            price.upgrade_tax_amount.silentValue = 0
            price.upgrade_mile_price_program_id.silentValue = 0
        })
    }

    $setSellType(oldType, type, pre_ticket_number = 100) {
        if (type === 'award') {
            this.form.field.check_payment_ps.value = TransactionService.defaultCheckPaymentPs()
        }

        const formObj = this.formData
        const paxInfo = `${formObj.adult_count} ${formObj.child_count} ${formObj.infant_count}`
        const index = this.$info.multiTktForm.field.preTickets.items.findIndex(x => x.pre_ticket_number.value === pre_ticket_number)
        const currentOneTktValue = this.$info.oneTktForm.data.preTickets[0]
        const currentMultiTktValue = this.$info.multiTktForm.data.preTickets[index]
        const oldTitle = oldType + '_' + pre_ticket_number
        const newTitle = type + '_' + pre_ticket_number

        this.$info.oldValues[oldTitle] = {
            paxInfo: paxInfo,
            data: currentOneTktValue,
        }

        if (this.oneTkt && pre_ticket_number === 100) {
            this.$info.oldValues[oldTitle] = {
                paxInfo: paxInfo,
                data: currentOneTktValue,
            }

            if (this.$info.oldValues[newTitle]?.paxInfo === paxInfo) {
                this.$info.oneTktForm.field.preTickets.updateItem(0, this.$info.oldValues[newTitle].data, true)
            } else {
                delete this.$info.oldValues[newTitle]
            }

            this.$info.oneTktForm.field.preTickets.get(0, true).sell_type.value = type

            if (type === 'award') {
                this.$clearOneTktUpgrade()
            }
        } else {
            this.$info.oldValues[oldTitle] = {
                paxInfo: paxInfo,
                data: currentMultiTktValue,
            }

            if (this.$info.oldValues[newTitle]?.paxInfo === paxInfo) {
                this.$info.multiTktForm.field.preTickets.updateItem(index, this.$info.oldValues[newTitle].data, true)
            } else {
                delete this.$info.oldValues[newTitle]
            }

            this.$info.multiTktForm.field.preTickets.get(index, true).sell_type.value = type

            if (type === 'award') {
                this.$clearMultiTktUpgrade(index)
            }
        }

        this.updateSegmentsGroup()
    }

    $handlePrices(indexPrice, editField, pre_ticket_number = 100) {
        let prices = null

        const handlePrice = () => {
            const fareAmount = prices.get(indexPrice, true).fare_amount.value
            const commissionPs = prices.get(indexPrice, true).commission_ps.value

            if (editField === 'fareAmount' && !!commissionPs) {
                // editField = ''
                prices.get(indexPrice, true).commission.value = Number(Number(fareAmount) * Number(commissionPs) / 100).toFixed(2)
            }

            if (editField === 'commission' && !!fareAmount) {
                // editField = ''
                prices.get(indexPrice, true).commission_ps.value = Number(0.00)
            }

            if (editField === 'commissionPs' && !!commissionPs) {
                // editField = ''
                prices.get(indexPrice, true).commission.value = Number(Number(fareAmount) * Number(commissionPs) / 100).toFixed(2)
            }
        }

        if (this.oneTkt) {
            prices = this.$info.oneTktForm.field.preTickets.get(0, true).prices
            // prices.event.addListener('change', handlePrice)
            handlePrice()
        } else {
            const index = this.$info.multiTktForm.field.preTickets.items.findIndex(x => x.pre_ticket_number.value === pre_ticket_number)
            prices = this.$info.multiTktForm.field.preTickets.get(index, true).prices
            // prices.event.addListener('change', handlePrice)
            handlePrice()
        }
    }

    $getData() {
        const data = {
            ...this.form.data,
            ...this.preTicketForm.data,
        }

        if (!this.oneTkt) {
            data.preTickets = data.preTickets.filter(preTicket => {
                return !!data.segments.find(record => record.pre_ticket_number === preTicket.pre_ticket_number)
            })

            if (this.mode === 'clone') {
                data.preTickets.forEach((preTicket, index) => {
                    preTicket.id = `new_${index}`
                })
            }

            const segments = data.segments.map(segment => {
                const index = data.preTickets.findIndex(preTicket => {
                    return preTicket.pre_ticket_number === segment.pre_ticket_number
                })
                segment.pre_ticket_id = index >= 0 ? `${data.preTickets[index].id}` : null
                //
                delete segment.typePreticketNumber

                return segment
            }).filter((item, index) => {
                return !!item
            })
        } else {
            if (this.mode === 'clone') {
                data.segments.forEach((segment, index) => {
                    segment.pre_ticket_id = `new_0`
                    delete segment.typePreticketNumber
                })
            }
        }

        return data
    }

    // -------------------- Calc functions ------------------------

    calcSummary() {
        const data = {
            airlineChargeAmount: 0,
            inhouseChargeAmount: 0,
            profit: 0,
            grandTotal: 0,
            markUpAlert: false,
        }

        const preTicketSummary = {}

        this.form.field.segments.items.forEach(segment => {
            preTicketSummary[segment.pre_ticket_id.value] = preTicketSummary[segment.pre_ticket_id.value] || {}
            preTicketSummary[segment.pre_ticket_id.value][segment.pre_ticket_number.value] = 1
        })

        if (this.oneTkt) {
            const preTicket = this.$info.oneTktForm.field.preTickets.get(0, true)

            data.grandTotal += Number.fromString(this.grandTotal(preTicket)) * this.columnsTkt.length
            data.airlineChargeAmount += Number.fromString(this.airlineChargeAmount(preTicket)) * this.columnsTkt.length
            data.inhouseChargeAmount += Number.fromString(this.inhouseChargeAmount(preTicket)) * this.columnsTkt.length
            data.profit += Number.fromString(this.profit(preTicket)) * this.columnsTkt.length
            data.markUpAlert = data.markUpAlert ? true : this.markUpAlert(preTicket)
        } else {
            this.columnsTkt.forEach(item => {
                const index = this.$info.multiTktForm.field.preTickets.items.findIndex(x => x.pre_ticket_number.value === item.segments[0]?.pre_ticket_number)
                const preTicketTkt = this.$info.multiTktForm.field.preTickets.get(index, true)

                if (!preTicketTkt) {
                    return
                }

                data.grandTotal += Number.fromString(this.grandTotal(preTicketTkt))
                data.airlineChargeAmount += Number.fromString(this.airlineChargeAmount(preTicketTkt))
                data.inhouseChargeAmount += Number.fromString(this.inhouseChargeAmount(preTicketTkt))
                data.profit += Number.fromString(this.profit(preTicketTkt))
                data.markUpAlert = data.markUpAlert ? true : this.markUpAlert(preTicketTkt)
            })
        }

        this.$info.summary.airlineChargeAmount = data.airlineChargeAmount
        this.$info.summary.inhouseChargeAmount = data.inhouseChargeAmount
        this.$info.summary.profit = data.profit
        this.$info.summary.grandTotal = data.grandTotal
        this.$info.summary.markUpAlert = data.markUpAlert

        return this.$info.summary
    }

    calc(pre_ticket_number = null) {
        this.$changeAllTktToCk()

        const data = {
            airlineChargeAmount: 0,
            inhouseChargeAmount: 0,
            profit: 0,
            grandTotal: 0,
        }

        const preTicketSummary = {}

        this.form.field.segments.items.forEach(segment => {
            preTicketSummary[segment.pre_ticket_id.value] = preTicketSummary[segment.pre_ticket_id.value] || {}
            preTicketSummary[segment.pre_ticket_id.value][segment.pre_ticket_number.value] = 1
        })

        if (!!pre_ticket_number && !this.oneTkt) {
            const index = this.$info.multiTktForm.field.preTickets.items.findIndex(x => x.pre_ticket_number.value === pre_ticket_number)
            const preTicket = this.$info.multiTktForm.field.preTickets.get(index, true)

            data.grandTotal += Number.fromString(this.grandTotal(preTicket))
            data.airlineChargeAmount += Number.fromString(this.airlineChargeAmount(preTicket))
            data.inhouseChargeAmount += Number.fromString(this.inhouseChargeAmount(preTicket))
            data.profit += Number.fromString(this.profit(preTicket))
        } else {
            const preTicket = this.$info.oneTktForm.field.preTickets.get(0, true)

            data.grandTotal += Number.fromString(this.grandTotal(preTicket)) * this.columnsTkt.length
            data.airlineChargeAmount += Number.fromString(this.airlineChargeAmount(preTicket)) * this.columnsTkt.length
            data.inhouseChargeAmount += Number.fromString(this.inhouseChargeAmount(preTicket)) * this.columnsTkt.length
            data.profit += Number.fromString(this.profit(preTicket)) * this.columnsTkt.length
        }

        this.calcSummary()

        return data
    }

    upgradeAmount(preTicket) {
        let upgrade = 0

        preTicket?.prices?.items.forEach((price, index) => {
            const passenger_type = price.passenger_type.value
            let passenger_count = 0

            if (passenger_type === 'adult') {
                passenger_count = Number.fromString(this.adult_count.value)
            }

            if (passenger_type === 'child') {
                passenger_count = Number.fromString(this.child_count.value)
            }

            if (passenger_type === 'infant') {
                passenger_count = Number.fromString(this.infant_count.value)
            }
            upgrade += Number.fromString(price.upgrade.value) * passenger_count

            // if (price.is_award_upgrade.value === 1) {
            //     const price_program = price.upgrade_mile_price_program_id.currentItem()
            //
            //     if (price_program?.mile_price) {
            //         upgrade += (Number.fromString(price.upgrade_miles_count.value) * Number(price_program.mile_price)) * passenger_count
            //     }
            //     upgrade += Number.fromString(price.upgrade_tax_amount.value) * passenger_count
            // } else {
            //
            // }
        })

        return upgrade
    }

    fareAmount(preTicket) {
        let fare = 0

        preTicket?.prices?.items.forEach((price, index) => {
            if (price.passenger_type.value === 'adult') {
                fare += Number.fromString(price.fare_amount.value) * Number.fromString(this.adult_count.value)
            }

            if (price.passenger_type.value === 'child') {
                fare += Number.fromString(price.fare_amount.value) * Number.fromString(this.child_count.value)
            }

            if (price.passenger_type.value === 'infant') {
                fare += Number.fromString(price.fare_amount.value) * Number.fromString(this.infant_count.value)
            }
        })

        return fare
    }

    commissionPs(preTicket) {
        if (preTicket.sell_type.value === 'private') {
            return 25
        }

        return 0
    }

    commissionValue(preTicket) {
        if (preTicket.sell_type.value === 'private') {
            return this.totalMarkUP(preTicket)
        }

        if (this.preTicketType === 'award') {
            return 0
        }

        let commission = 0

        preTicket?.prices?.items.forEach((price, index) => {
            if (price.passenger_type.value === 'adult') {
                commission += Number.fromString(price.commission.value) * Number.fromString(this.adult_count.value)
            }

            if (price.passenger_type.value === 'child') {
                commission += Number.fromString(price.commission.value) * Number.fromString(this.child_count.value)
            }

            if (price.passenger_type.value === 'infant') {
                commission += Number.fromString(price.commission.value) * Number.fromString(this.infant_count.value)
            }
        })

        return commission
    }

    totalMarkUP(preTicket) {
        if (preTicket.sell_type.value === 'private') {
            const markUP = this.totalSellPrice(preTicket) - this.totalNetPrice(preTicket)
            const commission = this.fareAmount(preTicket) * this.commissionPs(preTicket) / 100

            return Math.min(commission, markUP)
        }

        return 0
    }

    totalNetPrice(preTicket) {
        let sum = 0

        if (preTicket.sell_type.value === 'award') {
            const mileProgram = this.milePriceProgramList.filter(item => item.value === preTicket.mile_price_program_id?.value)[0]?.mile_price ?? 0
            sum = Number.fromString(preTicket?.miles_count.value) * mileProgram + Number.fromString(preTicket?.award_tax_amount.value)
        } else {
            preTicket?.prices?.items.forEach((price, index) => {
                if (price.passenger_type.value === 'adult') {
                    sum += Number.fromString(price.net_price.value) * Number.fromString(this.adult_count.value)
                }

                if (price.passenger_type.value === 'child') {
                    sum += Number.fromString(price.net_price.value) * Number.fromString(this.child_count.value)
                }

                if (price.passenger_type.value === 'infant') {
                    sum += Number.fromString(price.net_price.value) * Number.fromString(this.infant_count.value)
                }
            })
        }

        return sum
    }

    airlineChargeAmount(preTicket) {
        if (preTicket.sell_type.value === 'award') {
            // let sumTax = 0
            //todo: remove this code from here
            preTicket?.prices?.items.forEach((price, index) => {
                const mileProgram = this.milePriceProgramList.filter(item => item.value === preTicket.mile_price_program_id?.value)[0]?.mile_price ?? 0
                const result = Number.fromString(preTicket.miles_count.value) * mileProgram + Number.fromString(preTicket.award_tax_amount.value)

                if (price.passenger_type.value === 'adult') {
                    price.net_price.value = parseFloat(result).toFixed(2)
                }

                if (price.passenger_type.value === 'child') {
                    price.net_price.value = parseFloat(result).toFixed(2)
                }

                if (price.passenger_type.value === 'infant') {
                    price.net_price.value = parseFloat(result).toFixed(2)
                }
            })

            return Number.fromString(preTicket.award_tax_amount.value)
        }

        if (preTicket?.sell_type.value === 'inhouse' || this.$info.is_award) {
            return 0
        }

        let sum = 0

        preTicket?.prices?.items.forEach((price, index) => {
            if (price.passenger_type.value === 'adult') {
                sum += Number.fromString(price.net_price.value) * Number.fromString(this.adult_count.value)
            }

            if (price.passenger_type.value === 'child') {
                sum += Number.fromString(price.net_price.value) * Number.fromString(this.child_count.value)
            }

            if (price.passenger_type.value === 'infant') {
                sum += Number.fromString(price.net_price.value) * Number.fromString(this.infant_count.value)
            }
        })

        sum += this.totalMarkUP(preTicket)

        return +sum
    }

    totalSellPrice(preTicket) {
        let sum = 0

        preTicket?.prices?.items.forEach((price, index) => {
            if (price.passenger_type.value === 'adult') {
                sum += Number.fromString(price.sell_amount.value) * Number.fromString(this.adult_count.value)
            }

            if (price.passenger_type.value === 'child') {
                sum += Number.fromString(price.sell_amount.value) * Number.fromString(this.child_count.value)
            }

            if (price.passenger_type.value === 'infant') {
                sum += Number.fromString(price.sell_amount.value) * Number.fromString(this.infant_count.value)
            }
        })

        return sum
    }

    inhouseChargeAmount(preTicket) {
        return this.totalSellPrice(preTicket) - this.airlineChargeAmount(preTicket)
    }

    grandTotal(preTicket) {
        return Number.fromString(this.inhouseChargeAmount(preTicket)) + Number.fromString(this.airlineChargeAmount(preTicket))
    }

    profit(preTicket) {
        const ck = this.inhouseChargeAmount(preTicket) * Number(this.form.field.check_payment_ps.value) / 100 // 0.035 || 0

        return this.totalSellPrice(preTicket) - this.totalNetPrice(preTicket) - this.totalMarkUP(preTicket) - this.upgradeAmount(preTicket) - ck + this.commissionValue(preTicket)
    }

    totalMinimalMarkUp(preTicket) {
        let sum = 0

        preTicket?.prices?.items.forEach((price, index) => {
            sum += price.mark_up.value
        })

        return sum
    }

    markUpAlert(preTicket) {
        const markupNotSet = preTicket?.prices?.items.every(price => {
            return !parseInt(price.mark_up.value)
        })

        if (this.totalSellPrice(preTicket) === 0) {
            return false
        } else if (markupNotSet) {
            return false
        }

        return this.totalSellPrice(preTicket) < this.totalNetPrice(preTicket) + this.totalMinimalMarkUp(preTicket)
    }
}
