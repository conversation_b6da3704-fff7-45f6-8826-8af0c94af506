import router from '~/router'
import type { AnyObject } from '@/types'
import { useAppLayout } from '~/stores/useAppLayout'

export type Route = {
    name: string,
    params?: AnyObject,
    query?: AnyObject,
}

export type Period = {
    month: string,
    year: string
}

export const routeTo = (name: string, params: AnyObject = {}, query: AnyObject = {}): Route => {
    const route: Route = { name }

    if (params) {
        route.params = params
    }

    if (query) {
        route.query = JSON.parse(JSON.stringify(query)) as AnyObject
    }

    return route
}

export const linkTo = (route: Route): string => router.resolve(route).fullPath

export const goTo = async (route: Route | string, keepQuery = false) => {
    if (keepQuery) {
        route = withCurrentQuery(route)
    }

    return router.push(route)
}

export const withCurrentQuery = (route: Route | string) => {
    if (typeof route === 'object') {
        const routeQuery = structuredClone(toRaw(router.currentRoute.value?.query) || {})
        delete routeQuery['modal']

        route.query = Object.assign({}, routeQuery, route.query || {})
    }

    if (typeof route === 'string') {
        // @todo add query to string route
        console.warn('Does not support string routes yet')
    }

    return route
}

export const openInNewTab = (route: Route | string) => {
    if (typeof route === 'object') {
        route = linkTo(route)
    }

    window.open(route, '_blank')
}

// Client
export const routeToClient = (pk: PrimaryKey) => routeTo('clients.edit', { pk: pk })
export const linkToClient = (pk: PrimaryKey) => linkTo(routeToClient(pk))
export const goToClient = (pk: PrimaryKey, keepQuery = false) => goTo(routeToClient(pk), keepQuery)
// ClientList
export const routeToClientList = () => routeTo('clients')
export const linkToClientList = () => linkTo(routeToClientList())
export const goToClientList = (keepQuery = false) => goTo(routeToClientList(), keepQuery)
// Agent
export const routeToAgent = (pk: PrimaryKey) => routeTo('agents.edit', { pk })
export const linkToAgent = (pk: PrimaryKey) => linkTo(routeToAgent(pk))
export const goToAgent = (pk: PrimaryKey, keepQuery = false) => goTo(routeToAgent(pk), keepQuery)// Agent
// AgentList
export const routeToAgentList = () => routeTo('agents')
export const linkToAgentList = () => linkTo(routeToAgentList())
export const goToAgentList = (keepQuery = false) => goTo(routeToAgentList(), keepQuery)
// Lead
export const routeToLead = (pk: number | PrimaryKey) => routeTo('leads', { pk })
export const linkToLead = (pk: number | PrimaryKey) => linkTo(routeToLead(pk))
export const goToLead = (pk: number | PrimaryKey, keepQuery = false) => goTo(routeToLead(pk), keepQuery)
// Lead Management
export const routeToLeadManagement = (pk: number | PrimaryKey) => routeTo('lead.management', { pk })
export const linkToLeadManagement = (pk: number | PrimaryKey) => linkTo(routeToLeadManagement(pk))
export const goToLeadManagement = (pk: number | PrimaryKey, keepQuery = false) => goTo(routeToLeadManagement(pk), keepQuery)

// LeadList
export const routeToLeadList = () => routeTo(useAppLayout().leadPageMode === 'list' ? 'leads.list' : 'leads')
export const linkToLeadList = () => linkTo(routeToLeadList())
export const goToLeadList = (keepQuery = false) => goTo(routeToLeadList(), keepQuery)

// Lead Management List
export const routeToLeadManagementList = () => routeTo(useAppLayout().leadPageMode === 'list' ? 'lead.management.list' : 'lead.management')
export const routeToLeadManagementCardsList = () => routeTo('lead.management')
export const linkToLeadManagementList = () => linkTo(routeToLeadManagementList())
export const goToLeadManagementList = (keepQuery = false) => goTo(routeToLeadManagementList(), keepQuery)
export const goToLeadManagementCardsList = () => goTo(routeToLeadManagementCardsList())

// LeadBonus
export const routeToLeadBonus = () => routeTo('leads.bonus')
export const linkToLeadBonus = () => linkTo(routeToLeadBonus())
export const goToLeadBonus = (keepQuery = false) => goTo(routeToLeadBonus(), keepQuery)

// ExpertLead
export const routeToExpertLead = (pk: number | PrimaryKey) => routeTo('experts.edit', { pk })
export const linkToExpertLead = (pk: number | PrimaryKey) => linkTo(routeToExpertLead(pk))
export const goToExpertLead = (pk: number | PrimaryKey, keepQuery = false) => goTo(routeToExpertLead(pk), keepQuery)

// ExpertLeadList
export const routeToExpertLeadList = () => routeTo(useAppLayout().leadPageMode === 'list' ? 'experts.list' : 'experts')
export const linkToExpertLeadList = () => linkTo(routeToExpertLeadList())
export const goToExpertLeadList = (keepQuery = false) => goTo(routeToExpertLeadList(), keepQuery)

// Sale
export const routeToSale = (id: number | PrimaryKey) => routeTo('sale.edit', { id })
export const linkToSale = (id: number | PrimaryKey) => linkTo(routeToSale(id))
export const goToSale = (id: number | PrimaryKey, keepQuery = false) => goTo(routeToSale(id), keepQuery)
// SaleList
export const routeToSaleList = () => routeTo('sales')
export const linkToSaleList = () => linkTo(routeToSaleList())
export const goToSaleList = (keepQuery = false) => goTo(routeToSaleList(), keepQuery)
// Sale Communication
export const routeToSaleCommunication = () => routeTo('sales.processing')
export const linkToSaleCommunication = () => linkTo(routeToSaleCommunication())
export const goToSaleCommunication = (keepQuery = false) => goTo(routeToSaleCommunication(), keepQuery)
// Customer Support Requests
export const routeToCustomerSupportIssueList = () => routeTo('issues-customer-support')
export const linkToCustomerSupportIssueList = () => linkTo(routeToCustomerSupportIssueList())
export const goToCustomerSupportIssueList = (keepQuery = false) => goTo(routeToCustomerSupportIssueList(), keepQuery)

export const routeToCustomerSupportIssue = (pk: PrimaryKey) => routeTo('issues-customer-support', { pk })
export const linkToCustomerSupportIssue = (pk: PrimaryKey) => linkTo(routeToCustomerSupportIssue(pk))
export const goToCustomerSupportIssue = (pk: PrimaryKey, keepQuery = false) => goTo(routeToCustomerSupportIssue(pk), keepQuery)
// Issues
export const routeToIssueList = () => routeTo('issues-approve-request')
export const linkToIssueList = () => linkTo(routeToIssueList())
export const goToIssueList = (keepQuery = false) => goTo(routeToIssueList(), keepQuery)

export const routeToIssue = (pk: PrimaryKey) => routeTo('issues', { pk })
export const linkToIssue = (pk: PrimaryKey) => linkTo(routeToIssue(pk))
export const goToIssue = (pk: PrimaryKey, keepQuery = false) => goTo(routeToIssue(pk), keepQuery)

// Expected Amounts
export const routeToExpectedAmount = (recordLocator: string) => routeTo('expected-amounts', {}, { tags: `Record Locator: ${recordLocator}` })
export const linkToExpectedAmount = (recordLocator: string) => linkTo(routeToExpectedAmount(recordLocator))
export const goToExpectedAmount = (recordLocator: string) => goTo(routeToExpectedAmount(recordLocator))

export const routeToExpectedAmountPk = (pk: string) => routeTo('expected-amounts', {}, { tags: `ID: ${pk}` })
export const linkToExpectedAmountPk = (pk: string) => linkTo(routeToExpectedAmountPk(pk))
export const goToExpectedAmountPk = (pk: string) => goTo(routeToExpectedAmountPk(pk))

// Bookkeeping
export const routeToBookkeeping = () => routeTo('bookkeeping')
export const linkToBookkeeping = () => linkTo(routeToBookkeeping())
export const goToBookkeeping = (keepQuery = false) => goTo(routeToBookkeeping(), keepQuery)
// Award
export const routeToAwardAccountList = () => routeTo('award-accounts')
export const linkToAwardAccountList = () => linkTo(routeToAwardAccountList())
export const goToAwardAccountList = (keepQuery = false) => goTo(routeToAwardAccountList(), keepQuery)

export const routeToAwardAccount = (pk: PrimaryKey) => routeTo('award-accounts', { pk })
export const linkToAwardAccount = (pk: PrimaryKey) => linkTo(routeToAwardAccount(pk))
export const goToAwardAccount = (pk: PrimaryKey, keepQuery = false) => goTo(routeToAwardAccount(pk), keepQuery)

// Settings
export const routeToSettings = () => routeTo('settings')
export const linkToSettings = () => linkTo(routeToSettings())
export const goToSettings = (keepQuery = false) => goTo(routeToSettings(), keepQuery)
// Festive Events Settings
export const routeToFestiveEventSettings = () => routeTo('settings.festive-events')
export const linkToFestiveEventSettings = () => linkTo(routeToFestiveEventSettings())
export const goToFestiveEventSettings = (keepQuery = false) => goTo(routeToFestiveEventSettings(), keepQuery)

//Transactions Old todo: remove after bk ready
export const routeToTransactionListOld = () => routeTo('transactions.old')
export const linkToTransactionListOld = () => linkTo(routeToTransactionListOld())
export const goToTransactionListOld = (keepQuery = false) => goTo(routeToTransactionListOld(), keepQuery)
//Transactions
export const routeToTransactionList = () => routeTo('transactions')
export const linkToTransactionList = () => linkTo(routeToTransactionList())
export const goToTransactionList = (keepQuery = false) => goTo(routeToTransactionList(), keepQuery)
// Transaction
export const routeToTransaction = (pk: PrimaryKey) => routeTo('transaction.edit', { pk })
export const linkToTransaction = (pk: PrimaryKey) => linkTo(routeToTransaction(pk))
export const goToTransaction = (pk: PrimaryKey, keepQuery = false) => goTo(routeToTransaction(pk), keepQuery)
//Invoices
export const routeToInvoiceList = () => routeTo('invoices')
export const linkToInvoiceList = () => linkTo(routeToInvoiceList())
export const goToInvoiceList = (keepQuery = false) => goTo(routeToInvoiceList(), keepQuery)
// Invoice
export const routeToInvoice = (pk: PrimaryKey) => routeTo('invoice.edit', { pk })
export const linkToInvoice = (pk: PrimaryKey) => linkTo(routeToInvoice(pk))
export const goToInvoice = (pk: PrimaryKey, keepQuery = false) => goTo(routeToInvoice(pk), keepQuery)

//Airline reports
export const routeToAirlineReportList = () => routeTo('airline.reports')
export const linkToAirlineReportList = () => linkTo(routeToAirlineReportList())
export const goToAirlineReportList = (keepQuery = false) => goTo(routeToAirlineReportList(), keepQuery)

// Airline report
export const routeToAirlineReport = (pk: PrimaryKey) => routeTo('airline.report.edit', { pk })
export const linkToAirlineReport = (pk: PrimaryKey) => linkTo(routeToAirlineReport(pk))
export const goToAirlineReport = (pk: PrimaryKey, keepQuery = false) => goTo(routeToAirlineReport(pk), keepQuery)

// AgentReport
export const routeToAgentReport = (pk: PrimaryKey) => routeTo('agent.report.edit', { pk })
export const linkToAgentReport = (pk: PrimaryKey) => linkTo(routeToAgentReport(pk))
export const goToAgentReport = (pk: PrimaryKey, keepQuery = false) => goTo(routeToAgentReport(pk), keepQuery)

// AgentReportList
export const routeToAgentReportList = () => routeTo('agent.reports')
export const linkToAgentReportList = () => linkTo(routeToAgentReportList())
export const goToAgentReportList = (keepQuery = false) => goTo(routeToAgentReportList(), keepQuery)

// AgentInvoice
export const routeToAgentInvoice = () => routeTo('agent.invoice')
export const linkToAgentInvoice = () => linkTo(routeToAgentInvoice())
export const goToAgentInvoice = () => goTo(routeToAgentInvoice(), false)

// Summary Lead Management
export const routeToLeadManagementDashboard = () => routeTo('summary.dashboard.lead-management')
export const linkToLeadManagementDashboard = () => linkTo(routeToLeadManagementDashboard())
export const goToLeadManagementDashboard = () => goTo(linkToLeadManagementDashboard(), false)

// Summary Expert
export const routeToExpertDashboard = () => routeTo('summary.dashboard.expert')
export const linkToExpertDashboard = () => linkTo(routeToExpertDashboard())
export const goToExpertDashboard = () => goTo(linkToExpertDashboard(), false)

// Summary Sale
export const routeToSaleDashboard = () => routeTo('summary.dashboard.sale')
export const linkToSaleDashboard = () => linkTo(routeToSaleDashboard())
export const goToSaleDashboard = () => goTo(linkToSaleDashboard(), false)

// Release Note
export const routeToReleasePostList = () => routeTo('release.posts')
export const linkToReleasePostList = () => linkTo(routeToReleasePostList())
export const goToReleasePostList = () => goTo(linkToReleasePostList(), false)

export const routeToReleasePost = (pk: PrimaryKey) => routeTo('release.posts.edit', { pk })
export const linkToReleasePost = (pk: PrimaryKey) => linkTo(routeToReleasePost(pk))
export const goToReleasePost = (pk: PrimaryKey, keepQuery = false) => goTo(routeToReleasePost(pk), keepQuery)

// Flight Hack
export const routeToFlightHackList = () => routeTo('flight-hack.list')
export const linkToFlightHackList = () => linkTo(routeToFlightHackList())
export const goToFlightHackList = () => goTo(routeToFlightHackList())

export const routeToFlightHack = (id: string) => routeTo('flight-hack.edit', { id })
export const linkToFlightHack = (id: string) => linkTo(routeToFlightHack(id))
export const goToFlightHack = (id: string, keepQuery = false) => goTo(routeToFlightHack(id), keepQuery)

export const routeToCreateFlightHack = () => routeTo('flight-hack.create')
export const linkToCreateFlightHack = () => linkTo(routeToCreateFlightHack())
export const goToCreateFlightHack = (keepQuery = false) => goTo(routeToCreateFlightHack(), keepQuery)

// Markup
export const routeToMarkupList = (category: 'offer' | 'baggage') => routeTo('markup.list', { category })

export const routeToMarkupOfferRule = (id: string) => routeTo('markup.edit', { category: 'offer', id })
export const linkToMarkupOfferRule = (id: string) => linkTo(routeToMarkupOfferRule(id))
export const goToMarkupOfferRule = (id: string, keepQuery = false) => goTo(routeToMarkupOfferRule(id), keepQuery)

export const routeToMarkupBaggageRule = (id: string) => routeTo('markup.edit', { category: 'baggage', id })
export const linkToMarkupBaggageRule = (id: string) => linkTo(routeToMarkupBaggageRule(id))
export const goToMarkupBaggageRule = (id: string, keepQuery = false) => goTo(routeToMarkupBaggageRule(id), keepQuery)

export const routeToMarkupOfferCreateRule = () => routeTo('markup.create', { category: 'offer' })
export const linkToMarkupOfferCreateRule = () => linkTo(routeToMarkupOfferCreateRule())
export const goToMarkupOfferCreateRule = (keepQuery = false) => goTo(routeToMarkupOfferCreateRule(), keepQuery)

export const routeToMarkupBaggageCreateRule = () => routeTo('markup.create', { category: 'baggage' })
export const linkToMarkupBaggageCreateRule = () => linkTo(routeToMarkupBaggageCreateRule())
export const goToMarkupBaggageCreateRule = (keepQuery = false) => goTo(routeToMarkupBaggageCreateRule(), keepQuery)

// Price Drops Voidable
export const routeToPriceDropsVoidable = () => routeTo('price.drops.voidable')
export const linkToPriceDropsVoidable = () => linkTo(routeToPriceDropsVoidable())
export const goToPriceDropsVoidable = () => goTo(linkToPriceDropsVoidable(), false)

// Price Drops Refundable
export const routeToPriceDropsRefundable = () => routeTo('price.drops.refundable')
export const linkToPriceDropsRefundable = () => linkTo(routeToPriceDropsRefundable())
export const goToPriceDropsRefundable = () => goTo(linkToPriceDropsRefundable(), false)

// Credit Voucher
export const routeToVoucherList = () => routeTo('credit.vouchers')
export const linkToVoucherList = () => linkTo(routeToVoucherList())
export const goToVoucherList = () => goTo(linkToVoucherList(), false)

// ElrFrtCheckList
export const routeToElrFrtCheckList = () => routeTo('elrfrt.check')
export const linkToElrFrtCheckList = () => linkTo(routeToElrFrtCheckList())
export const goToElrFrtCheckList = (keepQuery = false) => goTo(linkToElrFrtCheckList(), keepQuery)

// Gambling Tool
export const routeToGamblingLot = (pk: number | PrimaryKey) => routeTo('betting.tool', { pk })
export const linkToGamblingLot = (pk: number | PrimaryKey) => linkTo(routeToGamblingLot(pk))
export const goToGamblingLot = (pk: number | PrimaryKey, keepQuery = false) => goTo(routeToGamblingLot(pk), keepQuery)

// Pnr Info
export const routeToPnrInfo = (pk: number | PrimaryKey) => routeTo('pnr.info', { pk })
export const linkToPnrInfo = (pk: number | PrimaryKey) => linkTo(routeToPnrInfo(pk))
export const goToPnrInfo = (pk: number | PrimaryKey, keepQuery = false) => goTo(linkToPnrInfo(pk), keepQuery)

