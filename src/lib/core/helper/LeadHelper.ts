export const getItinerary = function(itinerary_type: string, short = true): string | null {
    return ({
        oneWay: short ? 'OW' : 'One Way',
        multiCity: short ? 'MC' : 'Multi City',
    })[itinerary_type] ?? null
}

export const dateFormatItinerary = function(date: string, itinerary_type: string, short = true): string {
    return getItinerary(itinerary_type, short) ?? date
}

export const getLeadPqCounterColor = (lead) => {
    if (lead.priceQuoteCount === 0) {
        return 'text-gray-500'
    } else if (lead.priceQuoteCount > 0) {
        const now = Math.floor(Date.now() / 1000)
        const diff = now - lead.priceQuoteLastTime
        const diff_days = diff / 86400

        if (diff_days < 3) return 'text-green-500'
        else if (7 > diff_days && diff_days > 3) return 'text-orange-500'
        else if (diff_days > 7) return 'text-red-500'
        else return 'text-danger'
    }
}

export const leadStatusHelper = {
    'is_bonus': {
        short_name: 'B',
        full_name: 'Bonus',
        color_class: '--bonus',
        color_tailwind: 'bg-vodka-200',
    },

    'Sold': {
        short_name: 'S',
        full_name: 'Sold',
        color_class: '--sold',
        color_tailwind: 'bg-vista-blue-300',
    },

    'New': {
        short_name: 'N',
        full_name: 'New',
        color_class: '--new',
        color_tailwind: 'bg-orange-500',
    },

    'Follow up': {
        short_name: 'F',
        full_name: 'Follow up',
        color_class: '--follow-up',
        color_tailwind: 'bg-feldspar-300',
    },

    'Potential': {
        short_name: 'P',
        full_name: 'Potential',
        color_class: '--potential',
        color_tailwind: 'bg-vodka-200',
    },

    'Closed': {
        short_name: 'C',
        full_name: 'Closed',
        color_class: '--closed',
        color_tailwind: 'bg-red-500',
    },

    'Lost': {
        short_name: 'L',
        full_name: 'Lost',
        color_class: '--lost',
        color_tailwind: 'bg-slate-500',
    },

    'Sale rejected': {
        short_name: 'R',
        full_name: 'Sale rejected',
        color_class: '--sale-rejected',
        color_tailwind: 'bg-rose-700',
    },

    'Sale rejected (inactive)': {
        short_name: 'R',
        full_name: 'Sale rejected (inactive)',
        color_class: '--sale-rejected-inactive',
        color_tailwind: 'bg-indigo-900',
    },
    'Fraud': {
        short_name: 'FD',
        full_name: 'Fraud',
        color_class: '--fraud',
        color_tailwind: 'bg-danger',
    },
}
