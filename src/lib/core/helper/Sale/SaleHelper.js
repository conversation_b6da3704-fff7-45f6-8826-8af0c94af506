import UnMountModel from '@/lib/mixin/UnMount/UnMountModel'
import ReactiveHelper from '@/lib/core/helper/ReactiveHelper'
import SalePageModel from '@/api/models/Sale/SalePageModel'
import SaleVersionModel from '@/api/models/Sale/SaleVersionModel'
import PassengerModel from '@/api/models/Passenger/PassengerModel'
import TicketModel from '@/api/models/Ticket/TicketModel'
import AdditionalExpenseModel from '@/api/models/AdditionalExpense/AdditionalExpenseModel'
import IncentiveSaleModel from '@/api/models/Sale/IncentiveSaleModel'
import SaleCreditCardModel from '@/api/models/Sale/SaleCreditCardModel'
import SaleTransactionModel from '@/api/models/Sale/SaleTransactionModel'
import { computed, getCurrentInstance } from 'vue'
import TransactionService from '@/lib/service/TransactionService'
import SalePriceQuoteModel from '@/api/models/Sale/SalePriceQuoteModel'
import ClientInvoiceModel from '@/api/models/Invoice/ClientInvoiceModel'
import SaleListModel from '@/api/models/Sale/SaleListModel'
import SaleModel from '@/api/models/Sale/SaleModel'
import { watchOnce } from '@vueuse/core'
import ProjectCardModel from '@/api/models/Project/ProjectCardModel'
import { getWorkspaceProject, getWorkspaceProjectDefinedInInstance } from '@/lib/core/helper/WorkspaceProjectHelper'
import { getTicketColorClass } from '@/lib/core/helper/ColorClassHelper'
import { SaleInsuranceType } from '~/api/dictionaries/Static/Sale/SaleInsuranceTypeDictionary'
import { getWorkspaceFromObject } from '~/utils/workspace'
import { ProductPayType, ProductType } from '~/api/models/Product/Product'

export default class SaleHelper extends UnMountModel {
    saleVersions = SaleVersionModel.contextInstance().list(true)
    passengers = PassengerModel.contextInstance().list(true)
    tickets = TicketModel.contextInstance().list(true)
    creditCardData = SaleCreditCardModel.contextInstance().list(true)
    transactions = SaleTransactionModel.contextInstance().list(true)
    clientInvoices = ClientInvoiceModel.contextInstance().list(true)
    saleGroup = SaleListModel.contextInstance().list(true)

    resourceContextOptions = null
    incentiveSalesList = null
    additionalExpensesList = null

    $info = {
        saleId: null,
        saleModel: null,
        saleVersion: null,
        saleSummaryData: null,
        segmentsGroup: {},
        priceQuote: SalePriceQuoteModel,
        compared: [],
        virtualCards: [],
    }

    constructor(saleId) {
        super()
        this.$info = ReactiveHelper.create(this.$info)

        this.$info.saleId = saleId

        this.$info.saleModel = SalePageModel.contextInstance().findByPKSyncResult(saleId)

        const instance = getCurrentInstance()

        watchOnce(() => this.$info.saleModel.$ready, () => {
            this.resourceContextOptions = {
                http: {
                    workspace: getWorkspaceFromObject(this.$info.saleModel),
                },
            }

            this.saleVersions.syncChannelAddConditions(andWhere => {
                andWhere.eq('sale_id', this.saleId)
            }).request().where(andWhere => {
                andWhere.eq('sale_id', this.saleId)
            }).noLimit().findRaw().then(() => {
                this.saleVersions.records.forEach(record => {
                    if (record.is_active) {
                        this.$info.saleVersion = record
                    }
                })

                this.fetchPack(instance)
            })

            this.transactions.syncChannelAddConditions(where => {
                where.eq('sale_id', this.saleId)
            }, true).request().where(andWhere => {
                andWhere.eq('sale_id', this.saleId)
            }).orderBy('id', 'desc').pageSize(0).find()
        })

        this.$prepareSaleSummary()

        // Handle sale group update event
        this.saleGroup.syncChannel.onUpdate(({ changes, pk }) => {
            if (
                ('group_id' in changes || 'remark' in changes) &&
                (this.saleModel.group_id === changes.group_id || this.saleGroup.records.find(sale => sale.id === pk))
            ) {
                this.saleGroup.refresh()
            }
        })
    }

    get hasInsurance() {
        const records = this.incentiveSalesList?.records ? this.incentiveSalesList.records.filter(item => item.product.item_type === ProductType.Insurance && item.product.sub_type !== SaleInsuranceType.BaggageProtection) : []

        const totalPrice = records.reduce((acc, item) => {
            return acc + Number(item.product.sell_price)
        }, 0)

        return records.length > 0 && totalPrice > 0
    }

    get hasPaidBaggageProtection() {
        const records = this.incentiveSalesList?.records ? this.incentiveSalesList.records.filter(item => item.product.sub_type === SaleInsuranceType.BaggageProtection) : []

        const totalPrice = records.reduce((acc, item) => {
            return acc + Number(item.product.sell_price)
        }, 0)

        return records.length > 0 && totalPrice > 0
    }

    get saleId() {
        return this.$info.saleId
    }

    get saleModel() {
        return this.$info.saleModel
    }

    get saleVersion() {
        return this.$info.saleVersion
    }

    set saleVersion(v) {
        this.$info.saleVersion = v
        this.fetchPack()
    }

    get virtualCards() {
        return this.$info.virtualCards
    }

    get saleVersionId() {
        return this.$info.saleVersion?.id
    }

    get priceQuote() {
        return this.$info.priceQuote
    }

    set priceQuote(v) {
        this.$info.priceQuote = v
    }

    get saleSummaryData() {
        return this.$info.saleSummaryData
    }

    set saleSummaryData(v) {
        this.$info.saleSummaryData = v
    }

    get segmentsGroup() {
        return this.$info.segmentsGroup
    }

    set segmentsGroup(v) {
        this.$info.segmentsGroup = v
    }

    get compared() {
        return this.$info.compared
    }

    set compared(value) {
        this.$info.compared = value
    }

    get isComparing() {
        return this.compared.length > 0
    }

    get incentiveSales() {
        return this.incentiveSalesList?.records || []
    }

    get additionalExpenses() {
        return this.additionalExpensesList?.records || []
    }

    addVirtualCard(virtualCard) {
        const index = this.$info.virtualCards.findIndex(item => item.id === virtualCard.id)

        if (index === -1) {
            this.$info.virtualCards.push(virtualCard)
        }
    }

    $onMount(register) {
        super.$onMount(register)
        register(this.saleModel)
        register(this.saleVersions)
        register(this.passengers)
        register(this.tickets)
        register(this.creditCardData)
        register(this.transactions)
        register(this.clientInvoices)
        register(this.saleGroup)
    }

    fetchPack(instance) {
        this.fetchSaleGroup(instance)
        this.fetchPriceQuote(instance)
        this.fetchPassengers(instance)
        this.fetchTickets(instance)
        this.fetchCreditCardData(instance)
        this.fetchClientInvoices(instance)
        this.fetchVirtualCards(instance)

        this.fetchExpenses(instance)
        this.fetchIncentiveSales(instance)
    }

    $onUnmount() {
        //
    }

    fetchPassengers() {
        this.passengers.request().where(andWhere => {
            andWhere.eq('sale_version_id', this.$info.saleVersion.id)
        }).pageSize(0).find()
    }

    fetchTickets() {
        this.tickets.request().where(andWhere => {
            andWhere.eq('sale_version_id', this.$info.saleVersion.id)
            andWhere.eq('is_expense', 0)
        }).pageSize(0).find()
    }

    fetchExpenses() {
        this.additionalExpensesList = markRaw(useModel('AdditionalExpense', this.resourceContextOptions).useResourceList({
            name: 'SaleVersionAdditionalExpenseList',
            pk: String(this.$info.saleVersion.id),
        }, {
            with: ['product', 'product.createdBy'],
        }))
        this.additionalExpensesList.fetch()
    }

    fetchIncentiveSales() {
        this.incentiveSalesList = markRaw(useModel('IncentiveSale', this.resourceContextOptions).useResourceList({
            name: 'SaleVersionIncentiveSaleList',
            pk: String(this.$info.saleVersion.id),
        }, {
            with: ['product', 'product.createdBy'],
        }))

        this.incentiveSalesList.fetch()
    }

    fetchCreditCardData() {
        this.creditCardData.request().where(andWhere => {
            andWhere.eq('sale_version_id', this.$info.saleVersion.id)
        }).pageSize(0).find()
    }

    fetchPriceQuote(instance) {
        const result = SalePriceQuoteModel.instance({ workspaceProject: getWorkspaceProject(instance) }).findByPKSyncResult(this.saleVersion?.price_quote_id)

        const unwatch = watch(() => result.value.$ready, (ready) => {
            if (ready) {
                this.priceQuote = result.value
                this.composeSegmentsGroups(result.value)

                unwatch()
            }
        })
    }

    fetchClientInvoices() {
        this.clientInvoices.request().where(andWhere => {
            andWhere.eq('sale_id', this.saleId)
        }).pageSize(0).find()
    }

    fetchVirtualCards(instance) {
        ProjectCardModel.instance({ workspaceProject: getWorkspaceProject(instance) }).virtualCardsForSale(this.saleModel.id).then(apiResponse => {
            this.$info.virtualCards = apiResponse.result
        }).catch(apiError => {
            console.log(apiError)
        })
    }

    $prepareSaleSummary() {
        this.saleSummaryData = computed(() => {
            const tickets = TransactionService.mapProducts(this.tickets.records)
            const incentiveSales = this.incentiveSalesList?.records ? TransactionService.mapProducts(this.incentiveSalesList.records) : []
            const additionalExpenses = this.additionalExpensesList?.records ? TransactionService.mapProducts(this.additionalExpensesList.records) : []

            const saleSummaryData = {
                additional: {
                    netPrice: TransactionService.getSumNetPrice(additionalExpenses),
                    sellPrice: TransactionService.getSumByField(additionalExpenses, 'sell_price'),
                    checkPayment: TransactionService.getSumByField(additionalExpenses, 'check_payment'), // prodCk
                    profit: TransactionService.getSumByField(additionalExpenses, 'profit'),
                },
                incentive: {
                    netPrice: TransactionService.getSumNetPrice(incentiveSales),
                    sellPrice: TransactionService.getSumByField(incentiveSales, 'sell_price'),
                    checkPayment: TransactionService.getSumByField(incentiveSales, 'check_payment'), // prodCk
                    profit: TransactionService.getSumByField(incentiveSales, 'profit'),
                },
                ticket: {
                    netPrice: TransactionService.getSumNetPrice(tickets), // tktNetPrice
                    sellPrice: TransactionService.getSumByField(tickets, 'sell_price'), // tktSellPrice
                    checkPayment: TransactionService.getSumByField(tickets, 'check_payment'), //tktCk
                    profit: TransactionService.getSumByField(tickets, 'profit') + TransactionService.getSumByField(additionalExpenses, 'profit'), // tktProfit
                },
                total: {
                    checkPayment: TransactionService.getSumByField([
                        TransactionService.getSumByField(incentiveSales, 'check_payment'),
                        TransactionService.getSumByField(tickets, 'check_payment'),
                        TransactionService.getSumByField(additionalExpenses, 'check_payment'),
                    ], ''), // ckAll
                    issuingFee: TransactionService.getSumByField([
                        TransactionService.getSumByField(incentiveSales, 'issuing_fee'),
                        TransactionService.getSumByField(tickets, 'issuing_fee'),
                        TransactionService.getSumByField(additionalExpenses, 'issuing_fee'),
                    ], ''),
                    commission: TransactionService.getSumByField([
                        TransactionService.getSumByField(incentiveSales, 'commission'),
                        TransactionService.getSumByField(tickets, 'commission'),
                        TransactionService.getSumByField(additionalExpenses, 'commission'),
                    ], ''), // commnAll
                    profit: TransactionService.getSumByField([
                        TransactionService.getSumByField(incentiveSales, 'profit'),
                        TransactionService.getSumByField(tickets, 'profit'),
                        TransactionService.getSumByField(additionalExpenses, 'profit'),
                    ], ''), // totalProfit
                },
                sellPrice: TransactionService.getSumByField([
                    TransactionService.getSumByField(incentiveSales, 'sell_price'),
                    TransactionService.getSumByField(tickets, 'sell_price'),
                    TransactionService.getSumByField(additionalExpenses, 'sell_price'),
                ], ''), // sellPriceAll
                airlineCharge: TransactionService.getSumByField([
                    TransactionService.getSumByField(TransactionService.calcAirlineCharges(tickets), 'airlineCharge'),
                    TransactionService.getSumByField(TransactionService.calcAirlineCharges(incentiveSales), 'airlineCharge'),
                    TransactionService.getSumByField(TransactionService.calcAirlineCharges(additionalExpenses), 'airlineCharge'),
                ], ''),
                paid_amount: this.saleModel?.paid_amount ?? 0,
            }

            Object.defineProperty(saleSummaryData, 'debt', {
                get: function() {
                    return (saleSummaryData.inhouseCharge - saleSummaryData.paid_amount).toMoney()
                },
            })

            Object.defineProperty(saleSummaryData, 'inhouseCharge', {
                get: function() {
                    return (saleSummaryData.sellPrice - saleSummaryData.airlineCharge).toMoney()
                },
            })

            return saleSummaryData
        })
    }

    passengerTitle(ticketsGroup) {
        const passenger = this.passengers.findByPK(ticketsGroup[0].passenger_id)

        const first_name = passenger?.first_name
        const last_name = passenger?.last_name
        const type = passenger?.passenger_type
        let text = '(ADT)'

        switch (type) {
        case 'child': {
            text = '(CHD)'
            break
        }
        case 'infant':
            text = '(INF)'
            break
        }

        return `${last_name} ${first_name} ${text}`
    }

    airLineChargesByCardId(cardId) {
        const ticketsSum = TransactionService.calcProductsSumByCard(this.tickets.records, cardId)
        const additionalExpensesSum = this.additionalExpensesList?.records ? TransactionService.calcProductsSumByCard(this.additionalExpensesList.records, cardId) : 0

        let sum = 0;
        [ticketsSum, additionalExpensesSum].forEach(item => {
            sum += Number(item).toMoney()
        })

        return Number(sum).toMoney()
    }

    composeSegmentsGroups() {
        this.segmentsGroup = this.priceQuote?.segments.reduce((r, a) => {
            const index = this.priceQuote.preTickets.findIndex(x => x.id === a.pre_ticket_id)
            const sell_type = this.priceQuote.preTickets[index].sell_type

            if (r[sell_type]) {
                const unique = r[sell_type].findIndex(item => item.pre_ticket_number === a.pre_ticket_number) === -1

                if (unique) {
                    r[sell_type] = [...r[sell_type] || [], a]
                }
            } else {
                r[sell_type] = [...r[sell_type] || [], a]
            }

            r[sell_type].sort((a, b) => {
                if (a.pre_ticket_number < b.pre_ticket_number) {
                    return -1
                } else if (a.pre_ticket_number > b.pre_ticket_number) {
                    return 1
                }

                return 0
            })

            return r
        }, {})
    }

    getPreTicketColor(segment) {
        const index = this.priceQuote.preTickets.findIndex(x => x.id === segment.pre_ticket_id)
        const sell_type = this.priceQuote.preTickets[index].sell_type

        const indexSeg = this.segmentsGroup[sell_type].findIndex(item => {
            return item.pre_ticket_number === segment.pre_ticket_number
        })

        return getTicketColorClass(sell_type, indexSeg, 'bg')
    }

    getTicketColor(type, ticket) {
        const sell_type = ticket.sell_type
        const pre_ticket_number = ticket.pre_ticket_number
        let indexSeg = 0

        if (this.segmentsGroup[sell_type]) {
            indexSeg = this.segmentsGroup[sell_type].findIndex(item => {
                return +item.pre_ticket_number === +pre_ticket_number
            })
        }

        return getTicketColorClass(sell_type, indexSeg, type)
    }

    getSaleTypeName(sys_name) {
        const list = SaleModel.saleTypesList()

        const index = list.findIndex(sale_type => {
            return sale_type.sys_name === sys_name
        })

        if (index > -1) {
            return list[index].name
        } else {
            return 'Sale'
        }
    }

    fetchSaleGroup() {
        if (!this.saleModel?.group_id) {
            return
        }

        const conditions = (q) => q.eq('group_id', this.saleModel.group_id)

        this.saleGroup.request().where(conditions).orderBy('id', 'desc').noLimit().getLazy()
    }

    isComparedWith(saleId) {
        return this.compared.includes(saleId)
    }

    compareWith(saleId) {
        this.compared = [saleId]
    }

    clearCompareList() {
        this.compared = []
    }

    removeFromCompareList(saleId) {
        this.compared = this.compared.filter(id => id !== saleId)
    }

    get isSaleAdjusted() {
        return Boolean(this.saleModel?.is_sale_adjusted)
    }

    get isSaleClosed() {
        return Boolean(this.saleVersion?.is_sale_closed)
    }

    get isSaleDeleted() {
        return Boolean(this.saleModel?.is_hidden)
    }

    get products() {
        const products = []
        this.tickets.records.forEach((ticket) => {
            products.push(ticket.product)
        })

        if (this.additionalExpensesList?.records) {
            this.additionalExpensesList.records.forEach((additionalExpense) => {
                products.push(additionalExpense.product)
            })
        }

        if (this.incentiveSalesList?.records) {
            this.incentiveSalesList.records.forEach((incentiveSale) => {
                products.push(incentiveSale.product)
            })
        }

        return products
    }

    get isValidSellPrices() {
        return this.products.every((element, index, array) => {
            if (['CC'].includes(element.pay_type)) {
                return element.sell_price >= element.net_price_base
            } else {
                return true
            }
        })
    }

    get allTicketsCCSet() {
        return this.tickets.records.every((element, index, array) => {
            if (['CC', 'comCC', 'comVCC'].includes(element.product.pay_type)) {
                return !!element.product.card_identity
            } else {
                return !!element.product?.pay_type
            }
        })
    }

    get allProductsHaveConsolidator() {
        return this.products.every((element, index, array) => {
            return !!element.consolidator_area_id || !!element.consolidator_area_pk
        })
    }

    get sellPriceAll() {
        return this.saleSummaryData.sellPrice
    }
}
