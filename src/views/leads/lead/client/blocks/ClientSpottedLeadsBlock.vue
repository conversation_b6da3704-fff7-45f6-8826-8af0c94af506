<template>
    <div v-if="duplicates.length > 0">
        <div class="flex items-center justify-between py-2">
            <AppButton
                class="mr-3 dark:bg-dark-3 dark:border-dark-5 dark:hover:bg-dark-5 dark:text-gray-400 dark:hover:text-gray-400"
                @click="toggleSpotted = !toggleSpotted"
            >
                <ChevronUpIcon />Hide duplicates
            </AppButton>
            <div class="flex">
                <AppTablePagination :pagination="pagination" mode="compact" />
                <AppPageSize :pagination="pagination" :options="[5,10,20,40]" />
            </div>
        </div>
        <div
            class="customer-spotted-leads max-h-[370px] flex-none overflow-y-auto fancy-scroll pt-0"
        >
            <table class="customer-spotted-leads-table table-fixed">
                <colgroup>
                    <col
                        v-if="$can('edit', 'DuplicateLead')"
                        width="4%"
                    >
                    <col width="3%">
                    <col width="3%">
                    <col width="3%">
                    <col width="5%">
                    <col width="5%">

                    <col width="6.5%">
                    <col width="10%">
                    <col width="7%">
                    <col width="7%">
                    <col width="10%">

                    <col v-if="!isExpertMode" width="7%">
                    <col v-if="!isExpertMode" width="12%">
                    <col width="3%">
                    <col width="5%">
                    <col width="4%">
                    <col
                        v-if="$can('readUTM', 'all')"
                        width="3%"
                    >
                </colgroup>
                <thead>
                    <tr>
                        <th
                            v-if="$can('edit', 'DuplicateLead')"
                            align="right"
                            class="!px-2"
                        >
                            <div class="inline-flex items-center">
                                <input
                                    type="checkbox"
                                    class="form-checkbox"
                                    :checked="allDuplicatesChecked"
                                    @click="markAllDuplicates"
                                >
                                <Dropdown teleport class="ml-2">
                                    <template #toggle="{ open }">
                                        <button class="dropdown-toggle p-0.5" @click="open">
                                            <ChevronDownIcon
                                                class="w-3 h-3"
                                            />
                                        </button>
                                    </template>
                                    <template #content>
                                        <div class="dropdown-menu__content box p-2 border border-slate-200/60 dark:bg-dark-1 dark:border-transparent">
                                            <button
                                                class="dropdown-menu__item"
                                                @click="mergeDuplicates"
                                            >
                                                Merge leads
                                            </button>
                                            <button
                                                class="dropdown-menu__item"
                                                @click="ignoreDuplicates"
                                            >
                                                Ignore merge
                                            </button>
                                        </div>
                                    </template>
                                </Dropdown>
                            </div>
                        </th>
                        <th>#</th>
                        <th>From</th>
                        <th>To</th>
                        <th>Departure</th>
                        <th>Return</th>
                        <th>Resource</th>
                        <th>Current agent</th>
                        <th>Created</th>
                        <th>Taken</th>
                        <th>Customer</th>
                        <th v-if="!isExpertMode">
                            Phone
                        </th>
                        <th v-if="!isExpertMode">
                            Email
                        </th>
                        <th class="text-center">
                            PQs
                        </th>
                        <th>Lead status</th>
                        <th>Status</th>
                        <th v-if="$can('readUTM', 'all')">
                            UTM
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        v-for="item_duplicate in pagination.records"
                        :key="item_duplicate.id"
                        :class="{
                            'selectedRow': leadId === item_duplicate.id
                        }"
                    >
                        <td
                            v-if="$can('edit', 'DuplicateLead')"
                            align="right"
                            class="!px-2"
                        >
                            <div class="inline-flex items-center pr-6">
                                <label
                                    v-tooltip="{content: 'Make this default'}"
                                    class="inline-flex items-center justify-center p-1 px-2 cursor-pointer"
                                    @click="setDuplicateAsMain(item_duplicate.id)"
                                >
                                    <input
                                        type="radio"
                                        name="default-duplicate"
                                        class="customer-spotted-leads-table-radio"
                                        :checked="main_lead.value === item_duplicate.id"
                                    >
                                </label>
                                <input
                                    type="checkbox"
                                    class="form-checkbox"
                                    :checked="markedDuplicates.includes(item_duplicate.id)"
                                    @click="markDuplicate(item_duplicate)"
                                >
                            </div>
                        </td>
                        <td :title="item_duplicate.id">
                            <router-link
                                class="text-blue-500 cursor-pointer truncate"
                                :to="{
                                    name: 'leads',
                                    params: { pk: item_duplicate.id},
                                    query: $route.query
                                }"
                            >
                                {{ item_duplicate.id }}
                            </router-link>
                        </td>
                        <td :title="item_duplicate.fromIata.code" class="truncate">
                            {{ item_duplicate.fromIata.code }}
                        </td>
                        <td :title="item_duplicate.toIata.code" class="truncate">
                            {{ item_duplicate.toIata.code }}
                        </td>
                        <td :title="Date.fromUnixTimestamp(item_duplicate.departure_at).toFormatUTC('dd MMM yy')" class="truncate">
                            {{ Date.fromUnixTimestamp(item_duplicate.departure_at).toFormatUTC('dd MMM yy') }}
                        </td>
                        <td :title="item_duplicate?.itinerary_type === 'roundTrip' ? Date.fromUnixTimestamp(item_duplicate.return_at).toFormatUTC('dd MMM yy') : item_duplicate.itinerary_type" class="truncate">
                            {{ item_duplicate?.itinerary_type === 'roundTrip' ? Date.fromUnixTimestamp(item_duplicate.return_at).toFormatUTC('dd MMM yy') : item_duplicate.itinerary_type }}
                        </td>
                        <td :title="item_duplicate.externalResource?.name" class="truncate">
                            {{ item_duplicate.externalResource?.name }}
                        </td>
                        <td :title="`${item_duplicate.executor?.first_name} ${ item_duplicate.executor?.last_name}`">
                            <router-link
                                v-if="item_duplicate.executor?.id"
                                class="text-indigo-500 cursor-pointer truncate"
                                :to="`/agents/edit/${item_duplicate.executor.id}`"
                            >
                                {{ item_duplicate.executor?.first_name }} {{ item_duplicate.executor?.last_name }}
                            </router-link>
                        </td>
                        <td :title="Date.fromUnixTimestamp(item_duplicate.created_at).toFormatReactive('dd MMM yy HH:mm')" class="truncate">
                            {{ Date.fromUnixTimestamp(item_duplicate.created_at).toFormatReactive('dd MMM yy HH:mm') }}
                        </td>
                        <td :title="item_duplicate.executor?.first_name ? Date.fromUnixTimestamp(item_duplicate.taken_at).toFormatReactive('dd MMM yy HH:mm') : ''" class="truncate">
                            {{ item_duplicate.executor?.first_name && item_duplicate.taken_at ? Date.fromUnixTimestamp(item_duplicate.taken_at).toFormatReactive('dd MMM yy HH:mm') : '' }}
                        </td>
                        <td :title="`${item_duplicate.client?.first_name} ${ item_duplicate.client?.last_name}`">
                            <router-link
                                class="text-indigo-500 cursor-pointer truncate"
                                :to="`/clients/edit/${item_duplicate.client.id}`"
                            >
                                {{ item_duplicate.client?.first_name }} {{ item_duplicate.client?.last_name }}
                            </router-link>
                        </td>
                        <td v-if="!isExpertMode" class="truncate">
                            <HiddenDataComponent :hidden-value="item_duplicate.client_phone">
                                <template #default>
                                    <div :content="item_duplicate.client_phone">
                                        {{ item_duplicate.client_phone }}
                                    </div>
                                </template>
                            </HiddenDataComponent>
                        </td>
                        <td v-if="!isExpertMode" class="truncate">
                            <HiddenDataComponent :hidden-value="item_duplicate.client_email">
                                <template #default>
                                    <div :content="item_duplicate.client_email">
                                        {{ item_duplicate.client_email }}
                                    </div>
                                </template>
                            </HiddenDataComponent>
                        </td>
                        <td :title="item_duplicate.priceQuoteCount" class="text-center truncate">
                            {{ item_duplicate.priceQuoteCount }}
                        </td>
                        <td class="text-center">
                            <div class="inline-block">
                                <LeadStatusBadge
                                    v-tooltip="{ content: getLeadStatusTooltip(item_duplicate) }"
                                    :status-pk="String(item_duplicate.status_id)"
                                    class="cursor-pointer"
                                    small
                                />
                            </div>
                        </td>
                        <td
                            :class="{
                                'text-red-500': item_duplicate?.duplicate_status != 'Claimed',
                                'text-green-600': item_duplicate?.duplicate_status == 'Claimed',
                            }"
                        >
                            {{ item_duplicate.duplicate_status }}
                        </td>
                        <td
                            v-if="$can('readUTM', 'all')"
                            class="!p-0"
                            align="center"
                        >
                            <a
                                :href="`https://ipinfo.io/${item_duplicate.utmIP}/geo`"
                                target="_blank"
                            >
                                <img
                                    v-if="!!item_duplicate.utmSource && getIcon(item_duplicate)"
                                    v-tooltip="{content: utmDesc(item_duplicate)}"
                                    :src="getIcon(item_duplicate)"
                                    class="w-4 h-4 inline-block"
                                    alt=""
                                >
                                <span v-if="item_duplicate.utmIPCountry === 'MD'"> <span v-if="!!item_duplicate.utmSource">/</span> {{ item_duplicate.utmIPCountry }}</span>
                            </a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script>
import ChildMixin from '@/lib/mixin/ComponentRelation/ChildMixin'
import InputField from '@/lib/FormField/InputField'
import UtmSourceHelper from '@/lib/core/helper/UtmSourceHelper'
import { FrontendPagination } from '~/lib/Model/FrontendPagination'
import HiddenDataComponent from '~/sections/Lead/List/components/HiddenDataComponent.vue'
import { SuspiciousAction } from '~/sections/Lead/composable/useSuspiciousActivityTracker'
import LeadStatusBadge from '~/components/Page/Lead/LeadStatusBadge.vue'
import { ClosingReasons } from '~/api/models/Lead/Lead'
import WebUser from '@/lib/service/WebUser'

export default defineComponent({
    name: 'ClientSpottedLeadsBlock',
    components: { LeadStatusBadge, HiddenDataComponent },

    parentName: ['ClientComponent', 'LeadComponent'],
    mixins: [ChildMixin],
    setup() {
        const duplicates = ref([])

        const pagination = computed(() => {
            return new FrontendPagination(duplicates, {
                page: 1,
                pageSize: 10,
            })
        })

        return {
            duplicates,
            pagination: pagination.value,
        }
    },

    data() {
        const leadId = this.$related.LeadComponent.lead.id

        return {
            leadId,
            markedDuplicates: [],
            main_lead: new InputField(false, 'main_lead'),
            leadStatuses: this.$models.LeadStatusModel.getAllList(),
            webUser: WebUser,
        }
    },

    computed: {
        SuspiciousAction() {
            return SuspiciousAction
        },

        inactiveStatuses() {
            return this.leadStatuses.filter(item => item.category === 'Inactive').map(item => item.value)
        },

        allDuplicatesChecked() {
            if (this.duplicates && this.duplicates.length > 0) {
                return this.duplicates.every(duplicate => {
                    return this.markedDuplicates.includes(duplicate.id)
                })
            } else { return false }
        },

        isExpertMode() {
            return this.$related.LeadComponent.isExpertMode
        },

        toggleSpotted: {
            get() {
                return !!this.webUser.spottedShow
            },

            set(v) {
                this.webUser.spottedShow = v
            },
        },
    },

    mounted() {
        if (this.leadId) {
            this.main_lead.value = this.leadId
            this.fetchDuplicates()
        }
    },

    methods: {
        getIcon(lead) {
            return UtmSourceHelper.getImage(lead.utmSource)
        },

        utmDesc(lead) {
            return `Source: ${lead.utmSource} <br> Ip: ${lead.utmIP} <br>`
        },

        fetchDuplicates() {
            return this.$models.AgentDuplicateModel.findByPK(this.leadId).then(apiResponse => {
                this.duplicates = apiResponse.result.fullDuplicates
            })
        },

        refresh() {
            this.main_lead.value = this.leadId
            this.markedDuplicates = []

            setTimeout(() => {
                this.fetchDuplicates()
            }, 400)
        },

        mergeDuplicates() {
            let valid = true
            this.duplicates.forEach(duplicate => {
                if (duplicate.id !== this.main_lead.value && this.markedDuplicates.includes(duplicate.id)) {
                    if (this.inactiveStatuses.includes(duplicate.status_id)) {
                        this.toastError(`#${duplicate.id} can not be deleted or merged!`, { duration: 3000 })
                        valid = false
                    }
                }
            })

            if (valid) {
                this.$models.DuplicateLeadModel.mergeDuplicates(this.main_lead.value, this.markedDuplicates).then(apiResponse => {
                    this.refresh()
                    this.toastSuccess(`Merging successful`)
                }).catch(apiError => {
                    apiError.processWithMessage(apiError.message ? apiError.message : `Merging Error`)
                })
            }
        },

        ignoreDuplicates() {
            this.$models.DuplicateLeadModel.ignoreDuplicates(this.main_lead.value, this.markedDuplicates).then(apiResponse => {
                this.refresh()
            }).catch(apiError => {
                apiError.processWithMessage()
            })
        },

        markDuplicate(duplicate) {
            if (this.markedDuplicates.includes(duplicate.id)) {
                this.markedDuplicates = this.markedDuplicates.filter(item => {
                    return item !== duplicate.id
                })
            } else {
                this.markedDuplicates.push(duplicate.id)
            }
        },

        markAllDuplicates() {
            if (this.allDuplicatesChecked) {
                this.markedDuplicates = []
            } else {
                this.duplicates.forEach(duplicate => {
                    this.markedDuplicates.push(duplicate.id)
                })
            }
        },

        setDuplicateAsMain(id) {
            this.main_lead.value = id
        },

        getLeadStatusTooltip(duplicatedLead) {
            const statusName = useGeneralDictionary('LeadStatus').find(String(duplicatedLead.status_id)).name
            const closingReason = duplicatedLead.closing_reason
            const closingReasonRemark = duplicatedLead.closing_reason_remark ?? ''

            if (['Lost', 'Closed'].includes(statusName) && closingReason) {
                const baseResult = statusName + ' (reason: '

                if (closingReason === ClosingReasons.Other) {
                    return baseResult + this.truncateText(closingReasonRemark, 150) + ')'
                }

                const reasonTitle = useGeneralDictionary('ClosingReason').find(closingReason).title

                return baseResult + reasonTitle + ')'
            }

            return statusName
        },

        truncateText(text, maxLength) {
            if (text.length <= maxLength) return text

            return text.substring(0, maxLength) + '...'
        },
    },
})
</script>

<style lang="postcss" scoped>
.selectedRow {
    box-shadow: 0px 1px 10px -3px red;
}
</style>
