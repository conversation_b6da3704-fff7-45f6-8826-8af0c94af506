<template>
    <div class="customer-bar-profile mr-10">
        <div class="customer-bar-profile-badge">
            <div
                v-if="lead.status"
                v-tooltip="{ content: profileBadgeTooltip, html: false }"
                :class="leadStatusHelper[lead.status.name].color_tailwind"
                class="relative lead-card-badge lead-card-badge-lg mr-3"
            >
                {{ leadStatusHelper[lead.status.name].short_name }}
                <div
                    v-if="lead.is_bonus"
                    class="lead-card-badge w-5 h-5 --bonus absolute -top-2 -right-2 m-0 border-2 border-white shadow-sm"
                >
                    B
                </div>
            </div>
        </div>

        <PageLeadClientStatus :lead-pk="String(lead.id)" />
    </div>

    <div class="customer-bar-contacts">
        <ClientEnrichedButton
            v-if="!isExpertMode"
            :client-pk="String(lead.client_id)"
            class="--large"
            highlight-visibility
        />
        <Dropdown v-if="($can('edit', 'Lead', lead) && !lead.status?.name !== 'Sold') && !isExpertMode" teleport>
            <template #toggle="{ toggle }">
                <button
                    class="dropdown-toggle btn text-xs btn-outline-secondary min-w-32"
                    :disabled="isStatusListDisabled"
                    @click="toggle"
                >
                    <template v-if="lead.status">
                        <span
                            :class="leadStatusHelper[lead.status.name].color_tailwind"
                            class="flex-none flex items-center justify-center w-4 h-4 rounded-full mr-2 text-2xs text-white font-semibold"
                        >
                            {{ leadStatusHelper[lead.status.name].short_name }}
                        </span>
                        {{ leadStatusHelper[lead.status.name].full_name }}
                    </template>
                    <ChevronDownIcon class="w-3 h-3 text-theme-33 ml-auto" />
                </button>
            </template>
            <template #content="{ close }">
                <div class="dropdown-menu__content box dark:bg-dark-1 p-2 w-32 mt-0">
                    <a
                        v-for="status in statusList"
                        :key="status"
                        class="dropdown-menu__item"
                        @click.stop="() => {
                            editLeadStatus(status.value)
                            close()
                        }"
                    >
                        <span
                            :class="leadStatusHelper[status.system_name].color_tailwind"
                            class="flex-none flex items-center justify-center w-4 h-4 rounded-full mr-2 text-2xs text-white font-semibold"
                        >
                            {{ leadStatusHelper[status.system_name].short_name }}
                        </span>
                        {{ leadStatusHelper[status.system_name].full_name }}
                    </a>
                </div>
            </template>
        </Dropdown>

        <ClientEmailDataDropdown
            v-if="!isExpertMode"
            :client-pk="String(lead.client_id)"
            :with-edit="$can('edit', 'Lead', lead)"
            with-remark
            with-title
            :default-email="lead.client_email"
            :set-default-email="(email_pk) => {
                return leadModel.actions.setDefaultEmail({
                    email_pk,
                    lead_pk: String(lead.id)
                })
            }"
            @copy="trackSuspiciousAction(SuspiciousAction.LeadCopy, { pk: String(lead.id) })"
            @show="trackSuspiciousAction(SuspiciousAction.LeadShow, { pk: String(lead.id) })"
        />

        <ClientPhoneDataDropdown
            v-if="!isExpertMode"
            :client-pk="String(lead.client_id)"
            :with-edit="$can('edit', 'Lead', lead)"
            with-remark
            with-title
            :default-phone="lead.client_phone"
            :set-default-phone="(phone_pk) => {
                return leadModel.actions.setDefaultPhone({
                    phone_pk,
                    lead_pk: String(lead.id)
                })
            }"
            @copy="trackSuspiciousAction(SuspiciousAction.LeadCopy, { pk: String(lead.id) })"
            @show="trackSuspiciousAction(SuspiciousAction.LeadShow, { pk: String(lead.id) })"
        />

        <template v-if="!isLeadManagementMode">
            <button
                v-if="!isLeadSoldOrLost && (!isExpertMode || (isExpertMode && $can('requestExpertOnExpertsPage', 'Lead')))"
                :disabled="isSent || (lead.expert_request_at && !lead.expert_request_is_done)"
                class="flex-none btn btn-outline-secondary btn-sm box text-primary-1 px-3.5 py-2 font-medium"
                @click="sendExpertRequestHelp"
            >
                <LifeBuoyIcon class="w-4 h-4 mr-2" />
                {{ requestTitle }}
            </button>
            <!--  -->
            <button
                v-if="isExpertMode && isWorkStarted && !$can('manage', 'all')"
                :disabled="lead.expert_request_at && lead.expert_request_is_done || lead.expert_request_is_done"
                class="flex-none btn btn-outline-secondary btn-sm box text-primary-1 px-3.5 py-2 font-medium"
                @click="expertRequestIsDone"
            >
                <LifeBuoyIcon class="w-4 h-4 mr-2" />
                Done
            </button>

            <button
                v-if="isExpertMode && !isWorkStarted && lead.expert_request_at && !$can('manage', 'all')"
                class="flex-none btn btn-outline-secondary btn-sm box text-primary-1 px-3.5 py-2 font-medium"
                @click="expertStartWork"
            >
                <LifeBuoyIcon class="w-4 h-4 mr-2" />
                Start Work
            </button>

            <button
                v-if="lead.expert_request_at && !lead.expert_request_start_at && isUserExpertManager"
                class="flex-none btn btn-outline-secondary btn-sm box text-primary-1 px-3.5 py-2 font-medium"
                @click="sendLeadBackToQueue"
            >
                <CornerUpLeftIcon class="w-4 h-4 mr-2" />
                Send back
            </button>
        </template>
        <!--  -->
    </div>

    <div v-if="isLeadManagementMode && leadManagementStatus?.start_working_date" class="ml-8">
        <div class="flex text-xs self-center">
            Work started:
            {{ $format.datetime(leadManagementStatus?.start_working_date)
            }}
        </div>
    </div>
    <div v-if="isLeadManagementMode" class="ml-8">
        <div class="btn-group">
            <button
                v-if="!leadManagementStatus?.start_working_date"
                class="flex-none btn btn-outline-secondary btn-sm box ml-12  px-3.5 py-2 font-medium"
                @click="leadManagementStartWork"
            >
                <PlayIcon class="w-4 h-4 mr-2 text-primary-1" />
                Start Management Work
            </button>

            <button
                v-if="leadManagementStatus?.start_working_date"
                class="flex-none btn btn-outline-secondary btn-sm box  px-3.5 py-2 font-medium"
                @click="leadManagementResolutionModal.open({ leadPk: String(lead.id), sessionStartDate: leadManagementStatus?.session_start_date })"
            >
                <CheckIcon class="w-4 h-4 mr-2 text-green-600" />
                End Work
            </button>
        </div>
    </div>
    <div class="customer-bar-extra space-x-6">
        <div v-if="lead.duplicateLeadCount > 0 && $can('viewLeadDuplicatesSection', 'Lead')" class="btn-group">
            <button
                class="flex-none btn btn-outline-secondary btn-sm box text-red-500 px-3.5 py-2 font-medium"
                @click="toggleSpotted = !toggleSpotted"
            >
                <AlertTriangleIcon class="w-4 h-4 mr-2" />
                Duplicate leads [{{ lead.duplicateLeadCount + 1 }}]
            </button>
            <Dropdown v-if="!isExpertMode">
                <template #toggle="{ toggle }">
                    <button
                        class="dropdown-toggle btn btn-outline-secondary btn-sm box text-red-500 px-2 py-2.5 !rounded-l-none"
                        @click="toggle"
                    >
                        <ChevronDownIcon class="w-3 h-3" />
                    </button>
                </template>
                <template #content="{ close }">
                    <div
                        class="dropdown-menu__content box p-2 border border-slate-200/60 dark:bg-dark-1 dark:border-transparent w-28 mt-0"
                    >
                        <button
                            class="dropdown-menu__item"
                            @click="() => {
                                close()
                                markDuplicateStatus('Claimed')
                            }"
                        >
                            Claim lead
                        </button>
                        <button
                            class="dropdown-menu__item"
                            @click="() => {
                                close()
                                markDuplicateStatus('Rejected')
                            }"
                        >
                            Reject lead
                        </button>
                    </div>
                </template>
            </Dropdown>
        </div>
        <div
            v-if="agentInfo?.id"
            :key="agentInfo.id + '_agentInfo'"
            class="customer-bar-agent"
            @click="setAgent(agentInfo?.id)"
        >
            <AppAvatar
                v-if="agentInfo?.avatar"
                :image="agentInfo.avatar"
                :info="agentInfo"
                :name="`${agentInfo.first_name} ${agentInfo.last_name}`"
                class="customer-bar-agent-avatar"
            />
            <div class="customer-bar-agent-info">
                <div class="customer-bar-agent-label max-w-40 truncate">
                    Agent<span v-if="agentTeam" v-tooltip="agentTeam">, {{ agentTeam }}</span>
                </div>
                <div v-if="agentInfo.first_name || agentInfo.last_name" class="customer-bar-agent-name">
                    {{ agentInfo?.first_name }} {{ agentInfo?.last_name }}
                </div>
                <div v-else>
                    No data
                </div>
            </div>
        </div>
        <div
            v-else
            class="customer-bar-agent"
            @click="setAgent(null)"
        >
            <div class="customer-bar-agent-add mr-2">
                <PlusIcon class="w-4 h-4 !stroke-2" />
            </div>
            <div class="customer-bar-agent-info">
                <div class="customer-bar-agent-label">
                    Agent
                </div>
                <div class="customer-bar-agent-name">
                    Not assigned
                </div>
            </div>
        </div>
        <div class="customer-bar-created">
            <div class="customer-bar-created-label">
                Created at
            </div>
            <div class="customer-bar-created-value">
                {{ Date.fromUnixTimestamp(created_at).toFormatReactive('dd MMM yyyy') }}
            </div>
        </div>
        <div class="customer-bar-actions space-x-1">
            <div class="flex gap-x-1">
                <AppButton
                    v-tooltip="{ content: 'Follow-up Progress' }"
                    class="--only h-[32px] w-[32px] p-1 shadow-sm dark:bg-dark-3 dark:border dark:border-secondary-700 dark:text-white dark:hover:bg-secondary-700 dark:hover:text-white"
                    @click="followUpProgressModal.open({
                        leadPk:leadPk
                    })"
                >
                    <BreakOutIcon />
                </AppButton>
                <Dropdown>
                    <template #toggle="{ toggle }">
                        <button class="dropdown-toggle btn btn-outline-secondary px-2" @click="toggle">
                            <MoreVerticalIcon class="w-3.5 h-3.5" />
                        </button>
                    </template>
                    <template #content="{ close }">
                        <div class="dropdown-menu__content box dark:bg-dark-1 p-2 whitespace-nowrap mt-0">
                            <button
                                v-if="$can('edit', 'Lead', lead) && !isExpertMode"
                                class="flex items-center p-2 transition w-full duration-300 ease-in-out bg-white dark:bg-dark-1 hover:bg-gray-200 dark:hover:bg-dark-2 rounded-md cursor-pointer"
                                @click="() => {
                                    editLead()
                                    close()
                                }"
                            >
                                <EditIcon class="w-4 h-4 mr-2" />
                                Edit
                            </button>
                            <button
                                v-if="lead.executor_id && (webUser.id === lead.executor_id || $can('manage', 'all'))"
                                class="flex items-center p-2 transition w-full duration-300 ease-in-out bg-white dark:bg-dark-1 hover:bg-gray-200 dark:hover:bg-dark-2 rounded-md cursor-pointer"
                                @click="clientStatusChangeModal.open({ leadPk: String(lead.id), clientPk: String(lead.client_id), executorPk: String(lead.executor_id) })"
                            >
                                <Edit3Icon class="w-4 h-4 mr-2" />
                                Client status
                            </button>
                            <button
                                v-if="!isExpertMode && ((leadSessionData?.shouldShowRemainingTime) || isLeadDelayed)"
                                v-tooltip="{ content: leadSessionData?.activeKeepClientRequestPk && 'Request is active' }"
                                class="flex items-center p-2 transition w-full duration-300 ease-in-out bg-white dark:bg-dark-1 hover:bg-gray-200 dark:hover:bg-dark-2 rounded-md cursor-pointer"
                                :disabled="leadSessionData?.activeKeepClientRequestPk"
                                @click="keepClientRequestModal.open({ leadPk: String(lead.id) })"
                            >
                                <UserIcon class="w-4 h-4 mr-2" />
                                Keep client
                            </button>
                            <button
                                v-if="!isExpertMode && leadSessionData?.dropClientIsAvailable"
                                class="flex items-center p-2 transition w-full duration-300 ease-in-out bg-white dark:bg-dark-1 hover:bg-gray-200 dark:hover:bg-dark-2 rounded-md cursor-pointer"
                                @click="dropClient"
                            >
                                <UserIcon class="w-4 h-4 mr-2" />
                                Drop Client
                            </button>
                            <button
                                v-if="!isExpertMode"
                                class="flex items-center p-2 transition w-full duration-300 ease-in-out bg-white dark:bg-dark-1 hover:bg-gray-200 dark:hover:bg-dark-2 rounded-md cursor-pointer"
                                @click="claimSale"
                            >
                                <SplitIcon class="w-4 h-4 mr-2" />
                                Claim sale
                            </button>
                            <button
                                v-if="!isExpertMode && !lead.is_bonus && canAddLeadToQueue && !leadManagementStatus?.is_in_queue"
                                class="flex items-center p-2 transition w-full duration-300 ease-in-out bg-white dark:bg-dark-1 hover:bg-gray-200 dark:hover:bg-dark-2 rounded-md cursor-pointer"
                                @click="() => {
                                    addLeadToQueue()
                                    close()
                                }"
                            >
                                <UserIcon class="w-4 h-4 mr-2" />
                                Add lead to queue
                            </button>

                            <button
                                class="flex items-center p-2 transition w-full duration-300 ease-in-out bg-white dark:bg-dark-1 hover:bg-gray-200 dark:hover:bg-dark-2 rounded-md cursor-pointer"
                                @click="logsModal.open({ leadPk: usePk('Lead', lead.id) })"
                            >
                                <ChevronsLeftIcon class="w-4 h-4 mr-2" />
                                Show logs
                            </button>

                            <button
                                class="flex items-center p-2 transition w-full duration-300 ease-in-out bg-white dark:bg-dark-1 hover:bg-gray-200 dark:hover:bg-dark-2 rounded-md cursor-pointer"
                                @click="findVoucher"
                            >
                                <BookIcon class="w-4 h-4 mr-2" />
                                Attach voucher
                            </button>

                            <!--  -->
                            <button
                                v-copy="href"
                                class="flex items-center p-2 transition w-full duration-300 ease-in-out bg-white dark:bg-dark-1 hover:bg-gray-200 dark:hover:bg-dark-2 rounded-md cursor-pointer"
                            >
                                <CopyIcon class="w-4 h-4 mr-2" />
                                Copy Link
                            </button>
                            <button
                                v-if="$can('delete', 'Lead', lead) && is_hidden === 1"
                                class="flex items-center p-2 transition w-full duration-300 ease-in-out bg-white dark:bg-dark-1 hover:bg-gray-200 dark:hover:bg-dark-2 rounded-md cursor-pointer"
                                @click="restoreLead"
                            >
                                <RotateCcwIcon class="w-4 h-4 mr-2" />
                                Restore
                            </button>
                            <button
                                v-if="$can('delete', 'Lead', lead) && is_hidden === 0"
                                class="flex items-center p-2 transition w-full duration-300 ease-in-out bg-white dark:bg-dark-1 hover:bg-gray-200 dark:hover:bg-dark-2 rounded-md cursor-pointer"
                                @click="hideLead"
                            >
                                <Trash2Icon class="w-4 h-4 mr-2" />
                                Delete
                            </button>
                        </div>
                    </template>
                </Dropdown>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import ChildMixin from '@/lib/mixin/ComponentRelation/ChildMixin'
import LeadStatusModel from '@/api/models/Lead/LeadStatusModel'
import ReactiveHelper from '@/lib/core/helper/ReactiveHelper'
import ChildTypedMixin from '@/lib/mixin/ComponentRelation/ChildTypedMixin'
import WebUser from '@/lib/service/WebUser'
import LeadCreateModal from '~/sections/Lead/modals/Create/LeadCreateModal.vue'
import SaleClaimModal from '@/views/sales/sale/modals/SaleClaimModal.vue'
import CloseLeadReasonRequestModal from '~/components/Page/Lead/CloseLeadReasonRequestModal.vue'
import Dropdown from '~/components/Dropdown.vue'
import PageLeadClientStatus from '~/components/Page/Lead/PageLeadClientStatus.vue'
import ClientStatusChangeModal from '~/components/Page/Lead/ClientStatusChangeModal.vue'
import KeepClientRequestModal from '~/components/Page/Lead/KeepClientRequestModal.vue'
import SplitIcon from '~/assets/icons/SplitIcon.svg?component'
import { leadStatusHelper } from '@/lib/core/helper/LeadHelper'
import { ClosingReasons } from '~/api/models/Lead/Lead'
import { $confirm } from '@/plugins/ConfirmPlugin'
import { useRoute } from 'vue-router'
import LeadManagementResolutionModal from '~/modals/LeadManagementResolutionModal.vue'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import useLeadManagementMode from '~/composables/useLeadManagementMode'
import LeadLogsSideModal from '~/sections/Lead/modals/LeadLogsSideModal'
import LeadFollowUpProgressModal from '~/sections/Lead/modals/LeadFollowUpProgressModal'
import { SuspiciousAction, useSuspiciousActivityTracker } from '~/sections/Lead/composable/useSuspiciousActivityTracker'
import NotificationTextIcon from '~assets/icons/NotificationTextIcon.svg?component'
import BreakOutIcon from '~assets/icons/BreakOutIcon.svg?component'
import ClientPhoneDataDropdown from '~/components/ClientPhoneData/ClientPhoneDataDropdown.vue'
import ClientEmailDataDropdown from '~/components/ClientEmailData/ClientEmailDataDropdown.vue'
import VoucherFindModal from '~/sections/Voucher/modals/VoucherFindModal.vue'
import { DepartmentName } from '~/api/models/Department/Department'
import { PositionName } from '~/api/models/Position/Position'
import { getFullName } from '~/lib/Helper/PersonHelper'
import { LeadStatusName } from '~/api/models/Lead/LeadStatus'
import { MessageType } from '@/types/enums/MessageType'
import ClientEnrichedButton from '~/sections/Client/components/ClientEnrichedButton.vue'

export default defineComponent({
    name: 'ClientBarBlock',
    parentType: 'LeadsController',
    parentName: ['ClientComponent', 'LeadComponent'],

    components: {
        ClientEnrichedButton,
        ClientEmailDataDropdown,
        ClientPhoneDataDropdown,
        PageLeadClientStatus,
        AlertTriangleIcon,
        RotateCcwIcon,
        BreakOutIcon,
        Dropdown,
        SplitIcon,
    },

    mixins: [ChildMixin, ChildTypedMixin],

    inject: ['leadSessionData'],

    props: {
        leadPk: {
            type: String,
            required: true,
        },
    },

    async setup(props) {
        const { record: lead, useModel, hasPermission, useDictionary, useCurrentUser } = useContext()

        const currentUser = useCurrentUser()
        const route = useRoute()
        const leadModel = useModel('Lead')

        const agentDictionary = useDictionary('Agent')
        const teamDictionary = useDictionary('Team')

        const leadStatusDictionary = useGeneralDictionary('LeadStatus')

        const elevenMonthInSeconds = Timespan.months(11).inSeconds
        const dateInElevenMonthInSeconds = Date.currentUnixTimestamp() + elevenMonthInSeconds

        const isLeadDelayed = computed(() => {
            if (lead.return_date) {
                return (dateInElevenMonthInSeconds - lead.return_date) <= 0
            }

            return (dateInElevenMonthInSeconds - lead.departure_date) <= 0
        })

        const isLeadSoldOrLost = computed(() => {
            const status = leadStatusDictionary.find(lead.status_pk)?.system_name

            return [LeadStatusName.Lost, LeadStatusName.Sold, LeadStatusName.Fraud].includes(status)
        })

        const logsModal = useModal(LeadLogsSideModal)

        const agentTeam = computed(() => {
            const agent = lead.executor_pk ? agentDictionary.find(lead.executor_pk) : undefined

            return agent?.team_pk ? teamDictionary.find(agent.team_pk).name : undefined
        })

        async function dropClient() {
            await $confirm({ text: 'Are you sure you want to drop this client?' })

            await useModel('Lead').actions.dropClient({
                lead_pk: props.leadPk,
            })
        }

        const canViewVoucher = computed(() => {
            return hasPermission('openPageVoucher', 'all')
        })

        async function addLeadToQueue() {
            await useModel('Lead').actions.addLeadToQueue({
                lead_pk: props.leadPk,
            })

            toastSuccess('Successfuly added to management queue')
        }

        async function leadManagementStartWork() {
            await useModel('LeadAdditionalManagementStatus').actions.startWork({
                lead_pk: props.leadPk,
            })

            toastSuccess('Management work started')
        }

        // attach voucher to lead

        const connectVoucherModal = useModal(VoucherFindModal)

        function findVoucher() {
            connectVoucherModal.open({
                lead_pk: props.leadPk,
            })
        }

        //

        //

        const followUpProgressModal = useModal(LeadFollowUpProgressModal)

        const { trackSuspiciousAction } = useSuspiciousActivityTracker()

        //

        const leadAdditionalModelRecord = await useModel('LeadAdditionalManagementStatus').useRecord().fetch(props.leadPk)
        const leadManagementStatus = leadAdditionalModelRecord.value

        return {
            leadModel,
            agentTeam,
            clientStatusChangeModal: useModal(ClientStatusChangeModal),
            keepClientRequestModal: useModal(KeepClientRequestModal),
            leadManagementResolutionModal: useModal(LeadManagementResolutionModal),
            isLeadManagementMode: useLeadManagementMode(route),
            leadManagementStatus,
            logsModal,
            dropClient,
            addLeadToQueue,
            leadManagementStartWork,
            followUpProgressModal,
            isLeadDelayed,
            isLeadSoldOrLost,
            findVoucher,
            canViewVoucher,
            currentUser,
            trackSuspiciousAction,
            agentDictionary,
        }
    },

    data() {
        return {
            webUser: WebUser,
            statusData: ReactiveHelper.toData(LeadStatusModel.contextInstance().getAllList()),
            isSent: false,
            placeNameEmails: 'dropdown' + Date.now(),
            placeNamePhones: 'dropdown' + Date.now(),
            leadStatusHelper: leadStatusHelper,
        }
    },

    computed: {
        SuspiciousAction() {
            return SuspiciousAction
        },

        getStatus() {
            const client = this.lead.client
            const statuses = this.$models.ClientStatusModel.getAll()
            const index = statuses.findIndex(status => {
                if (!!client.pendingStatusId) {
                    return status.value === client.pendingStatusId
                } else {
                    return status.value === client.status_id
                }
            })

            if (index > -1) {
                return statuses[index].label
            } else {
                return `------`
            }
        },

        profileBadgeTooltip() {
            const closingReason = this.leadSessionData?.closingReason
            const closingReasonRemark = this.leadSessionData?.closingReasonRemark

            if (['Closed', 'Lost'].includes(this.lead.status.name) && closingReason) {
                const baseResult = this.lead.status.name + ' (reason: '

                if (closingReason === ClosingReasons.Other) {
                    return baseResult + closingReasonRemark + ')'
                }

                const reasonTitle = useGeneralDictionary('ClosingReason').find(closingReason).title

                return baseResult + reasonTitle + ')'
            }

            return leadStatusHelper[this.lead.status.name].full_name
        },

        lead() {
            return this.$related.LeadComponent.lead
        },

        canViewAppointmentLogs() {
            if (this.lead?.id) {
                return this.$can('viewAppointmentLogs', 'Lead', this.lead)
            } else {
                return false
            }
        },

        canAddLeadToQueue() {
            return this.$can('openPageLeadManagement', 'all') && WebUser.positionName === 'Manager'
        },

        canUnAssignAgentFromLead() {
            return this.$can('unAssignAppointment', 'Lead', this.lead)
        },

        statusList() {
            return this.statusData.filter(item => item.category === 'Active'
                && !['New', 'Sale rejected'].includes(item.system_name))
        },

        href() {
            return window.location.origin + this.linkToLead(this.lead.id)
        },

        clientDetails() {
            return this.$related.ClientComponent.clientDetails
        },

        agentInfo() {
            return this.$related.ClientComponent.agentInfo
        },

        client_id() {
            return this.$related.ClientComponent.lead.client_id
        },

        created_at() {
            return this.$related.ClientComponent.lead.created_at
        },

        is_hidden() {
            return this.$related.ClientComponent.lead.is_hidden
        },

        toggleSpotted: {
            get() {
                return !!WebUser.spottedShow
            },

            set(v) {
                WebUser.spottedShow = v
            },
        },

        isExpertMode() {
            return this.leadSessionData?.isExpertMode
        },

        isStatusListDisabled() {
            return useGeneralDictionary('LeadStatus')
                .inactiveRecords.map((status) => usePk(status)).includes(String(this.lead.status_id)) || this.lead.status.name === 'Sale rejected'
        },

        requestTitle() {
            let title = 'Request expert'

            if (this.lead.expert_request_at && this.lead.expert_id === null && this.lead.expert_request_is_done === null) {
                title = 'Request sent'
            }

            if (this.lead.expert_request_at && this.lead.expert_id && this.lead.expert_request_is_done === null && this.lead.expert_request_start_at > 0) {
                title = 'Expert working'
            }

            if (this.lead.expert_request_at && this.lead.expert_id && this.lead.expert_request_is_done) {
                title = 'PQ added'
            }

            return title
        },

        isWorkStarted() {
            return this.lead.expert_id === WebUser.id && this.lead.expert_request_start_at > 0
        },

        isUserExpertManager() {
            return isUserInDepartment(this.currentUser, DepartmentName.Experts)
                && isUserHasPosition(this.currentUser, PositionName.Manager)
        },
    },

    methods: {
        claimSale() {
            this.$showModal(SaleClaimModal, {}, {
                leadPk: usePk('Lead', this.lead.id),
            })
        },

        openMailsContextMenu(event, mailRecord, closeDropdown) {
            const wrapper = this.$refs.clientsContextMenuEmail
            const dropdown = event.target.closest('.dropdown__content')
            wrapper.menuClicked(mailRecord)
            const options = [
                {
                    enabled: true,
                    text: 'Copy',
                    icon: CopyIcon,
                    onClick: () => {
                        wrapper.makeCopy()
                        closeDropdown()
                    },
                },
                {
                    enabled: wrapper.emailsComputed.length > 1,
                    text: 'Copy all',
                    icon: LayersIcon,
                    onClick: () => {
                        wrapper.makeCopyAll()
                        closeDropdown()
                    },
                },
                {
                    enabled: true,
                    text: mailRecord.remark ? 'Edit Remark' : 'Add Remark',
                    icon: NotificationTextIcon,
                    onClick: () => {
                        if (mailRecord.remark) {
                            wrapper.editRemark(mailRecord.remark)
                        } else {
                            wrapper.createRemark()
                        }
                    },
                },
                {
                    enabled: this.$can('edit', 'Lead', this.lead) && this.lead.client_email !== mailRecord.email,
                    text: 'Edit',
                    icon: Edit2Icon,
                    onClick: () => {
                        wrapper.editEmail()
                    },
                },
                {
                    enabled: true,
                    text: 'Set default',
                    icon: DiscIcon,
                    onClick: () => {
                        wrapper.setLeadEmail(this.lead, mailRecord)
                        closeDropdown()
                    },
                },
                {
                    enabled: this.$can('edit', 'Lead', this.lead) && this.lead.client_email !== mailRecord.email,
                    text: 'Delete',
                    icon: Trash2Icon,
                    class: 'text-red-500',
                    onClick: () => {
                        wrapper.deleteEmail()
                        closeDropdown()
                    },
                },
            ].filter(option => option.enabled !== false)
            const rect = dropdown.getBoundingClientRect()
            this.$openContextMenu(
                {
                    x: event.clientX - rect.x,
                    y: event.clientY - rect.y,
                },
                options,
                {
                    place: this.placeNameEmails,
                })
        },

        openPhonesContextMenu(event, phoneRecord, closeDropdown) {
            const wrapper = this.$refs.clientsContextMenuPhone
            const dropdown = event.target.closest('.dropdown__content')
            wrapper.menuClicked(phoneRecord)
            const rect = dropdown.getBoundingClientRect()

            const options = [
                {
                    enabled: false,
                    text: 'Call',
                    icon: PhoneIcon,
                    onClick: () => {
                        wrapper.makeCall()
                        closeDropdown()
                    },
                },
                {
                    enabled: true,
                    text: 'Copy',
                    icon: CopyIcon,
                    onClick: () => {
                        wrapper.makeCopy()
                        closeDropdown()
                    },
                },
                {
                    enabled: true,
                    text: phoneRecord.remark ? 'Edit Remark' : 'Add Remark',
                    icon: NotificationTextIcon,
                    onClick: () => {
                        if (phoneRecord.remark) {
                            wrapper.editRemark(phoneRecord.remark)
                        } else {
                            wrapper.createRemark()
                        }
                    },
                },
                {
                    enabled: this.$can('edit', 'Lead', this.lead) && this.lead.client_phone !== phoneRecord.phone,
                    text: 'Edit',
                    icon: Edit2Icon,
                    onClick: () => {
                        wrapper.editPhone()
                    },
                },
                {
                    enabled: true,
                    text: 'Set default',
                    icon: DiscIcon,
                    onClick: () => {
                        wrapper.setLeadPhone(this.lead, phoneRecord)
                        closeDropdown()
                    },
                },
                {
                    enabled: this.$can('edit', 'Lead', this.lead) && this.lead.client_phone !== phoneRecord.phone,
                    text: 'Delete',
                    icon: Trash2Icon,
                    class: 'text-red-500',
                    onClick: () => {
                        wrapper.deletePhone()
                        closeDropdown()
                    },
                },
            ].filter(option => option.enabled !== false)

            this.$openContextMenu(
                {
                    x: event.clientX - rect.x,
                    y: event.clientY - rect.y,
                }, options, {
                    place: this.placeNamePhones,
                })
        },

        setAgent(id) {
            if (this.isUserExpertManager) {
                return this.setExpert()
            }

            if (!this.canViewAppointmentLogs) {
                return this.toastError('You don\'t have permission to assign agent to lead')
            }

            this.$related.LeadComponent.showAgentAssignModal(id)
        },

        setExpert() {
            if (this.lead.expert_id) {
                return this.toastError(`Lead is already assigned to expert ${getFullName(this.agentDictionary.find(String(this.lead.expert_id)))}`)
            }

            if (this.lead.expert_request_at && !this.lead.expert_request_is_done) {
                return this.$related.LeadComponent.assignExpertForLead(this.lead.id)
            }

            return this.toastError('Expert help is not requested yet')
        },

        editLead() {
            this.$showModal(LeadCreateModal, {}, {
                editLeadPk: this.leadPk,
            }).then(response => {
                this.$typedParent.updateLeadCardData(response.result.id)
            }).catch(() => {
            })
        },

        editLeadStatus(id) {
            const closedStatus = this.statusData.filter((item) => item.system_name === 'Closed')

            if (id === closedStatus.pop().value) {
                this.$showModal(CloseLeadReasonRequestModal, {}, {
                    leadPk: usePk('Lead', this.lead.id),
                })
            } else {
                this.$related.LeadComponent.editLeadStatus(this.lead.id, id)
            }
        },

        sendExpertRequestHelp() {
            this.isSent = true
            this.$related.LeadComponent.sendExpertRequestHelp(this.lead.id)
        },

        expertRequestIsDone() {
            this.$related.LeadComponent.expertRequestIsDone(this.lead.id)
        },

        async expertStartWork() {
            const currentExpert = this.agentDictionary.records.find((agent) => agent.pk === String(this.lead.expert_id))

            if (this.isUserExpertManager && this.lead.expert_id !== this.currentUser.id) {
                this.$related.LeadComponent.assignExpertForLead(this.lead.id)

                return
            }

            if (this.lead.expert_id && !this.lead.expert_request_is_done && this.lead.expert_request_start_at && this.lead.expert_id !== this.currentUser.id) {
                const confirmed = await $confirm({
                    type: MessageType.Warning,
                    text: `This lead is already being worked on by expert ${getFullName(currentExpert)}. Are you sure you want to take over this lead?`,
                    confirmButton: 'Yes',
                })

                if (!confirmed) return
            }

            this.$related.LeadComponent.expertStartWork()
        },

        async sendLeadBackToQueue() {
            await this.$related.LeadComponent.sendLeadBackToQueue(this.lead.id)
        },

        goToClient(id) {
            this.$router.push({ name: 'clients.edit', params: { id: id } })
        },

        goToClientMiddle(id) {
            this.$typedParent.route = true
            const routeData = this.$router.resolve({ name: 'clients.edit', params: { id: id } })
            window.open(routeData.href, '_blank')
        },

        markDuplicateStatus(status) {
            this.$models.LeadPageModel.setDuplicateStatus(this.lead.id, status).then(() => {
            }).catch(apiError => {
                apiError.processWithMessage()
            })
        },

        hideLead() {
            this.$models.LeadPageModel.setHidden(this.lead.id, { is_hidden: 1 }).then(() => {
                this.toastSuccess('Lead is hidden now')
            }).catch(apiError => {
                apiError.processWithMessage()
            })
        },

        restoreLead() {
            this.$models.LeadPageModel.setHidden(this.lead.id, { is_hidden: 0 }).then(() => {
                this.toastSuccess('Lead is restored now')
            }).catch(apiError => {
                apiError.processWithMessage()
            })
        },
    },
})
</script>
