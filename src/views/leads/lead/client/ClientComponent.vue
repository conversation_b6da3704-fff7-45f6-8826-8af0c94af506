<template>
    <div class="leads-board-customer">
        <div class="customer-bar">
            <ClientBarBlock :lead-pk="String(lead.id)" />
        </div>
        <ClientSpottedLeadsBlock
            v-if="$can('viewLeadDuplicatesSection', 'Lead') && !webUser.spottedShow && lead.duplicateLeadCount > 0"
        />

        <!--        <ClientIdentityBlock-->
        <!--            v-if="!isExpertMode"-->
        <!--        />-->
    </div>
</template>

<script>
import ClientBarBlock from '@/views/leads/lead/client/blocks/ClientBarBlock'
import ParentMixin from '@/lib/mixin/ComponentRelation/ParentMixin'
import ClientSpottedLeadsBlock from '@/views/leads/lead/client/blocks/ClientSpottedLeadsBlock'
import ChildMixin from '@/lib/mixin/ComponentRelation/ChildMixin'
import ChildTypedMixin from '@/lib/mixin/ComponentRelation/ChildTypedMixin'
import WebUser from '@/lib/service/WebUser'

export default {
    name: 'ClientComponent',
    components: {
        ClientSpottedLeadsBlock,
        ClientBarBlock,
        // ClientIdentityBlock
    },

    parentType: 'LeadsController',
    parentName: ['LeadComponent'],
    mixins: [ParentMixin, ChildMixin, ChildTypedMixin],

    data() {
        return {
            webUser: WebUser,
        }
    },

    computed: {
        lead() {
            return this.$related.LeadComponent.lead
        },

        duplicatesCount() {
            return this.lead.duplicateLeadCount
        },

        client() {
            return this.lead?.client_id || null
        },

        isExpertMode() {
            return this.$typedParent.isExpertMode
        },

        openDuplicates() {
            return this.$typedParent.openDuplicates
        },

        agentInfo() {
            return this.lead?.executor
        },

        clientDetails() {
            return this.lead?.client
        },
    },
}
</script>
