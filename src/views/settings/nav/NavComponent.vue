<template>
    <div class="settings-sidebar">
        <div class="settings-search">
            <div class="relative">
                <input
                    type="text"
                    class="form-control text-xs leading-5 pr-10"
                    placeholder="Search in settings..."
                >
                <button
                    type="button"
                    class="absolute top-px right-px flex items-center justify-center w-9 h-9 text-theme-33"
                >
                    <SearchIcon
                        class="w-3.5 h-3.5 !stroke-2"
                    />
                </button>
            </div>
        </div>
        <ul class="settings-nav">
            <li class="settings-nav-item">
                <router-link
                    :to="{ name: 'settings'}"
                    class="settings-nav-link"
                    active-class="active"
                >
                    General settings
                </router-link>
            </li>
            <li
                v-if="superUser"
                class="settings-nav-item"
            >
                <router-link
                    :to="{ name: 'settings.award'}"
                    class="settings-nav-link"
                    active-class="active"
                >
                    Award settings
                </router-link>
            </li>
            <li
                v-if="superUser"
                class="settings-nav-item"
            >
                <router-link
                    :to="{ name: 'settings.iata.codes'}"
                    class="settings-nav-link"
                    active-class="active"
                >
                    IATA
                </router-link>
            </li>
            <li
                v-if="superUser"
                class="settings-nav-item"
            >
                <router-link
                    :to="{ name: 'settings.project.cards'}"
                    class="settings-nav-link"
                    active-class="active"
                >
                    Project Cards
                </router-link>
            </li>
            <li
                v-if="superUser || $can('openPageLeadTakeRules', 'all')"
                class="settings-nav-item"
            >
                <router-link
                    :to="{ name: 'settings.lead.take.rules'}"
                    class="settings-nav-link"
                    active-class="active"
                >
                    Lead take rules
                </router-link>
            </li>
            <li
                v-if="superUser"
                class="settings-nav-item"
            >
                <router-link
                    :to="{ name: 'settings.expert.invoices'}"
                    class="settings-nav-link"
                    active-class="active"
                >
                    Expert invoices
                </router-link>
            </li>
            <li
                v-if="superUser"
                class="settings-nav-item"
            >
                <router-link
                    :to="{ name: 'settings.consolidator'}"
                    class="settings-nav-link"
                    active-class="active"
                >
                    Consolidator credentials
                </router-link>
            </li>
            <li
                v-if="hasAnyFestiveEvent"
                class="settings-nav-item"
            >
                <router-link
                    :to="{ name: 'settings.festive-events'}"
                    class="settings-nav-link"
                    active-class="active"
                >
                    Festive events settings
                </router-link>
            </li>
            <li
                v-if="$can('updateSkillList', 'all')"
                class="settings-nav-item"
            >
                <router-link
                    :to="{ name: 'settings.skills'}"
                    class="settings-nav-link"
                    active-class="active"
                >
                    Skills settings
                </router-link>
            </li>

            <li
                v-if="$can('manage', 'all')"
                class="settings-nav-item"
            >
                <router-link
                    :to="{ name: 'settings.payment.gateway.balance'}"
                    class="settings-nav-link"
                    active-class="active"
                >
                    Payment Gateway Balance
                </router-link>
            </li>

            <li
                v-if="$can('manage', 'all')"
                class="settings-nav-item"
            >
                <router-link
                    :to="{ name: 'settings.office.settings'}"
                    class="settings-nav-link"
                    active-class="active"
                >
                    Office Settings
                </router-link>
            </li>

            <li
                v-if="$can('editConsolidatorManagementTool', 'all')"
                class="settings-nav-item"
            >
                <router-link
                    :to="{ name: 'settings.consolidators'}"
                    class="settings-nav-link"
                    active-class="active"
                >
                    Consolidators
                </router-link>
            </li>

            <li
                v-if="$can('manage', 'all')"
                class="settings-nav-item"
            >
                <router-link
                    :to="{ name: 'settings.marketing'}"
                    class="settings-nav-link"
                    active-class="active"
                >
                    Marketing budget
                </router-link>
            </li>
        </ul>
    </div>
</template>

<script>
import { SearchIcon } from '@zhuowenli/vue-feather-icons'

export default {
    name: 'NavComponent',
    components: {
        SearchIcon,
    },

    computed: {
        superUser() {
            return this.$can('manage', 'all')
        },

        hasAnyFestiveEvent() {
            return Object.keys(this.$festiveEvents).length
        },
    },
}
</script>
