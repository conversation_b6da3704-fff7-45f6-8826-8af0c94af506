<template>
    <SendModalWrapper
        ref="modalWrapper"
        v-model:current-tab="currentTab"
        :email="email"
        :tabs="tabs"
        :show-trust-pilot="showTrustPilot"
        :review-link="reviewLink"
        :client-pk="clientPk"
        :can-edit="canEdit"
        :default-email="defaultEmail"
        header="Send Option"
        class="!min-w-[1200px]"
        :loading="loading"
        @submit="submit"
    >
        <div
            v-if="currentTab === 'ticket'"
            class="flex-grow overflow-x-hidden overflow-y-auto fancy-scroll"
        >
            <iframe
                :src="previewUrl"
                class="w-full min-h-full border-none"
            />
        </div>
        <div
            v-if="currentTab === 'ticket-protection'"
            class="flex-grow overflow-x-hidden overflow-y-auto fancy-scroll"
        >
            <iframe
                :src="previewTicketProtectionUrl"
                class="w-full min-h-full border-none"
            />
        </div>
        <div
            v-if="currentTab === 'baggage-protection'"
            class="flex-grow overflow-x-hidden overflow-y-auto fancy-scroll"
        >
            <iframe
                :src="previewBaggageProtectionUrl"
                class="w-full min-h-full border-none"
            />
        </div>
    </SendModalWrapper>
</template>

<script lang="ts">
import SendModalWrapper from '@/components/Modals/sale/SendModalWrapper'
import { preventDuplication } from '~/lib/Helper/PromiseHelper'

export enum SendOptionType {
    TrustPilot = 'trust_pilot',
    GoogleReview = 'google',
    Empty = 'empty'
}
export default defineComponent({
    name: 'SaleSendTicketModal',
    components: {
        SendModalWrapper,
    },

    props: {
        saleHelper: {
            type: Object,
            required: true,
        },

        tabOnOpening: {
            type: String,
            default: null,
        },
    },

    async setup(props) {
        const { useModel, hasPermission } = useContext()

        let canEdit = false

        try {
            // @todo We should not fetch leads that we don't have access to
            const { record: lead, fetch } = useModel('Lead').useRecord().destructable()

            await fetch(usePk('Lead', props.saleHelper.saleModel.lead_id))

            canEdit = !lead.value ? false : hasPermission('edit', 'Lead', lead)
        } catch (e) {
            canEdit = false
        }

        return {
            canEdit,
            defaultEmail: props.saleHelper.saleModel.clientEmail,
        }
    },

    data() {
        return {
            email: '',
            previewUrl: null,
            previewTicketProtectionUrl: null,
            previewBaggageProtectionUrl: null,
            tabs: [],
            currentTab: this.tabOnOpening || 'ticket',
            reviewLink: '',
            loading: false,
        }
    },

    computed: {
        saleVersion() {
            return this.saleHelper.saleVersion
        },

        showTrustPilot() {
            return this.saleHelper.saleModel.project?.enabledTrustPilot
        },

        hasInsurance() {
            return this.saleHelper.hasInsurance
        },

        hasPaidBaggageProtection() {
            return this.saleHelper.hasPaidBaggageProtection
        },

        computedTabs() {
            const tabs = [{ id: 'ticket', title: 'Ticket' }]

            if (this.hasInsurance) {
                tabs.push({ id: 'ticket-protection', title: 'Ticket protection' })
            }

            if (this.hasPaidBaggageProtection) {
                tabs.push({ id: 'baggage-protection', title: 'Baggage protection' })
            }

            return tabs
        },

        clientPk() {
            return usePk('Client', this.saleHelper.saleModel.client_id)
        },

        leadPk() {
            return usePk('Lead', this.saleHelper.saleModel.lead_id)
        },
    },

    created() {
        this.tabs = this.computedTabs
        this.email = this.saleVersion.client_email
        this.$models.SaleVersionModel.accessRequest(this.saleVersion.id)
            .then(({ result: { url, tp_url, bp_url, review_link } }) => {
                this.reviewLink = review_link
                this.previewUrl = url
                this.previewTicketProtectionUrl = tp_url
                this.previewBaggageProtectionUrl = bp_url
            })
            .catch(apiError => this.$refs.modalWrapper.error(apiError))
    },

    methods: {
        async submit(email = null, review_link = 'empty') {
            const self = this

            console.log('submit', email, review_link)
            await preventDuplication(async () => {
                try {
                    const link = review_link === SendOptionType.Empty ? '' : review_link

                    await self.$models.SaleVersionModel.sendMail(self.saleVersion.id, email, link)

                    self.$refs.modalWrapper.success()
                } catch (apiError) {
                    self.$refs.modalWrapper.error(apiError)
                }
            }, computed({
                get() {
                    return self.loading
                },
                set(value) {
                    console.log('value', value)
                    self.loading = value
                },
            }))
        },
    },

})
</script>
