<template>
    <AppModalWrapper
        header="Rebalance Sell Price"
        header-align="left"
        close-button
        class="!max-w-6xl"
    >
        <template #default>
            <div class="p-4 pt-0">
                <table class="custom-table table-fixed">
                    <colgroup>
                        <col style="width: 10%">
                        <col style="width: 16%">
                        <col style="width: 12%">
                        <col style="width: 12%">
                        <col style="width: 12%">
                        <col style="width: 10%">
                        <col style="width: 7%">
                    </colgroup>

                    <tr class="bg-gray-100 dark:bg-gray-900 text-theme-7 font-medium text-2xs">
                        <td colspan="2" />
                        <td>Airline Charge</td>
                        <td>Old Sell Price</td>
                        <td>New Sell Price</td>
                        <td>New CK</td>
                        <td class="text-center">
                            Action
                        </td>
                    </tr>
                    <tr
                        v-for="{map: {field: product}} in form.field.products.items"
                        :key="product.id.value"
                        class="leading-5"
                        :class="{
                            'bg-blue-50 dark:bg-blue-900': product.sell_price.value && product.sell_price.value !== product.sell_price_old.value
                        }"
                    >
                        <td class="!border-r-0">
                            <div class="text-sm">
                                {{ product.type.value }}
                            </div>
                            <div
                                v-if="product.type.value === 'Ticket'"
                                class="text-theme-7"
                            >
                                {{ product.external_number.label }}
                            </div>
                            <div
                                v-if="product.type.value === 'Ticket'"
                                class="text-theme-7"
                            >
                                {{ product.consolidator_order_id.label }}
                            </div>
                        </td>

                        <td class="!border-l-0">
                            <div>
                                <span class="text-theme-7">{{ paymentTypeParts(product.pay_type.valueLabel)[0] }}</span>
                                {{ paymentTypeParts(product.pay_type.valueLabel)[1] }}

                                <span v-if="hasManyCards && getCardNumber(product.card_identity.value)">
                                    <span class="text-theme-7">•</span> {{ getCardNumber(product.card_identity.value) }}
                                </span>
                            </div>
                            <div
                                v-if="product.type.value === 'Ticket'"
                                :class="{
                                    'text-theme-16': !product.external_number.value,
                                }"
                            >
                                {{ product.external_number.value || 'Not set' }}
                            </div>
                            <div
                                v-if="product.type.value === 'Ticket'"
                                :class="{
                                    'text-theme-16': !product.consolidator_order_id.value,
                                }"
                            >
                                {{ product.consolidator_order_id.value || 'Not set' }}
                            </div>
                        </td>

                        <td>
                            <InputLayoutWrapper v-model="product.airline_charge">
                                <FormInputNumberMask
                                    class="form-control form-control-sm"
                                    disabled
                                />
                            </InputLayoutWrapper>
                        </td>
                        <td>
                            <InputLayoutWrapper v-model="product.sell_price_old">
                                <FormInputNumberMask
                                    class="form-control form-control-sm"
                                    disabled
                                />
                            </InputLayoutWrapper>
                        </td>
                        <td>
                            <InputLayoutWrapper v-model="product.sell_price">
                                <FormInputNumberMask class="form-control form-control-sm" />
                                <div
                                    v-if="product.sell_price.errors.length"
                                    class="mt-1 text-2xs leading-tight text-red-500 whitespace-normal"
                                >
                                    {{ formatError(product.sell_price.errors[0]) }}
                                </div>
                            </InputLayoutWrapper>
                        </td>
                        <td>
                            <InputLayoutWrapper v-model="product.check_payment">
                                <FormInputNumberMask class="form-control form-control-sm" />
                                <div
                                    v-if="product.check_payment.errors.length"
                                    class="mt-1 text-2xs leading-tight text-red-500 whitespace-normal"
                                >
                                    {{ formatError(product.check_payment.errors[0]) }}
                                </div>
                            </InputLayoutWrapper>
                        </td>
                        <td>
                            <button
                                v-if="balance > 0 || (balance < 0 && product.sell_price.value > 0)"
                                type="button"
                                class="btn btn-outline-secondary box w-8 block mx-auto text-xs px-2.5 py-1.5"
                                @click="setSellPrice(product.id.value)"
                            >
                                <span v-if="balance > 0">+</span>
                                <span v-else-if="balance < 0">-</span>
                            </button>
                        </td>
                    </tr>
                </table>
            </div>
        </template>

        <template #footer>
            <div class="text-right">
                <span
                    v-if="!form.isValid"
                    class="text-danger text-xs mr-8"
                >
                    {{ errorMessage[0] }}
                </span>

                <AppModalButton
                    class="mr-4"
                    :class="{
                        'btn-primary': balance === 0 && form.isValid,
                        'btn-danger': balance !== 0 || !form.isValid,
                    }"
                    :disabled="balance !== 0 || !form.isValid"
                    @click="submit"
                >
                    <template v-if="balance === 0">
                        Confirm changes
                    </template>
                    <template v-else>
                        Balance: {{ balance }}
                    </template>
                </AppModalButton>
                <AppModalButton @click="cancel">
                    Close
                </AppModalButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script>
import AppModalButton from '@/components/Modals/core/AppModalButton.vue'
import InputLayoutWrapper from '@/components/Form/FormWrappers/InputLayoutWrapper'
import FormInputNumberMask from '@/components/Form/FormField/FormInputNumberMask'
import FormHelper from '@/lib/core/helper/FormHelper'
import ArrayMapField from '@/lib/FormField/ArrayMapField'
import ProductFormField from '@/lib/FormField/Models/ProductFormField'
import InputNumberField from '@/lib/FormField/InputNumberField'
import TransactionService from '@/lib/service/TransactionService'
import CardHelper from '@/lib/core/helper/CardHelper'

export default {
    name: 'ChangeSellPriceModal',

    components: {
        AppModalButton,
        InputLayoutWrapper,
        FormInputNumberMask,
    },

    modal: {
        promise: true,
    },

    props: {
        saleHelper: {
            type: Object,
            required: true,
        },
    },

    emits: ['resolve', 'reject'],

    data() {
        return {
            form: new FormHelper({
                products: new ArrayMapField(
                    new ProductFormField({
                        sell_price_old: new InputNumberField(false, 'Sale Price'),
                        airline_charge: new InputNumberField(false, 'Airline Charge'),
                    }), [], (data, number) => data.id || number,
                ),
            }),
        }
    },

    computed: {
        products() {
            return [
                ...this.saleHelper.tickets.records.map(v => v.product),
                ...this.saleHelper.additionalExpenses.map(v => v.product),
                ...this.saleHelper.incentiveSales.map(v => v.product),
            ]
        },

        creditCards() {
            return this.saleHelper.creditCardData?.records ?? []
        },

        errorMessage() {
            return this.form.errorSummary
        },

        productsTotal() {
            return this.saleHelper.saleSummaryData
        },

        sellPriceSum() {
            let sum = 0

            this.form.field.products.items.forEach(productForm => {
                sum += Number(productForm.map.field.sell_price.value).toMoney()
            })

            return sum
        },

        balance() {
            return Number(Number(this.productsTotal.sellPrice) - Number(this.sellPriceSum)).toMoney()
        },

        hasManyCards() {
            return this.creditCards.length > 1
        },
    },

    created() {
        this.seedForm()
        this.applyDiff()
    },

    methods: {
        seedForm() {
            // @NOTE Products contains v1 and v2 product models
            this.products.forEach(product => {
                const productField = this.form.field.products.addItem({
                    id: product.id,
                    type: product.type || product.item_type,
                    sell_price_old: product.sell_price,
                    sell_price: product.pay_type === 'CC' ? Math.max(product.sell_price, product.net_price_base) : product.sell_price,
                    net_price_base: product.net_price_base,
                    airline_charge: product.pay_type === 'CC' ? (Number(product.isAward) === 1 ? product.tax : product.net_price_base) : product.net_price_base,  // Airline charge
                    pay_type: product.pay_type,
                    check_payment: product.check_payment,
                    commission_ps: product.commission_ps,
                    fare: product.fare,
                    commission: product.commission,
                    tax: product.tax,
                    issuing_fee: product.issuing_fee,
                    external_number: product.external_number,
                    consolidator_order_id: product.consolidator_order_id || product.consolidator_area_pk,
                    card_identity: product.card_identity,
                    isAward: product.isAward || product.is_award,
                }).map

                const {
                    recalculateCK,
                } = productField.useCalculations()

                recalculateCK()

                productField.event.addListener('change', (...v) => {
                    if (v[0].field !== 'check_payment') {
                        recalculateCK()
                    }
                })
            })
        },

        applyDiff() {
            const products = []

            this.form.data.products.forEach(product => {
                products.push({
                    id: product.id,
                    sellPriceOld: !!product.sell_price_old ? product.sell_price_old : 0,
                    sellPrice: !!product.sell_price ? product.sell_price : 0,
                    pay_type: product.pay_type,
                    type: product.type,
                    minPrice: product.pay_type === 'CC' ? (Number(product.isAward) === 1 ? product.tax : product.net_price_base) : 0,
                })
            })

            const result = TransactionService.productsAutoBalance(products)

            result.forEach(product => {
                const found = this.form.field.products.items.find(productForm => productForm.map.field.id.value === product.id)

                found.map.field.sell_price.value = product.sellPrice
            })
        },

        setSellPrice(id) {
            if (!id) {
                return
            }

            const product = this.form.field.products.items.find(productForm => {
                return productForm.map.field.id.value === id
            })

            const minPrice = product.map.field.pay_type.value === 'CC' ? product.map.field.net_price_base.value : 0

            product.map.field.sell_price.value = Math.max(
                product.map.field.sell_price.value + this.balance,
                minPrice,
            )

            this.form.validate()
        },

        submit() {
            if (!this.form.validate()) {
                return
            }

            const data = this.form.data.products.map(product => ({
                id: product.id,
                sell_price: product.sell_price,
                check_payment: product.check_payment,
            }))

            this.$emit('resolve', data)
        },

        cancel() {
            this.$emit('reject')
        },

        //

        paymentTypeParts(paymentType) {
            const match = paymentType.match(/(.+) ((CC.*)|(VCC.*))/)

            if (match) {
                return [match[1], match[2]]
            }

            return [paymentType]
        },

        getCardNumber(cardIdentity) {
            if (!cardIdentity) {
                return null
            }

            const card = this.creditCards.find(card => card.id === cardIdentity)

            if (!card) {
                return null
            }

            return CardHelper.strip(card.payload.card)
        },

        formatError(error) {
            const value = error.replace(/^(The Field .+?)(?=should|cannot)/, '')

            return value[0].toUpperCase() + value.substring(1)
        },
    },
}
</script>

<style scoped>
.custom-table td {
    @apply px-4;
}
</style>
