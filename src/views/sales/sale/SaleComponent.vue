<template>
    <div>
        <SaleSummaryBlock v-if="saleId" :sale-pk="String(saleId)">
            <slot name="tabs-append" />
        </SaleSummaryBlock>

        <SaleGroupBlock v-if="showSaleGroups" :compared-with="comparedWith" />
        <SalePassengersSection
            v-if="saleVersion"
            :key="saleVersionPk"
            :sale-version-pk="saleVersionPk"
            :is-editable="isPassengerEditable"
            class="mt-5"
        />
        <SaleSegmentsBlock />
        <SaleTicketsBlock :key="`saleVersionTickets--${saleVersionPk}`" />

        <SalePnrInfoSection
            v-if="saleVersionId"
            :key="saleVersionPk"
            :sale-pk="String(saleId)"
            :sale-version-pk="saleVersionPk"
        />

        <SaleDocumentsSection
            v-if="saleVersionPk"
            :key="saleVersionPk"
            :sale-version-pk="saleVersionPk"
        />

        <SaleIncentiveSection
            v-if="saleVersionPk"
            :key="saleVersionPk"
            :sale-version-pk="saleVersionPk"
        />

        <SaleClientRequestSection
            v-if="saleVersionPk && saleModel?.canViewClient"
            :key="saleVersionPk"
            :sale-version-pk="saleVersionPk"
        />

        <SaleAdditionalExpenseSection
            v-if="saleVersionPk"
            :key="saleVersionPk"
            :sale-version-pk="saleVersionPk"
        />

        <SaleExtraProfits v-if="$can('openPageAirlineReport', 'all') && saleId" :sale-pk="String(saleId)" />
        <!--                <SaleOafBlock />-->

        <SalePaymentSection
            v-if="saleVersionPk"
            :key="`${saleVersionPk}|${saleId}`"
            class="mt-5"
            :sale-pk="String(saleId)"
            :sale-version-pk="saleVersionPk"
        />

        <SaleUsedVcc v-if="canViewVccUsage" :sale-pk="String(saleId)" />

        <BillingHistorySection :sale-pk="String(saleId)" />
        <!--        BillingHistoryBlock NEW START-->
        <!--        <SaleTransactionAssignments />-->
        <!--        BillingHistoryBlock NEW END-->
        <SaleHistoryBlock />
        <!--       SaleHistoryBlock NEW START-->
        <!--        <ActivityLogList-->
        <!--            :model="{model_name: 'Sale', model_pk: String(saleModel.id)}"-->
        <!--            title="Activity logs"-->
        <!--            class="h-[300px]"-->
        <!--        />-->
        <!--       SaleHistoryBlock NEW END-->
    </div>
</template>

<script lang="ts">
import SaleSummaryBlock from '@/views/sales/sale/blocks/SaleSummary/SaleSummaryBlock.vue'
import SaleGroupBlock from '@/views/sales/sale/blocks/SaleGroupBlock.vue'
import SaleSegmentsBlock from '@/views/sales/sale/blocks/SaleSegmentsBlock.vue'
import SaleHistoryBlock from '@/views/sales/sale/blocks/SaleHistoryBlock.vue'
import ParentMixin from '@/lib/mixin/ComponentRelation/ParentMixin'
import SaleTicketsBlock from '@/views/sales/sale/blocks/SaleTicketsBlock.vue'

import ProgressBarMixin from '@/lib/mixin/ProgressBarMixin'
import type SaleHelper from '@/lib/core/helper/Sale/SaleHelper'
import { mountBuilder, unmountBuilder } from '@/lib/mixin/UnMount/UnMountMixin'
import { defineComponent, type PropType } from 'vue'

import SalePassengersSection from '~/components/Page/Sale/sections/SalePassengers/SalePassengersSection.vue'
import AssignAgentModal, { type AssignmentResult } from '~/modals/AssignAgentModal.vue'
import { AssignLogColumn } from '~/api/models/AssignLog/AssignLog'
import SaleExtraProfits from '~/sections/Sale/components/SaleExtraProfits.vue'
import SaleUsedVcc from '~/sections/Sale/components/SaleUsedVcc.vue'
import SalePnrInfoSection from '~/sections/Sale/sections/SalePnrInfoSection.vue'
import BillingHistorySection from '~/sections/Sale/BillingHistory/BillingHistorySection.vue'
import SalePaymentSection from '~/sections/Sale/Payment/SalePaymentSection.vue'
import SaleClientRequestSection from '~/sections/Sale/sections/SaleClientRequestSection.vue'
import SaleDocumentsSection from '~/sections/Sale/sections/SaleDocumentsSection.vue'
import SaleAdditionalExpenseSection from '~/sections/Sale/AdditionalExpense/SaleAdditionalExpenseSection.vue'
import SaleIncentiveSection from '~/sections/Sale/Incentive/SaleIncentiveSection.vue'

// import SaleInvoicesBlock from '~/components/Page/Sale/SalePayment/SaleInvoicesBlock.vue'
// import SaleTransactionAssignments from '~/components/Page/Sale/SaleTransactionAssignments/SaleTransactionAssignments.vue'
// import ActivityLogList from '~/components/ActivityLog/ActivityLogList.vue'

export default defineComponent({
    name: 'SaleComponent',

    type: [
        'OrganizerIntegration',
        'HasSegmentEditor',
        'HasAppointment',
        'AddIncentive',
        'AddExpenses',
        'hasSaleManualApproveWarning',
        'hasBillingHistory',
        'hasRefund',
        'hasSellPriceReBalance',
    ],

    components: {
        SaleIncentiveSection,
        SaleAdditionalExpenseSection,
        SaleClientRequestSection,
        SaleDocumentsSection,
        // v2 blocks
        BillingHistorySection,
        SalePaymentSection,
        SalePnrInfoSection,
        SaleExtraProfits,
        SalePassengersSection,
        SaleUsedVcc,
        // ActivityLogList,
        // SaleTransactionAssignments,
        // SaleInvoicesBlock,
        // v1 blocks
        SaleTicketsBlock,
        SaleHistoryBlock,
        // IncentiveSalesBlock,
        // AdditionalExpensesBlock,
        SaleSegmentsBlock,
        SaleSummaryBlock,
        SaleGroupBlock,
    },

    mixins: [ParentMixin, ProgressBarMixin],

    provide() {
        return {
            saleHelper: this.saleHelper,
        }
    },

    props: {
        saleHelper: {
            type: Object as PropType<SaleHelper>,
            required: true,
        },

        comparedWith: {
            type: Array,
            default: () => [],
        },

        showSaleGroups: {
            type: Boolean,
            default: true,
        },
    },

    data() {
        return {
            organizerWrapper: null,
            // priceQuote: SalePriceQuoteModel,
            currentChatRoomId: null,
            organizer: {
                organizerObject: 'Sale',
                organizerObjectModel: () => this.saleModel,
                chatCategory: 'Sales',
                tasks: {
                    generalTaskGroup: null,
                    customerSupportTaskGroup: null,
                    disclaimerTaskGroup: null,
                    ticketTaskGroup: null,
                    checkInTaskGroup: null,
                },
            },

            saleSummaryBlock: null,
            infoManualApproveWarningForId: null,
            ticketComponentGroups: {},
            updateFareTypeQueue: {},
        }
    },

    computed: {
        saleId() {
            return this.saleHelper.saleId as unknown as number
        },

        saleVersionPk() {
            return usePk('Sale', this.saleVersionId)
        },

        saleModel() {
            return this.saleHelper.saleModel
        },

        saleVersions() {
            return this.saleHelper.saleVersions
        },

        saleVersion: {
            get() {
                return this.saleHelper.saleVersion
            },

            set(v) {
                // eslint-disable-next-line vue/no-mutating-props
                this.saleHelper.saleVersion = v
            },
        },

        saleVersionId() {
            return this.saleHelper.saleVersionId
        },

        passengers() {
            return this.saleHelper.passengers
        },

        tickets() {
            return this.saleHelper.tickets
        },

        creditCardData() {
            return this.saleHelper.creditCardData
        },

        virtualCards() {
            return this.saleHelper.virtualCards
        },

        transactions() {
            return this.saleHelper.transactions
        },

        priceQuote() {
            return this.saleHelper.priceQuote
        },

        segmentsGroup() {
            return this.saleHelper.segmentsGroup
        },

        salePermission() {
            const rule = !this.isSaleAdjusted || !this.isSaleClosed

            // @ts-ignore
            return this.$can('manage', this.organizer.organizerObject) ? false : rule
        },

        tasks() {
            return this.organizer.tasks
        },

        saleVersionComputed() {
            return this.saleVersion || null
        },

        isSaleAdjusted() {
            return this.saleHelper?.isSaleAdjusted
        },

        isSaleClosed() {
            return !!this.saleVersion?.is_sale_closed
        },

        isPassengerEditable() {
            // how to get hasPermission from context ? How to use context?
            return this.$can('edit', 'Sale', this.saleModel) && !this.isSaleAdjusted && !this.isSaleClosed
        },

        hasTicketProtection() {
            return this.saleHelper.hasInsurance
        },

        canViewVccUsage() {
            // @ts-ignore
            return this.$can('viewVccUsage', 'Sale', this.saleModel)
        },
    },

    // watch: {
    //     saleVersion(saleVersion) {
    //         this.getTaskGroups(saleVersion)
    //     },
    // },

    mounted() {
        mountBuilder(this)(this.saleHelper)
        // this.fetchVirtualCards()
    },

    unmounted() {
        unmountBuilder(this)()
    },

    methods: {
        onAddSaleVersion(version) {
            this.saleVersions.refresh().then(() => {
                this.saleVersions.records.forEach(record => {
                    if (record.is_active) {
                        this.saleVersion = record
                    }
                })
            })
        },

        onExchangePriceQuote() {
            this.saleHelper.fetchPriceQuote()
        },

        changeSaleType(type) {
            this.$models.SalePageModel.changeSaleType(this.saleModel.id, type)
        },

        checkSaleAdjusted() {
            if (this.saleHelper.isSaleAdjusted) {
                this.$toast.open({
                    position: 'top-right',
                    message: 'Sale adjusted!',
                    type: 'error',
                    duration: 1000,
                })

                return true
            }

            return false
        },

        openChatRoom(id) {
            this.currentChatRoomId = id
        },

        $mountChild(component, name) {
            // console.log('ParentMixin $mountChild', component, name)
            if (name === 'OrganizerWrapper') {
                this.organizerWrapper = component
            }

            if (name === 'TicketComponent') {
                if (this.ticketComponentGroups[component.groupIdentity] === undefined) {
                    this.ticketComponentGroups[component.groupIdentity] = []
                }

                this.ticketComponentGroups[component.groupIdentity].push(component)
            }
        },

        $unmountChild(component, name) {
            if (name === 'TicketComponent') {
                this.ticketComponentGroups[component.groupIdentity] = this.ticketComponentGroups[component.groupIdentity].filter(item => item !== component)
            }
        },

        showAgentAssignModal(id, canUnAssign) {
            this.$showModal(AssignAgentModal, {
                workspaceProject: this.saleModel?.project_id,
            }, {
                model: {
                    name: 'Sale',
                    id: this.saleId,
                },
                column: AssignLogColumn.Executor,
                canUnAssign: canUnAssign ?? true,
                defaultAgentPk: id ? String(id) : undefined, // @todo usePk()
                messageSuccess: 'Executor has been assigned',
                onAssign: (params: AssignmentResult) => this.setExecutor(params.agent_pk),
            })
        },

        setExecutor(id) {
            return this.$models.SalePageModel.setAgent(this.saleId, { executor_id: id })
                .then(apiResponse => {
                    this.saleModel.executor_id = apiResponse.result.executor_id

                    return apiResponse
                })
        },

        // updateIncentiveSale(data) {
        //     this.incentiveSales.addRecord(data)
        // },
        //
        // refetchSaleExpenses() {
        //     this.additionalExpenses.refresh()
        // },
        //
        // updateSaleExpenses(data) {
        //     this.additionalExpenses.addRecord(data)
        // },

        onUpdateSegmentsData(data) {
            if (data?.segments) {
                // @ts-ignore
                this.priceQuote.segments = data?.segments
            }
        },

        ticketOtherFormOpened(component) {
            return this.ticketComponentGroups[component.groupIdentity].findIndex(other => other !== component && other.isEditMode) !== -1
        },

        ticketFormChange(component, form) {
            this.ticketComponentGroups[component.groupIdentity].forEach(other => {
                if (other !== component && other.isEditMode && other.isSubscribe) {
                    other.onOtherFormChange(form, component.ticket.passengerType !== other.ticket.passengerType)
                }
            })
        },

        ticketFormToggleSubscribe(component, status) {
            this.ticketComponentGroups[component.groupIdentity].forEach(comp => {
                comp.onToggleFormSubscribe(status)
            })
        },

        getFormDataOtherSubscribe(component) {
            let formData = null

            this.ticketComponentGroups[component.groupIdentity].forEach(other => {
                if (other !== component && other.isEditMode && other.isSubscribe) {
                    formData = other.form.data
                }
            })

            if (formData.product?.external_number) {
                delete formData.product.external_number
            }

            return formData
        },

        closeAllFormTicket(component) {
            this.ticketComponentGroups[component.groupIdentity].forEach(comp => {
                comp.closeTicket()
            })
        },

        saveAndCloseAllFormTickets(component) {
            for (const other of this.ticketComponentGroups[component.groupIdentity]) {
                if (other !== component && other.isEditMode && other.isSubscribe) {
                    other.save()
                }
            }
            this.ticketFormToggleSubscribe(component, false)
        },

        addAirlinePnrs(component) {
            this.ticketComponentGroups[component.groupIdentity].forEach(other => {
                if (other !== component && other.isEditMode && other.isSubscribe) {
                    other.addFormAirlinePnrs()
                }
            })
        },

        dellAirlinePnrs(component, identity) {
            this.ticketComponentGroups[component.groupIdentity].forEach(other => {
                if (other !== component && other.isEditMode && other.isSubscribe) {
                    other.delFormAirlinePnr(identity)
                }
            })
        },
    },
})
</script>
