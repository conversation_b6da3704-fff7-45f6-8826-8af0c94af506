<template>
    <SuspenseManual :loading="loading">
        <div class="box">
            <div class="flex items-center justify-between bg-gray-200 dark:bg-dark-2 rounded-t-md pr-2 gap-x-2">
                <div class="flex items-center justify-between w-full max-w-[800px]">
                    <Tabs
                        class="tabs--inverted tabs--dynamic-height flex-grow px-2 pt-1"
                        :model-value="saleVersion.id"
                        :tabs="tabs"
                        @update:model-value="changeSaleVersion"
                    >
                        <template #item-content="{ tab, index }">
                            <div class="text-center py-2">
                                <div
                                    class="font-semibold"
                                    :class="{
                                        ' text-theme-25 dark:text-white': tab.saleVersion.id === saleVersion.id,
                                    }"
                                >
                                    {{
                                        tab.saleVersion.is_active ? `Sale #${tab.saleVersion.sale_id}` : `Old Sale #${tab.saleVersion.sale_id}/${index + 1}`
                                    }}
                                </div>
                                <div class="font-medium text-gray-600">
                                    {{
                                        Date.fromUnixTimestamp(tab.saleVersion.created_at).toFormatWithTimezone('dd MMM ?yy HH:mm')
                                    }}
                                </div>
                            </div>
                        </template>
                    </Tabs>

                    <div class="flex-none">
                        <slot />
                    </div>
                </div>
                <AppButton
                    v-if="hasPermission('createRequestHelp', 'Sale')"
                    class="--warning"
                    @click="openRequestExpertHelpModal"
                >
                    <HelpCircleIcon />
                    Expert Help
                </AppButton>
            </div>

            <div class="bg-white dark:bg-dark-3 rounded-none" :class="{ 'rounded-b-md': !needApprove }">
                <div class="p-3 text-xs flex items-center gap-x-2">
                    <div>
                        <div
                            v-if="sale.is_hidden"
                            class="text-red-500"
                        >
                            Deleted Sale
                        </div>
                        <div
                            v-else
                            class="font-medium text-gray-600"
                            :class="{
                                'hover:bg-theme-39 rounded cursor-pointer w-[fit-content] -mx-1 px-1 py-0.5': canManageSale || canChangeSaleDate,
                            }"
                        >
                            <!-- Set date Actions -->
                            <Dropdown
                                v-if="(canManageSale || canChangeSaleDate) && sale"
                                teleport
                            >
                                <template #toggle="{ toggle }">
                                    <button
                                        class="font-medium text-gray-600 hover:bg-theme-39 rounded cursor-pointer w-[fit-content] -mx-1 px-1 py-0.5"
                                        type="button"
                                        @click="toggle"
                                    >
                                        {{
                                            sale.sale_at ? Date.fromUnixTimestamp(sale.sale_at).toFormatWithTimezone('dd MMM ?yy HH:mm') : 'No sale date'
                                        }}
                                    </button>
                                </template>
                                <template #content="{ close }">
                                    <SaleSummaryDatepickerDropdownContent
                                        class="my-1"
                                        :sale="sale"
                                        @close="close"
                                    />
                                </template>
                            </Dropdown>
                            <div
                                v-else
                                class="font-medium text-gray-600"
                            >
                                {{
                                    sale.sale_at ? Date.fromUnixTimestamp(sale.sale_at).toFormatWithTimezone('dd MMM ?yy HH:mm') : 'No sale date'
                                }}
                            </div>
                        </div>
                        <Component
                            :is="sale.canViewClient ? 'RouterLink' : 'div'"
                            v-if="client"
                            :to="routeToClient(sale.client_id)"
                            :class="{
                                'hover:text-primary-1': sale.canViewClient,
                            }"
                            class="text-sm font-medium truncate max-w-36 block"
                        >
                            {{ client ? getClientFullName(client) : '' }}
                        </Component>
                    </div>

                    <div class="flex gap-x-2">
                        <!-- Emails Button -->
                        <ClientEmailDataDropdown
                            v-if="sale.canViewClient || isUserExpertManager"
                            :client-pk="String(sale.client_id)"
                            :with-edit="$can('edit', 'Sale', sale) && !sale.is_hidden"
                            :default-email="saleVersion.client_email"
                            :set-default-email="(email_pk) => {
                                return SaleVersionModel.actions.setDefaultEmail({
                                    email_pk,
                                    sale_version_pk: String(saleVersion.id)
                                })
                            }"
                            @show="trackSuspiciousAction(SuspiciousAction.SaleShow, { pk: String(sale.id) })"
                            @copy="trackSuspiciousAction(SuspiciousAction.SaleCopy, { pk: String(sale.id) })"
                        />

                        <!-- Phones Button -->
                        <ClientPhoneDataDropdown
                            v-if="sale.canViewClient || isUserExpertManager"
                            :client-pk="String(sale.client_id)"
                            :with-edit="$can('edit', 'Sale', sale) && !sale.is_hidden"
                            :default-phone="saleVersion.client_phone"
                            :set-default-phone="(phone_pk) => {
                                return SaleVersionModel.actions.setDefaultPhone({
                                    phone_pk,
                                    sale_version_pk: String(saleVersion.id)
                                })
                            }"
                            @show="trackSuspiciousAction(SuspiciousAction.SaleShow, { pk: String(sale.id) })"
                            @copy="trackSuspiciousAction(SuspiciousAction.SaleCopy, { pk: String(sale.id) })"
                        />

                        <!-- Passport Info Button -->
                        <div v-if="sale.canViewClient">
                            <button
                                type="button"
                                class="btn btn-outline-secondary box"
                                @click="showPassportInfo = !showPassportInfo"
                            >
                                Passport
                                <span class="ml-1.5" :class="{'rotate-180': showPassportInfo}">
                                    <ChevronDownIcon class="w-3 h-3" />
                                </span>
                            </button>
                        </div>

                        <!-- OAF Info Button -->
                        <Dropdown
                            v-if="canEditSaleSimple"
                            teleport
                            placement="bottom-end"
                        >
                            <template #toggle="{ toggle }">
                                <button
                                    class="btn btn-outline-secondary box"
                                    :disabled="!saleVersion.oaf_url.length"
                                    @click="toggle"
                                >
                                    {{ oafFormName }}
                                    <ExternalLinkIcon class="ml-2 text-gray-500 w-3 h-3" />
                                </button>
                            </template>
                            <template #content>
                                <div
                                    class="dropdown-menu__content my-1 box dark:bg-dark-1 border border-theme-38/20 p-2 select-none"
                                    @click="()=>{
                                        if(activityMonitoringIsActive){
                                            trackSuspiciousAction(SuspiciousAction.SalePaxShow, { pk: String(saleVersion.sale_id) })
                                        }
                                    }"
                                >
                                    <a
                                        v-for="(url, $i) in saleVersion.oaf_url"
                                        :key="$i"
                                        :href="url"
                                        class="dropdown-menu__item"
                                        target="_blank"
                                    >
                                        OAF Step {{ $i + 1 }}
                                        <ExternalLinkIcon class="ml-2 text-gray-500 w-3 h-3" />
                                    </a>
                                </div>
                            </template>
                        </Dropdown>

                        <!-- Sale Type Button -->
                        <Dropdown
                            v-if="canEditSale"
                            teleport
                            placement="bottom-end"
                        >
                            <template #toggle="{ toggle }">
                                <button
                                    type="button"
                                    class="btn btn-outline-secondary box"
                                    @click="toggle"
                                >
                                    {{ saleType.title || 'Undefined' }}
                                    <ChevronDownIcon class="w-3 h-3 ml-2" />
                                </button>
                            </template>
                            <template #content="{ close }">
                                <div
                                    class="dropdown-menu__content my-1 box dark:bg-dark-1 border border-theme-38/20 p-2 select-none"
                                >
                                    <button
                                        v-for="item in saleTypes"
                                        :key="item.system_name"
                                        type="button"
                                        class="dropdown-menu__item w-full"
                                        :class="{
                                            'cursor-not-allowed': saleType.system_name === item.system_name
                                        }"
                                        :disabled="saleType.system_name === item.system_name"
                                        @click="() => {
                                            changeSaleType(item.system_name)
                                            close()
                                        }"
                                    >
                                        {{ item.title }}
                                    </button>
                                </div>
                            </template>
                        </Dropdown>
                        <button
                            v-else
                            disabled
                            class="btn btn-outline-secondary box cursor-not-allowed"
                        >
                            {{ saleType.title || 'Undefined' }}
                        </button>
                    </div>

                    <!-- Sale Activity Logs-->
                    <div v-if="canViewLogs">
                        <AppButton
                            class="h-[30px] dark:bg-dark-3 dark:text-gray-400 dark:border-gray-700"
                            @click="logsModal.open({ salePk: String(sale.id) })"
                        >
                            <ChevronsLeftIcon />
                            Show logs
                        </AppButton>
                    </div>

                    <!-- Agent Button -->

                    <div class="flex ml-auto items-center gap-2">
                        <AgentNameButton
                            v-if="sale.executor_id"
                            :agent-pk="String(sale.executor_id)"
                            :agent-name="members?.length >= 2 ? members?.map(member => shortFullName(member.info)).join(' / ') : null"
                            :members="members"
                            class="customer-bar-agent"
                            @click="agentButtonAction"
                        />

                        <!-- General Actions -->
                        <Dropdown
                            :class="{
                                'invisible': !actionsMenuOptions.length,
                            }"
                            teleport
                            placement="bottom-end"
                        >
                            <template #toggle="{ toggle }">
                                <button
                                    type="button"
                                    class="dropdown-toggle btn btn-outline-secondary ml-auto mr-1.5 px-2"
                                    @click="toggle"
                                >
                                    <MoreVerticalIcon class="w-4 h-4" />
                                </button>
                            </template>

                            <template #content="{ close }">
                                <ContextMenu
                                    class="my-1"
                                    :options="actionsMenuOptions"
                                    @close="close"
                                />
                            </template>
                        </Dropdown>
                    </div>
                </div>

                <!-- Passport -->
                <ClientPassportSection
                    v-if="sale.canViewClient"
                    v-show="showPassportInfo"
                    :client-pk="String(sale.client_id)"
                    is-compact-mode
                    class="h-full p-0 border-b"
                />

                <div class="text-2xs flex" :class="{ 'border-t dark:border-dark-5': !showPassportInfo }">
                    <!-- General Info -->
                    <div class="p-3 flex-[4] border-r">
                        <div class="flex gap-x-4">
                            <div class="data-list flex-1">
                                <div class="data-list__item">
                                    <span class="data-list__title">Lead</span>
                                    <Component
                                        :is="client && canOpenLeads ? 'RouterLink': 'div'"
                                        :to="routeToLead(sale.lead_id)"
                                        class="data-list__value text-primary-1"
                                    >
                                        {{ sale.lead_id }}
                                    </Component>
                                </div>
                                <div class="data-list__item">
                                    <span class="data-list__title">Cabin</span>
                                    <span v-copy class="data-list__value cursor-[copy]">
                                        {{ priceQuoteComputed?.lead?.itinerary_class }}
                                    </span>
                                </div>
                                <div class="data-list__item">
                                    <span class="data-list__title">Com PNR</span>
                                    <span v-copy class="data-list__value cursor-[copy]">
                                        {{ sale.external_id }}
                                    </span>
                                </div>
                            </div>
                            <div class="data-list flex-1">
                                <div class="data-list__item">
                                    <span class="data-list__title">VC</span>
                                    <span v-copy class="data-list__value cursor-[copy]">
                                        {{ vc }}
                                    </span>
                                </div>
                                <div class="data-list__item">
                                    <span class="data-list__title">From</span>
                                    <span v-copy class="data-list__value cursor-[copy]">
                                        {{ priceQuoteComputed?.fromIata?.code }}
                                    </span>
                                </div>
                                <div class="data-list__item">
                                    <span class="data-list__title">To</span>
                                    <span v-copy class="data-list__value cursor-[copy]">
                                        {{ priceQuoteComputed?.toIata?.code }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Dropdown-Button Actions -->
                        <div class="flex gap-x-3 mt-3">
                            <Dropdown teleport>
                                <template #toggle="{ toggle }">
                                    <div
                                        class="dropdown-button"
                                        @click="toggle"
                                    >
                                        <button
                                            v-tooltip="{content: 'Download', disabled: !canEditSaleSimple}"
                                            type="button"
                                            class="dropdown-button__button"
                                            :disabled="!canEditSaleSimple"
                                            @click.stop="download()"
                                        >
                                            <DownloadIcon />
                                        </button>
                                        <ChevronDownIcon class="dropdown-button__icon" />
                                    </div>
                                </template>
                                <template #content="{ close }">
                                    <ContextMenu
                                        v-if="canEditSaleSimple"
                                        class="my-1"
                                        :options="downloadOptions"
                                        @close="close"
                                    />
                                </template>
                            </Dropdown>

                            <Dropdown teleport>
                                <template #toggle="{ toggle }">
                                    <div
                                        class="dropdown-button"
                                        @click="toggle"
                                    >
                                        <button
                                            v-tooltip="{content: 'Send', disabled: !canEditSaleSimple}"
                                            type="button"
                                            :disabled="!canEditSaleSimple"
                                            class="dropdown-button__button"
                                            @click.stop="send()"
                                        >
                                            <SendIcon />
                                        </button>
                                        <ChevronDownIcon class="dropdown-button__icon" />
                                    </div>
                                </template>
                                <template #content="{ close }">
                                    <ContextMenu
                                        v-if="canEditSaleSimple"
                                        class="my-1"
                                        :options="sendOptions"
                                        @close="close"
                                    />
                                </template>
                            </Dropdown>

                            <Dropdown teleport>
                                <template #toggle="{ toggle }">
                                    <div
                                        class="dropdown-button"
                                        @click="toggle"
                                    >
                                        <button
                                            v-tooltip="{content: 'Create invoice', disabled: !canEditSaleSimple}"
                                            type="button"
                                            class="dropdown-button__button"
                                            :disabled="!canEditSaleSimple"
                                            @click.stop="createInvoice"
                                        >
                                            <FilePlusIcon />
                                        </button>
                                        <span
                                            class="mx-1 w-3 text-center dark:text-gray-300"
                                            :class="{
                                                'mr-0': invoicesList.records.length > 0,
                                            }"
                                        >{{ invoicesList.records.length }}</span>
                                        <ChevronDownIcon
                                            v-if="invoicesList.records.length"
                                            class="dropdown-button__icon"
                                        />
                                    </div>
                                </template>
                                <template #content="{ close }">
                                    <div
                                        v-if="canEditSaleSimple"
                                        class="dropdown-menu__content my-1 p-2 space-y-1 box dark:bg-dark-1"
                                    >
                                        <div v-if="!invoicesList.records.length" class="px-2 text-xs">
                                            No invoices
                                        </div>
                                        <div
                                            v-for="invoice in invoicesList.records"
                                            :key="invoice.id"
                                            class="flex justify-between"
                                        >
                                            <a
                                                v-tooltip="{ content: 'Download', placement: 'left' }"
                                                target="_blank"
                                                tabindex="0"
                                                class="dropdown-menu__item whitespace-nowrap mr-1"
                                                :href="invoice.url || 'javascript:void(0)'"
                                            >
                                                Invoice #{{ invoice.id }}

                                                <LoaderIcon
                                                    v-if="!invoice.file_id && !invoice.url"
                                                    class="animate-spin ml-2 w-3 h-3"
                                                />

                                                <span
                                                    v-else
                                                    class="ml-2"
                                                >
                                                    <svg
                                                        height="14"
                                                        width="14"
                                                        viewBox="0 0 24 24"
                                                        class="text-blue-500"
                                                        fill="none"
                                                    >
                                                        <path
                                                            d="m3.6 12.2l3.3 2.4 6.3-7.3 1.6 1.4-7.7 8.7-4.7-3.7zm9.5 1.9"
                                                            fill="currentColor"
                                                        />
                                                        <path
                                                            v-if="invoice.send_at"
                                                            d="m14.1 17.4l-2.2-1.8 1.2-1.6 0.8 0.6 6.3-7.3 1.6 1.4z"
                                                            fill="currentColor"
                                                        />
                                                    </svg>
                                                </span>
                                            </a>

                                            <button
                                                v-tooltip="{ content: 'Send email', placement: 'right' }"
                                                class="btn btn-sm btn-outline-secondary box ml-auto"
                                                @click.stop.prevent="() => {
                                                    close()
                                                    sendInvoice(invoice.id)
                                                }"
                                            >
                                                <SendIcon />
                                            </button>
                                        </div>
                                    </div>
                                </template>
                            </Dropdown>

                            <SaleSummaryVouchers v-if="hasPermission('create','Voucher')" :sale-pk="salePk" />
                        </div>
                    </div>

                    <!-- Product + Ticket -->
                    <div class="p-3 flex-[4] border-r">
                        <div class="flex gap-x-4">
                            <div class="data-list flex-1">
                                <div class="data-list__item">
                                    <span class="data-list__title">Prod. net</span>
                                    <span v-copy.number class="data-list__value cursor-[copy]">
                                        {{ Number.formatMoney(saleSummaryData.incentive.netPrice) }}
                                    </span>
                                </div>
                                <div class="data-list__item">
                                    <span class="data-list__title">Prod. sell</span>
                                    <span v-copy.number class="data-list__value cursor-[copy]">
                                        {{ Number.formatMoney(saleSummaryData.incentive.sellPrice) }}
                                    </span>
                                </div>
                                <div class="data-list__item">
                                    <span class="data-list__title">Prod. CK</span>
                                    <span v-copy.number class="data-list__value cursor-[copy]">
                                        {{ Number.formatMoney(saleSummaryData.incentive.checkPayment) }}
                                    </span>
                                </div>
                                <div class="data-list__item data-list__item--blue">
                                    <span class="data-list__title">Prod. profit</span>
                                    <span v-copy.number class="data-list__value cursor-[copy]">
                                        {{ Number.formatMoney(saleSummaryData.incentive.profit) }}
                                    </span>
                                </div>
                            </div>
                            <div class="data-list flex-1">
                                <div class="data-list__item">
                                    <span class="data-list__title">TKT. net</span>
                                    <span v-copy.number class="data-list__value cursor-[copy]">
                                        {{ Number.formatMoney(saleSummaryData.ticket.netPrice) }}
                                    </span>
                                </div>
                                <div class="data-list__item">
                                    <span class="data-list__title">TKT. sell</span>
                                    <span v-copy.number class="data-list__value cursor-[copy]">
                                        {{ Number.formatMoney(saleSummaryData.ticket.sellPrice) }}
                                    </span>
                                </div>
                                <div class="data-list__item">
                                    <span class="data-list__title">TKT. CK</span>
                                    <span v-copy.number class="data-list__value cursor-[copy]">
                                        {{ Number.formatMoney(saleSummaryData.ticket.checkPayment) }}
                                    </span>
                                </div>
                                <div class="data-list__item data-list__item--blue">
                                    <span class="data-list__title">TKT. profit</span>
                                    <span v-copy.number class="data-list__value cursor-[copy]">
                                        {{ Number.formatMoney(saleSummaryData.ticket.profit) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Profit -->
                    <div class="p-3 flex-[2] border-r">
                        <div class="flex gap-x-4">
                            <div class="data-list flex-1">
                                <div class="data-list__item">
                                    <span class="data-list__title">CK</span>
                                    <span v-copy.number class="data-list__value cursor-[copy]">
                                        {{ Number.formatMoney(saleSummaryData.total.checkPayment) }}
                                    </span>
                                </div>
                                <div class="data-list__item">
                                    <span class="data-list__title">Iss. fee</span>
                                    <span v-copy.number class="data-list__value cursor-[copy]">
                                        {{ Number.formatMoney(saleSummaryData.total.issuingFee) }}
                                    </span>
                                </div>
                                <div class="data-list__item">
                                    <span class="data-list__title">Comm.</span>
                                    <span v-copy.number class="data-list__value cursor-[copy]">
                                        {{ Number.formatMoney(saleSummaryData.total.commission) }}
                                    </span>
                                </div>
                                <div class="data-list__item data-list__item--green">
                                    <span class="data-list__title">Sale profit</span>
                                    <span
                                        v-copy.number
                                        class="data-list__value cursor-[copy]"
                                        :class="{
                                            'text-theme-20': Number(saleSummaryData.total.profit) > 0,
                                            'text-red-500': Number(saleSummaryData.total.profit) < 0
                                        }"
                                    >
                                        {{ Number.formatMoney(saleSummaryData.total.profit) }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div
                            v-if="!!sale.is_test"
                            class="bg-danger rounded text-2xs text-white font-semibold px-1.5 leading-normal self-center mt-1 w-full text-center"
                        >
                            Test Sale
                        </div>
                    </div>

                    <!-- Debt -->
                    <div class="p-3 flex-[3]">
                        <div class="data-list flex-1">
                            <div class="data-list__item">
                                <span class="data-list__title">To be paid</span>
                                <span v-copy.number class="data-list__value cursor-[copy]">
                                    {{ Number.formatMoney(saleSummaryData.sellPrice) }}
                                </span>
                            </div>
                            <div class="data-list__item">
                                <span class="data-list__title">Com charge</span>
                                <span v-copy.number class="data-list__value cursor-[copy]">
                                    {{ Number.formatMoney(saleSummaryData.inhouseCharge) }}
                                </span>
                            </div>
                            <div class="data-list__item data-list__item--red">
                                <span class="data-list__title">Debt</span>
                                <span
                                    v-copy.number
                                    class="data-list__value cursor-[copy]"
                                    :class="{
                                        'text-green-500': saleSummaryData.debt === 0,
                                        'text-red-500': saleSummaryData.debt !== 0,
                                    }"
                                >
                                    {{ Number.formatMoney(saleSummaryData.debt) }}
                                </span>
                            </div>
                        </div>
                        <div class="mt-3 flex gap-x-2">
                            <InputLayoutWrapper v-model="chargeAmount">
                                <FormInputNumberMask
                                    :readonly="!canEditSale"
                                    class="form-control form-control-sm"
                                    @enter-value="addPaidAmount"
                                />
                            </InputLayoutWrapper>

                            <button
                                :disabled="!canEditSale"
                                class="btn btn-sm btn-outline-secondary box"
                                @click="addPaidAmount"
                            >
                                <PlusIcon class="w-3.5 h-3.5 !stroke-2 mr-1.5" />
                                Charge
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer Actions -->
            <div
                v-if="needApprove && canEditSale"
                class="bg-white dark:bg-dark-3 border-t flex items-center justify-end p-2 rounded-none rounded-b-md dark:border-dark-5"
            >
                <button
                    class="btn btn-sm btn-outline-secondary box ml-auto"
                    @click="manualApprove"
                >
                    Manual approve
                </button>
                <button
                    class="btn btn-sm btn-success ml-2"
                    @click="sendNewForm"
                >
                    Send New Form
                </button>
            </div>
        </div>

        <template #fallback>
            <PlaceholderBlock class="h-[253px]" />
        </template>
    </SuspenseManual>
</template>

<script lang="ts">
import ChildMixin from '@/lib/mixin/ComponentRelation/ChildMixin'
import ParentMixin from '@/lib/mixin/ComponentRelation/ParentMixin' // Types
import type SaleHelper from '@/lib/core/helper/Sale/SaleHelper'
import type AgentFields from '@/api/active-record/Agent'
import type CrudList from '@/lib/core/crud/list/CrudList' // Parts
import ClientPassportSection from '~/sections/Client/sections/ClientPassport/ClientPassportSection.vue'
import SaleSummaryDatepickerDropdownContent
    from '@/views/sales/sale/blocks/SaleSummary/Parts/SaleSummaryDatepickerDropdownContent.vue' // Utils
import { defineComponent, inject, markRaw, type PropType } from 'vue'
import { dataURItoBlobPdf } from '@/lib/core/helper/FileHelper'
import { shortFullName } from '@/lib/core/helper/AvatarHelper' // Icons
import {
    ChevronDownIcon,
    DownloadIcon,
    ExternalLinkIcon,
    FilePlusIcon,
    LoaderIcon,
    MailIcon,
    MoreVerticalIcon,
    PhoneIcon,
    PlusIcon,
    PlusSquareIcon,
    RotateCcwIcon,
    SendIcon,
    Trash2Icon,
} from '@zhuowenli/vue-feather-icons'
import ArrowsUpDownIcon from '~/assets/icons/SplitIcon.svg?component' // components
import Tabs from '@/components/tabs/Tabs.vue'
import Dropdown from '~/components/Dropdown.vue'
import SuspenseManual from '@/components/SuspenseManual/SuspenseManual.vue'
import InputNumberField from '@/lib/FormField/InputNumberField'
import ContextMenu from '@/components/ContextMenu/ContextMenu.vue'
import InputLayoutWrapper from '@/components/Form/FormWrappers/InputLayoutWrapper.vue'
import FormInputNumberMask from '@/components/Form/FormField/FormInputNumberMask.vue' // Models and CrudList
import SaleSendNewModal from '@/views/sales/sale/modals/SaleSendNewModal.vue'
import SaleCreateInvoiceModal from '@/views/sales/sale/modals/SaleCreateInvoiceModal.vue'
import SaleSendTicketModal from '@/views/sales/sale/modals/SaleSendTicketModal.vue'
import SaleSendInvoiceModal from '@/views/sales/sale/modals/SaleSendInvoiceModal.vue'
import CreateSaleRequestHelpModal from '~/modals/Sale/CreateSaleRequestHelpModal.vue'
import ReactiveHelper from '@/lib/core/helper/ReactiveHelper'
import ArrayHelper from '@/lib/core/helper/ArrayHelper'
import SaleGroupModal from '@/components/Modals/sale/SaleGroupModal.vue'
import SaleSplitModal from '@/views/sales/sale/modals/SaleSplitModal.vue'
import SaleShareModal from '@/views/sales/sale/modals/SaleShareModal.vue'
import SaleShareExpertGp from '@/views/sales/sale/modals/SaleShareExpertGp.vue'
import AgentNameButton from '~/components/Agent/AgentNameButton.vue'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import { getClientFullName } from '~/lib/Helper/PersonHelper'
import { saleTypes } from '@/api/models/Sale/SaleListModel'
import { $confirm } from '@/plugins/ConfirmPlugin'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import { SaleType } from '~/api/models/Sale/Sale'
import GetContactModal from '~/sections/ContactsStock/modals/GetContactModal.vue'
import { DepartmentName } from '~/api/models/Department/Department'
import { unique } from '~/lib/Helper/ArrayHelper'
import SaleLogsSideModal from '~/sections/Sale/modals/SaleLogsSideModal.vue'
import { SuspiciousAction, useSuspiciousActivityTracker } from '~/sections/Lead/composable/useSuspiciousActivityTracker'
import ChangeSaleTypeModal from '~/sections/Sale/modals/ChangeSaleTypeModal.vue'
import ClientPhoneDataDropdown from '~/components/ClientPhoneData/ClientPhoneDataDropdown.vue'
import ClientEmailDataDropdown from '~/components/ClientEmailData/ClientEmailDataDropdown.vue'
import { goToVoucherList, linkToVoucherList } from '@/lib/core/helper/RouteNavigationHelper'
import SaleSummaryVouchers from '~/sections/Sale/components/SaleSummaryVouchers.vue'
import PromptModal from '~/modals/PromptModal.vue'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import { PositionName } from '~/api/models/Position/Position'
import CreateSettlementModal from '~/modals/Sale/CreateSettlementModal.vue'
import AwardAccountQuickAltVpnModal from '~/sections/AwardAccount/modals/AwardAccountQuickAltVpnModal.vue'

export default defineComponent({
    name: 'SaleSummaryBlock',

    parentName: ['SaleComponent'],

    components: {
        SaleSummaryVouchers,
        ClientEmailDataDropdown,
        ClientPhoneDataDropdown,
        ClientPassportSection,
        AgentNameButton,
        PlaceholderBlock,
        FormInputNumberMask,
        InputLayoutWrapper,
        ContextMenu,
        Tabs,
        Dropdown,
        SuspenseManual,

        // Icons
        MailIcon,
        ChevronDownIcon,
        ExternalLinkIcon,
        MoreVerticalIcon,
        DownloadIcon,
        SendIcon,
        FilePlusIcon,
        LoaderIcon,
        PlusIcon,
        PhoneIcon,

        // Parts
        SaleSummaryDatepickerDropdownContent,
    },

    mixins: [ParentMixin, ChildMixin],

    props: {
        salePk: {
            type: String as PropType<PrimaryKey>,
            required: true,
        },
    },

    async setup(props) {
        const {
            useModel,
            useCurrentUser,
            hasPermission,
            useDictionary,
        } = useContext()

        const projectDictionary = useDictionary('Project')
        const showConsultantTool = config.productConsultantTool.enabled
        const saleTypeDictionary = useGeneralDictionary('SaleType')
        const currentUser = useCurrentUser()

        const shareSaleModal = useModal(SaleShareModal)
        const shareExpertGp = useModal(SaleShareExpertGp)
        const saleHelper = inject<SaleHelper>('saleHelper')
        const getContactModal = useModal(GetContactModal)
        const logsModal = useModal(SaleLogsSideModal)
        const SaleVersionModel = useModel('SaleVersion')

        const changeSaleTypeModal = useModal(ChangeSaleTypeModal)

        const {
            record: client,
            fetch: fetchClient,
            loading: clientLoading,
        } = useModel('ClientPreview').useRecord().destructable()

        const {
            record: saleRecord,
            fetch: fetchSale,
        } = useModel('Sale').useRecord().destructable()

        // Suspicious Activity Monitoring

        const suspiciousActivityTracker = useSuspiciousActivityTracker()

        const activityMonitoringIsActive = computed(() => {
            const closedBy = saleRecord.value.closed_by_pk

            if (closedBy) {
                const closedByUser = useDictionary('Agent').find(closedBy)

                if (isUserInDepartment(closedByUser, DepartmentName.Bookkeeping)) {
                    return false
                }
            }

            return !saleHelper?.isSaleClosed &&
                (
                    isUserInDepartment(currentUser, DepartmentName.TicketingAward) ||
                    isUserInDepartment(currentUser, DepartmentName.TicketingRevenue) ||
                    isUserInDepartment(currentUser, DepartmentName.CustomerSupport) ||
                    isUserInDepartment(currentUser, DepartmentName.Verification) ||
                    isUserExpertManager.value
                )
        })

        const trackSuspiciousAction: typeof suspiciousActivityTracker['trackSuspiciousAction'] = (action, data) => {
            if (activityMonitoringIsActive.value) {
                suspiciousActivityTracker.trackSuspiciousAction(action, data)
            }
        }

        async function sendPriceDropStagingTest() {
            await useModel('Sale').actions.sendPriceDropStagingTest({
                pk: String(saleHelper?.saleId),
            })

            toastSuccess('Price Drop check is sent.')
        }

        //

        const promptModal = useModal(PromptModal)

        //

        const isUserExpertManager = computed(() => {
            return isUserInDepartment(currentUser, DepartmentName.Experts) && isUserHasPosition(currentUser, PositionName.Manager)
        })

        await fetchSale(props.salePk)

        const createSettlementModal = useModal(CreateSettlementModal)
        const requestExpertHelpModal = useModal(CreateSaleRequestHelpModal)

        function openRequestExpertHelpModal() {
            requestExpertHelpModal.open({
                salePk: props.salePk,
            })
        }

        return {
            hasPermission,
            SaleVersionModel,
            saleHelper,
            fetchClient,
            client,
            clientLoading,
            currentUser,
            shareSaleModal,
            shareExpertGp,
            getContactModal,
            logsModal,
            trackSuspiciousAction,
            isUserExpertManager,
            activityMonitoringIsActive,
            changeSaleTypeModal,
            async cloneSale(type: SaleType) {
                // no multy mode
                const an = type === SaleType.Refund ? 'a' : 'an'

                await $confirm({
                    text: `Are you sure you want to create ${an} ${(saleTypeDictionary.find(type)).title}?`,
                    confirmButton: 'Create',
                })

                await changeSaleTypeModal.open({
                    salePk: String(saleHelper?.saleId),
                    mode: 'clone',
                    type,
                })

                toastSuccess(`Refund sale will be created soon`)

                // goToSale(sale_pk)
            },

            showConsultantTool,
            sendPriceDropStagingTest,
            promptModal,
            createSettlementModal,
            projectDictionary,
            openRequestExpertHelpModal,
        }
    },

    data() {
        const clientStatuses = this.$models.ClientStatusModel.getAll()

        return {
            clientStatuses,
            showPassportInfo: false,
            saleTypes,

            chargeAmount: new InputNumberField(false, 'Charge Amount'),

            shouldShowDatepickerCreated: false,
            agents: null,
        }
    },

    computed: {
        SuspiciousAction() {
            return SuspiciousAction
        },

        sale() {
            return this.saleHelper?.saleModel
        },

        saleVersion() {
            return this.saleHelper.saleVersion
        },

        saleVersions() {
            return this.saleHelper.saleVersions.records
        },

        saleVersionsList(): CrudList {
            return this.saleHelper.saleVersions
        },

        saleType() {
            return this.saleTypes.find(item => item.system_name === this.sale.type)
        },

        lastApprovedBy(): string {
            return this.saleVersion?.origin_approved_by
        },

        oafFormName() {
            const formType = this.lastApprovedBy?.toLowerCase()

            return formType === 'agent' ? 'Manual OAF'
                : formType === 'client' ? 'Pax OAF'
                    : 'OAF Form'
        },

        loading() {
            return !this.sale?.id || !this.saleVersion?.id || !this.agents?.length || this.clientLoading
        },

        tabs() {
            return this.saleVersions.map(version => ({
                id: version.id,
                saleVersion: version,
            }))
        },

        agent(): AgentFields {
            return this.agents ? this.agents.find(agent => agent.id === this.sale.executor_id) : null
        },

        members() {
            return this.sale.members ? this.sale.members.map((member) => {
                return {
                    ...member,
                    info: this.agents.find(agent => agent.id === member.agent_id),
                }
            }).sort((a, b) => {
                if (a.agent_id === this.sale.executor_id) return -1
                else if (b.agent_id === this.sale.executor_id) return 1
            }) : null
        },

        actionsMenuOptions() {
            const canViewPriceDrops = this.$can('openPageMarkup', 'all')

            const canDelete = this.canDeleteSale
            const isClosed = this.saleHelper.isSaleClosed
            const isDeleted = this.saleHelper.isSaleDeleted
            const canViewCompanyContact = this.$can('viewCompanyContact', 'all')

            if (isDeleted) {
                if (!canDelete) {
                    return []
                }

                return [
                    {
                        text: 'Recover sale',
                        icon: markRaw(RotateCcwIcon),
                        class: 'text-primary-1',
                        onClick: () => this.recoverSale(),
                    },
                ]
            }

            return [
                (canDelete) ? {
                    text: 'Add to group',
                    icon: markRaw(PlusIcon),
                    class: 'text-primary-1',
                    onClick: () => this.addSaleToGroup(),
                } : null,
                (canDelete) ? {
                    text: 'Create group',
                    icon: markRaw(PlusSquareIcon),
                    class: 'text-primary-1',
                    onClick: () => this.createSaleGroup(),
                } : null,
                (this.$can('clone', 'Sale')) ? {
                    text: 'Create refund',
                    icon: markRaw(RotateCcwIcon),
                    class: 'text-primary-1',
                    onClick: () => this.cloneSale(SaleType.Refund),
                } : null,
                (this.$can('clone', 'Sale')) ? {
                    text: 'Create exchange',
                    icon: markRaw(RotateCcwIcon),
                    class: 'text-primary-1',
                    onClick: () => this.cloneSale(SaleType.Exchange),
                } : null,
                (this.$can('clone', 'Sale')) ? {
                    text: 'Create Inv. Exchange',
                    icon: markRaw(RotateCcwIcon),
                    class: 'text-primary-1',
                    onClick: () => this.cloneSale(SaleType.InvoiceExchange),
                } : null,
                (this.$can('clone', 'Sale')) ? {
                    text: 'Create Inv. Refund',
                    icon: markRaw(RotateCcwIcon),
                    class: 'text-primary-1',
                    onClick: () => this.cloneSale(SaleType.InvoiceRefund),
                } : null,
                (canDelete && !isClosed) ? {
                    text: 'Delete sale',
                    icon: markRaw(Trash2Icon),
                    class: 'text-red-500',
                    onClick: () => this.deleteSale(),
                } : null,
                (this.canSplitSale) ? {
                    text: 'Split sale',
                    icon: markRaw(ArrowsUpDownIcon),
                    class: 'text-theme-20',
                    onClick: () => this.splitSale(),
                } : null,
                (this.canEditSale && !this.canSplitSale) ? {
                    text: 'Share sale',
                    icon: markRaw(ArrowsUpDownIcon),
                    class: 'text-theme-20',
                    onClick: () => this.shareSale(),
                } : null,
                (this.canManageAll || this.canViewExpertGp) ? {
                    text: 'Split Experts GP',
                    icon: markRaw(ArrowsUpDownIcon),
                    class: 'text-theme-20',
                    onClick: () => this.shareGp(),
                } : null,
                (canViewCompanyContact) ? {
                    text: 'Get Contacts',
                    icon: markRaw(FramerIcon),
                    class: 'text-primary-1',
                    onClick: () => this.getContacts(),
                } : null,
                (this.showConsultantTool && canViewPriceDrops) ? {
                    text: 'Check price drop',
                    icon: markRaw(ActivityIcon),
                    class: 'text-primary-1',
                    onClick: () => this.sendPriceDropStagingTest(),
                } : null,
                (this.$can('createQuickVpn', 'all')) ? {
                    text: 'Quick alt. vpn',
                    icon: markRaw(CloudLightningIcon),
                    class: 'text-primary-1',
                    onClick: () => this.$showModal(AwardAccountQuickAltVpnModal),
                } : null,
            ].filter(Boolean)
        },

        downloadOptions() {
            return [
                {
                    text: 'Ticket',
                    onClick: () => this.download(),
                },
                {
                    text: 'Ticket Protection',
                    onClick: this.hasInsurance ? () => this.download('ticketProtection') : null,
                    class: !this.hasInsurance ? 'opacity-50 cursor-not-allowed' : '',
                    tooltip: !this.hasInsurance ? 'The ticket insurance is not available' : null,
                },
                this.hasPaidBaggageProtection ? {
                    text: 'Baggage Protection',
                    onClick: () => this.download('baggageProtection'),
                } : undefined,
            ].filter(Boolean)
        },

        sendOptions() {
            const options = [
                {
                    text: 'Ticket',
                    onClick: () => this.send(),
                },
                {
                    text: 'Ticket Protection',
                    onClick: this.hasInsurance ? () => this.send('ticket-protection') : null,
                    class: !this.hasInsurance ? 'opacity-50 cursor-not-allowed' : '',
                    tooltip: !this.hasInsurance ? 'The ticket insurance is not available' : null,
                },
            ]

            if (this.hasPaidBaggageProtection) {
                options.push({
                    text: 'Baggage Protection',
                    onClick: () => this.send('baggage-protection'),
                })
            }

            if (
                [SaleType.Refund, SaleType.InvoiceRefund].includes(this.sale.type)
                && this.hasPermission('create', 'SignedDocument')
                && this.projectDictionary.isTBCProject(String(this.sale.project_id))
            ) {
                options.push({
                    text: 'Create Settlement',
                    onClick: () => this.createSettlementModal.open({
                        saleVersionPk: String(this.saleVersion.id),
                    }),
                })
            }

            return options
        },

        invoicesList() {
            return this.saleHelper.clientInvoices
        },

        hasInsurance() {
            return this.saleHelper.hasInsurance
        },

        hasPaidBaggageProtection() {
            return this.saleHelper.hasPaidBaggageProtection
        },

        needApprove() {
            return this.saleVersion.need_approve && !this.saleHelper.isSaleClosed && !this.saleHelper.isSaleAdjusted
        },

        canEditSale() {
            // @ts-ignore
            return this.$can('edit', 'Sale', this.sale) && !this.saleHelper.isSaleClosed && !this.saleHelper.isSaleAdjusted
        },

        canSplitSale() {
            return this.canEditSale && this.canManageSale
        },

        canViewExpertGp() {
            return this.currentUser && isUserInDepartment(this.currentUser, DepartmentName.Experts)
        },

        canViewLogs() {
            return this.$can('viewLogs', 'Sale', this.sale)
        },

        canOpenLeads() {
            return this.$can('openPage', 'Lead', this.currentUser) // @todo Check this. Maybe it is a crutch, but possibly wrong object passed.
        },

        canEditSaleSimple() {
            // @ts-ignore
            return this.$can('edit', 'Sale', this.sale)
        },

        canManageSale() {
            // @ts-ignore
            return this.$can('manage', 'Sale', this.sale)
        },

        canChangeSaleDate() {
            // @ts-ignore
            return this.$can('changeSaleDate', 'Sale', this.sale)
        },

        canManageAll() {
            // @ts-ignore
            return this.$can('manage', 'all')
        },

        canDeleteSale() {
            // @ts-ignore
            return this.$can('delete', 'Sale', this.sale)
        },

        canSetAgent() {
            // @ts-ignore
            return this.$can('setAppointment', 'Sale', this.sale)
        },

        priceQuoteComputed() {
            return this.saleHelper.priceQuote
        },

        ticketsComputed() {
            return this.saleHelper.tickets.records
        },

        vc() {
            return this.ticketsComputed.map(e => e.validatingCarrier.code)
                .filter(ArrayHelper.getUnique)
                .join(',')
        },

        saleSummaryData() {
            return this.saleHelper.saleSummaryData
        },

        isSaleSplit() {
            return this.sale.members.length > 1
        },
    },

    watch: {
        'sale.client_id': {
            immediate: true,
            handler(client_id) {
                if (client_id) {
                    this.fetchClient(String(client_id))
                }
            },
        },
    },

    created() {
        this.$models.AgentModel.getAllListForRC().then(result => {
            this.agents = result
        })
    },

    methods: {
        linkToVoucherList,
        goToVoucherList,
        getClientFullName,
        shortFullName,

        addSaleToGroup() {
            this.$showModal(SaleGroupModal, {}, {
                workspaceProject: this.sale.project_id,
                mode: 'join',
                saleHelper: this.saleHelper,
            })
        },

        createSaleGroup() {
            this.$showModal(SaleGroupModal, {}, {
                workspaceProject: this.sale.project_id,
                mode: 'create',
                saleHelper: this.saleHelper,
            })
        },

        splitSale() {
            this.$showModal(SaleSplitModal, {}, {
                workspaceProject: this.sale.project_id,
                saleHelper: this.saleHelper,
                readonly: this.saleHelper.isSaleDeleted || (this.isSaleSplit && !this.canManageSale),
            })
        },

        shareSale() {
            this.shareSaleModal.open({
                salePk: String(this.saleHelper?.saleId),
            })
        },

        shareGp() {
            if (this.sale) {
                this.shareExpertGp.open({
                    salePk: String(this.saleHelper?.saleId),
                    saleExecutorPk: String(this.sale.executor_id),
                    totalProfit: this.sale.summary.ticketProfit,
                    saleIsHidden: Boolean(this.sale.is_hidden),
                    canSetAgent: this.canSetAgent,
                })
            }
        },

        getContacts() {
            const validTickets = this.saleHelper.tickets.records.filter(ticket => !!ticket.validating_carrier_id)

            const carrier_pks = validTickets.map(ticket => String(ticket.validating_carrier_id))

            const additional_airline_pks = []

            for (const ticket of validTickets) {
                if (ticket.airlinePnrs.length) {
                    for (const airline of ticket.airlinePnrs) {
                        additional_airline_pks.push(String(airline.airline_id))
                    }
                }
            }
            const validating_carriers = [...carrier_pks, ...additional_airline_pks]
            const airline_pks = unique(validating_carriers)

            this.getContactModal.open({
                salePk: String(this.saleHelper?.saleId),
                usedAirlinePks: airline_pks,
            })
        },

        changeSaleVersion(id) {
            this.saleHelper.saleVersion = this.saleVersions.find(version => version.id === id)
        },

        async changeSaleType(type) {
            if (type === 'sale') {
                this.$models.SalePageModel.changeSaleType(this.sale.id, type)
            } else {
                // multy mode
                await this.changeSaleTypeModal.open({
                    salePk: String(this.saleHelper?.saleId),
                    type,
                })
            }
        },

        changeSaleClientStatus(status) {
            const prev_name = this.sale.clientStatus.name
            const next_name = status.label
            this.$models.SalePageModel.changeSaleClientStatus(this.sale.id, status.value).then(() => {
                this.toastSuccess(`Sale Client status changed from ${prev_name} to ${next_name}`)
            }).catch(apiError => {
                apiError.processWithMessage()
            })
        },

        assignAgent() {
            // Todo: Refactor Set agent modal
            if (this.assertSaleIsNotAdjusted()) {
                return
            }

            if (!this.canSetAgent) {
                return
            }

            // @ts-ignore
            this.$related.SaleComponent.showAgentAssignModal(this.sale.executor_id, false)
        },

        async deleteSale() {
            if (this.assertSaleIsNotAdjusted()) {
                return
            }

            const deleteReason = await this.promptModal.open({
                title: 'Please enter the reason for sale deletion',
                placeholder: 'Delete reason',
                required: true,
                inputAttrs: {
                    maxLength: 1000,
                    minLength: 6,
                },
                validationRules: [
                    ValidationRules.MinLength(6, 'Reason should be more than {value} symbols'),
                ],
            })

            if (deleteReason) {
                this.$models.SaleVersionModel.deleteSaleVersion(this.saleVersion.id, deleteReason)
                    .then(() => {
                        this.toastWarning('Sale deleted')
                        this.$router.push({ name: 'sales' })
                    })
            }
        },

        async recoverSale() {
            if (this.assertSaleIsNotAdjusted()) {
                return
            }

            const recoverReason = await this.promptModal.open({
                title: 'Please enter the reason for sale recovery',
                placeholder: 'Recover reason',
                required: true,
                inputAttrs: { maxLength: 150 },
            })

            if (recoverReason) {
                this.$models.SaleVersionModel.recoverSaleVersion(this.saleVersion.id, recoverReason)
                    .then(apiResponse => {
                        ReactiveHelper.apply(this.sale, apiResponse.result)
                        this.toastSuccess('Sale recovered')
                    })
            }
        },

        download(type = 'sale') {
            this.$models.SaleVersionModel.download(this.saleVersion.id, type)
                .then(apiResponse => {
                    const blob = dataURItoBlobPdf(apiResponse.result)
                    const url = URL.createObjectURL(blob)
                    // to open the PDF in a new window
                    window.open(url, '_blank')
                })
                .catch(apiError => {
                    apiError.processWithMessage('File not found')
                })
        },

        async send(tab = 'ticket') {
            if (this.saleSummaryData.debt !== 0 && tab == 'ticket') {
                await $confirm({ text: 'The full amount has not been charged. Are you sure you want to send tickets to the customer without payment?' })
            }

            this.$showModal(SaleSendTicketModal, {}, {
                workspaceProject: this.sale.project_id,
                saleHelper: this.saleHelper,
                tabOnOpening: tab,
            })
        },

        createInvoice() {
            this.$showModal(SaleCreateInvoiceModal, {}, {
                saleHelper: this.saleHelper,
            })
        },

        sendInvoice(invoiceId) {
            this.$showModal(SaleSendInvoiceModal, {}, {
                invoiceId,
                saleHelper: this.saleHelper,
            })
        },

        assertSaleIsNotAdjusted() {
            if (this.saleHelper.isSaleAdjusted) {
                this.toastError('Sale is adjusted!')

                return true
            }

            return false
        },

        addPaidAmount() {
            if (this.assertSaleIsNotAdjusted()) {
                return
            }

            if (!this.chargeAmount.value) {
                return
            }

            this.$models.SalePageModel.addPaidAmount(this.sale.id, this.chargeAmount.value)
                .then(({ result }) => {
                    ReactiveHelper.apply(this.sale, result)
                    this.chargeAmount.value = null
                })
        },

        manualApprove() {
            this.$confirm({
                text: 'Please confirm manual approving',
                confirmButton: 'Confirm',
                cancelButton: 'Cancel',
            }, {
                workspaceProject: this.sale.project_id,
            }).then(this.approve)
        },

        approve() {
            this.$models.SaleVersionModel.manualApprove(this.saleVersion.id)
                .then(apiResponse => {
                    this.saleVersionsList.updateRecord(apiResponse.result)
                    this.toastSuccess('Sale Successfully Approved')
                }).catch(apiError => {
                    apiError.processed = true
                    this.toastError('Something went wrong. Please try again.')
                })
        },

        sendNewForm() {
            if (this.assertSaleIsNotAdjusted()) {
                return
            }

            this.$showModal(SaleSendNewModal, {}, {
                saleHelper: this.saleHelper,
            })
        },

        agentButtonAction() {
            if (this.members.length >= 2) {
                this.splitSale()

                return
            }

            this.assignAgent()
        },
    },
})
</script>
