<template>
    <div
        :id="`ticket-${ticket?.id}`"
        :class="getTicketColor('border', 100)"
        class="sales-ticket relative"
    >
        <div
            :class="getTicketColor('bg', 100)"
            class="sales-ticket-header flex justify-between gap-x-2 !py-2.5"
        >
            <!--            default               -->
            <div class="absolute top-[-10px] left-[-8px] badge --xs --secondary">
                ID: {{ ticket?.id }}
            </div>
            <!--            1               -->
            <div class="sales-ticket-cell border-none p-0 !max-w-[90px]">
                <div class="sales-ticket-label">
                    GDS PNR
                </div>
                <div class="sales-ticket-value relative">
                    <div v-if="editTickets === null" class="flex gap-2 items-center justify-between">
                        <span @click.right.prevent="openPnrContextMenu($event, ticket?.product?.consolidator_order_id)">
                            {{
                                !!ticket?.product?.consolidator_order_id ? ticket?.product?.consolidator_order_id : '&nbsp;'
                            }}
                        </span>

                        <div v-if="ticket.product?.consolidator_order_id" class="absolute flex items-center gap-1 top-[-18px] left-13">
                            <button
                                v-tooltip="'Check PNR'"
                                class="box rounded p-0.5 text-green-500 flex-none h-[15px] w-[15px] flex items-center justify-around"
                                @click="checkPnr"
                            >
                                <ArrowRightLeft class="w-3 h-3 !stroke-2" />
                            </button>
                            <AlertCircleIcon v-tooltip="{ content: consolidatorPccs }" class="h-[15px] w-[15px]" />
                        </div>
                    </div>
                    <div v-else>
                        <InputLayoutWrapper
                            v-model="form.field.product.field.consolidator_order_id"
                            class="flex-grow"
                        >
                            <FormInput
                                v-autowidth="{
                                    minWidth: '100%',
                                    maxWidth: '100%',
                                }"
                                class="inline-edit h-[20px]"
                            />
                        </InputLayoutWrapper>
                    </div>
                </div>
            </div>
            <!--            2               -->
            <div class="sales-ticket-cell border-none p-0 max-w-[70px]">
                <div class="sales-ticket-label">
                    Carrier
                </div>
                <div class="sales-ticket-value">
                    <span
                        v-if="editTickets === null"
                    >
                        {{ !!ticket?.validatingCarrier?.code ? ticket?.validatingCarrier?.code : '&nbsp;' }}
                        <span
                            v-if="!!airlineCodes"
                            v-tooltip="{content: airlineCodes}"
                            class="text-2xs leading-none ml-1 cursor-pointer"
                            @dblclick="copyToClipboard(airlineCodes)"
                        >
                            +{{ airlineCodes.toString().split(',').length }}
                        </span>
                    </span>
                    <InputLayoutWrapper
                        v-else
                        v-model="form.field.validating_carrier_id"
                        label-type="code"
                    >
                        <FormSelect
                            :class="{
                                'border-theme-21': form.field.validating_carrier_id.errors.length
                            }"
                            class="form-multiselect-inline h-[22px]"
                        />
                    </InputLayoutWrapper>
                </div>
            </div>
            <!--            3               -->
            <div class="sales-ticket-group">
                <div class="sales-ticket-row border-none flex gap-x-1">
                    <div class="sales-ticket-cell border-none p-0">
                        <div class="sales-ticket-label">
                            Air PNR
                        </div>
                        <div class="sales-ticket-value">
                            <span
                                v-if="editTickets === null"
                            >
                                {{ !!ticket?.validating_carrier_pnr ? ticket?.validating_carrier_pnr : '&nbsp;' }}
                                <span
                                    v-if="!!airlinePnrs"
                                    v-tooltip="{content: airlinePnrs}"
                                    class="text-2xs leading-none ml-1 cursor-pointer"
                                    @dblclick="copyToClipboard(airlinePnrs)"
                                >
                                    +{{ airlinePnrs.toString().split(',').length }}
                                </span>
                            </span>
                            <InputLayoutWrapper
                                v-else
                                v-model="form.field.validating_carrier_pnr"
                            >
                                <FormInput
                                    v-autowidth="{
                                        minWidth: '100%',
                                        maxWidth: '100%',
                                    }"
                                    :readonly="form.field.product.field.consolidator_order_id.value.length === 0"
                                    class="inline-edit h-[22px]"
                                />
                            </InputLayoutWrapper>
                        </div>
                    </div>
                    <div class="sales-ticket-cell flex-row pb-px min-w-[24px] max-w-[24px] !p-0 mt-auto pr-4">
                        <button
                            v-if="editTickets !== null"

                            :class="{
                                'text-green-500': form.field.product.field.consolidator_order_id.value.length !== 0,
                                'text-gray-500 cursor-not-allowed': form.field.product.field.consolidator_order_id.value.length === 0
                            }"
                            :disabled="form.field.product.field.consolidator_order_id.value.length === 0"
                            class="box rounded p-0.5 h-[20px] w-[20px] flex items-center justify-around"
                            @click="addAirlinePnr"
                        >
                            <PlusIcon class="w-3 h-3 !stroke-2" />
                        </button>
                    </div>
                </div>
                <template
                    v-if="editTickets !== null"
                >
                    <div
                        v-for="(item, ind) in form.field.airlinePnrs.items"
                        :key="ind"
                        class="flex gap-2"
                    >
                        <div class="flex flex-col justify-start flex-grow pr-1">
                            <div class="sales-ticket-cell w-full px-0 border-0">
                                <div class="sales-ticket-value">
                                    <InputLayoutWrapper
                                        v-model="form.field.airlinePnrs.items[ind].airline_id"
                                        label-type="code"
                                    >
                                        <FormSelect
                                            :class="{
                                                'border-theme-21': form.field.airlinePnrs.items[ind].airline_id.errors.length
                                            }"
                                            class="form-multiselect-inline h-[22px]"
                                        />
                                    </InputLayoutWrapper>
                                </div>
                            </div>
                            <div class="sales-ticket-cell w-full px-0 border-0">
                                <div class="sales-ticket-value">
                                    <InputLayoutWrapper
                                        v-model="form.field.airlinePnrs.items[ind].airline_pnr"
                                    >
                                        <FormInput
                                            v-autowidth="{
                                                minWidth: '100%',
                                                maxWidth: '100%',
                                            }"
                                            class="inline-edit h-[22px]"
                                        />
                                    </InputLayoutWrapper>
                                </div>
                            </div>
                        </div>
                        <div
                            class="sales-ticket-cell flex-row pb-px min-w-[24px] max-w-[24px] !p-0 mt-auto pr-4 border-0"
                        >
                            <button
                                class="box rounded p-0.5 text-red-500 h-[20px] w-[20px] flex items-center justify-around"
                                @click="delAirlinePnr(item.$identity)"
                            >
                                <XIcon class="w-3 h-3 !stroke-2" />
                            </button>
                        </div>
                    </div>
                </template>
            </div>
            <!--            4               -->
            <div class="sales-ticket-cell border-none p-0">
                <div class="sales-ticket-label">
                    Tkt. No.
                </div>
                <div class="sales-ticket-value">
                    <span
                        v-if="editTickets === null"
                    >{{ ticket?.product.external_number ?? 'None' }}</span>
                    <InputLayoutWrapper
                        v-else
                        v-model="form.field.product.field.external_number"
                    >
                        <FormInput
                            v-autowidth="{
                                minWidth: '100%',
                                maxWidth: '100%',
                            }"
                            class="inline-edit h-[22px]"
                            :class="{
                                '!border !border-theme-21': form.field.product.field.external_number.errors.length
                            }"
                        />
                    </InputLayoutWrapper>
                </div>
            </div>
            <!--            5               -->
            <div
                :key="`${consolidatorArea?.timezone}_timezone`"
                v-tooltip="{content: consolidatorAreaTimeString, disabled: !consolidatorAreaTimeString}"
                class="sales-ticket-cell border-none p-0 max-w-[80px]"
            >
                <div class="sales-ticket-label">
                    Issue date
                </div>
                <div class="sales-ticket-value">
                    <span v-if="editTickets === null || isSaleAdjusted">
                        {{ ticket.product?.issued_at ? $format.datetime(ticket.product.issued_at) : '' }}
                    </span>

                    <InputDate
                        v-else
                        :timestamp="form.field.product.field.issued_at.value"
                        timezone="UTC"
                        :placeholder-icon="false"
                        placeholder="dd/mm/yyyy"
                        size="xs"
                        :input-attrs="{
                            class: 'text-2xs leading-[10px] h-[26px] -mt-0.5'
                        }"
                        disable-toggle-on-select
                        @update:timestamp="updateProductIssueAtField($event)"
                    />
                </div>
            </div>
            <!--            6               -->
            <div class="sales-ticket-cell border-none p-0 max-w-[70px]">
                <div class="sales-ticket-label">
                    Issued with
                    <button
                        v-if="editTickets === null && ticket.product.pay_type === 'comVCC' && canEditPayments && ticket.product.card_identity"
                        class="inline box rounded p-0.5 text-green-500"
                        :class="{
                            'text-gray-500': cardInfoLoading
                        }"
                        :disabled="cardInfoLoading"
                        @click="showCredentials"
                        @click.right.prevent="($event) => openCardPreviewMenu($event)"
                    >
                        <LoaderIcon v-if="cardInfoLoading" class="w-2 h-2 !stroke-2" />
                        <EyeIcon v-else class="w-2 h-2 !stroke-2" />
                    </button>
                </div>
                <div class="sales-ticket-value">
                    <span
                        v-if="editTickets === null || isSaleAdjusted"
                    >{{ form.field.product.getCCPaymentTypeText(ticket) }} </span>
                    <InputLayoutWrapper
                        v-else
                        v-model="form.field.product.cardIdentity"
                        class="col-span-6"
                    >
                        <FormSelectGroups
                            :class="{
                                'border-theme-21': form.field.product.cardIdentity.errors.length
                            }"
                            class="form-multiselect-inline h-[22px]"
                        />
                    </InputLayoutWrapper>
                </div>
            </div>
            <!--            7               -->
            <div class="sales-ticket-cell border-none p-0 max-w-[100px]">
                <div class="sales-ticket-label relative">
                    Fare type
                    <LoaderIcon v-show="isFareTypeUpdating" class="animate-spin absolute right-[0px] top-0" />
                </div>
                <div class="sales-ticket-value mt-0.5 ">
                    <ProductFareType
                        v-if="ticket.product?.consolidator_order_id"
                        :key="ticket.product.consolidator_order_id"
                        :sale-pk="String(sale.id)"
                        :sale-version-pk="String(saleVersionComputed.id)"
                        :pnr="ticket.product.consolidator_order_id"
                        :is-edit-mode="!!editTickets"
                        @update="fareTypeUpdate"
                    />
                </div>
            </div>
            <!--            8               -->
            <div v-if="isAward" class="sales-ticket-cell border-none p-0">
                <div class="sales-ticket-label">
                    Program
                </div>
                <div class="sales-ticket-value">
                    <span
                        v-if="editTickets === null"
                    >{{ ticket?.milePriceProgram.name ?? 'None' }}</span>
                    <InputLayoutWrapper
                        v-else
                        v-model="form.field.mile_price_program_id"
                    >
                        <FormSelect
                            :class="{
                                'border-theme-21': form.field.mile_price_program_id.errors.length
                            }"
                            class="form-multiselect-inline h-[22px]"
                        />
                    </InputLayoutWrapper>
                </div>
            </div>
            <!--            9               -->
            <div class="sales-ticket-cell border-none p-0 max-w-[50px]">
                <div class="sales-ticket-label">
                    Paid ARC
                </div>
                <div
                    :class="{
                        'text-magic-mint-200': ticket?.product?.paid_at,
                        'text-gray-500': !ticket?.product?.paid_at
                    }"
                    class="sales-ticket-value"
                >
                    <CheckIcon class="w-4 h-4" />
                </div>
            </div>
            <!--            10               -->
            <div class="sales-ticket-cell border-none p-0 max-w-[40px]">
                <div class="sales-ticket-label">
                    Issued
                </div>
                <div
                    :class="{
                        'text-theme-20': ticket?.product?.issued_at,
                        'text-gray-500': !ticket?.product?.issued_at
                    }"
                    class="sales-ticket-value"
                >
                    <CheckIcon class="w-4 h-4" />
                </div>
            </div>
            <!--            11               -->
            <div class="sales-ticket-cell border-none p-0 max-w-[40px]">
                <div class="sales-ticket-label">
                    Remark
                </div>
                <div class="sales-ticket-value">
                    <ChatOpenButton
                        v-if="sale.generalChatRoom?.id"
                        v-slot="{messagesCount, hasNewMessages}"
                        :branch="ticket.product.chatBranchName"
                        :chat-room="sale.generalChatRoom"
                        class="relative items-center p-1 -my-1 hover:bg-gray-200 rounded"
                    >
                        <MessageCircleIcon class="w-4 h-4" />
                        <div
                            v-if="messagesCount"
                            :class="[hasNewMessages ? 'bg-orange-400 text-white' : 'bg-slate-200 text-gray-700']"
                            class="sales-ticket-badge top-0 right-0"
                        >
                            {{ messagesCount }}
                        </div>
                    </ChatOpenButton>
                </div>
            </div>
        </div>
        <div
            :class="getTicketColor('divide', 100)"
            class="sales-ticket-body"
        >
            <div
                v-if="!isAward"
                class="sales-ticket-cell w-18 max-w-18 "
            >
                <div class="sales-ticket-label">
                    Net price
                </div>
                <div class="sales-ticket-value">
                    <span
                        v-if="editTickets === null || isSaleAdjusted"
                    >{{ Number.formatMoney(ticket.product.net_price) }}</span>
                    <div
                        v-else
                        class="inline-edit cursor-not-allowed min-h-[17px]"
                    >
                        {{ Number.formatMoney(form.field.product.field.net_price_base.value) }}
                    </div>
                </div>
            </div>
            <div
                v-else
                class="sales-ticket-cell w-18 max-w-18"
            >
                <div class="sales-ticket-label">
                    Net price
                </div>
                <div class="sales-ticket-value">
                    <span
                        v-if="editTickets === null || isSaleAdjusted"
                    >{{ Number.formatMoney(ticket.product.net_price_base) }}</span>
                    <InputLayoutWrapper
                        v-else-if="!ticket.milePriceProgramAccount"
                        v-model="form.field.product.field.net_price_base"
                    >
                        <FormInputNumberMask
                            v-autowidth="{
                                minWidth: '100%',
                                maxWidth: '100%',
                            }"
                            class="inline-edit"
                        />
                    </InputLayoutWrapper>
                    <div v-else class="inline-edit cursor-not-allowed">
                        {{ Number.formatMoney(form.field.product.field.net_price_base.value) }}
                    </div>
                </div>
            </div>
            <div class="sales-ticket-cell w-18 max-w-18">
                <div class="sales-ticket-label">
                    Sell price
                </div>
                <div class="sales-ticket-value">
                    <span
                        v-if="editTickets === null || isSaleAdjusted"
                    >{{ Number.formatMoney(ticket.product.sell_price) }}</span>
                    <InputLayoutWrapper v-else v-model="form.field.product.field.sell_price">
                        <FormInputNumberMask
                            v-autowidth="{
                                minWidth: '100%',
                                maxWidth: '100%',
                            }"
                            class="inline-edit"
                        />
                    </InputLayoutWrapper>
                </div>
            </div>

            <div v-if="isAward" class="sales-ticket-cell w-22 max-w-22">
                <div class="sales-ticket-label">
                    Miles
                </div>
                <div class="sales-ticket-value">
                    <span
                        v-if="editTickets === null || isSaleAdjusted"
                    >{{ ticket.product.net_price }}</span>
                    <InputLayoutWrapper
                        v-else
                        v-model="form.field.product.field.net_price"
                    >
                        <FormInputNumberMask
                            v-autowidth="{
                                minWidth: '100%',
                                maxWidth: '100%',
                            }"
                            class="inline-edit"
                        />
                    </InputLayoutWrapper>
                </div>
            </div>
            <div
                v-if="!isAward"
                class="sales-ticket-cell w-19 max-w-19"
            >
                <div class="sales-ticket-label">
                    Com. value
                </div>
                <div class="sales-ticket-value">
                    <span
                        v-if="editTickets === null || isSaleAdjusted"
                    >{{ Number.formatMoney(ticket.product.commission) }}</span>
                    <InputLayoutWrapper v-else v-model="form.field.product.field.commission">
                        <FormInputNumberMask
                            v-autowidth="{
                                minWidth: '100%',
                                maxWidth: '100%',
                            }"
                            class="inline-edit"
                            @change="editInput('formCom')"
                        />
                    </InputLayoutWrapper>
                </div>
            </div>
            <div v-else class="sales-ticket-cell w-18 max-w-18">
                <div class="sales-ticket-label">
                    CPM
                </div>
                <div class="sales-ticket-value">
                    <span
                        v-if="editTickets === null || isSaleAdjusted"
                    >{{ form.field.product.field.exchange_rate.value }}</span>
                    <InputLayoutWrapper v-else v-model="form.field.product.field.exchange_rate">
                        <FormInputNumberMask
                            v-autowidth="{
                                minWidth: '100%',
                                maxWidth: '100%',
                            }"
                            class="inline-edit"
                        />
                    </InputLayoutWrapper>
                </div>
            </div>

            <div class="sales-ticket-cell w-18 max-w-18">
                <div class="sales-ticket-label">
                    Tax
                </div>
                <div class="sales-ticket-value">
                    <span
                        v-if="editTickets === null || isSaleAdjusted"
                    >{{ Number.formatMoney(ticket.product.tax) }}</span>
                    <InputLayoutWrapper v-else v-model="form.field.product.field.tax">
                        <FormInputNumberMask
                            v-autowidth="{
                                minWidth: '100%',
                                maxWidth: '100%',
                            }"
                            class="inline-edit"
                        />
                    </InputLayoutWrapper>
                </div>
            </div>
            <div v-if="!isAward" class="sales-ticket-cell w-18 max-w-18">
                <div class="sales-ticket-label">
                    Fare
                </div>
                <div class="sales-ticket-value">
                    <span
                        v-if="editTickets === null || isSaleAdjusted"
                    >{{ Number.formatMoney(ticket.product.fare) }}</span>
                    <InputLayoutWrapper v-else v-model="form.field.product.field.fare">
                        <FormInputNumberMask
                            v-autowidth="{
                                minWidth: '100%',
                                maxWidth: '100%',
                            }"
                            class="inline-edit"
                        />
                    </InputLayoutWrapper>
                </div>
            </div>
            <div class="sales-ticket-cell w-14 max-w-14">
                <div class="sales-ticket-label">
                    Iss. fee
                </div>
                <div class="sales-ticket-value">
                    <span
                        v-if="editTickets === null || isSaleAdjusted"
                    >{{ Number.formatMoney(ticket.product.issuing_fee) }}</span>
                    <InputLayoutWrapper
                        v-else
                        v-model="form.field.product.field.issuing_fee"
                    >
                        <FormInputNumberMask
                            v-autowidth="{
                                minWidth: '100%',
                                maxWidth: '100%',
                            }"
                            class="inline-edit"
                        />
                    </InputLayoutWrapper>
                </div>
            </div>
            <div class="sales-ticket-cell w-[80px] max-w-[80px]">
                <div class="sales-ticket-label">
                    Ck
                </div>
                <div class="sales-ticket-value">
                    <span
                        v-if="editTickets === null || isSaleAdjusted"
                    >{{ Number.formatMoney(ticket.product.check_payment) }}</span>
                    <div v-else class="flex items-center gap-1">
                        <InputCheckbox
                            :model-value="form.field.product.ckEnabled.value"
                            class="--primary focus:ring-0"
                            @update:model-value="form.field.product.toggleCk($event)"
                        />
                        <div class="w-[80px] max-w-[80px]">
                            <InputLayoutWrapper
                                v-model="form.field.product.field.check_payment"
                            >
                                <FormInputNumberMask
                                    v-autowidth="{
                                        minWidth: '100%',
                                        maxWidth: '100%',
                                    }"
                                    v-tooltip="{content: form.field.product.getCkTooltipContent() }"
                                    class="inline-edit"
                                    :class="{
                                        'warning': form.field.product.isCkManualWarning()
                                    }"
                                    :readonly="!form.field.product.ckEnabled.value"
                                />
                            </InputLayoutWrapper>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="!isAward" class="sales-ticket-cell w-32 max-w-32">
                <div class="sales-ticket-label">
                    Consolidator
                </div>
                <div class="sales-ticket-value">
                    <span v-if="!editTickets" class="flex gap-2 items-center justify-between">
                        {{ ticket.product.consolidatorArea.name }}

                        <button
                            v-if="ticket.product.consolidatorArea.name === 'Online'"
                            v-tooltip="'Process Online'"
                            class="box rounded p-0.5 text-green-500 flex-none h-[20px] w-[20px] flex items-center justify-around"
                            @click="processOnline"
                        >
                            <ArrowRightLeft class="w-3 h-3 !stroke-2" />
                        </button>
                    </span>
                    <InputLayoutWrapper
                        v-else
                        v-model="form.field.product.field.consolidator_area_id"
                    >
                        <FormSelect
                            :class="{
                                'border-theme-21': form.field.product.field.consolidator_area_id.errors.length
                            }"
                            class="form-multiselect-inline"
                        />
                    </InputLayoutWrapper>
                </div>
            </div>

            <div v-if="!isAward" class="sales-ticket-cell w-32 max-w-32">
                <div class="sales-ticket-label">
                    Product type
                </div>
                <div class="sales-ticket-value">
                    <span v-if="!editTickets">{{ form.field.ticket_type.valueLabel }}</span>
                    <InputLayoutWrapper
                        v-else
                        v-model="form.field.ticket_type"
                    >
                        <FormSelect
                            :class="{
                                'border-theme-21': form.field.ticket_type.errors.length
                            }"
                            class="form-multiselect-inline"
                        />
                    </InputLayoutWrapper>
                </div>
            </div>

            <div
                v-if="isAward"
                class="sales-ticket-cell pr-2"
            >
                <div class="sales-ticket-label">
                    Acc. used
                </div>
                <div :key="ticket.mile_price_program_account_id" class="sales-ticket-value">
                    <AppButton
                        v-if="!editTickets && $can('useAwardDetails', 'Sale', sale)"
                        class="--outline --xs -my-1 !h-5 !px-1.5 !gap-x-0.5"
                        :class="{
                            '--danger': ticket.milePriceProgramAccount?.account_number,
                        }"
                        @click.stop="selectAwardAccount"
                    >
                        <span class="truncate max-w-[70px]">
                            {{
                                ticket.milePriceProgramAccount?.account_number ? ticket.milePriceProgramAccount.account_number : 'Select'
                            }}
                        </span>

                        <ExternalLinkIcon class="flex-none w-2 h-2 !stroke-2 ml-0.5 mb-px" />
                    </AppButton>
                    <div v-else class="truncate max-w-[80px]">
                        {{
                            ticket.milePriceProgramAccount?.account_number ? ticket.milePriceProgramAccount.account_number : ''
                        }}
                    </div>
                </div>
            </div>
            <div class="sales-ticket-cell w-20 max-w-20 !ml-auto border-0">
                <div class="sales-ticket-label">
                    Profit
                </div>
                <div class="sales-ticket-value">
                    <span
                        v-if="editTickets === null"
                    >{{ Number.formatMoney(ticket.product.profit) }}</span>
                    <template
                        v-else
                    >
                        {{ Number.formatMoney(profit()) }}
                    </template>
                </div>
            </div>
        </div>
        <template v-if="$can('edit', 'Sale', sale) && (!isSaleAdjusted && !isSaleClosed)">
            <div
                v-if="editTickets === null"
                :class="getTicketColor('border', 100)"
                class="sales-ticket-action"
            >
                <button
                    class="p-1"
                    @click="editTicket"
                >
                    <Edit3Icon class="w-3.5 h-3.5 !stroke-2" />
                </button>
                <DropDownWrapper
                    v-slot="{
                        close
                    }"
                    class="dropdown"
                    tag="div"
                >
                    <DropDownButtonWrapper class="dropdown-toggle p-1 border-t border-slate-200" tag="button">
                        <MoreHorizontalIcon class="w-3.5 h-3.5 !stroke-2" />
                    </DropDownButtonWrapper>
                    <DropDownContentWrapper class="dropdown-menu w-40" tag="div">
                        <div
                            class="dropdown-menu__content box p-2 border border-slate-200/60 dark:bg-dark-1 dark:border-transparent"
                        >
                            <a
                                class="dropdown-menu__item"
                                href="#"
                                @click="() => {
                                    close()
                                    markTicketAs('Refund')
                                }"
                            >Refund</a>
                            <a
                                class="dropdown-menu__item"
                                href="#"
                                @click="() => {
                                    close()
                                    markTicketAs('Void')
                                }"
                            >Void</a>
                            <a
                                class="dropdown-menu__item"
                                href="#"
                                @click="() => {
                                    close()
                                    markTicketAs('Exchange')
                                }"
                            >Exchange</a>
                        </div>
                    </DropDownContentWrapper>
                </DropDownWrapper>
            </div>
            <div
                v-else
                :class="getTicketColor('border', 100)"
                class="sales-ticket-action"
            >
                <button
                    class="p-1 text-green-500"
                    :disabled="isFareTypeUpdating"
                    :class="{
                        'text-gray-600 cursor-not-allowed': isFareTypeUpdating
                    }"
                    @click="saveTicket"
                >
                    <CheckIcon class="w-3.5 h-3.5 !stroke-2" />
                </button>
                <button class="p-1 text-red-500" @click="cancelTicket">
                    <XIcon class="w-3.5 h-3.5 !stroke-2" />
                </button>
            </div>
        </template>
    </div>
</template>

<script lang="ts">
import FormSelect from '@/components/Form/FormField/FormSelect.vue'
import FormInput from '@/components/Form/FormField/FormInput.vue'
import InputLayoutWrapper from '@/components/Form/FormWrappers/InputLayoutWrapper.vue'
import ContextMenu from '@/components/ContextMenu/ContextMenu.vue'

import FormHelper from '@/lib/core/helper/FormHelper'
import InputField from '@/lib/FormField/InputField'
import SelectField from '@/lib/FormField/SelectField'
import ChildMixin from '@/lib/mixin/ComponentRelation/ChildMixin'
import DropDownWrapper from '@/components/dropdown/DropDownWrapper.vue'
import DropDownButtonWrapper from '@/components/dropdown/DropDownButtonWrapper.vue'
import DropDownContentWrapper from '@/components/dropdown/DropDownContentWrapper.vue'
import ChildTypedMixin from '@/lib/mixin/ComponentRelation/ChildTypedMixin'
// import FormDateInputTeleport from '@/components/Form/FormField/FormDateInputTeleport.vue'
import ArrayField from '@/lib/FormField/ArrayField'
import FormInputNumberMask from '@/components/Form/FormField/FormInputNumberMask.vue'
import Debounce from '@/lib/core/helper/Debounce'
import ParentMixin from '@/lib/mixin/ComponentRelation/ParentMixin'
import FormSelectGroups from '@/components/Form/FormField/FormSelectGroups.vue'
import TransactionService from '@/lib/service/TransactionService'
import ProductFormField from '@/lib/FormField/Models/ProductFormField'
import ChatOpenButton from '@/modules/chat/components/ChatOpenButton.vue'
import { MessageType } from '@/types/enums/MessageType'
import { pluckObjectKeys } from '~/lib/Helper/ObjectHelper'
import type { PropType } from 'vue'
import AwardAccountSelectModal from '~/sections/AwardAccount/modals/AwardAccountSelectModal.vue'

import type { Ticket } from '@/api/models/Ticket/TicketModel'
import { isValidProductExternalNumber } from '~/lib/Helper/ValidationHelper'
import ProductFareType from '~/sections/Sale/components/ProductFareType.vue'
import SaleTicketCheckPnrModal from '~/sections/Sale/components/SaleTicketCheckPnrModal.vue'
import ArrowRightLeft from '~/assets/icons/ArrowRightLeft.svg?component'
import ProcessOnlineConsolidatorModal from '~/sections/Sale/modals/ProcessOnlineConsolidatorModal.vue'
import VCCSelectModal from '~/sections/Card/VirtualCard/modals/VCCSelectModal.vue'
import { ProjectCardCategory } from '~/api/models/Card/ProjectCard'
import InputCheckbox from '~/components/Input/InputCheckbox.vue'
import { preventDuplication, wait } from '~/lib/Helper/PromiseHelper'
import { getChisinauGmtTime } from '@/lib/core/helper/DateHelper'
import { copyToClipboard } from '@/lib/core/helper/ClipboardHelper'
import { openContextMenu } from '@/plugins/ContextMenuPlugin'
import { getVccGds } from '~/lib/Helper/CreditCardHelper'
import { CopyIcon } from '@zhuowenli/vue-feather-icons'
import { toastError } from '@/lib/core/helper/ToastHelper'

export default defineComponent({
    name: 'TicketComponent',
    components: {
        ProductFareType,
        ChatOpenButton,
        FormSelectGroups,
        FormInputNumberMask,
        // FormDateInputTeleport,
        DropDownContentWrapper,
        DropDownButtonWrapper,
        DropDownWrapper,
        FormSelect,
        FormInput,
        InputLayoutWrapper,
        ArrowRightLeft,
        InputCheckbox,
    },

    mixins: [ParentMixin, ChildMixin, ChildTypedMixin],
    props: {
        ticket: {
            type: Object as PropType<Ticket>,

            required: true,
        },

        index: {
            type: String,
            default: '',
        },

    },

    setup(props) {
        const { useDictionary, useModel } = useContext()
        const vccSelectModal = useModal(VCCSelectModal)
        const vccModel = useModel('ProjectCard')
        const saleVersionPnrModel = useModel('SaleVersionPnr')
        const formatter = useService('formatter')

        const processConsolidatorOnlineModal = useModal(ProcessOnlineConsolidatorModal)

        const cardInfoLoading = ref(false)
        const openCardPreviewMenu = async (event) => {
            return preventDuplication(async () => {
                const card_info = await vccModel.actions.getCardInfo({
                    pk: String(props.ticket.product.card_identity),
                })
                const gds = getVccGds(card_info)
                const options = [
                    {
                        enabled: true,
                        text: 'Copy GDS format',
                        icon: CopyIcon,
                        onClick: () => {
                            copyToClipboard(gds)
                        },
                    },
                    {
                        enabled: true,
                        text: 'Copy full card info',
                        icon: LayersIcon,
                        onClick: () => {
                            copyToClipboard([
                                `Cardholder name: ${card_info.holder_name}`,
                                `Card number: ${card_info.card_number}`,
                                `Expiration: ${card_info.expiration}`,
                                `CVV: ${card_info.cvv}`,
                            ].join('\n'))
                        },
                    },
                ]

                await openContextMenu(event, options, {
                    disableNextTick: true,
                })
            }, cardInfoLoading)
        }

        return {
            processConsolidatorOnlineModal,
            useModel,
            saleVersionPnrModel,
            vccModel,
            vccSelectModal,
            projectDictionary: useDictionary('Project'),
            consolidatorAreaDictionary: useDictionary('ConsolidatorArea'),
            airlineDictionary: useDictionary('Airline'),
            formatter,
            openCardPreviewMenu,
            cardInfoLoading,
        }
    },

    parentType: 'SaleTicketsBlock',
    parentName: ['SaleComponent'],
    type: 'RefundModalParent',

    data() {
        return {
            // groupIdentity: `form-${this.ticket.sell_type}-${this.ticket.passengerType}-${this.ticket.pre_ticket_number}`,
            groupIdentity: `form-${this.ticket.sell_type}-${this.ticket.pre_ticket_number}-${this.ticket.type}`,
            trigger: 0,
            form: new FormHelper({
                product: new ProductFormField(),

                ticket_type: new SelectField(false, 'Product type', {
                    items: this.$models.TicketModel.getTicketTypeOptionsData(),
                    emptyLabel: '------',
                }),

                mile_price_program_id: new SelectField(false, 'Mice Price Cost', {
                    items: this.$models.MilePriceProgramModel.getAllList(),
                    emptyLabel: '------',
                    labelAdapter: (item) => {
                        return `${item.label} `// - ${item.mile_price}
                    },
                }),

                validating_carrier_id: new SelectField(false, 'Validating Carrier ID', {
                    items: this.$models.AirlineModel.getAllList(),
                    emptyLabel: '--',
                    labelAdapter: item => {
                        return `${item.system_name} - ${item.label}`
                    },
                }),

                validating_carrier_pnr: new InputField(false, 'Validating Carrier PNR', {}),

                airlinePnrs: new ArrayField({
                    id: new InputField(true, 'ID', { placeholder: '' }),
                    airline_id: new SelectField(true, 'Airline ID', {
                        items: this.$models.AirlineModel.getAllList(),
                        emptyLabel: '--',
                        labelAdapter: item => {
                            return `${item.system_name} - ${item.label}`
                        },
                    }),

                    airline_pnr: new InputField(true, 'Airline Pnr', { placeholder: '' }),
                }),

            }),

            editTickets: null,
            editField: null,
            updateForm: false,
            isEditMode: false,
            isSubscribe: false,
            isClosed: false,
            isSaved: false,
            debounceTicketFormChange: Debounce.create(150),
            vccPreventValidate: false,
            isCopyRequestInProgress: false,
        }
    },

    computed: {
        isFareTypeUpdating() {
            const pnr = this.ticket.product.consolidator_order_id

            if (!pnr) {
                return false
            }

            return this.$related.SaleComponent.updateFareTypeQueue[pnr]
        },

        isAward() {
            return this.ticket.sell_type === 'award'
        },

        project() {
            return this.projectDictionary.find(String(this.sale.project_id))
        },

        creditCards() {
            return this.$related.SaleComponent.creditCardData.records
        },

        virtualCards() {
            return this.$related.SaleComponent.virtualCards
        },

        formData() {
            return this.form.data
        },

        productTotals() {
            return TransactionService.calcProductTotals(this.formData.product)
        },

        sale() {
            return this.$related.SaleComponent.saleModel
        },

        saleVersionPk() {
            return this.$related.SaleComponent?.saleVersionPk
        },

        saleVersionComputed() {
            return this.$related.SaleComponent?.saleVersion
        },

        saleHelper() {
            return this.$related.SaleComponent.saleHelper
        },

        airlineCodesArr() {
            const codes = this.ticket.airlinePnrs.map(item => item.airline_id)

            return this.$models.AirlineModel.getAllList()
                .filter(item => codes.includes(item.value))
                .map(item => item.system_name)
        },

        airlineCodes() {
            return this.airlineCodesArr.join(', ')
        },

        airlinePnrs() {
            return this.ticket.airlinePnrs.map(item => item.airline_pnr).join(', ')
        },

        isSaleAdjusted() {
            return this.saleHelper.isSaleAdjusted
        },

        isSaleClosed() {
            return this.saleVersionComputed?.is_sale_closed || !this.saleVersionComputed?.is_active
        },

        consolidatorArea() {
            return this.consolidatorAreaDictionary.find(String(this.form.field.product.field.consolidator_area_id.value))
        },

        consolidatorAreaTimeString() {
            const timestamp = this.ticket.product.issued_at

            if (!timestamp) {
                return ''
            }
            const pattern = 'd MMM HH:mm'

            if (this.consolidatorArea?.name && ['Online', 'EGF', 'Other'].includes(this.consolidatorArea.name)) {
                return 'Tickets are issued across various ARC/BSP regions and details must be manually adjusted as there is no fixed GTM code.'
            }

            if (!this.consolidatorArea || !this.consolidatorArea?.timezone) {
                return ''
            } else {
                return `${this.formatter.datetime(timestamp, 'Europe/Chisinau', pattern)} (${this.chisinauGmtTime})`
            }

            return ''
        },

        chisinauGmtTime() {
            return getChisinauGmtTime()
        },

        consolidatorPccs() {
            return this.consolidatorAreaDictionary.mapForConsoleRecords.forSelect().filter(record => !record.disabled).map(record => record.title).join(', ') || 'No codes available'
        },
    },

    watch: {
        formData: {
            immediate: true,
            handler(val) {
                val.id = this.ticket.id
                val.product.profit = this.profit()

                if (this.$typedParent.forms[this.index] === undefined) {
                    this.$typedParent.forms[this.index] = []
                }
                const index = this.$typedParent.forms[this.index].findIndex(form => {
                    return form.id === this.ticket.id
                })

                if (index < 0) {
                    this.$typedParent.forms[this.index].push(val)
                } else {
                    this.$typedParent.forms[this.index][index] = val
                }
            },
        },

        editEnabled(val) {
            this.$typedParent.editEnabled = val
        },

        creditCards: {
            deep: true,
            immediate: true,
            handler(val) {
                this.form.field.product.setCreditCards(val)
            },
        },

        virtualCards: {
            deep: true,
            immediate: true,
            handler(val) {
                this.form.field.product.setInhouseVCC(val, this.canEditPayments && this.sale.is_connex_pay_enabled)
            },
        },

        isSaleAdjusted(val) {
            if (!!val && this.editTickets) {
                this.closeTicket()
            }
        },
    },

    async mounted() {
        this.form.setData(this.ticket)
        this.form.field.product.init()

        const {
            recalculateCK,
        } = this.form.field.product.useCalculations()

        const milePriceProgram = this.form.field.mile_price_program_id
        const recalculateAwardFare = () => {
            if (!!this.ticket && this.ticket?.sell_type === 'award') {
                const mile_price = this.form.field.product.field.exchange_rate.value
                this.form.field.product.field.fare.value = TransactionService.calcTicketFare(this.form.field.product.field.net_price.value, mile_price)
            }
        }
        milePriceProgram.event.addListener('change', recalculateAwardFare)
        this.form.field.product.field.net_price.event.addListener('change', recalculateAwardFare)

        const runTicketFormChange = () => {
            this.debounceTicketFormChange(() => {
                this.$related.SaleComponent.ticketFormChange(this, this.form)
            })
        }

        this.form.field.product.event.addListener('change', (...v) => {
            if (v[0].field === 'card_identity') {
                const product = this.form.field.product.value

                if (product.pay_type === 'comVCC' && !product.card_identity && !v[0].data) {
                    this.showVccSelectModal()
                }

                if (product.pay_type === 'comVCC' && product.card_identity) {
                    this.vccValidate()
                }
            }

            if (!this.updateForm && v[0].field !== 'external_number') {
                if (v[0].field !== 'check_payment') {
                    recalculateCK()
                }

                runTicketFormChange()
            } else {
                setTimeout(() => {
                    this.updateForm = false
                }, 1)
            }
        })

        this.form.field.mile_price_program_id.event.addListener('change', (...v) => {
            const selected = milePriceProgram.currentItem()
            this.form.field.product.field.exchange_rate.value = selected?.mile_price ?? 0.0135
            runTicketFormChange()
        })

        this.form.field.product.field.exchange_rate.event.addListener('change', (...v) => {
            recalculateAwardFare()
        })

        this.form.field.validating_carrier_id.event.addListener('change', (...v) => {
            runTicketFormChange()
        })

        this.form.field.validating_carrier_pnr.event.addListener('change', (...v) => {
            runTicketFormChange()
        })

        this.form.field.airlinePnrs.event.addListener('change', (...v) => {
            runTicketFormChange()
        })

        this.form.field.product.cardIdentity.event.addListener('change', (...v) => {
            runTicketFormChange()
        })

        this.form.field.ticket_type.event.addListener('change', (...v) => {
            runTicketFormChange()
        })
    },

    methods: {
        fareTypeUpdate(data: {pnr: string, updating: boolean}) {
            this.$related.SaleComponent.updateFareTypeQueue[data.pnr] = data.updating
        },

        getSaleData() {
            return this.sale
        },

        onOtherFormChange(form, differentPassengerTypes = false) {
            const formData = form.data

            if (differentPassengerTypes) {
                const alwaysValidate = [
                    'airlinePnrs',
                    'validating_carrier_id',
                    'validating_carrier_pnr',
                    'consolidator_order_id',
                ]

                const data = {
                    ...pluckObjectKeys(formData, alwaysValidate),
                    product: {
                        ...this.form.data.product,
                        ...pluckObjectKeys(formData.product, alwaysValidate),
                    },
                }

                this.form.applyData(data)

                return
            }

            const cardIdentityValue = form.field.product.cardIdentity.value
            const card_identity = form.field.product.field.card_identity.value
            const pay_type = form.field.product.field.pay_type.value

            delete formData.product.issued_at
            delete formData.product.external_number
            delete formData.product.card_identity
            delete formData.product.pay_type

            this.form.applyData(formData)

            if (pay_type === 'comVCC' && !card_identity) {
                // to prevent open multiple select vccModals
            } else {
                this.form.field.product.cardIdentity.silentValue = cardIdentityValue
                this.form.field.product.field.card_identity.silentValue = card_identity
                this.form.field.product.field.pay_type.silentValue = pay_type
            }
        },

        onToggleFormSubscribe(status) {
            this.isSubscribe = status
        },

        checkSaleClosed() {
            if (this.saleVersionComputed?.is_sale_closed) {
                this.toastError('Sale closed!')

                return true
            }

            return false
        },

        checkSaleAdjusted() {
            if (this.isSaleAdjusted) {
                this.toastWarning('Sale adjusted!')

                return true
            }

            return false
        },

        editTicket() {
            if (this.checkSaleClosed()) {
                return
            }

            this.checkSaleAdjusted()

            const updateFromOtherOpenedForm = () => {
                let otherComponent = this.$related.SaleComponent.ticketComponentGroups[this.groupIdentity].find(other => {
                    if (other !== this && other.isEditMode) {
                        return this.ticket.passengerType === other.ticket.passengerType
                    }
                })

                if (!otherComponent) {
                    otherComponent = this.$related.SaleComponent.ticketComponentGroups[this.groupIdentity].find(other => {
                        return other !== this && other.isEditMode
                    })
                }

                this.onOtherFormChange(otherComponent.form, this.ticket.passengerType !== otherComponent.ticket.passengerType)
            }

            const loadOriginalData = () => {
                const data = this.ticket

                this.form.setData(data)
                this.form.field.product.applyCardIdentityFromFormData(this.ticket)
            }

            if (this.$related.SaleComponent.ticketOtherFormOpened(this) && !this.isSubscribe) {
                this.$confirm({
                    type: MessageType.Info,
                    text: 'Do you want to duplicate ticket data?',
                    description: 'The same changes will be applied to all the selected tickets',
                    confirmButton: 'Yes, duplicate',
                    cancelButton: 'No',
                }).then(() => {
                    updateFromOtherOpenedForm()

                    this.$related.SaleComponent.ticketFormToggleSubscribe(this, true)

                    // const formData = this.$related.SaleComponent.getFormDataOtherSubscribe(this)
                    // formData.product.issued_at = this.ticket.product.issued_at
                    // formData.product.external_number = this.ticket.product.external_number
                    // this.form.setData(formData)
                    // this.form.field.product.applyCardIdentityFromFormData(formData)
                }).catch(() => {
                    loadOriginalData()
                }).finally(() => {
                    this.isEditMode = true
                    this.editTickets = this.ticket.id
                })
            } else if (this.isSubscribe) {
                updateFromOtherOpenedForm()
                this.isEditMode = true
                this.editTickets = this.ticket.id
            } else {
                loadOriginalData()
                this.isEditMode = true
                this.editTickets = this.ticket.id
            }
        },

        validateTicketNumber() {
            this.form.field.product.field.external_number.resetErrors()

            const is_award = !!this.form.field.product.field.isAward.value
            const value = this.form.field.product.field.external_number.value
            const consolidator_area_system_name = this.form.field.product.field.consolidator_area_id.systemName

            if (value && value.length >= 14) {
                const valid = isValidProductExternalNumber(value, is_award, consolidator_area_system_name)

                if (!valid) {
                    const message = 'Please enter valid Tkt.Number'
                    this.form.field.product.field.external_number.addError(message)
                    this.toastError(message)
                }

                return valid
            }

            return true
        },

        save() {
            if (this.checkSaleAdjusted()) {
                this.editTickets = null

                return
            }

            const formData = this.form.data

            const valid = this.validateTicketNumber()

            if (!valid) {
                return
            }

            const prevTicket = JSON.parse(JSON.stringify(this.ticket)) as Ticket
            const currentData = this.form.data

            this.$models.TicketModel.update(this.ticket.id, this.form.data).then(apiResponse => {
                this.$related.SaleComponent.tickets.updateRecord(apiResponse.result)
                this.editTickets = null
                this.isEditMode = false
                this.isSubscribe = false
            }).catch(apiError => {
                if (apiError.name === 'ApiErrorForm') {
                    this.form.setErrors(apiError.data)
                }
            })
        },

        async sendPopulateTicket(ticket_pk: PrimaryKey) {
            await this.useModel('SaleVersion').actions.populateTicket({
                ticket_pk,
            })
        },

        saveTicket() {
            if (this.checkSaleAdjusted()) {
                this.editTickets = null

                return
            }

            if (this.$related.SaleComponent.ticketOtherFormOpened(this) && this.isSubscribe) {
                this.$confirm({
                    type: 'info',
                    text: 'Do you want to save all duplicate ticket data?',
                    confirmButton: 'Yes, save all',
                    cancelButton: 'No, only one',
                }).then(() => {
                    this.$related.SaleComponent.saveAndCloseAllFormTickets(this)
                    this.save()
                }).catch(() => {
                    this.save()
                })
            } else {
                this.save()
            }
        },

        closeTicket() {
            this.editTickets = null
            this.isEditMode = false
            this.isSubscribe = false
            this.isClosed = true
            this.form.setData(this.ticket)
        },

        cancelTicket() {
            if (this.$related.SaleComponent.ticketOtherFormOpened(this) && this.isClosed === false) {
                this.$confirm({
                    type: 'info',
                    text: 'Do you want to close all duplicate ticket data?',
                    confirmButton: 'Yes, close all',
                    cancelButton: 'No, only one',
                }).then(() => {
                    this.$related.SaleComponent.closeAllFormTicket(this)
                }).catch(() => {
                    this.closeTicket()
                })
            } else if (this.isClosed) {
                this.$related.SaleComponent.closeAllFormTicket(this)
            } else {
                this.closeTicket()
            }
        },

        getTicketColor(type, size) {
            return this.saleHelper.getTicketColor(type, this.ticket)
        },

        openChatRoom(id) {
            this.$related.SaleComponent.openChatRoom(id)
        },

        editInput(val) {
            this.editField = val
        },

        profit() {
            return TransactionService.calcProductProfit(this.form.data.product)
        },

        markTicketAs(type) {
            if (this.checkSaleAdjusted() || this.checkSaleClosed())
                return

            this.$models.TicketModel.markTicketAs(this.ticket.id, type)
        },

        addFormAirlinePnrs() {
            if (this.form.field.airlinePnrs.items.length < 10) {
                this.form.field.airlinePnrs.addItem({
                    airline_id: '',
                    airline_pnr: '',
                })
            }
        },

        addAirlinePnr() {
            if (this.form.field.airlinePnrs.items.length < 10) {
                this.addFormAirlinePnrs()
                this.$related.SaleComponent.addAirlinePnrs(this)
            }
        },

        delFormAirlinePnr(identity) {
            try {
                this.form.field.airlinePnrs.delItem(identity)
            } catch (err) {
                //
            }
        },

        delAirlinePnr(identity) {
            this.delFormAirlinePnr(identity)

            this.$related.SaleComponent.dellAirlinePnrs(this, identity)
        },

        async showVccSelectModal() {
            await preventDuplication(async () => {
                this.vccSelectModal.open({
                    salePk: String(this.sale.id),
                    ticketPk: String(this.ticket.id),
                }).then((card_pk) => {
                    this.vccPreventValidate = true
                    this.$nextTick().then(() => {
                        this.saleHelper.fetchVirtualCards()
                        ///
                        this.form.field.product.cardIdentity.value = `${ProjectCardCategory.ProjectVirtualCard}-${card_pk}`
                        setTimeout(() => {
                            this.vccPreventValidate = false
                        }, 400)
                    })
                }).catch(payload => {
                    this.form.field.product.cardIdentity.value = ''
                    this.form.field.product.field.card_identity.value = ''
                })
            })
        },

        async vccValidate() {
            if (this.vccPreventValidate) {
                return
            }
            const ticket = this.form.data
            const type = ticket.product.pay_type
            const pk = String(ticket.product.card_identity)
            const ticket_airline_pk = String(ticket.validating_carrier_id)

            if (type === 'comVCC' && pk && !!ticket_airline_pk) {
                const record = await this.vccModel.useRecord({
                    with: ['vccInfo.airline'],
                }).fetch(pk)
                const ticket_airline = this.airlineDictionary.findOrFail(ticket_airline_pk)

                if (ticket_airline_pk !== record.value.vccInfo.airline_pk) {
                    this.$confirm({
                        text: `Are you sure you want to pay with ${record.value.vccInfo.airline?.code}?`,
                        description: `Mismatch between ticket carrier (${ticket_airline.code}) and selected card carrier (${record.value.vccInfo.airline?.code})`,
                        cancelButton: 'Cancel',
                        confirmButton: 'Confirm',
                    }).then(() => {
                        //
                    }).catch(() => {
                        this.form.field.product.cardIdentity.value = ''
                        this.form.field.product.field.card_identity.value = ''
                    })
                }
            }
        },

        async showCredentials() {
            if (this.canEditPayments) {
                const credentials = await this.$models.ProjectCardModel.getCredentialsUrl(this.ticket.product.id)
                const { url, card } = credentials.result
                window.open(url, '_blank')

                // this.$showModal(VirtualCardCredentialsModal, {}, {
                //     objectWithProduct: this.ticket,
                // })
            }
        },

        selectAwardAccount() {
            this.$showModal(AwardAccountSelectModal, {}, {
                salePk: String(this.sale.id),
                saleType: this.sale.type,
                ticketPk: String(this.ticket.id),
                accountPk: this.ticket.mile_price_program_account_id ? String(this.ticket.mile_price_program_account_id) : undefined,
                ticketAmount: Number(this.ticket.product.net_price),
                saleIsAdjusted: Boolean(this.isSaleAdjusted),
            })
        },

        updateProductIssueAtField(value?: number) {
            if (!value) {
                this.form.field.product.field.issued_at.value = undefined

                return
            }

            const date = new Date(value * 1000)

            date.setUTCHours(12, 0, 0, 0)
            this.form.field.product.field.issued_at.silentValue = date.unixTimestamp()
        },

        checkPnr() {
            this.$showModal(SaleTicketCheckPnrModal, {}, {
                salePk: String(this.sale.id),
                ticketPk: String(this.ticket.id),
                consolidatorAreaPk: String(this.ticket.product.consolidator_area_id),
                pnr: this.ticket.product.consolidator_order_id,
            })
        },

        processOnline() {
            this.processConsolidatorOnlineModal.open({
                salePk: String(this.sale.id),
                ticketPk: String(this.ticket.id),
            })
        },

        async copyPnrInfo(pnr: string) {
            const pnrData = await this.saleVersionPnrModel.actions.getPnrDetails({
                pnr: pnr,
                sale_version_pk: this.saleVersionPk,
                ticket_pk: String(this.ticket.id),
            })

            copyToClipboard(pnrData.text, true)
        },

        openPnrContextMenu(event, pnr) {
            if (!pnr) return

            openContextMenu(event, [
                {
                    icon: CopyIcon,
                    text: 'Copy PNR details',
                    keep: true,
                    loading: () => this.isCopyRequestInProgress,
                    onClick: async (event) => {
                        if (this.isCopyRequestInProgress) {
                            return
                        }

                        try {
                            this.isCopyRequestInProgress = true
                            await this.copyPnrInfo(pnr)
                        } catch (e) {
                            toastError('Please try again later')
                        } finally {
                            this.isCopyRequestInProgress = false
                        }

                        useExistingModal({ group: 'context-menu' })?.close()
                    },
                },
            ])
        },

    },
})
</script>
