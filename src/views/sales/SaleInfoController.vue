<template>
    <div class="sales-content sales-content-details">
        <div class="sales-wrapper">
            <div v-if="saleModel?.id" class="sales-main">
                <SaleComponent
                    v-if="saleHelper"
                    :key="id"
                    :sale-helper="saleHelper"
                >
                    <template #tabs-append>
                        <button
                            v-if="isComparing"
                            class="btn btn-outline-primary btn-sm box m-auto mr-2"
                            @click="openOrganizer"
                        >
                            Open Organizer
                            <LogInIcon class="ml-2 rotate-180" />
                        </button>
                    </template>
                </SaleComponent>
            </div>

            <div v-if="isComparing && comparedSaleHelper" class="sales-main">
                <SaleComponent
                    :key="comparedSaleHelper.saleId"
                    :sale-helper="comparedSaleHelper"
                    :compared-with="[saleHelper.saleId]"
                >
                    <template #tabs-append>
                        <button class="btn btn-outline-secondary btn-sm box m-auto mr-2" @click="saleHelper.clearCompareList()">
                            End Comparing
                            <XIcon class="ml-2" />
                        </button>
                    </template>
                </SaleComponent>
            </div>

            <Organizer
                v-if="!isComparing && saleModel && saleModel?.id"
                with-tabs
                :model="{
                    ...saleModel,
                    client: {
                        id: saleModel.clientId,
                        first_name: saleModel.clientFullName,
                        last_name: '',
                    }
                }"
                :can-edit-files="canEditFiles"
                :can-edit-object="canEditSale"
                :can-read-client-messages="canReadClientMessages"
                class="sales-chat"
                :model-identification="{
                    model_name: 'Sale',
                    model_pk: String(saleModel.id),
                }"
            >
                <template #sidebar>
                    <AppTip
                        v-if="isFraud"
                        type="danger"
                        class="items-center p-1 m-2 gap-8"
                    >
                        <div class="inline-flex items-center">
                            Sale contacts are marked as Fraud
                        </div>
                    </AppTip>
                    <AppTip
                        v-if="saleModel.isWarningVpn"
                        type="warning"
                        class="items-center p-1 m-2 gap-8"
                    >
                        <div class="inline-flex items-center">
                            Access to booking only through VPN
                        </div>
                    </AppTip>
                    <OrganizerSectionTaskGroups
                        resource-name="SaleTaskGroupList"
                        :model="{
                            model_name: 'Sale',
                            model_pk: String(saleModel.id),
                        }"
                    />
                    <OrganizerBranchesBlock />
                    <OrganizerMembersBlock />
                    <OrganizerAttachmentsBlock />
                </template>
                <template #timersBlock>
                    <div v-if="hasPermission('viewSaleMembers', 'Sale', saleModel)" class="flex justify-end flex-1">
                        <AppButton
                            class="mr-2"
                            @click="openSaleActivityModal(String(saleModel.id))"
                        >
                            <UsersIcon />
                            Members
                        </AppButton>
                    </div>
                </template>
            </Organizer>
        </div>
    </div>
</template>

<script lang="ts">
import SaleHelper from '@/lib/core/helper/Sale/SaleHelper'
import Organizer from '@/components/Organizer/Organizer.vue'
import OrganizerAttachmentsBlock from '@/components/Organizer/Block/OrganizerAttachmentsBlock.vue'
import OrganizerMembersBlock from '@/components/Organizer/Block/OrganizerMembersBlock.vue'
import OrganizerBranchesBlock from '@/components/Organizer/Block/OrganizerBrachesBlock.vue'
import SaleComponent from '@/views/sales/sale/SaleComponent.vue'
import { LogInIcon, XIcon } from '@zhuowenli/vue-feather-icons'
import SaleOrganizerSideModal from '@/views/sales/sale/modals/SaleOrganizerSideModal.vue'
import ParentMixin from '@/lib/mixin/ComponentRelation/ParentMixin'
import { defineComponent } from 'vue'
import { watchOnce } from '@vueuse/core'
import OrganizerSectionTaskGroups from '~/sections/Organizer/parts/OrganizerSectionTaskGroups.vue'
import SaleActivityModal from '~/sections/Sale/modals/SaleActivityModal.vue'

export default defineComponent({
    name: 'SaleInfoController',

    type: [
        'OrganizerIntegration',
    ],

    components: {
        OrganizerSectionTaskGroups,
        SaleComponent,

        // Icons
        XIcon,
        LogInIcon,

        // Organizer
        Organizer,
        OrganizerAttachmentsBlock,
        OrganizerMembersBlock,
        OrganizerBranchesBlock,
    },

    mixins: [ParentMixin],

    props: {
        id: {
            type: Number,
            required: true,
        },
    },

    async setup(props) {
        const saleActivityModal = useModal(SaleActivityModal)
        const { hasPermission, useModel } = useContext()

        const { record: sale, fetch } = useModel('Sale').useRecord({
            with: ['clientPreview'],
        }).destructable()

        await fetch(String(props.id))

        function openSaleActivityModal(salePk: PrimaryKey) {
            saleActivityModal.open({ salePk })
        }

        const isFraud = computed(() => {
            return sale.value?.clientPreview?.is_fraud ?? false
        })

        return {
            isFraud,
            hasPermission,
            openSaleActivityModal,
        }
    },

    data() {
        return {
            organizer: {
                organizerObject: 'Sale',
                organizerObjectModel: () => this.saleModel,
                tasks: {
                    generalTaskGroup: null,
                    ticketTaskGroup: null,
                    checkInTaskGroup: null,
                    customerSupportTaskGroup: null,
                    disclaimerTaskGroup: null,
                },
            },
        }
    },

    computed: {
        saleModel() {
            return this.saleHelper.saleModel
        },

        canEditSale() {
            return this.$can('edit', 'Sale', this.saleModel)
        },

        canReadClientMessages() {
            return this.$can('readClientMessages', 'Sale', this.saleModel)
        },

        saleVersion() {
            return this.saleHelper.saleVersion as unknown as AnyObject
        },

        isSaleAdjusted() {
            return !!this.saleHelper?.isSaleAdjusted
        },

        isSaleClosed() {
            return !!this.saleVersion?.is_sale_closed
        },

        salePermission() {
            const rule = !this.isSaleAdjusted || !this.isSaleClosed

            // @ts-ignore
            return this.$can('manage', this.organizer.organizerObject) ? false : rule
        },

        canEditFiles() {
            // @ts-ignore
            return this.$can('chatUploadFile', 'Sale', this.saleModel) && !this.salePermission
        },

        isComparing() {
            return this.saleHelper.isComparing
        },

        saleHelper() {
            return new SaleHelper(this.id)
        },

        comparedSaleHelper() {
            const firstToCompare = this.saleHelper.compared.at(0)

            if (! firstToCompare) {
                return
            }

            return new SaleHelper(firstToCompare)
        },
    },

    mounted() {
        watchOnce(() => this.saleVersion, () => {
            this.fetchTaskGroups()
        })
    },

    methods: {
        openOrganizer() {
            this.$showModal(SaleOrganizerSideModal, {
                position: 'right',
            }, {
                saleHelper: this.saleHelper,
                canEditFiles: this.canEditFiles,
                tasks: this.organizer.tasks,
            })
        },

        fetchTaskGroups() {
            for (const group in this.organizer.tasks) {
                this.organizer.tasks[group] = null

                if (this.saleVersion[group]) {
                    this.$models.TaskGroupModel.findByPK(this.saleVersion[group].id).then(({ result }) => {
                        this.organizer.tasks[group] = result
                    })
                }
            }
        },

        updateTaskGroups() {
            this.fetchTaskGroups()
        },
    },
})
</script>
