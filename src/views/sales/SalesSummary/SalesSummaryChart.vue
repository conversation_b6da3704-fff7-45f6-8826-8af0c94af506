<template>
    <Line
        :data="chartData"
        :options="chartOptions"
    />
</template>

<script setup lang="ts">
import { Line } from 'vue-chartjs'
import type { ChartOptions } from 'chart.js'
import {
    Chart as ChartJS,
    Title,
    Tooltip,
    Legend,
    LineElement,
    PointElement,
    LinearScale,
    TimeScale,
    Filler,
    CategoryScale,
} from 'chart.js'
import 'chartjs-adapter-date-fns'

const props = defineProps<{
    points: PointData[];
}>()

const { isDark } = useDarkMode()

const verticalLinePlugin = {
    id: 'verticalLine',
    beforeDraw(chart: any) {
        const tooltip = chart.tooltip

        if (tooltip?._active?.length) {
            const ctx = chart.ctx
            const x = tooltip.caretX
            const minY = Math.min(...tooltip._active.map((el: any) => el.element.y))
            const bottomY = chart.scales.y.bottom

            ctx.save()
            ctx.beginPath()
            ctx.setLineDash([5, 5])
            ctx.moveTo(x, minY)
            ctx.lineTo(x, bottomY)
            ctx.lineWidth = 1
            ctx.strokeStyle = isDark.value ? '#E2E8F0' : '#94A3B8'
            ctx.stroke()
            ctx.restore()
        }
    },
}

ChartJS.register(
    Title,
    Tooltip,
    Legend,
    LineElement,
    PointElement,
    LinearScale,
    TimeScale,
    Filler,
    CategoryScale,
    verticalLinePlugin,
)

onUnmounted(() => {
    ChartJS.unregister(verticalLinePlugin)
})

type PointData = {
    day: Date;
    count: number;
    tooltip: string;
};

const chartData = computed(() => ({
    labels: props.points.map(p =>
        new Date(p.day).toLocaleDateString('en-US', {
            month: 'long',
            day: 'numeric',
            year: '2-digit',
        }),
    ),
    datasets: [
        {
            label: 'Base period',
            data: props.points.map(p => p.count),
            fill: true,
            borderColor: 'rgba(59, 118, 246, 1)',
            tension: 0.3,
            pointBackgroundColor: 'rgba(0,0,0,0)',
            pointBorderColor: 'rgba(0,0,0,0)',
            pointHoverBackgroundColor: isDark.value ? 'rgb(255, 255, 255)' : 'rgba(41, 49, 69, 1)',
            borderWidth: 1.5,
            backgroundColor: (ctx: any) => {
                const { chart } = ctx
                const { ctx: canvasCtx, chartArea } = chart

                if (!chartArea) return
                const gradient = canvasCtx.createLinearGradient(0, chartArea.top, 0, chartArea.bottom)
                gradient.addColorStop(0, 'rgba(59, 118, 246, 0.4)')
                gradient.addColorStop(1, 'rgba(59, 118, 246, 0)')

                return gradient
            },
        },
    ],
}))

const chartOptions: ChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
        mode: 'index',
        intersect: false,
    },
    normalized: true,
    scales: {
        x: {
            display: true,
            ticks: {
                color: isDark.value ? '#FFFFFF' : '#616F96',
            },
        },
        y: {
            grid: {
                display: true,
                color: isDark.value ? '#4D597C' : '#E2E8F0',
            },
            ticks: {
                color: isDark.value ? '#FFFFFF' : '#616F96',
                maxTicksLimit: 6,
                autoSkip: false,
            },
            beginAtZero: true,
        },
    },
    plugins: {
        legend: {
            display: false,
            labels: { color: '#FFFFFF' },
        },
        tooltip: {
            displayColors: false,
            callbacks: {
                label: (context: any) => {
                    const index = context.dataIndex
                    const point = props.points[index]
                    const value = point.count

                    if (value === 0 || value === null || !point.tooltip) return ''

                    return point.tooltip
                },
            },
        },
    },
}
</script>
