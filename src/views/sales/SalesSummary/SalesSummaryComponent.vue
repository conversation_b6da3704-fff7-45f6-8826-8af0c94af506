<!-- This component used in v2 -->
<!-- TODO: migrate to v2 -->
<template>
    <div class="sales-summary">
        <div class="flex justify-between items-center px-4 py-2">
            <div class="flex items-center">
                <h4 class="m-0">
                    Sales summary
                </h4>
                <span v-if="showFutureProfit" class="ml-4 mt-0.5 text-xs text-theme-33">{{
                    concatenateBasePeriodLabel }} & {{ concatenateProfitPeriodLabel }}</span>
                <span v-else class="ml-4 mt-0.5 text-xs text-theme-33">{{
                    selectedToCompareLabel
                }} & compared to {{ summaryRangeComputed }}</span>
            </div>
            <div
                v-if="$can('manage', 'all')"
                class="extraProfit flex flex-col gap-0.5 text-xs badge --primary h-[40px] p-4"
            >
                <div class="text-md">
                    Extra profits
                </div>
                <div class="flex gap-2">
                    <span>Negative: {{ Number.formatMoney(salesSummaryComputed.extraNegativeProfit) || '0' }}</span>
                    <span>Positive: {{ Number.formatMoney(salesSummaryComputed.extraPositiveProfit) || '0' }}</span>
                    <span>Total: {{ Number.formatMoney(salesSummaryComputed.extraTotalProfit) || '0' }}</span>
                </div>
            </div>

            <div class="flex space-x-2">
                <InputLayoutWrapper
                    v-if="enableSplits"
                    v-model="form.field.show_with_splits"
                    class="mr-4 flex items-center"
                >
                    <InputLabelWrapper v-tooltip="'Calculation considering split sales'" class="cursor-pointer select-none text-xs">
                        Split sales
                    </InputLabelWrapper>
                    <FormCheckBox class="form-check-input border ml-2" @change="onInputChange" />
                </InputLayoutWrapper>

                <div class="flex text-xs items-center">
                    Future profit:
                    <InputSwitch
                        v-model="showFutureProfit"
                        size="xs"
                        class="ml-2"
                    />
                </div>
                <div v-if="showFutureProfit" class="ml-auto flex items-center space-x-2">
                    <span class="text-xs">Base period:</span>
                    <InputDateRange
                        v-model="calculation_base_period"
                        :date-format="'dd.MM.yyyy'"
                        size="small"
                        class="--calendar"
                        @click="handleChangeBasePeriod"
                    />
                    <span class="text-xs">Profit period:</span>
                    <InputDateRange
                        v-model="calculation_profit_period"
                        :date-format="'dd.MM.yyyy'"
                        size="small"
                        class="--calendar"
                        @click="handleChangeProfitPeriod"
                    />
                </div>

                <div v-else class="flex items-center justify-end">
                    <div class="text-xs">
                        Compare with:
                    </div>
                    <InputLayoutWrapper
                        v-if="!isAgent"
                        v-model="form.field.executor_pk"
                        class="ml-2"
                    >
                        <FormSelectTeleport
                            class="form-control form-control-sm"
                            @input-change="onInputChange"
                        />
                    </InputLayoutWrapper>

                    <InputLayoutWrapper
                        v-model="form.field.sale_at"
                        @click="selectedToCompareRangeLabel=null"
                    >
                        <InputLabelWrapper class="btn btn-outline-secondary btn-sm box ml-2">
                            {{ selectedToCompareLabel }}
                            <ChevronDownIcon class="w-4 h-4 ml-1.5" />
                        </InputLabelWrapper>
                        <FormDateRangeInput
                            :is-button="true"
                            :is-helper="true"
                            placement="bottom-end"
                            @input-change="onInputChange"
                            @label-select="labelSelect"
                        />
                    </InputLayoutWrapper>
                </div>
            </div>
        </div>
        <div class="sales-summary-body">
            <SalesSummaryItemComponent
                v-for="item in summaryComponents"
                :key="item.key"
                :component-key="item.key"
                :label="item.label"
                :markup-percent="markupPercentsComputed[item.percentKey]"
                :sales-summary="salesSummaryComputed[item.key]"
                :sales-to-compare="salesToCompareComputed[item.key]"
                :sales-to-compare-day="salesToCompareComputedDay[item.key]"
                :day-percent="markupPercentsComputed[item.dayKey]"
                :base-period-sales="baseSalesComputed[item.key]"
                :predicted-sales="predictionComputed[item.key]"
                :is-compare-enabled="isCompareEnabledComputed"
                :show-second-compare="showSecondCompare"
                :show-future-profit="showFutureProfit"
                @open-chart="handleOpenChart"
            />
        </div>
        <div v-if="summaryItem">
            <hr>
            <div class="card card--bordered card__body flex flex-col flex-[7] p-5">
                <div class="flex pl-7 text-sm justify-between font-semibold mb-2 ">
                    Visual comparison chart: {{ summaryItemLabel }}
                    <AppButton class="--outline --only dark:bg-dark-1 dark:hover:bg-dark-4" @click="hideChart">
                        <XIcon class="text-primary dark:text-white" />
                    </AppButton>
                </div>
                <div class="pl-7 h-[242px]">
                    <div v-if="loading" class="flex items-center justify-center h-full gap-2">
                        <Loader /> Loading summary chart..
                    </div>
                    <SalesSummaryChart
                        v-else-if="filterRange"
                        :points="chartData"
                    />
                    <div v-else class="flex items-center justify-center h-full">
                        Please select a valid date range
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { ChevronDownIcon } from '@zhuowenli/vue-feather-icons'
import ChildMixin from '@/lib/mixin/ComponentRelation/ChildMixin'
import InputLayoutWrapper from '@/components/Form/FormWrappers/InputLayoutWrapper'
import FormDateRangeInput from '@/components/Form/FormField/FormDateRangeInput'
import DateRangeField from '@/lib/FormField/DateRangeField'
import FormHelper from '@/lib/core/helper/FormHelper'
import InputLabelWrapper from '@/components/Form/FormWrappers/InputLabelWrapper'
import SelectField from '@/lib/FormField/SelectField'
import FormSelectTeleport from '@/components/Form/FormField/FormSelectTeleport'
import Debounce from '@/lib/core/helper/Debounce'
import SalesSummaryItemComponent from '@/views/sales/SalesSummary/SalesSummaryItemComponent'
import {
    formatRange,
    getDateRangeRequestConditions,
    getDaysInRange,
    getRangeLabel,
    getRangeThisMonth,
    getSimilarRange,
    getUTCOffset,
    moveDateRangeFromTimeZone,
    samePeriodInPast,
} from '@/lib/core/helper/DateHelper'
import WebUser from '@/lib/service/WebUser'
import CheckBoxField from '@/lib/FormField/CheckBoxField'
import FormCheckBox from '@/components/Form/FormField/FormCheckBox.vue'
import { formatDate } from '@vueuse/core'
import ArrayConditionHelper from '~/lib/Helper/ArrayCondition/ArrayConditionHelper'
import SalesSummaryChart from '@/views/sales/SalesSummary/SalesSummaryChart.vue'
import { preventDuplication } from '~/lib/Helper/PromiseHelper'

export default {
    name: 'SalesSummaryComponent',

    components: {
        SalesSummaryChart,
        FormCheckBox,
        SalesSummaryItemComponent,
        FormSelectTeleport,
        InputLabelWrapper,
        FormDateRangeInput,
        InputLayoutWrapper,
        ChevronDownIcon,
    },

    mixins: [ChildMixin],

    // parentName: ['SalesController', 'SalesCommunicationController'],
    props: {
        searchController: {
            type: Object,
            required: true,
        },
    },

    async setup() {
        const { useModel } = useNewContext('selected')
        const saleModel = useModel('Sale')
        const chartData = ref([])

        return {
            saleModel,
            chartData,
        }
    },

    data() {
        return {
            showSecondCompare: false,
            showFutureProfit: false,
            selectedToCompareRangeLabel: null,
            basePeriodLabel: null,
            profitPeriodLabel: null,
            salesSummary: this.$models.SaleSummaryModel.list(),
            salesSummaryToCompare: this.$models.SaleSummaryModel.list(),
            salesSummaryToCompareDay: this.$models.SaleSummaryModel.list(),
            salesDebounce: Debounce.create(300),
            calculation_base_period: moveDateRangeFromTimeZone(getRangeThisMonth(), useAppTimezone().value),
            calculation_profit_period: moveDateRangeFromTimeZone(getRangeThisMonth(), useAppTimezone().value),
            form: new FormHelper({
                sale_at: new DateRangeField(false, 'Sale date'),
                executor_pk: new SelectField(false, 'Executor', {
                    items: [],
                    emptyLabel: '-----------',
                }),

                show_with_splits: new CheckBoxField('Show with splits', 0, {
                    isRequired: false,
                }),
            }),

            markupPercentsComputed: {
                ticketsCountPercent: 0,
                netPriceSumPercent: 0,
                sellPriceSumPercent: 0,
                feesSumPercent: 0,
                profitSumPercent: 0,
                insuranceSumPercent: 0,
                tipsSumPercent: 0,
                ticketProfitPercent: 0,
                insuranceProfitPercent: 0,
                tipsProfitPercent: 0,

                ticketsCountPercentDay: 0,
                netPriceSumPercentDay: 0,
                sellPriceSumPercentDay: 0,
                feesSumPercentDay: 0,
                profitSumPercentDay: 0,
                insuranceSumPercentDay: 0,
                tipsSumPercentDay: 0,
                ticketProfitPercentDay: 0,
                insuranceProfitPercentDay: 0,
                tipsProfitPercentDay: 0,
            },

            predictionComputed: {
                ticketsCount: { predictedValue: 0 },
                netPriceSum: { predictedValue: 0 },
                sellPriceSum: { predictedValue: 0 },
                feesSum: { predictedValue: 0 },
                profitSum: { predictedValue: 0 },
                insuranceSum: { predictedValue: 0 },
                tipsSum: { predictedValue: 0 },
                ticketProfit: { predictedValue: 0 },
                insuranceProfit: { predictedValue: 0 },
                tipsProfit: { predictedValue: 0 },
            },

            summaryComponents: [
                {
                    label: 'Tickets',
                    key: 'ticketsCount',
                    percentKey: 'ticketsCountPercent',
                    dayKey: 'ticketsCountPercentDay',
                }, {
                    label: 'Net Price',
                    key: 'netPriceSum',
                    percentKey: 'netPriceSumPercent',
                    dayKey: 'netPriceSumPercentDay',
                }, {
                    label: 'Sell Price',
                    key: 'sellPriceSum',
                    percentKey: 'sellPriceSumPercent',
                    dayKey: 'sellPriceSumPercentDay',
                }, {
                    label: 'Fees',
                    key: 'feesSum',
                    percentKey: 'feesSumPercent',
                    dayKey: 'feesSumPercentDay',
                }, {
                    label: 'TP',
                    key: 'insuranceProfit',
                    percentKey: 'insuranceProfitPercent',
                    dayKey: 'insuranceProfitPercentDay',
                }, {
                    label: 'Tips',
                    key: 'tipsProfit',
                    percentKey: 'tipsProfitPercent',
                    dayKey: 'tipsProfitPercentDay',
                }, {
                    label: 'Tickets GP',
                    key: 'ticketProfit',
                    percentKey: 'ticketProfitPercent',
                    dayKey: 'ticketProfitPercentDay',
                }, {
                    label: 'Total GP',
                    key: 'profitSum',
                    percentKey: 'profitSumPercent',
                    dayKey: 'profitSumPercentDay',
                },
            ],

            predictedSalesSummary: this.$models.SaleSummaryModel.list(),
            summaryItem: null,
            loading: false,
        }
    },

    computed: {
        currentUserPositionId() {
            return WebUser.positionID
        },

        currentUserTeamId() {
            return WebUser.teamID
        },

        currentUserDepartmentId() {
            return WebUser.departmentID
        },

        positionsDictionary() {
            return useGeneralDictionary('Position')
        },

        departmentDictionary() {
            return useGeneralDictionary('Department')
        },

        isAgent() {
            const position = this.positionsDictionary.find(String(this.currentUserPositionId))

            if (!position) {
                // to disable select
                return true
            }

            return position.system_name === 'agent'
        },

        isSupervisor() {
            const position = this.positionsDictionary.find(String(this.currentUserPositionId))

            if (!position) {
                return false
            }

            return position.system_name === 'supervisor'
        },

        isSalesDepartment() {
            const department = this.departmentDictionary.find(String(this.currentUserDepartmentId))

            if (!department) {
                return false
            }

            return department.system_name === 'sales'
        },

        controller() {
            return this.searchController
        },

        enableSplits() {
            const tag = this.controller.activeTags.find(tag => tag.label === 'Executor')

            return !!tag && !tag.values.includes('unassigned')
        },

        filterRange() {
            return this.controller.tags.sale_at.normalizedValues?.[0]
        },

        selectedToCompareLabel() {
            if (this.selectedToCompareRangeLabel) {
                return this.selectedToCompareRangeLabel
            }

            const range = this.form.field.sale_at.value

            if (!range) {
                return 'Not selected'
            }

            return `Selected: ${formatRange(range, 'dd.MM.yyyy')}`
        },

        getBasePeriod() {
            const period = this.calculation_base_period

            if (period.end > new Date() && period.start < new Date()) {
                return { start: period.start, end: new Date() }
            }

            return { start: period.start, end: period.end }
        },

        concatenateBasePeriodLabel() {
            const basePeriod = this.getBasePeriod
            const startDate = formatDate(basePeriod.start, 'DD.MM.YYYY')
            const endDate = formatDate(basePeriod.end, 'DD.MM.YYYY')

            return basePeriod ? `Base period: ${startDate} - ${endDate}` : ''
        },

        concatenateProfitPeriodLabel() {
            const profitPeriod = this.calculation_profit_period
            const startDate = formatDate(profitPeriod.start, 'DD.MM.YYYY')
            const endDate = formatDate(profitPeriod.end, 'DD.MM.YYYY')

            return profitPeriod ? `Profit period: ${startDate} - ${endDate}` : ''
        },

        summaryRangeComputed() {
            const range = this.filterRange

            if (!range) {
                return 'Not selected'
            }

            return formatRange(range, 'dd.MM.yyyy')
        },

        isCompareEnabledComputed() {
            let enabled = false

            Object.keys(this.form.data).forEach(key => {
                if (!!this.form.data[key]) enabled = true
            })

            return enabled
        },

        salesSummaryComputed() {
            return this.salesSummary?.records[0] ?? {}
        },

        salesToCompareComputed() {
            return this.salesSummaryToCompare?.records[0] ?? {}
        },

        salesToCompareComputedDay() {
            return this.salesSummaryToCompareDay?.records[0] ?? {}
        },

        baseSalesComputed() {
            return this.predictedSalesSummary?.records[0] ?? {}
        },

        predictedSalesComputed() {
            return this.predictedSalesSummary?.records[0] ?? {}
        },

        summaryItemLabel() {
            return this.summaryComponents.find(item => item.key === this.summaryItem)?.label
        },
    },

    watch: {
        'controller.activeTags': function() {
            this.setSummaryRanges()
            this.getSales()
            this.calculateLabel()
            this.getProfit()

            if (this.summaryItem) {
                this.fetchChartData()
            }
        },

        'showFutureProfit': function() {
            if (this.showFutureProfit) {
                this.hideChart()
            }
        },
    },

    mounted() {
        // TODO: maybe give sorting to backend (by ability of asking user)
        if (this.isSupervisor) {
            this.form.field.executor_pk.items = this.$models.AgentModel.getTeamList(this.currentUserTeamId)
        } else {
            this.form.field.executor_pk.items = this.$models.AgentModel.getAllList()
        }

        this.setSummaryRanges()
        this.getSales()
        this.calculateLabel()
        this.getProfit()
    },

    methods: {
        calculateLabel() {
            if (!this.form.field.sale_at.value) {
                this.labelSelect(null)

                return
            }

            this.labelSelect(getRangeLabel(this.form.field.sale_at.value, useAppTimezone().value) || null)
        },

        setSummaryRanges() {
            const summaryRange = this.filterRange

            if (!summaryRange) {
                return
            }

            this.form.field.sale_at.value = samePeriodInPast(summaryRange)
            this.calculateLabel()
        },

        onInputChange() {
            this.getSales()
        },

        handleChangeBasePeriod() {
            this.getProfit()
        },

        handleChangeProfitPeriod() {
            this.getProfit()
        },

        labelSelect(e) {
            this.selectedToCompareRangeLabel = e
        },

        getExcludeForRequest() {
            const form = this.form.data

            const exclude = []
            Object.keys(form).forEach(fieldKey => {
                const value = form[fieldKey]

                if (fieldKey === 'sale_at' && value) {
                    exclude.push(fieldKey)
                } else if (fieldKey === 'executor_pk' && value) {
                    exclude.push('executor_pk')
                }
            })

            return exclude
        },

        getSales() {
            if (!this.form.data.sale_at) {
                return
            }

            const optionalParams = [
                ['show_with_splits', this.form.data.show_with_splits],
            ]

            const exclude = this.getExcludeForRequest()

            this.salesDebounce(() => {
                this.salesSummary.request(optionalParams).where(andWhere => {
                    this.controller.applyCondition(andWhere)
                }).get().then(() => {
                    const form = this.form.data
                    this.salesSummaryToCompare.request(optionalParams).where(andWhere => {
                        if (exclude.length > 0) {
                            this.controller.applyCondition(andWhere, null, { exclude: exclude })

                            exclude.forEach(key => {
                                if (key === 'sale_at') {
                                    const range = moveDateRangeFromTimeZone(this.form.field.sale_at.value, WebUser.currentTimeZone)

                                    andWhere.and(getDateRangeRequestConditions(range, 'sale_at'))
                                }

                                if (key === 'executor_pk') {
                                    andWhere.eq('executor_pk', form.executor_pk)
                                }
                            })
                        }
                    }).get().then(() => {
                        setTimeout(() => {
                            this.markupPercentsCalculate()
                        }, 100)
                    })

                    // Similar range

                    if (!this.filterRange) {
                        return
                    }

                    const similarRange = getSimilarRange(this.form.field.sale_at.value, this.filterRange, WebUser.currentTimeZone)

                    this.showSecondCompare = !!similarRange

                    if (!similarRange) {
                        return
                    }

                    this.salesSummaryToCompareDay.request(optionalParams).where(andWhere => {
                        if (exclude.length > 0) {
                            this.controller.applyCondition(andWhere, null, { exclude: exclude })

                            exclude.forEach(key => {
                                if (key === 'sale_at') {
                                    const range = moveDateRangeFromTimeZone(similarRange, WebUser.currentTimeZone)

                                    andWhere.and(getDateRangeRequestConditions(range, 'sale_at'))
                                }

                                if (key === 'executor_pk') {
                                    andWhere.eq('executor_pk', form.executor_pk)
                                }
                            })
                        }
                    }).get().then(() => {
                        setTimeout(() => {
                            this.markupPercentsCalculateDay()
                        }, 100)
                    })
                })
            })
        },

        getProfit() {
            this.predictedSalesSummary.request().where(query => {
                query.and(getDateRangeRequestConditions(this.calculation_base_period, 'sale_at'))

                this.controller.applyCondition(query)
            }).get().then(apiResponse => {
                this.markupBaseCalculate()
                this.markupProfitCalculate(this.getBasePeriod,
                                           this.calculation_profit_period)
            })
        },

        calculateMarkupPercent(currentValue, lastValue) {
            if (currentValue === 0) {
                if (lastValue > 0) {
                    return -100
                } else if (lastValue === 0) {
                    return 0
                }
            }

            if (lastValue === 0) return 100

            if (currentValue - lastValue === 0) return 0
            const change = currentValue - lastValue

            return parseFloat((Math.abs((currentValue - lastValue) / lastValue * 100) * (change >= 0 ? 1 : -1)).toFixed(2))
        },

        markupPercentsCalculateDay() {
            for (const [key, lastValue] of Object.entries(this.salesToCompareComputedDay)) {
                const currentValue = this.salesSummaryComputed[key]
                this.markupPercentsComputed[`${key}PercentDay`] = this.calculateMarkupPercent(Number(currentValue), Number(lastValue))
            }
        },

        markupPercentsCalculate() {
            for (const [key, lastValue] of Object.entries(this.salesToCompareComputed)) {
                const currentValue = this.salesSummaryComputed[key]
                this.markupPercentsComputed[`${key}Percent`] = this.calculateMarkupPercent(Number(currentValue), Number(lastValue))
            }
        },

        markupBaseCalculate() {
            for (const [key, value ] of Object.entries(this.predictedSalesComputed)) {
                this.predictedSalesComputed[key] = value
            }
        },

        markupProfitCalculate(baseRange, profitRange) {
            const predictedValues = {}

            for (const [key, value ] of Object.entries(this.predictedSalesComputed)) {
                const averagePerDay =  value / (getDaysInRange(baseRange) + 1)
                const profitValue = parseFloat((averagePerDay * (getDaysInRange(profitRange) + 1)).toFixed(2))
                predictedValues[key] = {
                    predictedValue: profitValue,
                }
            }

            this.predictionComputed = predictedValues
        },

        handleOpenChart(key) {
            this.summaryItem = key
            this.fetchChartData()
        },

        hideChart() {
            this.summaryItem = null
            this.chartData = []
        },

        async fetchChartData() {
            if (!this.summaryItem || !this.filterRange) return
            await preventDuplication(async () => {
                const where = new ArrayConditionHelper()
                this.searchController.applyCondition(where)

                const offset = getUTCOffset(useAppTimezone().value)

                const response = await this.saleModel.actions.getSummaryChartData({
                    search_params: where.getParams(),
                    summary_item: this.summaryItem,
                    utc_offset: offset,
                })

                const rawData = response.data ?? []

                this.chartData = rawData.map(([ts, count]) => {
                    const day = new Date(Number(ts))

                    return {
                        day,
                        count,
                        tooltip: `Amount: ${count.toLocaleString()}`,
                    }
                })
            })
        },
    },
}
</script>

<style lang="postcss" scoped>
.extraProfit {
    position: absolute;
    top: -14px;
    right: 50%;
    margin-right: -200px;
    width: 400px;
    text-align: center;
}
</style>

