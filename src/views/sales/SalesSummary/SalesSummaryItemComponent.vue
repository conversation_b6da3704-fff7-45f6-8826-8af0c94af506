<template>
    <div class="sales-summary-item">
        <div class="sales-summary-item-head">
            <h6 class="sales-summary-item-title">
                {{ label }}
            </h6>
            <AppButton
                v-if="!showFutureProfit && hasPermission('manage', 'all')"
                v-tooltip="'Show in chart'"
                class="--outline --only dark:bg-dark-1 dark:hover:bg-dark-4"
                @click="openChart"
            >
                <BarChartIcon class="text-primary dark:text-white" />
            </AppButton>
        </div>
        <div class="sales-summary-item-amounts">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                class="flex-none w-2 mr-1.5 text-theme-2"
                viewBox="0 0 8 21"
            ><path
                fill="currentColor"
                d="M0 .5a.5.5 0 0 1 1 0v2a.5.5 0 0 1-1 0v-2ZM0 5.5a.5.5 0 0 1 1 0v2a.5.5 0 0 1-1 0v-2ZM0 10.5a.5.5 0 0 1 1 0v2a.5.5 0 0 1-1 0v-2ZM0 15.5a.5.5 0 0 1 1 0v3a.5.5 0 0 1-1 0v-3Z"
            /><path
                fill="currentColor"
                d="M0 .5A.5.5 0 0 1 .5 0h2a.5.5 0 0 1 0 1h-2A.5.5 0 0 1 0 .5ZM0 18.5a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5Z"
            /><path
                fill="currentColor"
                d="M7.86 18.837a.483.483 0 0 1-.68 0l-2.04-2.023a.474.474 0 0 1 0-.674.483.483 0 0 1 .68 0l2.04 2.023a.474.474 0 0 1 0 .674Z"
            /><path
                fill="currentColor"
                d="M7.86 18.163a.474.474 0 0 1 0 .674L5.82 20.86a.483.483 0 0 1-.68 0 .474.474 0 0 1 0-.674l2.04-2.023a.483.483 0 0 1 .68 0Z"
            /></svg>

            <div v-if="showFutureProfit" class="sales-summary-item-box">
                <div class="sales-summary-item-amount text-theme-2">
                    <span
                        class="sales-summary-item-compare"
                    >{{ Number.formatMoney(basePeriodSales) }}</span>
                </div>
                <div class="sales-summary-item-amount">
                    {{ Number.formatMoney(predictedSales.predictedValue) }}
                </div>
            </div>
            <div v-else class="sales-summary-item-box">
                <div class="sales-summary-item-amount text-theme-2">
                    <span
                        v-if="isCompareEnabled"
                        class="sales-summary-item-compare"
                    >{{ Number.formatMoney(salesToCompare) }}</span>
                </div>
                <div class="sales-summary-item-amount">
                    {{ Number.formatMoney(salesSummary) || '0' }}
                    <span
                        class="sales-summary-item-graph"
                        :class="{
                            '--danger': markupPercent < 0,
                            '--success': markupPercent > 0,
                        }"
                    >
                        {{ markupPercent > 0 ? '+' : '' }}{{ markupPercent }}%
                        <ArrowUpIcon
                            v-if="markupPercent > 0"
                            class="w-2.5 h-2.5 ml-1"
                        />
                        <ArrowDownIcon
                            v-else-if="markupPercent < 0"
                            class="w-2.5 h-2.5 ml-1"
                        />
                    </span>
                </div>
            </div>
        </div>
        <div
            v-if="showSecondCompare && !showFutureProfit"
            class="sales-summary-item-amounts"
        >
            <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                class="flex-none w-2 mr-1.5 text-theme-2"
                viewBox="0 0 8 21"
            ><path
                fill="currentColor"
                d="M0 .5a.5.5 0 0 1 1 0v2a.5.5 0 0 1-1 0v-2ZM0 5.5a.5.5 0 0 1 1 0v2a.5.5 0 0 1-1 0v-2ZM0 10.5a.5.5 0 0 1 1 0v2a.5.5 0 0 1-1 0v-2ZM0 15.5a.5.5 0 0 1 1 0v3a.5.5 0 0 1-1 0v-3Z"
            /><path
                fill="currentColor"
                d="M0 .5A.5.5 0 0 1 .5 0h2a.5.5 0 0 1 0 1h-2A.5.5 0 0 1 0 .5ZM0 18.5a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5Z"
            /><path
                fill="currentColor"
                d="M7.86 18.837a.483.483 0 0 1-.68 0l-2.04-2.023a.474.474 0 0 1 0-.674.483.483 0 0 1 .68 0l2.04 2.023a.474.474 0 0 1 0 .674Z"
            /><path
                fill="currentColor"
                d="M7.86 18.163a.474.474 0 0 1 0 .674L5.82 20.86a.483.483 0 0 1-.68 0 .474.474 0 0 1 0-.674l2.04-2.023a.483.483 0 0 1 .68 0Z"
            /></svg>
            <div class="sales-summary-item-box">
                <div class="sales-summary-item-amount text-theme-2">
                    <span
                        v-if="isCompareEnabled"
                        class="sales-summary-item-compare"
                    >{{ Number.formatMoney(salesToCompareDay) }}</span>
                </div>
                <div class="sales-summary-item-amount">
                    {{ Number.formatMoney(salesSummary) || '0' }}
                    <span
                        class="sales-summary-item-graph"
                        :class="{
                            '--danger': dayPercent < 0,
                            '--success': dayPercent > 0,
                        }"
                    >
                        {{ dayPercent > 0 ? '+' : '' }}{{ dayPercent }}%
                        <ArrowUpIcon
                            v-if="dayPercent > 0"
                            class="w-2.5 h-2.5 ml-1"
                        />
                        <ArrowDownIcon
                            v-else-if="dayPercent < 0"
                            class="w-2.5 h-2.5 ml-1"
                        />
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { ArrowDownIcon, ArrowUpIcon } from '@zhuowenli/vue-feather-icons'

export default {
    name: 'SalesSummaryItemComponent',
    components: {
        ArrowUpIcon,
        ArrowDownIcon,
    },

    props: {
        isCompareEnabled: {
            type: Boolean,
            default: false,
        },

        showSecondCompare: {
            type: Boolean,
            default: true,
        },

        componentKey: {
            type: String,
            default: 'profitSum',
        },

        label: {
            type: String,
            required: true,
        },

        salesSummary: {
            type: Number,
            default: 0,
        },

        salesToCompare: {
            type: Number,
            default: 0,
        },

        markupPercent: {
            type: Number,
            default: 0,
        },

        dayPercent: {
            type: Number,
            default: 0,
        },

        salesToCompareDay: {
            type: Number,
            default: 0,
        },

        showFutureProfit: {
            type: Boolean,
            default: false,
        },

        basePeriodSales: {
            type: Number,
            default: 0,
        },

        predictedSales: {
            predictedValue: {
                type: Number,
                default: 0,
            },
        },
    },

    emits: ['openChart'],

    setup() {
        const { hasPermission } = useContext()

        return {
            hasPermission,
        }
    },

    methods: {
        openChart() {
            this.$emit('openChart', this.componentKey)
        },
    },
}
</script>
