import ContextMenu from '@/components/ContextMenu/ContextMenu.vue'
import { useCurrentElement } from '@vueuse/core'
import { type App, markRaw, nextTick } from 'vue'
import type { Component, MaybeRefOrGetter } from 'vue'
import type { AnyObject, Point } from '@/types'
import type { ModalOptions } from '~/composables/useModal'
import { usePluginProvide } from '~/lib/Helper/PluginHelper'
import { AnyWorkspace } from '~/service/WorkspaceService'

export type ContextMenuOption = {
    // Text to display
    text?: string,
    // Icon to display
    icon?: string | Component,
    // Class to apply to item
    class?: string | string[] | AnyObject,
    // On click handler
    onClick?: (event: PointerEvent) => void,
    // Keeps context menu opened on option select
    keep?: boolean,
    // Don't stop event propagation
    stop?: boolean,
    // Tooltip to display
    tooltip?: string | null
    //
    loading?: MaybeRefOrGetter<boolean>
}

export const openContextMenu = async function(
    event: PointerEvent | Point,
    options: ContextMenuOption[],
    config: {
        component?: string | Component,
        disableNextTick?: boolean
    } & ModalOptions = {},
) {
    const key = 'context-menu-' + Date.now()
    const component = config.component ?? ContextMenu
    const group = config.group ?? 'context-menu'

    useExistingModal({ group })?.close()

    const modal = useModal(component, {}, Object.assign({
        key,
        position: 'context-menu',
        group,
        workspace: AnyWorkspace,
        onMounted() {
            const el = useCurrentElement()?.value as HTMLElement

            let x: number
            let y: number

            const update = (x: number, y: number) => {
                el.style.left = x + 'px'
                el.style.top = y + 'px'
            }

            if (path) {
                x = (event as PointerEvent).clientX
                y = (event as PointerEvent).clientY
            } else {
                x = event.x
                y = event.y
            }

            update(x, y)

            if (!config.disableNextTick) {
                nextTick(() => {
                    const rect = el.getBoundingClientRect()

                    if (y + rect.height > window.innerHeight) {
                        y = window.innerHeight - rect.height - 10
                    }

                    if (x + rect.width > window.innerWidth) {
                        x = window.innerWidth - rect.width - 10
                    }

                    update(x, y)
                })
            }
        },
        onUnmounted() {
            clearListeners()
        },
    }, config))

    const scrollListeners: [HTMLElement, () => void][] = []

    const path: EventTarget[] | null = 'composedPath' in event ? event.composedPath() : null

    // @todo When Point is passed, then modal is not hidden on scroll. Do something with it

    if (path && path.length) {
        (path as HTMLElement[]).forEach((el) => {
            if (el.scrollWidth > el.clientWidth || el.scrollHeight > el.clientHeight) {
                const overflow = window.getComputedStyle(el).overflow

                if (overflow === 'auto' || overflow === 'scroll') {
                    const listener = () => {
                        modal.close()
                    }

                    scrollListeners.push([el, listener])
                    el.addEventListener('scroll', listener)
                }
            }
        })
    }

    const clearListeners = () => {
        scrollListeners.forEach(([el, listener]) => {
            el.removeEventListener('scroll', listener)
        })
    }

    return await modal.open({
        options: markRaw(options),
    })
}

const { provide } = usePluginProvide({
    $openContextMenu: openContextMenu,
})

export default {
    install(app: App) {
        provide(app)
    },
}

declare module 'vue' {
    interface ComponentCustomProperties {
        $openContextMenu: typeof openContextMenu,
    }
}
