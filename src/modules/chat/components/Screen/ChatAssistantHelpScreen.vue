<template>
    <div class="flex flex-col items-center justify-center gap-4 pt-2">
        <div>How can I help you</div>

        <AppButton
            v-for="prompt in prompts"
            :key="prompt"
            class="--primary --outline"

            @click="createMessage(prompt)"
        >
            {{ prompt }}
        </AppButton>
    </div>
</template>

<script setup lang="ts">
import type Chat from '@/modules/chat/lib/Chat/Chat'

const props = defineProps<{
    chat: Chat,
}>()

const prompts = [
    'PNR creation', 'Ticket voiding', 'Ticket exchange',
]

function createMessage(prompt: string) {
    props.chat.sendPredefinedMessage(prompt)
}
</script>

