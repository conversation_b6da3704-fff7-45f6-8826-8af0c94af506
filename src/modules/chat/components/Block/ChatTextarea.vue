<template>
    <QuillEditor
        ref="editor"
        :content="modelValue"
        class="chat__textarea"
        :options="editorOptions"
        @update:content="$emit('update:modelValue', $event)"
    />
</template>

<script lang="ts">
import { Quill, QuillEditor } from '@vueup/vue-quill'
import 'quill-mention'
import Clipboard from 'quill/modules/clipboard'
import type Chat from '@/modules/chat/lib/Chat/Chat'
import { ChatType } from '@/modules/chat/lib/Chat/Chat'
import QuillMentions from '@/lib/QuillMention/QuillMentions'
import { getChatPlaceholder } from '~/lib/Easter/ChatEaster'
import { isEmptyQuillDelta } from '@/modules/chat/lib/Helpers/ChatHelpers'
import Delta from 'quill-delta'
import { anyToFile } from '~/lib/Helper/File/FileHelper'
import type ChatWithAttachments from '@/modules/chat/lib/Chat/Extended/ChatWithAttachments'
import type { PropType } from 'vue'
import { ChatTextareaKeyBindingsOption } from '~/service/AppSettingsService'

export default defineComponent({
    name: 'ChatTextarea',

    components: {
        QuillEditor,
    },

    props: {
        modelValue: {
            type: [Array, Object],
            default: () => [],
        },

        chat: {
            type: Object as PropType<Chat>,
            required: true,
        },
    },

    emits: ['submit', 'update:modelValue', 'cancel', 'editLastMessage'],

    data() {
        const keyBindingsValue = useAppSettingsValue('chatTextareaKeyBindings')
        const handlers = {
            enter: (range) => {
                keyBindingsValue.value === ChatTextareaKeyBindingsOption.EnterSubmit
                    ? this.$emit('submit')
                    : this.quill.insertText(range.index, '\n')
            },

            ctrl_enter: () => keyBindingsValue.value === ChatTextareaKeyBindingsOption.EnterNewLine ? this.$emit('submit') : null,
            shift_enter: (range) => keyBindingsValue.value === ChatTextareaKeyBindingsOption.EnterSubmit ? this.quill.insertText(range.index, '\n') : null,

            // @TODO use and test these new handlers
            backspace: (range) => range.length === 0 ? this.quill.deleteText(range.index, 1) : null,
            arrow_up: () => {
                if (!isEmptyQuillDelta(this.modelValue) || this.chat.uploadList.length) {
                    return true
                }

                this.$emit('editLastMessage')

                return true
            },
        }

        const bindings = {
            esc: {
                key: 27,
                handler: () => this.$emit('cancel'),
            },

            editMessageWithArrowUp: {
                key: 38,
                handler: () => {
                    if (!isEmptyQuillDelta(this.modelValue) || this.chat.uploadList.length) {
                        return true
                    }

                    this.$emit('editLastMessage')

                    return true
                },
            },

            removeEmptyBlockWithBackspace: {
                key: 8,
                format: ['code-block'],
                collapsed: true,
                empty: true,
                handler: function(range, context) {
                    if (range.length === 0) {
                        this.quill.deleteText(range.index, 1)
                    }
                },
            },

            enter: {
                key: 13,
                handler: handlers.enter,
            },

            ctrl_enter: {
                key: 13,
                ctrlKey: true,
                handler: handlers.ctrl_enter,
            },

            shift_enter: {
                key: 13,
                shiftKey: true,
                handler: handlers.shift_enter,
            },
        }

        const placeholder = this.chat.type === ChatType.Assistant ? 'Ask me anything about…' : getChatPlaceholder('Say something ...')

        return {
            content: [],
            editorOptions: {
                contentType: 'delta',
                toolbar: 'disable',
                theme: 'snow',
                placeholder: placeholder,
                readOnly: false,
                modules: {
                    toolbar: false,
                    keyboard: { bindings },
                    clipboard: {
                        matchVisual: false,
                        matchers: [
                            [Node.ELEMENT_NODE, (node, delta) => {
                                const text = String(node.innerText)

                                if (this.chat.canUseAttachments) {
                                    const data = node.getAttribute('src')

                                    if (data?.match(/^data:[^;]+;base64,/)) {
                                        anyToFile(data, { workspace: getWorkspaceFromObject(this.chat.service.gateway) }).then((file) => {
                                            (this.chat as ChatWithAttachments).handleFileUpload([file])
                                        })
                                    }
                                }

                                return new Delta().insert(text + '\n')
                            }],
                        ],
                    },

                    mention: QuillMentions.init(Object.values(this.chat.mentionChars), () => this.quill, this.chat.me),
                },
            },
        }
    },

    computed: {
        quill() {
            return this.$refs.editor.getQuill()
        },
    },

    methods: {
        setContents(delta) {
            this.quill.setContents(delta, 'api')
            this.focus()
        },

        focus() {
            this.quill.focus()

            setTimeout(() => {
                const index = Math.max(0, this.quill.getText().length - 1)

                if (index === 0) {
                    return
                }

                this.quill.setSelection(index, 0)
            }, 10)
        },
    },

    expose: [
        'quill',
        'setContents',
        'focus',
    ],
})

class CustomClipboard extends Clipboard {
    public convert(html = null) {
        const delta = super.convert(html)

        const isCodeBlock = delta.ops.some((op) => {
            if (op.insert && typeof op.insert === 'string') {
                return op.insert.match(/\b\d{1,2}(JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)\b/gm)
            }
        })

        if (!isCodeBlock) {
            return delta
        }

        const ops = []

        ops.push()

        for (const op of delta.ops) {
            if (op.insert && typeof op.insert === 'string') {
                const lines = op.insert?.split('\n')

                for (const line of lines) {
                    ops.push({
                        insert: line,
                    })
                    ops.push({
                        insert: '\n',
                        attributes: {
                            'code-block': true,
                        },
                    })
                }
            } else {
                ops.push(op)
            }
        }

        delta.ops = ops

        return delta
    }
}

Quill.register('modules/clipboard', CustomClipboard, true)
</script>
