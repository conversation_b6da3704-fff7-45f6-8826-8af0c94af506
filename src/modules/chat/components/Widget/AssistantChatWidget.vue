<template>
    <div
        v-show="widgetIsVisible"
        class="floating_assistant-chat-widget"
        :class="{
            'floating_assistant-chat-widget--wide': wide,
        }"
    >
        <div
            v-show="wide"
            ref="handle"
            class="floating_assistant-chat-widget__handle"
        >
            <DragHandleIcon class="mx-auto" />
        </div>

        <div
            v-if="loading"
            class="floating_assistant-chat-widget__content"
        >
            <LoaderIcon class="chat__send-loader chat__send-loader--active" />
        </div>
        <ChatAssistantHelpScreen
            v-if="wide && service.chat && service.chat.activeScreen === ChatScreen.AssistantHelp"
            :chat="service.chat"
            class="absolute top-[71px] left-[2px] w-full z-50 bg-white dark:bg-dark-3 py-4"
            style="height: calc(100% - 116px); width: calc(100% - 4px);"
        />
        <div
            v-if="service.chat && wide"
            class="floating_assistant-chat-widget__content"
        >
            <ChatComponent
                :chat="service.chat"
                class="floating_assistant-chat-widget__item"
            >
                <template #header>
                    <div class="chat__header">
                        <div class="chat__header__icon">
                            <div class="chat__icon">
                                <div class="!stroke-2 bg-purple-900 text-white rounded-full w-full h-full flex items-center justify-center text-xs">
                                    TBC
                                </div>
                            </div>
                        </div>

                        <div class="chat__header__title flex-col justify-start items-start mr-auto">
                            <div class="chat__header__title">
                                {{ service.chat?.name }}
                            </div>
                            <!--                            <div class="chat__header__title text-gray-500 text-xs">-->
                            <!--                                You can ask me anything-->
                            <!--                            </div>-->
                        </div>

                        <div class="flex items-center gap-2">
                            <AppButton
                                v-if="isFullHeightMode"
                                class="--ghost --only --xs"
                                @click="minimizeWidget"
                            >
                                <Minimize2Icon />
                            </AppButton>
                            <AppButton
                                v-else
                                class="--ghost --only --xs"
                                @click="expandWidget"
                            >
                                <Maximize2Icon />
                            </AppButton>
                            <AppButton class="--ghost --only --xs" @click="collapseWidget">
                                <MinusIcon />
                            </AppButton>
                            <AppButton class="--ghost --only --xs" @click="terminateWidget">
                                <XIcon />
                            </AppButton>
                        </div>
                    </div>
                </template>
            </ChatComponent>
        </div>
        <div
            v-else
            class="floating_assistant-chat-widget__main-button"
            @click="toggleExpand"
        >
            HELP
        </div>
    </div>
</template>

<script setup lang="ts">
import DragHandleIcon from '@/assets/icons/DragHandleIcon.svg?component'
import type AssistantChatService from '@/modules/chat/service/AssistantChatService'
import ChatComponent from '@/modules/chat/components/ChatComponent.vue'
import { ChatScreen } from '@/modules/chat/lib/Chat/Chat'
import ChatAssistantHelpScreen from '@/modules/chat/components/Screen/ChatAssistantHelpScreen.vue'
import useFloatingDimensionsElement from '~/composables/useFloatingDimensionsElement'

defineOptions({
    name: 'AssistantChatWidget',
})

const props = withDefaults(defineProps<{
    service: AssistantChatService,
    index: number,
}>(), {
    index: 0,
})

// Refs
const widgetIsVisible = ref(false)
const handle = ref<HTMLElement | undefined>()

// Expand

const wide = ref(false)
const toggleExpand = () => wide.value = !wide.value
watch(wide, (value) => {
    if (value) {
        disableFullViewportHeight()
    } else {
        collapse()
    }
})

function onOpenAssistantChatWidget() {
    widgetIsVisible.value = true
}

function expandWidget() {
    enableFullViewportHeight()
}

function minimizeWidget() {
    disableFullViewportHeight()
}

function collapseWidget() {
    wide.value = false
}

function terminateWidget() {
    console.log('terminate')
    wide.value = false
    // send terminate chat
    widgetIsVisible.value = false
}

//

const loading = ref(true)

async function fetch() {
    loading.value = true

    try {
        await props.service.init()
    } finally {
        loading.value = false
    }
}

onMounted(() => {
    fetch()

    useService('event').on('openAssistantChatWidget', onOpenAssistantChatWidget)
})

//

const { isFullHeightMode, fitInViewport, enableFullViewportHeight, disableFullViewportHeight, applySize, resetSize, collapse } = useFloatingDimensionsElement({
    initialPosition: {
        x: 40 + (80 * props.index),
        y: 0,
    },
    savePosition: true,
    key: 'assistant-floating-chat-widget-' + props.service.gateway.id,
    handle,
    minimalHeight: 480,
})
</script>

<style lang="postcss" scoped>
.floating_assistant-chat-widget {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;

    transition: width .2s;
    will-change: width;

    position: fixed;
    z-index: 50;
    //width: 320px;
    user-select: none;

    &--wide{
        --tw-bg-opacity: 1;
        background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
        --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
        width: 320px;
    }

    &__handle {
        @apply text-gray-500 py-1.5 border-b w-full cursor-move select-none;

        circle {
            fill: currentColor;
        }
    }

    &__content {
        position: relative;
        flex: 1 1 100%;
        @apply p-0.5 flex flex-col w-full text-theme-1 dark:text-gray-400;
    }

    &__item {
        min-width: 320px !important;
    }

    &__main-button {
        @apply rounded-full w-18 h-18 text-white bg-primary-9 flex items-center justify-center p-2;
    }
}
</style>
