import { MentionCredential } from '@/lib/QuillMention/MentionChar/MentionCredential'
import { MentionAgent } from '@/lib/QuillMention/MentionChar/MentionAgent'
import { MentionTag } from '@/lib/QuillMention/MentionChar/MentionTag'
import { MentionFile } from '@/lib/QuillMention/MentionChar/MentionFile'
import { MentionFileReply } from '@/lib/QuillMention/MentionChar/MentionFileReply'
import { MentionReplyToText } from '@/lib/QuillMention/MentionChar/MentionReplyToText'
import { isEmptyQuillDelta, makeUnknownUser } from '@/modules/chat/lib/Helpers/ChatHelpers'
import ChatMessage from '@/modules/chat/lib/Model/ChatMessage'
import ReplyMessageTemplate from '@/modules/chat/lib/MessageTemplate/ReplyMessageTemplate'
import { mountBuilder, unmountBuilder } from '@/lib/mixin/UnMount/UnMountMixin'
import BranchMessageTemplate from '@/modules/chat/lib/MessageTemplate/BranchMessageTemplate'
import { getChatLoadingText } from '~/lib/Easter/ChatEaster'
import WindowStorageHelper from '@/lib/core/helper/WindowStorage/WindowStorageHelper'
import type Agent from '@/api/active-record/Agent'
import type { AnyObject, ToRender } from '@/types'
import type { Delta, DeltaOption } from '@/modules/chat/lib/MessageTemplate/MessageTemplate'
import type MentionChar from '@/lib/QuillMention/MentionChar/MentionChar'
import type { RouteLocation } from 'vue-router'
import { ensureArray } from '~/lib/Helper/ArrayHelper'
import { ChatMessageType } from '~/api/models/Chat/ChatMessage'
import type { ChatServiceKey } from '~/service/ChatManagerService'
import type ChatServiceV2 from '@/modules/chat/service/ChatServiceV2'
import type { Component } from 'vue'

export type ChatUser = {
    id: number,
    ringcentral_id: number,
    avatar: string,
    first_name: string,
    last_name: string,
    isBot: boolean,
}

export type ChatMessageToRender = {
    component: string,
    message: ChatMessage,
    templates: ToRender[],
    attachments: ToRender[]
}

export type CurrentChatUserInfo = {
    id: number,
}

export type ChatInfo = {
    name?: string,
    type?: ChatType,
    avatar?: string,
    lastModifiedTime?: number,
}

export type ChatOptions = {
    info?: ChatInfo,
    chatId: number,
    agents: AnyObject[],
    user: CurrentChatUserInfo,
}

export interface ChatState {
    active: boolean,
    ready: boolean,
    sending: boolean,
    messages: Array<ChatMessage>,
    agents: ChatUser[],
    members: ChatUser[],
    message: Delta,
    editingMessage: ChatMessage | null,
    replyingToMessage: ChatMessage | null,
    branch: string | null,
    //
    name: string | null,
    type: ChatType | null,
    avatar: string | null,
    lastModifiedTime: number | null,
    //
    screen: ChatScreen,
    lastReadAt: number | null,
    //
    muted: boolean,
    currentUserWasMentioned: boolean,
}

export enum ChatScreen {
    Messages = 'messages',
    Help = 'help',
    PinnedMessageList = 'pinnedMessageList',
    AssistantHelp = 'assistantHelp'
}

export enum ChatType {
    Direct = 'direct',
    BO = 'bo',
    Team = 'team',
    Assistant = 'assistant',
}

export enum ChatScrollState {
    Fixed = 'fixed',
    Track = 'track',
}

export enum ChatStatus {
    Active = 'Active',
    Archived = 'Archived',
}

const emptyDelta: Delta = {
    ops: [],
}

export default abstract class Chat {
    declare options: ChatOptions
    declare user: CurrentChatUserInfo

    protected serviceKey: ChatServiceKey
    protected _state: ChatState
    protected _message = { ...emptyDelta }

    protected _scrollState = ChatScrollState.Fixed
    protected _scrollStateHistory: ChatScrollState | null = null

    protected _unknownUsers: Record<number, ChatUser> = {}

    protected _visibleLastReadAt: number | null = null

    declare _unwatch: () => void

    declare $mount
    declare $unmount
    declare $refs: any[]

    // Cache
    declare _me?: ChatUser
    declare _mentionChars?: MentionChar[]

    protected constructor(serviceKey: ChatServiceKey, options: ChatOptions, additionalState: AnyObject | null = null) {
        this.serviceKey = serviceKey
        this.options = options

        this.user = this.options.user

        // @ts-ignore
        this._state = reactive(Object.assign({
            active: false,
            ready: false,
            sending: false,

            messages: [],
            agents: options.agents ?? [],
            members: [],

            message: { ...emptyDelta },
            editingMessage: null,
            replyingToMessage: null,
            branch: null,

            name: options.info?.name,
            type: options.info?.type,
            avatar: options.info?.avatar,
            lastModifiedTime: options.info?.lastModifiedTime,

            screen: ChatScreen.Messages,
            lastReadAt: null,

            muted: this.initMutedState(),
            currentUserWasMentioned: false,
        }, additionalState))

        this.$mount = mountBuilder(this)
        this.$unmount = unmountBuilder(this)

        this.$refs = []

        WindowStorageHelper.local.listener.add('create', this.mutedKey, (value) => {
            this._state.muted = Boolean(Number(value))
        })

        WindowStorageHelper.local.listener.add('update', this.mutedKey, (value) => {
            this._state.muted = Boolean(Number(value))
        })
    }

    /**
     * Called when component is mounted
     */
    init(component): void {
        this._watchLoginState()

        this.activate()

        this.$refs.push(new WeakRef(component))

        this.selectDefaultBranch(component.$route)
    }

    initMutedState(): boolean {
        let muted = WindowStorageHelper.local.get(this.mutedKey)

        if (muted === null) {
            if (this.options.info?.type) {
                muted = this.options.info?.type === ChatType.BO || this.options.info?.type === ChatType.Team
            } else if (this.options.chatId) {
                muted = false
            }

            // if (muted) {
            //     localStorage.setItem(this.mutedKey, '1')
            // }
        }

        return Boolean(Number(muted))
    }

    // ========= Getters
    // =================

    abstract get key(): string

    protected abstract get settingsDisplayKey(): string;

    get id(): number {
        return this.options.chatId
    }

    get name(): string | null {
        return this._state.name
    }

    get link(): string | null {
        return null
    }

    abstract get service(): ChatServiceV2

    get type(): ChatType | null {
        return this._state.type
    }

    get avatar(): string {
        return this._state.avatar
    }

    get pinMessageFeatureEnabled(): boolean {
        return false
    }

    abstract get isAuthChecked(): boolean

    abstract get isLoggedIn(): boolean

    abstract get isArchived(): boolean
    protected abstract restore(): Promise<void>

    abstract get isMember(): boolean
    protected abstract addAsMember(): Promise<void>

    get me(): Agent {
        if (this._me) {
            return this._me
        }

        return this._me = this.agents.find(agent => agent.id === this.user.id)
    }

    // =========================

    get activeScreen(): ChatScreen {
        return this._state.screen
    }

    get messages(): ChatMessage[] {
        return this._state.messages
    }

    get agents(): ChatUser[] {
        return this._state.agents
    }

    get members(): ChatUser[] {
        return this._state.members
    }

    /**
     * Current message in editor
     */
    get message(): Delta {
        return this._message
    }

    /**
     * To have actual editor message
     * Use with v-model only
     */
    set message(value: Delta) {
        this._message = value
    }

    /**
     * Reactive message
     * Triggers editor change contents
     */
    get watchedMessage(): Delta {
        return this._state.message
    }

    /**
     * Sets editor content
     * Do not use with v-model
     */
    setEditorContent(value: DeltaOption[] | null): void {
        if (!value) {
            this._message = { ...emptyDelta }
            this._state.message = { ...emptyDelta }
        } else {
            this._state.message = { ops: value }
            this._message = { ops: value }
        }
    }

    /**
     * Sets editor content
     * Do not use with v-model
     */
    appendEditorContent(value: DeltaOption): void {
        if (isEmptyQuillDelta(this.message)) {
            this.setEditorContent([value])

            return
        }

        this.message.ops.push(value)
        this._state.message = Object.assign({}, this.message)
    }

    get isEmptyMessage(): boolean {
        return isEmptyQuillDelta(this.message)
    }

    get editingMessage(): ChatMessage | null {
        return this._state.editingMessage
    }

    get replyingToMessage(): ChatMessage | null {
        return this._state.replyingToMessage
    }

    get isActive(): boolean {
        return this._state.active
    }

    get isReady(): boolean {
        return this._state.ready
    }

    get isSending(): boolean {
        return this._state.sending
    }

    get messagesToRender(): ChatMessageToRender[] {
        if (!this.shouldDisplaySystemMessages) {
            return this.mapMessagesToRender(this.messagesInBranch(this.branch).filter((message) => !message.system))
        }

        return this.mapMessagesToRender(this.messagesInBranch(this.branch))
    }

    private mapMessagesToRender(messages: ChatMessage[]): ChatMessageToRender[] {
        return messages.map((message) => {
            const component = (!message.type || message.type === ChatMessageType.Text) ? 'ChatMessage' : 'ChatSystemMessage'

            return {
                component,
                message,
                attachments: message.toAttachments(this),
                templates: message.toTemplates(this),
            }
        })
    }

    get pinnedMessagesToRender() {
        if (!this.messages) {
            return []
        }

        return this.mapMessagesToRender(this.pinnedMessages)
    }

    get pinnedMessages(): ChatMessage[] {
        return this.messages.filter((message) => this.pinnedMessagePks?.includes(String(message.id)))
    }

    get canUseAttachments() {
        return false
    }

    /**
     * Chat shortcuts for QuillEditor
     */
    get mentionChars(): { [char: string]: MentionChar } {
        if (this._mentionChars) {
            return this._mentionChars
        }

        const mentions: MentionChar[] = [
            new MentionAgent(() => this.agents),
            new MentionTag(() => this.branches.map(branch => ({ id: branch, value: branch }))),
            new MentionCredential(() => this.agents),
            new MentionReplyToText(() => this.agents),
        ]

        if (this.canUseAttachments) {
            // @ts-ignore
            mentions.push(new MentionFile(() => this.attachments))
            // @ts-ignore
            mentions.push(new MentionFileReply(() => this.attachments))
        }

        return this._mentionChars = Object.fromEntries(mentions.map(item => [item.char, item]))
    }

    messagesInBranch(branch): ChatMessage[] {
        if (branch === null) {
            return this.messages
        }

        return this.messages.filter(message => message.branches.includes(branch))
    }

    // ========= Actions
    // =================

    abstract fetch(): Promise<boolean>

    abstract login(): Promise<void>

    abstract logout(): Promise<void>

    abstract deleteMessage(id: number): Promise<void>

    abstract pinMessage(message: ChatMessage, pin: boolean): Promise<void>

    abstract unpinAllMessages(): Promise<void>

    abstract get pinnedMessagePks(): PrimaryKey[]

    abstract isPinnedMessage(message: ChatMessage): boolean
    get isMutable() {
        return false
    }

    get isAuthorizable() {
        return false
    }

    protected _pushNewMessage(message) {
        this._state.messages.push(message)
    }

    _findMessageById(id): ChatMessage | null {
        return this._state.messages.find(message => message.id === Number(id))
    }

    protected _changeMessage(newMessage) {
        const message = this._state.messages.find(message => message.id === newMessage.id)

        if (message) {
            message.edit(newMessage)
        }
    }

    pushNewUnparsedMessage(unparsed): void {
        if (!this._findMessageById(unparsed.id)) {
            this._pushNewMessage(this.parseMessage(unparsed))
        }
    }

    changeUnparsedMessage(unparsed): void {
        const message = this._findMessageById(unparsed.id)

        if (message) {
            message.edit(this.parseMessage(unparsed))
        }
    }

    _deleteMessage(id: number): void {
        const foundIndex = this._state.messages.findIndex(message => message.id === Number(id))

        if (foundIndex !== -1) {
            this._state.messages.splice(foundIndex, 1)
        }
    }

    // ======================

    getUser(id: number | PrimaryKey | Empty): ChatUser {
        const found = this.agents.find(user => user.id === Number(id)) as ChatUser

        if (!found) {
            return this.getUnknownUser(id)
        }

        return found
    }

    getUnknownUser(id: number | PrimaryKey): ChatUser {
        id = Number(id)

        if (this._unknownUsers[id]) {
            return this._unknownUsers[id]
        }

        this._unknownUsers[id] = makeUnknownUser(id, Object.keys(this._unknownUsers).length + 1)

        return this._unknownUsers[id]
    }

    get loadingText(): string {
        return getChatLoadingText('Chat will be available in a few seconds ...')
    }

    private _watchLoginState(): void {
        this._unwatch = watch([() => this.isLoggedIn, () => this.isActive], async () => {
            // @todo Cleanup fetch

            if (this.isActive) {
                this._state.ready = false

                // noinspection RedundantIfStatementJS
                if (await this.fetch()) {
                    this._state.ready = true
                }
            }
        })
    }

    activate(): void {
        this._state.active = true
    }

    async deactivate(): Promise<void> {
        if (!this._state.active) {
            return
        }

        console.log('Deactivated', this.options)

        this._state.active = false

        await this.unload()
    }

    /**
     * To lower memory consumption
     */
    async unload(): Promise<void> {
        // @todo unload data

        this._state.ready = false

        if (this._unwatch) {
            this._unwatch()
        }

        this.$unmount()
    }

    $removeRef(component): void {
        this.$refs = this.$refs.filter(c => c.deref() !== component)

        this.deactivateIfNoRefs()
    }

    cleanupRefs(): void {
        this.$refs = this.$refs.filter(c => !!c.deref())
    }

    deactivateIfNoRefs(): void {
        if (!this.hasRefs) {
            this.deactivate()
        }
    }

    get hasRefs(): boolean {
        return !!this.$refs.length
    }

    async submitMessage(message: Delta): Promise<void> {
        if (isEmptyQuillDelta(message)) {
            message = { ...emptyDelta }
        }

        if (this.isSending) {
            return
        }

        if (this.isEmptyMessage) {
            return
        }

        this._state.sending = true

        try {
            await this.waitForMessageReady()
        } catch (e) {
            this._state.sending = false

            console.error(e)

            return
        }

        let messageText = ''

        const composedReplyText = this.replyAsText()
        const composedText = this.composeTextFromDeltaOptions(message.ops)

        messageText += composedReplyText

        if (this.branch && !ChatMessage.parseBranches(composedReplyText + ' ' + composedText).includes(this.branch)) {
            messageText += this.branchAsText()
        }

        messageText += this.mentionlessFilesAsText()
        messageText += composedText

        messageText = messageText.trim()

        if (!messageText.length) {
            return
        }

        this.setTrackScrollOnce()

        try {
            await this.saveMessage(messageText, this.editingMessage?.id)
        } catch (e) {
            console.error(e)
        }

        this._state.sending = false

        this.resetInputState()
    }

    async saveMessage(message: string, id: number | null) {
        if (this.isArchived) {
            await this.restore()
        } else if (!this.isMember) {
            await this.addAsMember()
        }

        let chatMessage: ChatMessage

        if (!id) {
            chatMessage = this.makeMessage(message)
            this._pushNewMessage(chatMessage)
        } else {
            chatMessage = this.makeMessage(message, {
                id,
                updatedAt: new Date(),
            })

            this._changeMessage(chatMessage)
        }

        this.setLastReadAt(Date.currentUnixTimestamp(), true, true)

        this._saveMessageRequest(chatMessage, id).then(message => {
            if (!id) {
                chatMessage.edit(this.parseMessage(message))
            }
        }).catch(error => {
            console.error(error)

            if (!id) {
                chatMessage.error = true
            }
        })
    }

    makeMessage(text: string, params = {}): ChatMessage {
        return new ChatMessage(Object.assign({
            id: null,
            author: this.me,
            createdAt: new Date(),
            updatedAt: null,
            text,
        }, params))
    }

    protected abstract _saveMessageRequest(message: ChatMessage, id: number | null): Promise<AnyObject>

    abstract parseMessage(info: AnyObject): ChatMessage

    resetInputState(): void {
        this.setEditorContent(null)
        this._state.editingMessage = null
        this._state.replyingToMessage = null
    }

    /**
     * Reset only if there is a certain state
     */
    softResetInputState(): void {
        if (this._state.editingMessage) {
            this.setEditorContent(null)
            this._state.editingMessage = null
        }

        if (this._state.replyingToMessage) {
            this._state.replyingToMessage = null
        }
    }

    cancelReply(): void {
        if (this._state.replyingToMessage) {
            this._state.replyingToMessage = null
        }
    }

    mentionlessFilesAsText(): string {
        return ''
    }

    replyAsText(): string {
        let result = ''

        if (this.replyingToMessage) {
            result += ReplyMessageTemplate.fromChatMessage(this.replyingToMessage, this).raw
        }

        return result
    }

    composeTextFromDeltaOptions(delta: DeltaOption[]): string {
        const result = []

        let codeBlockStarted = false

        for (let i = 0; i < delta.length; i++) {
            const item = delta[i]

            if (typeof item.insert === 'string') {
                const lines = item.insert?.split('\n') ?? []

                const isCodeblock = Boolean(item.attributes?.['code-block'] && item.insert.match('^[\n]+$'))
                const isNextCodeblock = delta[i + 1]?.attributes?.['code-block']

                if (isNextCodeblock && !codeBlockStarted) {
                    codeBlockStarted = true

                    for (let j = 0; j < lines.length; j++) {
                        if (j === lines.length - 1) {
                            result.push('```\n')
                        }

                        result.push(lines[j] + '\n')
                    }

                    continue
                }

                if (codeBlockStarted && (!delta[i + 1] || (!isCodeblock && !isNextCodeblock))) {
                    codeBlockStarted = false
                    result.push('```\n')
                }

                if (!isCodeblock) {
                    for (const line of lines) {
                        result.push(line + '\n')
                    }
                } else if (isCodeblock) {
                    result.push(item.insert.replace('\n', ''))
                }

                continue
            }

            const handler = this.mentionChars[item.insert.mention.denotationChar]

            if (!handler) {
                console.warn('Not processed mention', item)
                continue
            }

            const options = handler.deltaOptionToText(item, this)

            if (!options || !options.length) {
                continue
            }

            result.push(...ensureArray(options).map(option => option + ' '))
        }

        return result.filter(v => v.length > 0).join('').trim()
    }

    startEditMessage(message: ChatMessage): void {
        this.softResetInputState()

        message.changeChatState(this)
        this.setEditorContent(message.toDeltaOptions(this))
        this._state.editingMessage = message

        this._state.replyingToMessage = this.composeReplyData(message)
    }

    composeReplyData(message): ChatMessage | null {
        const reply = message.templates.find(template => template.constructor.name === 'ReplyMessageTemplate')

        if (!reply) {
            return null
        }

        return new ChatMessage({
            id: null,
            author: this.getUser(reply.data.id),
            text: reply.data.reply,
            createdAt: null,
        })
    }

    replyToMessage(message: ChatMessage): void {
        if (message.system) {
            if (message.branches.length) {
                this.selectBranch(message.branches.at(0))
            }
        } else {
            this._state.replyingToMessage = message
        }
    }

    // ============= Scroll State
    // ==========================

    hasFixedScroll(): boolean {
        const result = this._scrollState === ChatScrollState.Fixed
        this.releaseScroll()

        return result
    }

    hasTrackScroll(): boolean {
        const result = this._scrollState === ChatScrollState.Track
        this.releaseScroll()

        return result
    }

    fixScroll(): void {
        this._scrollState = ChatScrollState.Fixed
    }

    setTrackScroll(): void {
        this._scrollState = ChatScrollState.Track
    }

    releaseScroll(): void {
        if (this._scrollStateHistory) {
            this._scrollState = this._scrollStateHistory
            this._scrollStateHistory = null
        }
    }

    fixScrollOnce(): void {
        this._scrollStateHistory = this._scrollState
        this.fixScroll()
    }

    setTrackScrollOnce(): void {
        this._scrollStateHistory = this._scrollState
        this.setTrackScroll()
    }

    // ===============

    get visibleLastReadAt(): number | null {
        return this._visibleLastReadAt ?? 0
    }

    get lastReadAt(): number | null {
        return this._state.lastReadAt ?? 0
    }

    async fetchLastReadAt(): Promise<void> {
        //
    }

    /**
     * Without save
     */
    setLastReadAtSilent(timestamp: number | Date, withVisible = false) {
        //
    }

    setLastReadAt(timestamp: number | Date, withVisible = false, myAction = false) {
        //
    }

    async markAsRead(): Promise<void> {
        //
    }

    get lastModifiedTime(): number | null {
        return this._state.lastModifiedTime
    }

    set lastModifiedTime(value: number | null) {
        this._state.lastModifiedTime = value
    }

    get hasNewMessages(): boolean {
        return this.lastReadAt && this.lastModifiedTime > this.lastReadAt
    }

    // ===============

    openScreen(screen: ChatScreen): void {
        this._state.screen = screen
    }

    openMainScreen(): void {
        this.openScreen(ChatScreen.Messages)
    }

    // ===============

    get branches(): string[] {
        const branches = new Set<string>()

        this.messages.forEach(message => {
            if (!message.branches?.length) {
                return
            }

            message.branches.forEach(branch => {
                branches.add(branch)
            })
        })

        return Array.from(branches)
    }

    get branch(): string | null {
        return this._state.branch
    }

    selectMainBranch(): void {
        this.selectBranch(null)
    }

    selectBranch(branch): void {
        this._state.branch = branch
    }

    selectDefaultBranch(route: RouteLocation): void {
        const defaultBranch = route.hash.match(/#chat-branch=([a-z][\w-]*)\b/i)?.[1]

        if (defaultBranch) {
            this.selectBranch(defaultBranch)
        }
    }

    branchAsText(): string {
        if (!this.branch) {
            return ''
        }

        return BranchMessageTemplate.fromId(this.branch, this.link).raw + ' '
    }

    // ============ Mute
    // =================

    get isMuted(): boolean {
        return this._state.muted
    }

    get mutedKey(): string {
        return `${this.key}:muted`
    }

    mute(): void {
        this._state.muted = true
        WindowStorageHelper.local.set(this.mutedKey, '1')
    }

    unmute(): void {
        this._state.muted = false
        WindowStorageHelper.local.set(this.mutedKey, '0')
    }

    // =================

    get currentUserWasMentioned(): boolean {
        return this._state.currentUserWasMentioned
    }

    set currentUserWasMentioned(value: boolean) {
        this._state.currentUserWasMentioned = value
    }

    _shouldDisplaySystemMessages = useAppSetting('displaySystemMessagesInChat')

    get shouldDisplaySystemMessages(): boolean {
        if (!this._shouldDisplaySystemMessages.value.hasOwnProperty(this.settingsDisplayKey)) {
            return true
        }

        return this._shouldDisplaySystemMessages.value[this.settingsDisplayKey]
    }

    toggleShouldDisplaySystemMessages() {
        const value = this._shouldDisplaySystemMessages.value

        value[this.settingsDisplayKey] = !this.shouldDisplaySystemMessages

        this._shouldDisplaySystemMessages.value = value
    }

    public async waitForMessageReady(): Promise<void> {
        return
    }

    public get isReadonly(): boolean {
        return false
    }

    public async sendPredefinedMessage(message: string) {}
}
