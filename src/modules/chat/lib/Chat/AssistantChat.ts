import type { ChatIn<PERSON>, ChatMessageToRender, ChatUser, CurrentChatUserInfo } from '@/modules/chat/lib/Chat/Chat'
import { ChatScreen } from '@/modules/chat/lib/Chat/Chat'
import type { ChatFileAttachment, ChatWithAttachmentsState } from '@/modules/chat/lib/Chat/Extended/ChatWithAttachments'
import type { ComponentPublicInstance, Ref } from 'vue'
import type { ModelAttributes, ModelRef } from '~types/lib/Model'
import type ResourceList from '~/lib/Model/ResourceList'
import type ModelController from '~/lib/Model/ModelController'
import type { ChatServiceKey } from '~/service/ChatManagerService'
import type { ChatMessagePayload } from '@/modules/chat/lib/Model/ChatMessage'
import { ChatScrollState } from '@/modules/chat/lib/Chat/Chat'
import ChatMessage from '@/modules/chat/lib/Model/ChatMessage'
import ChatSystemMessage from '@/modules/chat/lib/Model/ChatSystemMessage'
import ChatWithAttachments from '@/modules/chat/lib/Chat/Extended/ChatWithAttachments'
import { defaultLabel } from '@/lib/core/helper/ConsoleHelper'
import { useDebounceFn } from '@vueuse/core'
import { useModel } from '~/composables/useModel'
import { pluckPks } from '~/lib/Helper/ArrayHelper'
import { wait } from '~/lib/Helper/PromiseHelper'
import WindowStorageHelper from '@/lib/core/helper/WindowStorage/WindowStorageHelper'
import type ArrayConditionHelper from '~/lib/Helper/ArrayCondition/ArrayConditionHelper'
import ChatFileModel from '@/api/models/Chat/ChatFileModel'
import { ChatMessageType } from '~/api/models/Chat/ChatMessage'
import type ResourceRecord from '~/lib/Model/ResourceRecord'

/**
 * @important Abandon hope, all ye who enter here
 */

export type InternalChatOptions = {
    info?: ChatInfo,
    chatId: number,
    agents: ChatUser[],
    user: CurrentChatUserInfo,
}

type DriverMessage = ModelRef<'ChatMessage', 'poll'>

export default class AssistantChat extends ChatWithAttachments {
    public declare options: InternalChatOptions

    public declare _state: ChatWithAttachmentsState & {
        messageGroupsPks: PrimaryKey[],
        mainGroupPks: PrimaryKey[],
        pinnedMessagesPks: PrimaryKey[],
    }

    public declare messagesList: ResourceList
    public declare messageModel: ModelController<'ChatMessage'>

    public constructor(serviceKey: ChatServiceKey, options: InternalChatOptions) {
        super(serviceKey, options, {
            messageGroupsPks: [],
            mainGroupPks: [],
        })

        this.messageModel = useModel('ChatMessage', this.resourceContextOptions)
        this.messagesList = this.messageModel.useResourceList({
            name: 'ChatChatMessageList',
            pk: composeResourcePk('ChatChatMessageList', {
                pk: usePk('Chat', this.chatId),
                auth_pk: this.userPk,
            }),
        }, {
            with: ['poll'],
        })
    }

    public get pinnedMessagePks(): PrimaryKey[] {
        return []
    }

    public get userPk() {
        return tryUsePk('Agent', this.me.id)
    }

    protected get settingsDisplayKey(): string {
        return this.chatModel?.model_name ?? 'Agent'
    }

    public get service() {
        return useService('chat').getServiceOrFail(String(this.serviceKey))
    }

    public get resourceContextOptions() {
        return this.service.resourceContextOptions
    }

    protected declare _chatRecord: Ref<ModelAttributes<'Chat'>>

    public get chatModel(): ModelAttributes<'Chat'> | undefined {
        return this._chatRecord.value ?? this._chatRecord
    }

    public setChatRecord(record: Ref<ModelAttributes<'Chat'>>) {
        this._chatRecord = ref({}) as Ref<ModelAttributes<'Chat'>>
        this._chatRecord = record
    }

    public async fetchChatModel() {
        return await useModel('Chat', this.resourceContextOptions).useRecord().fetch(usePk('Chat', this.chatId))
    }

    public async init(component: ComponentPublicInstance) {
        const resource = await this.fetchChatModel()

        if (!resource) {
            throw new Error(`Chat ${this.chatId} not found`)
        }

        this._chatRecord = resource
        const model = resource.value

        this._state.messageGroupsPks = model.chat_group_pks ?? []

        // this.groupsList = useModel('ChatGroup', this.resourceContextOptions).useList({
        //     pks: model.chat_group_pks ?? [],
        // })
        //
        // await this.pinnedMessagesList.fetch(usePk('Chat', this.chatId))

        super.init(component)

        this.openScreen(ChatScreen.AssistantHelp)

        await this.fetchLastReadAt()
    }

    public get isMutable() {
        return true
    }

    private _messageListeners: (() => void)[] = []

    public startListening() {
        this.stopListening()

        const messagesPk = composeResourcePk('ChatChatMessageList', {
            pk: usePk('Chat', this.chatId),
            auth_pk: this.userPk,
        })

        // Sync chat messages
        // ==================
        const { resourceRef } = useRef(this.resourceContextOptions)

        const messages = resourceRef('ChatChatMessageList', messagesPk)

        const unwatch = watch(() => messages.list, async (newValue, oldValue) => {
            const addedDifference: PrimaryKey[] = newValue.filter((x: PrimaryKey) => !oldValue.includes(x))
            const deletedDifference: PrimaryKey[] = oldValue.filter((x: PrimaryKey) => !newValue.includes(x))

            for (const pk of deletedDifference) {
                // Remove deleted messages from the list
                this._deleteMessage(Number(pk))
            }

            if (addedDifference.length) {
                const newMessages = await useModel('ChatMessage', this.resourceContextOptions).useList({
                    pks: addedDifference,
                    with: ['poll'],
                }).fetch() as DriverMessage[]

                for (const message of Object.values(newMessages)) {
                    // Add new messages to the list
                    this.pushNewUnparsedMessage(message)
                    // this.subscribeToUpdates(usePk(message))
                }
            }
        })

        this._messageListeners.push(unwatch)
    }

    public stopListening() {
        for (const stopListening of this._messageListeners) {
            stopListening()
        }
    }

    public async unload(): Promise<void> {
        await super.unload()
    }

    public get key(): string {
        return `assistant-chat-${this.chatId}`
    }

    public get chatId(): number {
        return this.options.chatId
    }

    public get name(): string | null {
        const model = this.chatModel

        if (!model) {
            return this._state.name
        }

        return model.name || `${model.model_name === 'Issue' ? 'Request' : model.model_name} #${model.model_pk}`
    }

    public get messagesPks() {
        return this.messages.map((message) => String(message.id))
    }

    // public get messageGroupsPksNormalized() {
    //     return this.messageGroupsPks.slice().sort()
    // }

    public get mainGroupPks() {
        return this._state.mainGroupPks
    }

    public initMutedState(): boolean {
        let muted = WindowStorageHelper.local.get(this.mutedKey)

        if (muted === null) {
            if (this.options.chatId) {
                muted = false
            }
        }

        return Boolean(Number(muted))
    }

    public replyToMessage(message: ChatMessage) {
        super.replyToMessage(message)

        this._state.messageGroupsPks = message.groupPks?.slice() || pluckPks(this.groups)
    }

    public async fetch(): Promise<boolean> {
        this._state.messages = (await this.messagesList.fetch()).map(this.parseMessage.bind(this)).sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())

        await Promise.all([
            this.fetchAttachments(),
        ])

        this._state.mainGroupPks = useRef(this.resourceContextOptions).resourceRef('ChatChatMessageList', composeResourcePk('ChatChatMessageList', {
            id: this.chatId,
            auth_pk: this.userPk,
        })).main_group_pks ?? []

        this.startListening()
        this.startListeningToAttachments()

        this.logInfo()

        return true
    }

    public get attachmentPks() {
        return this.messages.flatMap(message => message.attachments_pks)
    }

    public get messagesToRender(): ChatMessageToRender[] {
        return super.messagesToRender
    }

    public get pinnedMessagesToRender(): ChatMessageToRender[] {
        return super.pinnedMessagesToRender
    }

    private attachmentsStorage = ref<ChatFileAttachment[]>([])

    private _attachmentListeners: (() => void)[] = []

    private startListeningToAttachments() {
        const unwatch = watch(() => this.attachmentPks, async (newValue, oldValue) => {
            const addedAttachments = newValue.filter(pk => !oldValue.includes(pk))
            const removedAttachments = oldValue.filter(pk => !newValue.includes(pk))

            if (addedAttachments.length) {
                const attachments = (await Promise.all(
                    addedAttachments.map(pk => this.fetchAttachment(pk)),
                )).filter(Boolean)

                this.attachmentsStorage.value.push(...attachments)
            }

            if (removedAttachments.length) {
                this.attachmentsStorage.value = this.attachmentsStorage.value.filter(attachment => !removedAttachments.includes(attachment.id))
            }
        })

        this._attachmentListeners.push(unwatch)
    }

    public async fetchAttachment(pk: PrimaryKey): Promise<ChatFileAttachment> {
        const list = ChatFileModel.instance({
            workspaceProject: Number(this.service.gateway.project_pk),
        }).list()

        await list.request().where((q: any) => {
            q.eq('room_id', this.chatId)
            q.eq('file_id', pk)
        }).get()

        return list.records?.[0]
    }

    public addAttachment(attachment: ChatFileAttachment) {
        this.attachmentsStorage.value.push(attachment)
    }

    public async deleteAttachment(attachmentID: number): Promise<void> {
        await super.deleteAttachment(attachmentID)

        this.attachmentsStorage.value = this.attachmentsStorage.value.filter(attachment => attachment.id !== attachmentID)
    }

    public stopListeningToAttachments() {
        for (const stopListening of this._attachmentListeners) {
            stopListening()
        }
    }

    public async fetchAttachments(): Promise<ChatFileAttachment[]> {
        if (!this.canUseAttachments) {
            return []
        }

        if (!this.chatId) {
            return []
        }

        const fileIds = this.attachmentPks

        if (!fileIds.length) {
            return []
        }

        const conditions = (q: ArrayConditionHelper) => q.in('file_id', fileIds)

        const attachmentsList = await this.attachmentsList
            .request()
            .where(conditions)
            .noLimit().get()

        for (const attachment of this.attachmentsList.records) {
            this.attachmentsStorage.value.push(attachment)
        }

        return attachmentsList
    }

    public get attachments(): ChatFileAttachment[] {
        return this.attachmentsStorage.value
    }

    public makeMessage(text: string, params = {}): ChatMessage {
        return new ChatMessage(Object.assign({
            id: undefined,
            author: this.me as ChatUser,
            createdAt: new Date(),
            updatedAt: undefined,
            text,
            isImportant: false,
            groupPks: [], //this.messageGroupsPksNormalized,
        }, params))
    }

    protected async _saveMessageRequest(message: ChatMessage, id: number | null = null): Promise<DriverMessage> {
        const branches: string[] = []
        const agent_mention_pks: PrimaryKey[] = []
        const group_mention_pks: PrimaryKey[] = []
        const attachments_pks: PrimaryKey[] = message.getAttachmentsPks()

        for (const template of message.templates) {
            if (template.name === 'Branch') {
                if (template.data?.id) {
                    branches.push(template.data.id)
                }
            } else if (template.constructor.name === 'AgentMessageTemplate') {
                if (template.data?.id) {
                    agent_mention_pks.push(usePk('Agent', template.data.id))
                }
            }
        }

        const context = {
            attachments_pks,
            branches,
            agent_mention_pks,
            group_mention_pks,
        }

        if (id) {
            return await this.messageModel.actions.update({
                pk: String(id),
                message: {
                    chat_pk: String(this.options.chatId),
                    type: ChatMessageType.Text,
                    content: {
                        text: message.text,
                    },
                    group_pks: [], // this.messageGroupsPksNormalized,
                    ...context,
                },
            })
        }

        return this.messageModel.actions.create({
            message: {
                chat_pk: String(this.chatId),
                type: ChatMessageType.Text,
                content: {
                    text: message.text,
                },
                group_pks: [], // this.messageGroupsPksNormalized,
                ...context,
            },
        })
    }

    public parseMessage(info: DriverMessage) {
        const isSystem = Boolean(info.type !== ChatMessageType.Text)

        const text = (isSystem ? info.preview : info.content.text) || ''

        const data: ChatMessagePayload = {
            id: Number(info.id),
            author: this.getUser(info.created_by_pk),
            createdAt: Date.fromUnixTimestamp(info.created_at),
            updatedAt: info.created_at !== info.updated_at ? Date.fromUnixTimestampOrNull(info.updated_at) : undefined,
            text: text,
            mentions: info.agent_mention_pks?.map(pk => this.getUser(pk)) || [],
            groupPks: info.group_pks,
            attachments_pks: info.attachments_pks,
            poll: info.poll,
            type: info.type,
            content: info.content,
            isImportant: info.is_important,
        }

        return isSystem ? new ChatSystemMessage(data, info.content) : new ChatMessage(data)
    }

    public async deleteMessage(id: number) {
        await this.messageModel.actions.delete({ pk: String(id) })
        this._deleteMessage(id)
    }

    public async pinMessage(message: ChatMessage, pin: boolean) {
        await useWaitForResourceEvent(
            await this.messageModel.actions.pin({
                message_pk: String(message.id),
                chat_pk: String(this.chatId),
                pin,
            }),
            'ChatChatMessagePinnedList',
            'update',
            String(this.chatId),
        )
    }

    public async unpinAllMessages() {
        await useWaitForResourceEvent(
            await this.messageModel.actions.bulkUnpin({
                message_pks: this.pinnedMessagePks,
                chat_pk: String(this.chatId),
            }),
            'ChatChatMessagePinnedList',
            'update',
            String(this.chatId),
        )
    }

    public get pinMessageFeatureEnabled() {
        return true
    }

    public isPinnedMessage(message: ChatMessage): boolean {
        return this.pinnedMessagePks.includes(String(message.id))
    }

    protected _changeMessage(newMessage: ChatMessage) {
        const message = this.messages.find(message => message.id === newMessage.id)

        if (message) {
            message.edit(newMessage)
        }
    }

    public pushNewUnparsedMessage(unparsed: DriverMessage): void {
        super.pushNewUnparsedMessage(unparsed)

        this._markReadFromNotification(unparsed)
    }

    public changeUnparsedMessage(unparsed: DriverMessage): void {
        super.changeUnparsedMessage(unparsed)

        this._markReadFromNotification(unparsed)
    }

    private logInfo(): void {
        if (isProduction) {
            return
        }

        console.group(...defaultLabel('Internal Chat'), '------------- ' + this.constructor.name + ' (#' + this.chatId + (this.isArchived ? ' / Archived' : '') + ') -------------')
        useLogger('chat').log({
            '[All] Chat': this,
            'Agents': this.agents,
            'Attachments': this.attachments,
            'Messages': this.messages,
            'Members': this.members,
            'User': this.user,
        })
        console.groupEnd()
    }

    // Read status
    // =========================

    public async fetchLastReadAt(): Promise<void> {
        const { fetchResource: fetchInfo } = useResourceFetch('ChatAdditionalInfo', this.resourceContextOptions)

        const infoPk = composeResourcePk('ChatChatMessageList', {
            id: this.chatId,
            auth_pk: this.userPk,
        })

        const info = await fetchInfo(infoPk)

        const timestamp = info?.last_read_at || null

        this._state.lastReadAt = timestamp
        this._visibleLastReadAt = timestamp
    }

    public saveLastReadAt(): void {
        // noinspection JSIgnoredPromiseFromCall
        useModel('Chat', this.resourceContextOptions).actions.setLastReadAt({
            pk: usePk('Chat', this.chatId),
            last_read_at: this.lastReadAt,
        })
    }

    public async markAsRead(): Promise<void> {
        const timestamp = Date.currentUnixTimestamp()

        this.setLastReadAt(timestamp)
    }

    protected _markChatAsRead(): void {
        // Mark chat as read
    }

    public saveLastReadAtDebounced = useDebounceFn(this.saveLastReadAt.bind(this), 1000)

    public markAsReadDebounced = useDebounceFn(this._markChatAsRead.bind(this), 2000)

    /**
     * Without save
     */
    public setLastReadAtSilent(timestamp: number | Date, withVisible = false) {
        if (typeof timestamp === 'object') {
            timestamp = timestamp.unixTimestamp()
        }

        if (timestamp <= (this.lastReadAt ?? -1)) {
            return
        }

        this._state.lastReadAt = timestamp

        if (withVisible) {
            this._visibleLastReadAt = timestamp
        }
    }

    public setLastReadAt(timestamp: number | Date, withVisible = false, myAction = false): void {
        if (typeof timestamp === 'object') {
            timestamp = timestamp.unixTimestamp()
        }

        if (timestamp <= (this.lastReadAt ?? -1)) {
            return
        }

        this.setLastReadAtSilent(timestamp, withVisible)

        // noinspection JSIgnoredPromiseFromCall
        this.saveLastReadAtDebounced()

        if (!myAction && !this.hasNewMessages) {
            // noinspection JSIgnoredPromiseFromCall
            this.markAsReadDebounced()
        }
    }

    protected _markReadFromNotification(post: DriverMessage): void {
        if (Number(post.created_by_pk) === this.user.id) {
            this.setLastReadAt(Date.currentUnixTimestamp(), true, true)
        } else if (this.isActive && this._scrollState === ChatScrollState.Track) {
            this.setLastReadAt(Date.currentUnixTimestamp(), true)
        }
    }

    // Other
    // =========================

    public get isArchived(): boolean {
        return Boolean(this.chatModel?.is_archived)
    }

    protected async restore(): Promise<void> {
        await useWaitForResourceEvent(
            useModel('Chat', this.resourceContextOptions).actions.restoreFromArchive({
                pk: usePk('Chat', this.chatId),
            }),
            'Chat',
            'update',
            usePk(this.chatModel!),
        )
    }

    public get isAuthChecked(): boolean {
        return true
    }

    public get isLoggedIn(): boolean {
        return true
    }

    public get isMember(): boolean {
        return true
    }

    protected addAsMember(): Promise<void> {
        return Promise.resolve(undefined)
    }

    public async login() {
        // No need to login
    }

    public async logout() {
        // No need to logout
    }

    public get isReadonly(): boolean {
        return Boolean(this.chatModel?.is_readonly)
    }

    public async sendPredefinedMessage(prompt: string) {
        console.log('sendPredefinedPrompt', prompt)
        // TODO: send
        this.openScreen(ChatScreen.Messages)
    }
}
