import type { ModelAttributes, ModelRef } from '~types/lib/Model'
import AssistantChat from '@/modules/chat/lib/Chat/AssistantChat'
import ChatServiceV2 from '@/modules/chat/service/ChatServiceV2'
import { ChatType } from '@/modules/chat/lib/Chat/Chat'

export default class AssistantChatService extends ChatServiceV2 {
    public widgetComponent = defineAsyncComponent(() => import('@/modules/chat/components/Widget/AssistantChatWidget.vue'))

    protected _chat: AssistantChat | null = null

    public async register(): Promise<void> {
        await super.register()
    }

    public async unregister() {
        await super.unregister()
    }
    // protected agents: ChatUser[] = []

    public get workspace() {
        return this.resourceContextOptions.http.workspace as DefinedWorkspace
    }

    public get chat() {
        return this._chat
    }

    // private findAgentDepartament(agent: ModelRef<'Agent'>) {
    //     return useDictionary('Department', {
    //         workspace: this.workspace,
    //     }).findOrFail(agent.department_pk!)
    // }

    public async init() {
        const chatList = useModel('Chat', this.resourceContextOptions).useList({
            where: q => {
                q.eq('model_name', 'Agent')
                q.eq('model_pk', getUserPkForWorkspace(this.workspace))
            },
        })

        await chatList.fetch()

        if (chatList.records.length) {
            console.log(chatList.records)

            this.createChat(chatList.records[0])
        }
    }

    public createChat(record: ModelAttributes<'Chat'>) {
        console.log('createChat', this.serviceKey, record)
        const pk = usePk(record)
        //
        console.log(pk)

        const chat = new AssistantChat(this.serviceKey, {
            chatId: Number(pk),
            user: {
                id: Number(getUserPkForWorkspace(this.workspace)),
            },
            agents: [
                {
                    id: Number(getUserPkForWorkspace(this.workspace)),
                    avatar: '',
                    first_name: 'System',
                    last_name: 'Bot',
                },
            ],

            info: {
                name: 'Assistant',
                type: ChatType.Assistant,
                avatar: ChatType.Assistant,
            },

        })

        this._chat = chat

        //
        return chat
    }
    // public createChat(record: ModelAttributes<'Chat'>) {
    //     // const pk = usePk(record)
    //
    //     // return new InternalChat(this.serviceKey, {
    //     //     chatId: Number(pk),
    //     //     agents: this.agents,
    //     //     user: {
    //     //         id: Number(getUserPkForWorkspace(this.workspace)),
    //     //     },
    //     //     info: {
    //     //         name: 'Chat',
    //     //         type: ChatType.BO,
    //     //     },
    //     // })
    // }
}
