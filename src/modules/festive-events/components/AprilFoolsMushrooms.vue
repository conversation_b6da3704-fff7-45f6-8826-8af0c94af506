<template>
    <div>
        <div
            v-for="(mushroom) in mushrooms"
            :key="mushroom.id"
            :style="{
                'position': 'fixed',
                'top': mushroom.y + 'px',
                'left': mushroom.x + 'px',
                'width': `${mushroom.size}px`,
                'height': `${mushroom.size}px`,
                'z-index': 100,
                'user-select': 'none',
                'cursor': 'cell',
            }"
            @click="emitEvent(mushroom)"
        >
            <img
                :src="mushroom.icon"
                alt=""
                class="w-full"
            >
        </div>
    </div>
</template>

<script lang="ts">
import MushroomIcon from '@/assets/images/events/april-fools/mushroom.svg?url'
import MushroomIcon1 from '@/assets/images/events/april-fools/mushroom-1.svg?url'
import MushroomIcon2 from '@/assets/images/events/april-fools/mushroom-2.svg?url'
import MushroomIcon3 from '@/assets/images/events/april-fools/mushroom-3.svg?url'
import MushroomIcon4 from '@/assets/images/events/april-fools/mushroom-4.svg?url'
import MushroomIcon5 from '@/assets/images/events/april-fools/mushroom-4.svg?url'
import MushroomFartSound from '@/assets/sounds/festive-events/april-fools/fart.mp3?url'

export default defineComponent({
    name: 'AprilFoolsMushrooms',
})
</script>

<script lang="ts" setup>
import { defaultLabel } from '@/lib/core/helper/ConsoleHelper'
import type { FestiveEventPlugin } from '@/plugins/FestiveEventPlugin'

const $festiveEvents = inject<FestiveEventPlugin>('festiveEvents')

const minmax = (value, mx) => Math.max(mx[0], Math.min(mx[1], value))

//

const settings = reactive<{
    maxMushrooms: number
    spawnInterval: number
    spawnChance: number
    size: number
    duration: number
}>({
    maxMushrooms: 0,
    spawnInterval: 0,
    spawnChance: 1,
    size: 0,
    duration: 0,
})

const initSettings = () => {
    settings.maxMushrooms = minmax($festiveEvents.aprilFools.mushroomsMaxAmount, [0, 100])
    settings.spawnInterval = minmax($festiveEvents.aprilFools.mushroomsAppearInterval, [0, 99999999]) * 1000
    settings.size = minmax($festiveEvents.aprilFools.mushroomsSize, [10, 1000])
    settings.duration = minmax($festiveEvents.aprilFools.mushroomsDuration, [5, 3600])
}

watch(() => $festiveEvents.aprilFools, () => {
    initSettings()
}, { deep: true, immediate: true })

const mushrooms = ref<Mushroom[]>([])

function initExcluded() {
    document.querySelectorAll('#app, #app > div, #app > div > *, #app > div > * > *').forEach(element => element.setAttribute('disable-april-effects', '1'))
}

const isExcludedTarget = (target) => {
    return target.getAttribute('disable-april-effects')
}

const activeMushrooms = []
const mushroomSpinHandler: MushroomHandler = {
    elements: [],
    eventBehavior: null,
    mount(mushroom: Mushroom) {
        initExcluded()
        const effectEvent = (e) => {
            // console.log('e.target', e)
            const target = e.target

            if (!target || isExcludedTarget(target)) {
                return
            }

            this.elements.push(target)

            target.classList.add(mushroom.type)
        }

        if (this.eventBehavior) {
            const oldBehavior = this.eventBehavior

            document.getElementById('app').removeEventListener('mousemove', oldBehavior)
            this.eventBehavior = null
        }

        this.eventBehavior = effectEvent

        // document.addEventListener('mousemove', effectEvent)
        setTimeout(() => {
            document.getElementById('app').addEventListener('mousemove', effectEvent)
        }, 200)

        activeMushrooms.push(mushroom)
    },

    unmount(mushroom: Mushroom) {
        document.getElementById('app').removeEventListener('mousemove', this.eventBehavior)

        if (!activeMushrooms.some(m => m.id !== mushroom.id && m.type === mushroom.type)) {
            for (const mushroomElement of this.elements) {
                mushroomElement.classList.remove(mushroom.type as string)
            }
        }

        const index = activeMushrooms.indexOf(mushroom)

        if (index > -1) {
            activeMushrooms.splice(index, 1)
        }

        console.log(...defaultLabel('Mushroom unmounted'), activeMushrooms)
    },

}

const mushroomDvdHandler: MushroomHandler = {
    elements: [],
    eventBehavior: () => {},
    mount(mushroom: Mushroom) {
        const mainContainer = document.getElementById('app')

        const interval = setInterval(() => {
            const randomX = Math.floor(Math.random() * window.innerWidth)
            const randomY = Math.floor(Math.random() * window.innerHeight)

            const div = document.createElement('div')
            div.classList.add('fools-day--dvd')
            div.style.position = 'absolute'
            div.style.left = `${randomX}px`
            div.style.top = `${randomY}px`
            div.style.zIndex = '1'
            div.style.width = '200px'
            div.style.height = '200px'

            const dvdDiv = document.createElement('div')
            dvdDiv.classList.add('loader')

            div.appendChild(dvdDiv)

            mainContainer?.appendChild(div)
        }, 100)

        setTimeout(() => {
            clearInterval(interval)
        }, mushroom.duration - 200)
    },
    unmount(mushroom: Mushroom) {
        const mainContainer = document.getElementById('app')
        const randomDivs = mainContainer?.querySelectorAll('.fools-day--dvd')
        randomDivs?.forEach(div => {
            mainContainer?.removeChild(div)
        })
    },
}

let hasListener = false

const mushroomFartHandler: MushroomHandler = {
    elements: [],
    eventBehavior: null,
    mount() {
        if (hasListener) {
            return
        }

        this.eventBehavior = () => {
            const audio = new Audio(MushroomFartSound)
            audio.play()
        }

        document.addEventListener('click', this.eventBehavior)

        hasListener = true
    },
    unmount() {
        document.removeEventListener('click', this.eventBehavior)

        hasListener = false
    },
}

const mushroomTypes: {
    [key: string]: {
        handler: MushroomHandler
        duration?: number
        icon: string
    }
} = {
    // Disable DVD, because it has "TBC" in it, which should be changed for BCF and BCS.
    // 'fools-day--dvd': {
    //     handler: mushroomDvdHandler,
    //     icon: MushroomIcon2,
    // },
    'fools-day--fall': {
        handler: mushroomSpinHandler,
        icon: MushroomIcon2,
    },
    'fools-day--flip': {
        handler: mushroomSpinHandler,
        icon: MushroomIcon,
    },
    'fools-day--glitch-text': {
        handler: mushroomSpinHandler,
        icon: MushroomIcon,
    },
    'fools-day--random-bg': {
        handler: mushroomSpinHandler,
        icon: MushroomIcon1,
    },
    'fools-day--shake': {
        handler: mushroomSpinHandler,
        icon: MushroomIcon2,
    },
    'fools-day--spin': {
        handler: mushroomSpinHandler,
        icon: MushroomIcon2,
    },
    'fools-day--zoom': {
        handler: mushroomSpinHandler,
        icon: MushroomIcon5,
    },
    // 'fools-day--spin-inverted': {
    //     handler: mushroomSpinHandler,
    //     icon: MushroomIcon2,
    // },
    'fools-day--opacity': {
        handler: mushroomSpinHandler,
        icon: MushroomIcon3,
    },
    'fart': {
        handler: mushroomFartHandler,
        icon: MushroomIcon4,
    },
}

let interval
const timeouts = []

const initSpawner = () => {
    if (interval) {
        clearInterval(interval)
    }

    interval = setInterval(() => {
        if (mushrooms.value.length >= settings.maxMushrooms) {
            return
        }

        if (Math.random() > settings.spawnChance) {
            return
        }

        spawnMushroom()
    }, settings.spawnInterval)
}

watch([
    () => settings.maxMushrooms,
    () => settings.spawnInterval,
    () => settings.spawnChance,
], () => {
    initSpawner()
}, { deep: true })

onMounted(() => {
    initSpawner()

    document.addEventListener('keyup', onKeyup)
})

onUnmounted(() => {
    clearInterval(interval)

    mushrooms.value.forEach((mushroom: Mushroom) => {
        mushroom.handler.unmount(mushroom)
    })

    activeMushrooms.forEach((mushroom: Mushroom) => {
        mushroom.handler.unmount(mushroom)
    })

    timeouts.forEach((timeout) => {
        clearTimeout(timeout)
    })

    document.removeEventListener('keyup', onKeyup)
})

const onKeyup = (event) => {
    if (event.key === 'Escape') {
        activeMushrooms.forEach((mushroom: Mushroom) => {
            mushroom.handler.unmount(mushroom)
        })

        mushrooms.value.forEach(mushroom => despawnMushroom(mushroom))
    }
}

function spawnMushroom(type?: MushroomType) {
    if (!type) {
        type = Object.keys(mushroomTypes)[random(0, Object.keys(mushroomTypes).length - 1)] as MushroomType
    }

    const mushroomType = mushroomTypes[type]

    const duration = mushroomType.duration ?? random(5, settings.duration) * 1000

    const size = duration / 1000 / settings.duration * settings.size

    const mushroom: Mushroom = {
        id: Math.random(),
        x: Math.round(Math.random() * window.innerWidth),
        y: Math.round(Math.random() * window.innerHeight),
        type,
        handler: mushroomType.handler,
        duration,
        icon: mushroomType.icon,
        size: minmax(size, [10, settings.size]),
    }

    console.log(...defaultLabel('Mushroom spawned'), mushroom)

    mushrooms.value.push(mushroom)
}

function despawnMushroom(mushroom: Mushroom) {
    const index = mushrooms.value.indexOf(mushroom)

    if (index > -1) {
        mushrooms.value.splice(index, 1)
    }
}

function emitEvent(mushroom: Mushroom) {
    mushroom.handler.mount(mushroom)

    const timeout = setTimeout(() => {
        mushroom.handler.unmount(mushroom)
    }, mushroom.duration)

    timeouts.push(timeout)

    despawnMushroom(mushroom)

    console.log(...defaultLabel('Mushroom emitted'), mushroom)
}

//

const random = (min, max) => {
    return Math.floor(Math.random() * (max - min + 1) + min)
}

//

type MushroomType = keyof typeof mushroomTypes
type Mushroom = {
    id: number
    x: number
    y: number
    type: MushroomType
    handler: MushroomHandler
    duration: number
    size: number
    icon: any
}

interface MushroomHandler {
    elements: HTMLElement[]
    eventBehavior: (e: Event) => void
    mount: (mushroom: Mushroom) => void
    unmount: (mushroom: Mushroom) => void
}
</script>
