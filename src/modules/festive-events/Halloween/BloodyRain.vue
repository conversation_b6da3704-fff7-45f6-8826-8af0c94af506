<template>
    <div class="background">
        <canvas id="canvas1" />
        <canvas id="canvas2" />
        <canvas id="canvas3" />
    </div>
</template>

<script setup>
// // // // [ BLOODY RAIN ] // // // //

onMounted(() => {
    setTimeout(() => {
        const canvas1 = document.getElementById('canvas1')
        const canvas2 = document.getElementById('canvas2')
        const canvas3 = document.getElementById('canvas3')
        const ctx1 = canvas1.getContext('2d')
        const ctx2 = canvas2.getContext('2d')
        const ctx3 = canvas3.getContext('2d')

        const rainthroughnum = 500
        const speedRainTrough = 205
        const RainTrough = []

        const rainnum = 1000
        const rain = []

        const lightning = []
        let lightTimeCurrent = 0
        const lightTimeTotal = 200 // Adjust this value as needed

        const resizeCanvas = () => {
            const w = canvas1.width = canvas2.width = canvas3.width = window.innerWidth
            const h = canvas1.height = canvas2.height = canvas3.height = window.innerHeight
        }

        window.addEventListener('resize', resizeCanvas)
        resizeCanvas()

        const random = (min, max) => Math.random() * (max - min + 1) + min

        const clearCanvas1 = () => ctx1.clearRect(0, 0, canvas1.width, canvas1.height)

        const clearCanvas2 = () => ctx2.clearRect(0, 0, canvas2.width, canvas2.height)

        const clearCanvas3 = () => {
            ctx3.globalCompositeOperation = 'destination-out'
            ctx3.fillStyle = `rgba(0,0,0,${random(1, 30) / 100})`
            ctx3.fillRect(0, 0, canvas3.width, canvas3.height)
            ctx3.globalCompositeOperation = 'source-over'
        }

        const createRainTrough = () => {
            for (let i = 0; i < rainthroughnum; i++) {
                RainTrough[i] = {
                    x: random(0, canvas1.width),
                    y: random(0, canvas1.height),
                    length: Math.floor(random(1, 830)),
                    opacity: Math.random() * 0.2,
                    xs: random(-2, 2),
                    ys: random(10, 20),
                }
            }
        }

        const createRain = () => {
            for (let i = 0; i < rainnum; i++) {
                rain[i] = {
                    x: Math.random() * canvas2.width,
                    y: Math.random() * canvas2.height,
                    l: Math.random() * 1,
                    xs: -4 + Math.random() * 4 + 2,
                    ys: Math.random() * 10 + 10,
                }
            }
        }

        const createLightning = () => {
            const x = random(100, canvas3.width - 100)
            const y = random(0, canvas3.height / 4)

            const createCount = random(1, 3)
            for (let i = 0; i < createCount; i++) {
                const single = {
                    x: x,
                    y: y,
                    xRange: random(5, 30),
                    yRange: random(10, 25),
                    path: [{ x: x, y: y }],
                    pathLimit: random(40, 55),
                }
                lightning.push(single)
            }
        }

        const drawRainTrough = (i) => {
            ctx1.beginPath()
            const grd = ctx1.createLinearGradient(0, RainTrough[i].y, 0, RainTrough[i].y + RainTrough[i].length)
            grd.addColorStop(0, 'rgba(175,0,0,0)')
            grd.addColorStop(1, `rgba(175,0,0,${RainTrough[i].opacity})`)

            ctx1.fillStyle = grd
            ctx1.fillRect(RainTrough[i].x, RainTrough[i].y, 1, RainTrough[i].length)
            ctx1.fill()
        }

        const drawRain = (i) => {
            ctx2.beginPath()
            ctx2.moveTo(rain[i].x, rain[i].y)
            ctx2.lineTo(rain[i].x + rain[i].l * rain[i].xs, rain[i].y + rain[i].l * rain[i].ys)
            ctx2.strokeStyle = 'rgba(255,0,0,0.85)'
            ctx2.lineWidth = 1
            ctx2.lineCap = 'round'
            ctx2.stroke()
        }

        const drawLightning = () => {
            for (let i = 0; i < lightning.length; i++) {
                const light = lightning[i]

                light.path.push({
                    x: light.path[light.path.length - 1].x + (random(0, light.xRange) - (light.xRange / 2)),
                    y: light.path[light.path.length - 1].y + (random(0, light.yRange)),
                })

                if (light.path.length > light.pathLimit) {
                    lightning.splice(i, 1)
                }

                ctx3.strokeStyle = 'rgba(154, 154, 154, .05)'
                ctx3.lineWidth = 3

                if (random(0, 15) === 0) {
                    ctx3.lineWidth = 6
                }

                if (random(0, 30) === 0) {
                    ctx3.lineWidth = 8
                }

                ctx3.beginPath()
                ctx3.moveTo(light.x, light.y)
                for (let pc = 0; pc < light.path.length; pc++) {
                    ctx3.lineTo(light.path[pc].x, light.path[pc].y)
                }

                if (Math.floor(random(0, 30)) === 1) {
                    ctx3.fillStyle = `rgba(255, 255, 255, ${random(1, 3) / 100})`
                    ctx3.fillRect(0, 0, canvas3.width, canvas3.height)
                }
                ctx3.lineJoin = 'miter'
                ctx3.stroke()
            }
        }

        const animateRainTrough = () => {
            clearCanvas1()
            for (let i = 0; i < rainthroughnum; i++) {
                if (RainTrough[i].y >= canvas1.height) {
                    RainTrough[i].y = canvas1.height - RainTrough[i].y - RainTrough[i].length * 5
                } else {
                    RainTrough[i].y += speedRainTrough
                }
                drawRainTrough(i)
            }
        }

        const animateRain = () => {
            clearCanvas2()
            for (let i = 0; i < rainnum; i++) {
                rain[i].x += rain[i].xs
                rain[i].y += rain[i].ys

                if (rain[i].x > canvas2.width || rain[i].y > canvas2.height) {
                    rain[i].x = Math.random() * canvas2.width
                    rain[i].y = -20
                }
                drawRain(i)
            }
        }

        const animateLightning = () => {
            clearCanvas3()
            lightTimeCurrent++

            if (lightTimeCurrent >= lightTimeTotal) {
                createLightning()
                lightTimeCurrent = 0
            }
            drawLightning()
        }

        const init = () => {
            createRainTrough()
            createRain()
            window.addEventListener('resize', createRainTrough)
        }
        init()

        const animloop = () => {
            animateRainTrough()
            animateRain()
            animateLightning()
            requestAnimationFrame(animloop)
        }
        animloop()
    }, 100)
})
</script>

<style scoped>
.background {
    width: 100vw;
    height: 100vh;
    position: absolute;
    top: 0;
    left: 0;
    transform: scale(1,1);
    transition: transform 1.5s;
    background-color:transparent;
    pointer-events: none;
    z-index: 99;
}

/* rain */

canvas {
    width: 100%;
    height: 100%;
    z-index: 99;
}

canvas {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 99;
}

#canvas2 {
    z-index: 10;
    z-index: 99;
}

#canvas1 {
    filter: blur(1px);
    z-index: 100;
    z-index: 99;
}
</style>
