<template>
    <div
        ref="container"
        class="context-menu dropdown-menu-context min-w-40 pa-2 z-50"
        tabindex="-1"
    >
        <div class="dropdown-menu__content mt-0 card dark:bg-dark-1 border border-theme-38/20 p-2 select-none">
            <slot :options="options">
                <ButtonWrapper
                    v-for="(option, $i) in options"
                    :key="$i"
                    v-tooltip="{content: option.tooltip, disabled: !option.tooltip}"
                    class="dropdown-menu__item"
                    :class="option.class"
                    @click="(event) => {
                        option.stop ? event.stopPropagation() : null
                        option.onClick ? option.onClick(event) : {}

                        if (! option.keep) {
                            $emit('close')
                        }
                    }"
                >
                    <div v-if="hasIcon" class="w-[16px] h-[16px] mr-2">
                        <Loader v-if="toValue(option.loading)" />
                        <Component
                            :is="option.icon"
                            v-else-if="option.icon"
                        />
                    </div>
                    {{ option.text }}
                </ButtonWrapper>
            </slot>
        </div>
    </div>
</template>

<script setup lang='ts'>
import ButtonWrapper from '@/components/buttons/ButtonWrapper.vue'
import type { ContextMenuOption } from '@/plugins/ContextMenuPlugin'
import { toValue } from 'vue'
import { onClickOutside, useCurrentElement } from '@vueuse/core'

defineOptions({
    name: 'ContextMenu',
})

const props = withDefaults(defineProps<{
    options: ContextMenuOption[],
    clickAway?: boolean,
}>(), {
    clickAway: true,
})

const emit = defineEmits<{
    close: [],
}>()

const hasIcon = computed(() => {
    return props.options.some(option => option.icon || 'loading' in option)
})

function onClickAway() {
    if (props.clickAway) {
        emit('close')
    }
}

onClickOutside(useCurrentElement(), onClickAway)
</script>
