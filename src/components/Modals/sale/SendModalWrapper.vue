<template>
    <AppModalWrapper
        :header="header"
        close-button
        :class="{
            'min-w-[1000px]': showTrustPilot
        }"
    >
        <template #default>
            <form
                ref="form"
                class="border-t border-gray-200 h-[720px] flex flex-col"
                @submit.prevent="submit"
            >
                <div class="pr-3 border-b-4 flex gap-4 justify-between items-center">
                    <Tabs
                        v-if="tabs"
                        class="px-3 mr-3 self-end"
                        :tabs="tabs"
                        :model-value="currentTab"
                        @update:model-value="$emit('update:current-tab', $event)"
                    />

                    <div v-if="showTrustPilot" class="btn-group gap-x-0.5">
                        <InputRadioButton
                            v-model="send_option_type"
                            name="send_option_type"
                            value="empty"
                        >
                            Dont send
                        </InputRadioButton>
                        <InputRadioButton
                            v-model="send_option_type"
                            name="send_option_type"
                            value="trust_pilot"
                        >
                            Trustpilot
                        </InputRadioButton>
                        <InputRadioButton
                            v-model="send_option_type"
                            name="send_option_type"
                            value="google"
                        >
                            Google
                        </InputRadioButton>
                    </div>

                    <InputLayoutWrapper
                        ref="emailField"
                        v-model="emailField"
                        class="flex items-center whitespace-nowrap py-2 ml-4"
                    >
                        <InputLabelWrapper class="form-label text-xs font-medium m-0 mr-2.5">
                            Client email
                        </InputLabelWrapper>

                        <ClientEmailDataDropdown
                            ref="emailRef"
                            class="max-w-[fit-content]"
                            :client-pk="clientPk"
                            :with-edit="canEdit"
                            with-remark
                            with-title
                            :default-email="defaultEmail"
                            :selected-email-pk="String(emailField.value)"
                            :on-select="({ email }) => {
                                emailField.value = email.id
                                emailRef?.close()
                            }"
                        />
                    </InputLayoutWrapper>
                </div>
                <slot />
            </form>
        </template>

        <template #footer>
            <div class="flex">
                <AppModalButton
                    class="btn-close mr-auto"
                    @click="close"
                >
                    Close
                </AppModalButton>
                <AppButton
                    class="--outline ml-4"
                    :disabled="loading"
                    :loading="loading"
                    @click="submitToMe"
                >
                    Send to me
                </AppButton>
                <AppButton
                    class="--primary ml-4"
                    :disabled="loading"
                    :loading="loading"
                    @click="submit"
                >
                    Send
                </AppButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script lang="ts">
import AppModalButton from '@/components/Modals/core/AppModalButton.vue'
import Tabs from '@/components/tabs/Tabs.vue'
import InputLayoutWrapper from '@/components/Form/FormWrappers/InputLayoutWrapper'
import InputLabelWrapper from '@/components/Form/FormWrappers/InputLabelWrapper'
import ClientEmailDataDropdown from '~/components/ClientEmailData/ClientEmailDataDropdown.vue'
import InputField from '@/lib/FormField/InputField'

export default defineComponent({
    name: 'SendModalWrapper',

    components: {
        ClientEmailDataDropdown,
        Tabs,
        AppModalButton,
        InputLabelWrapper,
        InputLayoutWrapper,
    },

    props: {
        header: {
            type: String,
            required: true,
        },

        email: {
            type: String,
            default: '',
        },

        tabs: {
            type: Array,
            default: null,
        },

        currentTab: {
            type: [String, Number],
            default: null,
        },

        showTrustPilot: {
            type: Boolean,
            default: false,
        },

        reviewLink: {
            type: String,
            default: 'trust_pilot',
        },

        clientPk: {
            type: String,
            default: null,
        },

        canEdit: {
            type: Boolean,
            default: null,
        },

        defaultEmail: {
            type: String,
            default: '',
        },

        loading: {
            type: Boolean,
            default: false,
        },
    },

    emits: ['close', 'submit', 'update:current-tab'],

    async setup(props) {
        const emailRef = ref<{
            close(): void
        }>()

        const { useModel } = useContext()

        const clientEmail = useModel('ClientEmailData').useResourceList({
            name: 'ClientClientEmailDataList',
            pk: props.clientPk,
        }, {
            with: ['email'],
        })

        const defaultEmailPk = (await clientEmail.fetch()).find(email => email.email.value === props.defaultEmail)?.email_pk

        return {
            defaultEmailPk,
            emailRef,
        }
    },

    data() {
        return {
            emailField: new InputField(true, 'Email', { placeholder: '', value: Number(this.defaultEmailPk) }),
            send_option_type: this.reviewLink,
        }
    },

    watch: {
        reviewLink: {
            immediate: true,
            handler() {
                this.send_option_type = this.reviewLink
            },
        },
    },

    methods: {
        submitToMe() {
            this.$emit('submit', 'me')
        },

        submit() {
            if (this.emailField.validate()) {
                if (this.showTrustPilot) {
                    this.$emit('submit', this.emailField.value, this.send_option_type)
                } else {
                    this.$emit('submit', this.emailField.value)
                }
            } else {
                this.notify({
                    message: 'The email must be a valid email address',
                    type: 'error',
                })
            }
        },

        notify({ message, type }) {
            this.$toast.open({
                position: 'top-right',
                type,
                message,
                duration: 3000,
            })
        },

        close() {
            this.$emit('close')
        },

        success() {
            this.notify({
                message: 'Email was sent',
                type: 'success',
            })
            this.close()
        },

        error(error) {
            if (error.name === 'ApiErrorForm' && 'email' in error.data) {
                error.markAsProcessed()

                this.emailField.addError(error.data.email)

                this.notify({
                    message: `<strong>${error.text}</strong> <br> ${error.data.email[0]}`,
                    type: 'error',
                })
            }
        },
    },
})
</script>
