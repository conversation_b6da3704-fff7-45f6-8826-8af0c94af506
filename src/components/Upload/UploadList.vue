<template>
    <div class="upload-list">
        <UploadListFile
            v-for="(file, index) in files"
            :key="index"
            :file="file"
            :box="box"
            :can-preview="canPreview"
            :can-download="canDownload"
            :can-delete="canDelete"
            @cancel="$emit('cancel', file)"
        />
    </div>
</template>

<script>
import { getExtensionImage } from '@/modules/chat/lib/Helpers/ChatHelpers'
import UploadListFile from '@/components/Upload/UploadListFile'

export default {
    name: 'UploadList',

    components: {
        UploadListFile,
    },

    props: {
        /** @type {import('vue').PropOptions<FileData[]>} */
        files: {
            type: Array,
            default: () => [],
        },

        box: {
            type: Boolean,
            default: false,
        },

        canPreview: {
            type: Boolean,
            default: false,
        },

        canDownload: {
            type: Boolean,
            default: false,
        },

        canDelete: {
            type: Boolean,
            default: true,
        },
    },

    emits: ['cancel'],

    methods: {
        getExtensionImage,
    },
}
</script>
