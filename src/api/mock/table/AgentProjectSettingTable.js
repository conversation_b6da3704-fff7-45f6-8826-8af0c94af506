import FakeTable from '@/api/mock/lib/FakeTable'
import fakeDB from '@/api/mock/fakeDB'
import { faker } from '@faker-js/faker'
import FakeRecord from '@/api/mock/lib/FakeRecord'

class AgentProjectSettingRecord extends FakeRecord {
    static primaryKey = ['agent_id', 'project_id']

    agent_id = null
    project_id = null
    _email = null
    phone = null
    voip_ext = '123'

    get email() {
        return this._email
    }

    set email(v) {
        if (v && this.is_default) {
            fakeDB.agents.update(this.agent_id, { email: v })
        }

        this._email = v
    }

    /**
     * @return {AgentRecord|null}
     */
    get agent() {
        return fakeDB.agents.findByPK(this.agent_id)
    }

    /**
     * @return {ProjectRecord|null}
     */
    get project() {
        return fakeDB.projects.findByPK(this.project_id)
    }

    get is_default() {
        return this.project?.is_default || 0
    }

    set is_default(v) {
        this.project.is_default = v
    }
}

export default class AgentProjectSettingTable extends FakeTable {
    get recordModel() {
        return AgentProjectSettingRecord
    }

    constructor() {
        super('agentProjectSettings', [])
    }

    generateData() {
        fakeDB.agents.records.forEach(agent => {
            agent.companies.forEach(company => {
                const projects = fakeDB.projects.find({ company_id: company })
                projects.forEach(project => {
                    try {
                        this.insert({
                            agent_id: agent.id,
                            project_id: project.id,
                            email: agent.email.replace(/@.+$/i, `@${project.project_abbr.toLowerCase()}.loc`),
                            phone: faker.helpers.replaceSymbols('+48 ## ### ## ##'),
                        })
                    } catch (e) {
                        // console.log(e)
                        // console.log(agent.id, project.id)
                    }
                })
            })

            return true
        })
    }
}
