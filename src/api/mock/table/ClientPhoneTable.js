import FakeTable from '@/api/mock/lib/FakeTable'
import FakeRecord from '@/api/mock/lib/FakeRecord'
import { faker } from '@faker-js/faker'
import fakeDB from '@/api/mock/fakeDB'

class ClientPhoneRecord extends FakeRecord {
    static primaryKey = ['client_id', 'phone_id']

    client_id = null
    phone_id = null
    phone = null
    _is_default = null

    get is_default() {
        return this._is_default
    }

    set is_default(v) {
        if (v) {
            fakeDB.clientPhones.updateAll({ client_id: this.client_id, is_default: 1 }, { is_default: 0 })
        }
        this._is_default = v ? 1 : 0
    }

    $generatePrimaryKey(table) {
        this.phone_id = table.uiid()
    }

    /**
     * @param table {ClientPhoneTable}
     */
    $setDefaultValues(table) {
        this.$setDefaultValue('is_default', 0)
    }
}

export default class ClientPhoneTable extends FakeTable {
    get recordModel() {
        return ClientPhoneRecord
    }

    constructor() {
        super('clientPhones', [])
    }

    buildNew(client_id) {
        return this.insert({
            client_id: client_id,
            phone: faker.helpers.replaceSymbols('+48 ## ### ## ##'),
        })
    }
}
