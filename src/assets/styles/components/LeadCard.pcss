@layer components {
    .lead-card {
        @apply relative flex flex-col border border-theme-38/30 rounded-lg px-3.5 py-2.5 cursor-pointer mt-2  dark:bg-dark-1;
        box-shadow: 0px 4px 40px rgba(0, 0, 0, 0.05);

        &-header {
            @apply flex items-center pb-1.5 border-b border-theme-38/30 text-xs leading-tight;
        }

        &-created,
        &-extra,
        &-pqs {
            @apply flex items-center;
        }

        &-extra {
            @apply ml-auto
        }

        &-body {
            @apply relative pt-2.5 pb-2;
        }

        &-remaining {
            @apply absolute top-0 left-1/2 -translate-x-1/2 text-2xs font-medium leading-tight bg-orange-300/80 py-px px-1.5 rounded-b dark:text-gray-900
        }

        &-routes {
            @apply flex items-center justify-between;

            &-from,
            &-to {
                @apply text-xl font-semibold;
            }
        }

        &-dates {
            @apply flex items-center justify-between;
        }

        &-passengers {
            @apply flex items-center justify-between mt-2.5;

            &-count {
                @apply flex items-center space-x-3;
            }

            &-adt,
            &-chd,
            &-inf {
                @apply flex items-center font-medium leading-tight;
            }
        }

        &-footer {
            @apply flex justify-between pt-1.5 border-t border-theme-38/30;
        }

        &-customer,
        &-agent,
        &-expert {
            @apply flex flex-col;
        }

        &-active {
            @apply ring-2 ring-primary-1 border-transparent dark:ring-primary-8;
        }

        &-badge {
            @apply flex items-center justify-center w-4 h-4 rounded-full text-xs leading-none font-semibold mr-2 bg-red-500 text-white shadow-sm;

            &-lg {
                @apply w-9 h-9 text-base border-2 border-white shadow-sm dark:border-dark-3
            }

            &.\--potential {
                @apply bg-vodka-200 text-white dark:text-gray-800
            }

            &.\--closed {
                @apply bg-red-500 text-white
            }

            &.\--follow-up {
                @apply bg-feldspar-300 text-white dark:text-gray-100
            }

            &.\--sold {
                @apply bg-vista-blue-300 text-white dark:text-gray-800
            }

            &.\--bonus {
                @apply bg-aquamarine-300 text-white dark:text-gray-800
            }

            &.\--new {
                @apply bg-orange-500 text-white dark:text-gray-800
            }

            &.\--lost {
                @apply bg-slate-500 text-white dark:text-gray-800
            }

            &.\--sale-rejected {
                @apply bg-rose-700 text-white
            }

            &.\--sale-rejected-inactive {
                @apply bg-indigo-900 text-white
            }

            &.\--fraud {
                @apply bg-danger text-white
            }

        }

        &-abt {
            @apply bg-red-50 border-red-200 dark:bg-red-600/20 dark:border-red-600/40;
        }

        &-abt & {
            &-header,
            &-footer {
                @apply dark:border-red-600/40;
            }

            &-header {
                @apply text-gray-600  dark:text-gray-300
            }
        }

        &.\--lost {
            @apply border-orange-300 bg-orange-50 dark:bg-orange-500/10 dark:border-orange-300/80
        }

        &.\--lost & {
            &-header,
            &-footer {
                @apply border-orange-300 dark:border-orange-300/80;
            }
        }
    }
}
