import { defineNitroConfig } from 'nitropack/config'
import { autoImports } from './vite.config'
import { nitroAlias as alias } from './path.config'
import * as path from 'path'

const nitroAutoImports = structuredClone(autoImports)

// @ts-ignore
nitroAutoImports.dirs.push('./mock/composables/**/*')

export default defineNitroConfig({
    srcDir: path.resolve(__dirname, 'mock'),

    routeRules: {
        '/**': {
            cors: true,
        },
    },

    alias,

    imports: nitroAutoImports,

    rollupConfig: {
        plugins: [
            skipExtensions({
                extensions: ['svg', 'svg?component', 'vue'],
            }),
        ],

        external: [
            '@/modules/chat/lib/ChatManager',
        ],
    },

    runtimeConfig: {
        markup: {
            apiBase: 'https://mixer.test.tmgclick.com',
            secret: 'veryUnsafeSecret',
        },
        consolidatorTool: {
            apiBase: 'https://cmt.test.tmgclick.com',
            secret: 'SNDg782hJkk2Df4',

            // apiBase: 'https://cmt.stage.tmgclick.com',
            // secret: 'HSdy82hJh2iKJWd',
        },
    },

    compatibilityDate: '2025-01-23',
})

function skipExtensions(options: {
    extensions: string[]
}) {
    return {
        name: 'skip-extensions',

        resolveId(id: string) {
            if (options.extensions.some(ext => id.includes('.' + ext))) {
                return id
            }

            return null
        },

        load(id: string) {
            if (options.extensions.some(ext => id.includes('.' + ext))) {
                return {
                    code: 'export default null',
                    map: null,
                }
            }

            return null
        },
    }
}
