import ModelDatabaseSeeder from '~mock/seeds/ModelDatabaseSeeder'
import ReleasePostFactory from '~mock/factories/ReleasePost/ReleasePostFactory'

const modelName = 'ReleasePost'

export default class ReleasePostSeeder extends ModelDatabaseSeeder<typeof modelName> {
    public modelName = modelName

    public readonly factories = {
        releasePost: new ReleasePostFactory(),
    }

    public async seed() {
        // for (let i = 0; i < 20; i++) {
        //     await this.factories.releasePost.create(undefined, {
        //         withContents: true,
        //         withFeedbacks: true,
        //     })
        // }
    }
}
