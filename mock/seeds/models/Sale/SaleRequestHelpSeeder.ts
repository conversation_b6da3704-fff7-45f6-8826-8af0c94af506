import ModelDatabaseSeeder from '~mock/seeds/ModelDatabaseSeeder'
import SaleRequestHelpFactory from '~mock/factories/Sale/SaleRequestHelpFactory'

const modelName = 'SaleRequestHelp'

export default class SaleRequestHelpSeeder extends ModelDatabaseSeeder<typeof modelName> {
    public modelName = modelName

    public readonly factories = {
        saleRequestHelp: new SaleRequestHelpFactory(),
    }

    public async seed() {
        for (let i = 0; i < 5; i++) {
            await this.factories.saleRequestHelp.create()
        }
    }
}
