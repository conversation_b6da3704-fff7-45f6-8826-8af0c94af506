import ModelDatabaseSeeder from '~mock/seeds/ModelDatabaseSeeder'
import { findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import SaleVersionCardFactory from '~mock/factories/Card/SaleVersionCardFactory'

const modelName = 'SaleVersionCard'
export default class SaleVersionCardSeeder extends ModelDatabaseSeeder<typeof modelName> {
    public modelName = modelName

    public factories = {
        saleVersionCard: new SaleVersionCardFactory(),
    }

    public async seed() {
        const saleList = await findModelRecords('Sale')
        const clientCreditCardList = await findModelRecords('ClientCreditCard')

        for (const sale of saleList) {
            await this.factories.saleVersionCard.create({
                sale_pk: usePk(sale),
                sale_version_pk: sale.sale_version_pk,
                client_card_pk: randomElementPk(clientCreditCardList),
            })
        }
    }
}
