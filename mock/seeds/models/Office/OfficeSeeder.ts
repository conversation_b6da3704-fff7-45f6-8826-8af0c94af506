import ModelDatabaseSeeder from '~mock/seeds/ModelDatabaseSeeder'
import OfficeFactory from '~mock/factories/Office/OfficeFactory'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'

const modelName = 'Office'

export enum MockOfficeID {
    Chisinau = 1,
    ChisinauBC = 2,
    Deli = 3,
}

export default class OfficeSeeder extends ModelDatabaseSeeder<typeof modelName> {
    public modelName = modelName
    public readonly factories = {
        office: new OfficeFactory(),
    }

    public async seed() {
        await this.factories.office.create({
            id: MockOfficeID.Chisinau,
            name: 'Chisinau',
            ip_white_list: [],
            is_strict_access: false,
            system_name: 'md',
            created_at: dateToUnixTimestamp(new Date()),
        })

        await this.factories.office.create({
            id: MockOfficeID.ChisinauBC,
            name: 'Chisinau BCS',
            ip_white_list: [],
            is_strict_access: false,
            system_name: 'md_bc',
            created_at: dateToUnixTimestamp(new Date()),
        })

        await this.factories.office.create({
            id: MockOfficeID.Deli,
            name: '<PERSON>i',
            ip_white_list: ['*************', '*************', '*************'],
            is_strict_access: true,
            system_name: 'deli',
            created_at: dateToUnixTimestamp(new Date()),
        })
    }
}
