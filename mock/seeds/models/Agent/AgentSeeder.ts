import ModelDatabaseSeeder from '~mock/seeds/ModelDatabaseSeeder'
import { findModelRecordOrFail, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { PositionName } from '~/api/models/Position/Position'
import { tryUsePk } from '~/composables/usePk'
import { DepartmentName } from '~/api/models/Department/Department'
import AgentFactory from '~mock/factories/Agent/AgentFactory'
import { DepartmentCategory } from '~/api/models/Agent/Agent'
import { randomElement, randomElementPk } from '~/lib/Helper/ArrayHelper'
import { MockOfficeID } from '~mock/seeds/models/Office/OfficeSeeder'

const modelName = 'Agent'

export default class AgentSeeder extends ModelDatabaseSeeder<typeof modelName> {
    public readonly modelName = modelName
    public readonly relatedModels = ['Team']

    public readonly factories = {
        agent: new AgentFactory(),
    }

    public async seed() {
        const positions = await findModelRecords('Position')
        const departments = await findModelRecords('Department')
        const teams = await findModelRecords('Team')

        const positionManagerId = tryUsePk(positions.find(position => position.system_name === PositionName.Manager))
        const departmentITId = tryUsePk(departments.find(department => department.system_name === DepartmentName.IT))

        await this.factories.agent.create({
            email: '<EMAIL>',
            first_name: 'Admin',
            last_name: 'TBC',
            position_pk: positionManagerId,
            department_pk: departmentITId,
            department_category: [DepartmentCategory.Business],
            is_bot: false,
            office_pk: String(MockOfficeID.Chisinau),
        })

        await this.factories.agent.create({
            email: '<EMAIL>',
            first_name: 'Admin',
            last_name: 'BCF',
            position_pk: positionManagerId,
            department_pk: departmentITId,
            department_category: [DepartmentCategory.Business],
            is_bot: false,
            office_pk: String(MockOfficeID.Deli),
        })

        await this.factories.agent.create({
            email: '<EMAIL>',
            first_name: 'Admin',
            last_name: 'BCS',
            position_pk: positionManagerId,
            department_pk: departmentITId,
            department_category: [DepartmentCategory.Business],
            is_bot: false,
            office_pk: String(MockOfficeID.ChisinauBC),
        })

        await this.factories.agent.create({
            email: '<EMAIL>',
            first_name: 'Admin',
            last_name: 'TMG',
            position_pk: positionManagerId,
            department_pk: departmentITId,
            department_category: [DepartmentCategory.Business],
            is_bot: false,
            ringcentral_id: 524254030, // <EMAIL> user
        })

        await this.factories.agent.create({
            email: '<EMAIL>',
            first_name: 'System',
            last_name: 'Bot',
            position_pk: positionManagerId,
            department_pk: departmentITId,
            department_category: [DepartmentCategory.Business],
            is_bot: true,
            office_pk: String(MockOfficeID.Chisinau),
        })

        for (let i = 0; i < 50; i++) {
            await this.factories.agent.create({
                office_pk: '1',
                department_category: [randomElement([DepartmentCategory.Business, DepartmentCategory.Service])],
            })
        }

        for (let i = 0; i < 5; i++) {
            await this.factories.agent.create({
                first_name: 'Fallen Agent ' + i,
                last_name: '',
                team_pk: '21',
                ex_team_pk: randomElementPk(teams),
                department_category: [DepartmentCategory.Business],
            })
        }

        const position_agent = await findModelRecordOrFail('Position', { system_name: PositionName.Agent })

        const position_supervisor = await findModelRecordOrFail('Position', { system_name: PositionName.Supervisor })
        const experts_department = await findModelRecordOrFail('Department', {
            system_name: DepartmentName.Experts,
        })

        for (let i = 0; i < 10; i++) {
            await this.factories.agent.create({
                position_pk: usePk(position_agent),
                department_pk: usePk(experts_department),
            })
        }

        const special_services_department = await findModelRecordOrFail('Department', {
            system_name: DepartmentName.SpecialServices,
        })

        for (let i = 0; i < 3; i++) {
            await this.factories.agent.create({
                position_pk: usePk(position_agent),
                department_pk: usePk(special_services_department),
            })
        }

        const verification_department = await findModelRecordOrFail('Department', {
            system_name: DepartmentName.Verification,
        })

        for (let i = 0; i < 5; i++) {
            await this.factories.agent.create({
                position_pk: usePk(position_agent),
                department_pk: usePk(verification_department),
                office_pk: '2',
            })
        }
        await this.factories.agent.create({
            position_pk: usePk(position_supervisor),
            department_pk: usePk(verification_department),
            office_pk: '1',
        })
    }
}
