import ModelDatabaseSeeder from '~mock/seeds/ModelDatabaseSeeder'
import AgentReportFactory from '~mock/factories/AgentReport/AgentReportFactory'
import { randomElement, sortByKeyFn } from '~/lib/Helper/ArrayHelper'
import { findModelRecord, findModelRecords, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { usePk } from '~/composables/usePk'
import AgentReportSaleFactory from '~mock/factories/AgentReport/AgentReportSaleFactory'
import AgentReportInvoiceFactory from '~mock/factories/AgentReport/AgentReportInvoiceFactory'
import { DepartmentName } from '~/api/models/Department/Department'
import AgentReportTicketingFactory from '~mock/factories/AgentReport/AgentReportTicketingFactory'
import AgentReportInternalProfitFactory from '~mock/factories/AgentReport/AgentReportInternalProfitFactory'
import AgentReportAdditionalFactory from '~mock/factories/AgentReport/AgentReportAdditionalFactory'
import { PositionName } from '~/api/models/Position/Position'
import { AgentReportAdditionalType } from '~/api/models/AgentReport/AgentReportAdditional'
import { randomFloat } from '~/lib/Helper/NumberHelper'
import ActivityLogDetailedFactory from '~mock/factories/ActivityLog/ActivityLogDetailedFactory'
import { ActivityLogDetailedCategory } from '~/api/models/Log/ActivityLogDetailed'
import type { ModelFields } from '~types/lib/Model'

const modelName = 'AgentReport'

export default class AgentReportSeeder extends ModelDatabaseSeeder<typeof modelName> {
    public readonly modelName = modelName

    public readonly factories = {
        report: new AgentReportFactory(),
        agent_report_sale: new AgentReportSaleFactory(),
        agent_report_invoice: new AgentReportInvoiceFactory(),
        agent_report_ticketing: new AgentReportTicketingFactory(),
        agent_report_internal: new AgentReportInternalProfitFactory(),
        agent_report_additional: new AgentReportAdditionalFactory(),
        log: new ActivityLogDetailedFactory(),
    }

    public async seed() {
        const sales = await this.table('Sale')
        const prepareSales = splitToNChunks(sales, 1)

        const createLog = async (pk: string, attrs?: Partial<ModelFields<'ActivityLogDetailed'>>) => {
            await this.factories.log.create({
                model_pk: pk,
                model_name: 'AgentReport',
                category: ActivityLogDetailedCategory.Financial,
                ...attrs,
            })
        }

        const cs_department = await findModelRecord('Department', {
            system_name: DepartmentName.CustomerSupport,
        })
        const sales_department = await findModelRecord('Department', {
            system_name: DepartmentName.Sales,
        })

        const ticketing_rev_department = await findModelRecord('Department', {
            system_name: DepartmentName.TicketingRevenue,
        })

        const ticketing_award_department = await findModelRecord('Department', {
            system_name: DepartmentName.TicketingAward,
        })

        const verification_department = await findModelRecord('Department', {
            system_name: DepartmentName.Verification,
        })

        const special_services_department = await findModelRecord('Department', {
            system_name: DepartmentName.SpecialServices,
        })

        const executive_department = await findModelRecord('Department', {
            system_name: DepartmentName.Executive,
        })

        const lead_management_department = await findModelRecord('Department', {
            system_name: DepartmentName.LeadManagement,
        })

        if (!cs_department || !sales_department || !ticketing_rev_department || !ticketing_award_department || !verification_department || !special_services_department || !executive_department || !lead_management_department) {
            return
        }

        for (const salesChunk of prepareSales) {
            let profit = 0

            const desc = salesChunk.sort(sortByKeyFn('sale_at', 'desc'))
            const asc = salesChunk.sort(sortByKeyFn('sale_at', 'asc'))

            const max_date = desc[0].sale_at
            const min_date = asc[0].sale_at

            const report = await this.factories.report.create({
                from_date: min_date,
                to_date: max_date,
                total_gross: profit,
                total_net: profit * 0.93,
                completed_at: null,
                department_pks: [usePk(sales_department), usePk(cs_department), usePk(ticketing_rev_department), usePk(ticketing_award_department), usePk(verification_department), usePk(executive_department), usePk(special_services_department), usePk(lead_management_department)],
            })

            await createLog(usePk(report))

            //

            // Sale

            for (const sale of salesChunk) {
                let invoice = await findModelRecord('AgentReportInvoice', {
                    agent_report_pk: usePk(report),
                    agent_pk: sale.executor_pk,
                })

                if (!invoice) {
                    invoice = await this.factories.agent_report_invoice.create({
                        agent_report_pk: usePk(report),
                        agent_pk: sale.executor_pk,
                        department_pk: randomElement(report.department_pks),
                    })
                    await createLog(usePk(report))
                }

                // Ticketing Award
                if (invoice.department_pk === usePk(ticketing_award_department)) {
                    //
                }

                // Ticketing Rev
                if (invoice.department_pk === usePk(ticketing_rev_department)) {
                    const assignment = await this.factories.agent_report_ticketing.create({
                        sale_pk: usePk(sale),
                        agent_invoice_pk: usePk(invoice),
                    })
                    await createLog(usePk(report), {
                        field: 'total_gross',
                        from: String(profit),
                        to: String(profit + assignment.amount),
                    })

                    profit += assignment.amount
                }

                // Sales && CS && verification && SpecServ && LeadManage
                if ([
                    usePk(sales_department),
                    usePk(cs_department),
                    usePk(verification_department),
                    usePk(special_services_department),
                    usePk(lead_management_department),
                ].includes(invoice.department_pk)) {
                    await this.factories.agent_report_sale.create({
                        sale_pk: usePk(sale),
                        agent_invoice_pk: usePk(invoice),
                    })

                    const saleRecordAdditional = await findModelRecord('SaleAdditionalProfits', usePk(sale))

                    if (saleRecordAdditional) {
                        const amount = saleRecordAdditional.ticket_profit + saleRecordAdditional.ticket_protection + saleRecordAdditional.tips
                        await createLog(usePk(report), {
                            field: 'total_gross',
                            from: String(profit),
                            to: String(profit + amount),
                        })

                        profit += amount
                    }
                }

                // Verification
                if ([usePk(verification_department)].includes(invoice.department_pk)) {
                    const internal = await this.factories.agent_report_internal.create({
                        agent_invoice_pk: usePk(invoice),
                    })

                    await createLog(usePk(report), {
                        field: 'total_gross',
                        from: String(profit),
                        to: String(profit + internal.amount),
                    })

                    profit += internal.amount
                }
            }

            const manager_position = await findModelRecord('Position', { system_name: PositionName.Manager })

            if (manager_position) {
                const cs_manager = await findModelRecord('Agent', {
                    position_pk: usePk(manager_position),
                    department_pk: usePk(cs_department),
                })

                if (cs_manager) {
                    const invoice = await this.factories.agent_report_invoice.create({
                        agent_report_pk: usePk(report),
                        agent_pk: usePk(cs_manager),
                        department_pk: usePk(executive_department),
                    })

                    const assignments_types = [AgentReportAdditionalType.CsDepGP, AgentReportAdditionalType.SpecServDepGP, AgentReportAdditionalType.SpecServFeeSales, AgentReportAdditionalType.VerDepGP]

                    for (const type of assignments_types) {
                        const assignment = await this.factories.agent_report_additional.create({
                            agent_invoice_pk: usePk(invoice),
                            type,
                            additional_gp: randomFloat(500, 1000),
                            additional_net: 0,
                            remark: `${type} assignment`,
                        })

                        await createLog(usePk(report), {
                            field: 'total_gross',
                            from: String(profit),
                            to: String(profit + assignment.additional_gp),
                        })

                        profit += assignment.additional_gp
                    }
                }

                const rev_tkt_manager = await findModelRecord('Agent', {
                    position_pk: usePk(manager_position),
                    department_pk: usePk(ticketing_award_department),
                })

                if (rev_tkt_manager) {
                    const invoice = await this.factories.agent_report_invoice.create({
                        agent_report_pk: usePk(report),
                        agent_pk: usePk(rev_tkt_manager),
                        department_pk: usePk(executive_department),
                    })

                    const assignments_types = [AgentReportAdditionalType.PriceDrop, AgentReportAdditionalType.AltExtra]

                    for (const type of assignments_types) {
                        const assignment = await this.factories.agent_report_additional.create({
                            agent_invoice_pk: usePk(invoice),
                            type,
                            additional_gp: randomFloat(500, 1000),
                            additional_net: 0,
                            remark: `${type} assignment`,
                        })

                        await createLog(usePk(report), {
                            field: 'total_gross',
                            from: String(profit),
                            to: String(profit + assignment.additional_gp),
                        })

                        profit += assignment.additional_gp
                    }
                }
            }

            await updateModelRecord('AgentReport', usePk(report), {
                total_gross: profit,
                total_net: profit * 0.93,
            })
        }
    }
}

const splitToNChunks = (array: any[], n: number) => {
    const result = []
    for (let i = n; i > 0; i--) {
        result.push(array.splice(0, Math.ceil(array.length / i)))
    }

    return result
}
