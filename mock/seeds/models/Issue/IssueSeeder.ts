import ModelDatabaseSeeder from '~mock/seeds/ModelDatabaseSeeder'
import { dateToUnixTimestamp, randomBoolean, randomEnumValue, withChance } from '~mock/lib/Helper/SeedHelper'
import type { ModelAttributes, ModelFields, ModelLinkIdentification } from '~types/lib/Model'
import {
    createModelRecord,
    deleteModelRecords,
    findModelRecord,
    findModelRecords,
    makeModelRecord,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import {
IssueCategory,
IssueData,
IssueResult,
IssueStatus,
IssueUrgency,
} from '~/api/models/Issue/Issue'
import { usePk } from '~/composables/usePk'
import ChatSeederSimple from '~mock/seeds/models/Chat/ChatSeederSimple'
import ChatAdditionalInfoSeederSimple from '~mock/seeds/models/Chat/ChatAdditionalInfoSeederSimple'
import { MockProjectID } from '~mock/seeds/models/Project/ProjectSeeder'
import { randomFloat, randomInt } from '~/lib/Helper/NumberHelper'
import { Timespan } from '~/lib/Helper/TimespanHelper'
import ChatMessageSeederSimple from '~mock/seeds/models/Chat/ChatMessageSeederSimple'
import PollSeederSimple from '~mock/seeds/models/Poll/PollSeederSimple'
import { randomElement, randomElementPk, randomElements } from '~/lib/Helper/ArrayHelper'
import z from 'zod'
import { match } from '~/lib/Helper/EnumHelper'
import { ClientStatusName } from '~/api/models/Client/ClientStatus'
import { faker } from '@faker-js/faker'
import { TaskCategory } from '~/api/models/Task/TaskGroup'
import { ProductType } from '~/api/models/Product/Product'
import TaskGroupFactory from '~mock/factories/Task/TaskGroupFactory'
import IssueAdditionalAgentInfoFactory from '~mock/factories/Issue/IssueAdditionalAgentInfoFactory'
import { ClosingReasons } from '~/api/models/Lead/Lead'

const modelName = 'Issue'

export default class IssueSeeder extends ModelDatabaseSeeder<typeof modelName> {
    public modelName = modelName

    public readonly relatedSeeders = {
        chat: new ChatSeederSimple(),
        chatInfo: new ChatAdditionalInfoSeederSimple(),
        chatMessage: new ChatMessageSeederSimple(),
        poll: new PollSeederSimple(),
    }

    public factories = {
        taskGroup: new TaskGroupFactory(),
    }

    public async clear(): Promise<void> {
        await super.clear()

        await this.registerClear([
            this.relatedSeeders.chat,
            this.relatedSeeders.chatInfo, // Cleared in observer
            this.relatedSeeders.chatMessage, // Cleared in observer
            this.relatedSeeders.poll, // Cleared in observer
        ], async () => {
            await deleteModelRecords('Chat', {
                model_name: modelName,
            })
        })
    }

    public async seed(): Promise<void> {
        await this.seedCustomerSupportIssues('Sale', 12)
        await this.seedCustomerSupportIssues('Lead', 12)
        await this.seedSplitSalesIssues(12)
        await this.seedClientStatusIssues(12)
        await this.seedKeepClientIssues(12)
        await this.seedDisclaimerIssues()
        await this.seedClosingReasonIssues(12)
        await this.seedTicketingIssues(12)
        await this.seedVoucherIssues(10)
    }

    // ============================

    private async seedCustomerSupportIssues(modelName: 'Sale' | 'Lead', count: number) {
        const records = (await findModelRecords(modelName)).slice(0, count)

        for (const record of records) {
            await this.create({
                model_name: modelName,
                model_pk: usePk(record),
                category: IssueCategory.CustomerSupportGeneral,
            }, {
                withChat: true,
            })
        }
    }

    //

    private async seedSplitSalesIssues(count: number) {
        const sales = (await findModelRecords('Sale')).slice(0, count)

        for (const sale of sales) {
            await this.createSplitSaleIssue(sale, { withChat: true })
        }
    }

    public async createSplitSaleIssue(
        sale: ModelAttributes<'Sale'>,
        options: {
            withChat?: boolean,
        } = {},
    ) {
        return this.create({
            model_name: 'Sale',
            model_pk: usePk(sale),
            category: IssueCategory.SplitSale,
        }, {
            withChat: options.withChat,
        })
    }

    //

    private async seedClientStatusIssues(count: number) {
        const clients = (await findModelRecords('Client')).slice(0, count)

        for (const client of clients) {
            await this.createClientStatusIssue(client, { withChat: true })
        }
    }

    public async createClientStatusIssue(
        client: ModelAttributes<'Client'>,
        options: {
            withChat?: boolean,
        } = {},
    ) {
        return this.create({
            model_name: 'Client',
            model_pk: usePk(client),
            category: IssueCategory.ClientStatus,
        }, {
            withChat: options.withChat,
        })
    }

    //
    private async seedKeepClientIssues(count: number) {
        const leads = (await findModelRecords('Lead')).slice(0, count)

        for (const lead of leads) {
            await this.createKeepClientIssue(lead, { withChat: true })
        }
    }

    public async createKeepClientIssue(
        lead: ModelAttributes<'Lead'>,
        options: {
            withChat?: boolean,
        } = {},
    ) {
        return this.create({
            model_name: 'Lead',
            model_pk: usePk(lead),
            category: IssueCategory.KeepClient,
        }, {
            withChat: options.withChat,
        })
    }

    //
    private async seedClosingReasonIssues(count: number) {
        const leads = (await findModelRecords('Lead')).slice(0, count)

        for (const lead of leads) {
            await this.createClosingReasonIssue(lead, { withChat: true })
        }
    }

    public async createClosingReasonIssue(
        lead: ModelAttributes<'Lead'>,
        options: {
            withChat?: boolean,
        } = {},
    ) {
        return this.create({
            model_name: 'Lead',
            model_pk: usePk(lead),
            category: IssueCategory.ClosingReason,
        }, {
            withChat: options.withChat,
        })
    }

    //
    private async seedTicketingIssues(count: number) {
        const sales = (await findModelRecords('Sale')).slice(0, count)

        for (const sale of sales) {
            if (randomBoolean()) {
                await this.createPriceDropIssue(sale, { withChat: true })
            } else {
                await this.createAltExtraIssue(sale, { withChat: true })
            }
        }
    }

    //
    private async seedVoucherIssues(count: number) {
        const vouchers = (await findModelRecords('Voucher')).slice(0, count)

        for (const voucher of vouchers) {
            const issue = await this.createVoucherIssue(voucher, { withChat: true })

            await updateModelRecord('Voucher', usePk(voucher), {
                issue_pk: usePk(issue),
            })
        }
    }

    public async createVoucherIssue(
        voucher: ModelAttributes<'Voucher'>,
        options: {
            withChat?: boolean,
        } = {},
    ) {
        return this.create({
            model_name: 'Voucher',
            model_pk: usePk(voucher),
            category: IssueCategory.Voucher,
        }, {
            withChat: options.withChat,
        })
    }

    //

    public async createPriceDropIssue(
        sale: ModelAttributes<'Sale'>,
        options: {
            withChat?: boolean,
        } = {},
    ) {
        return this.create({
            model_name: 'Sale',
            model_pk: usePk(sale),
            category: IssueCategory.PriceDrop,
        }, {
            withChat: options.withChat,
        })
    }

    public async createAltExtraIssue(
        sale: ModelAttributes<'Sale'>,
        options: {
            withChat?: boolean,
        } = {},
    ) {
        return this.create({
            model_name: 'Sale',
            model_pk: usePk(sale),
            category: IssueCategory.AltExtra,
        }, {
            withChat: options.withChat,
        })
    }

    private async seedDisclaimerIssues() {
        const sale = randomElement(await findModelRecords('Sale'))

        const salePk = usePk(sale)

        const group = await findModelRecord('TaskGroup', {
            model_name: 'Sale',
            model_pk: salePk,
            category: TaskCategory.Disclaimer,
        })

        if (!group) {
            return
        }

        const tasks = (await findModelRecords('Task', {
            task_group_pk: usePk(group),
        }))

        for (const task of tasks) {
            const issue = await this.createDisclaimerIssue(sale, { withChat: true })
            await updateModelRecord('Task', usePk(task), {
                issue_pk: usePk(issue),
            })
        }
    }

    public async createDisclaimerIssue(
        sale: ModelAttributes<'Sale'>,
        options: {
            withChat?: boolean,
        } = {},
    ) {
        const issue = await this.create({
            model_name: 'Sale',
            model_pk: usePk(sale),
            category: IssueCategory.AirlineReimbursement,
        }, {
            withChat: options.withChat,
        })

        const modelInfo: ModelLinkIdentification = {
            model_name: modelName,
            model_pk: usePk(issue),
        }

        await this.factories.taskGroup.create({
            category: TaskCategory.General,
            ...modelInfo,
        }, {
            withTasks: true,
            withFollowupTasks: true,
        })

        return issue
    }

    //

    public async createPnrScheduleIssue(
        pnr: ModelAttributes<'PnrSchedule'>,
        options: {
            withChat?: boolean,
        } = {},
    ) {
        const issue = await this.create({
            model_name: 'PnrSchedule',
            model_pk: usePk(pnr),
            category: IssueCategory.PnrSchedule,
        }, {
            withChat: options.withChat,
        })

        return issue
    }

    //
    public async create(
        attributes: Partial<ModelFields<typeof modelName>> & {
            model_name: ModelName,
            model_pk: PrimaryKey,
        },
        options: {
            withChat?: boolean,
        } = {},
    ): Promise<ModelAttributes<typeof modelName>> {
        let issue = await createModelRecord(modelName, await this.make(attributes))
        const factory = new IssueAdditionalAgentInfoFactory()

        await factory.create({
            id: issue.id,
        })

        if (options.withChat) {
            const chat = await this.relatedSeeders.chat.create({
                model_name: modelName,
                model_pk: usePk(issue),
            })

            issue = await updateModelRecord(modelName, usePk(issue), { chat_pk: usePk(chat) })

            const context = {
                issue,
                chat,
            }

            const messages = [
                await this.relatedSeeders.chatMessage.createIssueSuggestMessage(context),
                await this.relatedSeeders.chatMessage.createIssueApplyMessage(context),
                await this.relatedSeeders.chatMessage.createIssueDeclineMessage(context),
            ]

            await this.relatedSeeders.chatInfo.createFromMessages(chat, messages)
        }

        return issue
    }

    public async makeResult(issue: ModelAttributes<'Issue'>) {
        const branch = `s-${randomInt(1, 10)}`

        const schema = z.object(IssueResult)
        type Content = z.infer<typeof schema>

        const leads = await findModelRecords('Lead')
        const agents = await findModelRecords('Agent')
        const sales = await findModelRecords('Sale')
        const clientStatuses = await findModelRecords('ClientStatus')
        const passengers = await findModelRecords('SaleVersionPassenger')

        const result = match(
            Object.fromEntries(Object.keys(IssueResult).map(key => [key, key])),
            issue.category, {
                [IssueCategory.CustomerSupportGeneral]: () => ({
                    response: 'Some response',
                }),
                [IssueCategory.Disclaimer]: () => ({
                    response: 'Some response',
                }),
                [IssueCategory.SplitSale]: () => ({
                    tips: randomInt(1, 100),
                    ticket_protection: randomInt(1, 100),
                    ticket_profit: randomInt(1, 100),
                }),
                [IssueCategory.ClientStatus]: () => {
                    const status = randomElement(clientStatuses)

                    return {
                        client_status_pk: usePk(status),
                        referral_client_pk: status.system_name === ClientStatusName.Referral ? randomElementPk(agents) : null,
                        return_from_sale_pk: status.system_name === ClientStatusName.Return ? randomElementPk(sales) : null,
                    }
                },
                [IssueCategory.TaskDiscussion]: () => ({
                    response: 'Response for TaskDiscussion',
                }),
                [IssueCategory.KeepClient]: () => ({
                    expires_at: dateToUnixTimestamp(faker.date.future()),
                }),
                [IssueCategory.ClosingReason]: () => {
                    const closing_reason = randomBoolean() ? randomEnumValue(ClosingReasons) : null

                    return {
                        closing_reason,
                        closing_reason_remark: withChance(faker.lorem.text()) || null,
                        duplicate_lead_pk: closing_reason === ClosingReasons.NewRequest ? randomElementPk(leads) : null,
                    }
                },
                [IssueCategory.PriceDrop]: () => ({
                    pnr: faker.string.alphanumeric(7),
                    old_net_price: randomFloat(1, 100),
                    old_commission: randomFloat(1, 100),
                    old_issuing_fee: randomFloat(1, 100),
                    old_check_payment: randomFloat(1, 100),
                    new_net_price: randomFloat(1, 100),
                    new_commission: randomFloat(1, 100),
                    new_issuing_fee: randomFloat(1, 100),
                    new_check_payment: randomFloat(1, 100),
                    amount: randomFloat(100, 2000),
                }),
                [IssueCategory.AltExtra]: () => ({
                    old_pnr: faker.string.alphanumeric(7),
                    new_pnr: faker.string.alphanumeric(7),
                    old_net_price: randomFloat(1, 100),
                    old_commission: randomFloat(1, 100),
                    old_issuing_fee: randomFloat(1, 100),
                    old_check_payment: randomFloat(1, 100),
                    new_net_price: randomFloat(1, 100),
                    new_commission: randomFloat(1, 100),
                    new_issuing_fee: randomFloat(1, 100),
                    new_check_payment: randomFloat(1, 100),
                    amount: randomFloat(100, 2000),
                }),

                // Verification
                [IssueCategory.VerificationTA]: () => ({
                    response: 'Some response',
                }),
                [IssueCategory.VerificationOther]: () => ({
                    response: 'Some response',
                }),

                // Airline reimbursement
                [IssueCategory.AirlineReimbursement]: () => ({
                    response: 'Some response',
                }),
                [IssueCategory.AirlineReimbursementRefund]: () => ({
                    response: 'Some response',
                }),
                [IssueCategory.AirlineReimbursementCredit]: () => ({
                    response: 'Some response',
                }),
                [IssueCategory.AirlineReimbursementCancellation]: () => ({
                    response: 'Some response',
                }),
                [IssueCategory.PnrSchedule]: () => ({
                    response: 'Some response',
                }),
                [IssueCategory.Voucher]: () => ({
                    response: 'Some response',
                }),
            } satisfies {
                [Key in keyof Content]: () => Content[Key]
            },
        )

        return {
            result,
            branch,
        }
    }

    public async make(
        attributes: Partial<ModelFields<typeof modelName>> & {
            model_name: ModelName,
            model_pk: PrimaryKey,
        },
    ): Promise<ModelFields<typeof modelName>> {
        const category = attributes.category ?? randomEnumValue(IssueCategory)

        const status = randomEnumValue(IssueStatus)
        const agents = await findModelRecords('Agent')
        const clients = await findModelRecords('Client')
        const sales = await findModelRecords('Sale')
        const pnrSchedules = await findModelRecords('PnrSchedule')

        const members = randomElements(agents, randomInt(2, 3))

        withChance(() => {
            members.push(agents[0])
        }, 0.5)

        const clientStatuses = await findModelRecords('ClientStatus')

        const passengers = await findModelRecords('SaleVersionPassenger')
        const tickets = await findModelRecords('Product', { item_type: ProductType.Ticket })
        const validTickets = tickets.filter(product => !!product.external_number)

        const leads = await findModelRecords('Lead')

        // ==============

        const schema = z.object(IssueData)
        type Content = z.infer<typeof schema>

        const getPnrsData = () => {
            return ({
                passengers_pks: randomElements(passengers, 2).map((passenger) => usePk(passenger)),
                pnrs: randomElements(validTickets, 2).map((product) => {
                    return product.external_number || '123'
                }),
            })
        }

        const data = match(
            Object.fromEntries(Object.keys(IssueData).map(key => [key, key])),
            category, {
                [IssueCategory.CustomerSupportGeneral]: () => getPnrsData(),
                [IssueCategory.Disclaimer]: () => null,
                [IssueCategory.SplitSale]: () => ({
                    agent_pk: randomElementPk(agents),
                    sale_lead_pk: randomElementPk(leads),
                    lead_pk: randomElementPk(leads),
                }),
                [IssueCategory.ClientStatus]: () => ({
                    client_pk: randomElementPk(clients),
                    old_client_status_pk: randomElementPk(clientStatuses),
                    executor_pk: randomElementPk(agents),
                }),
                [IssueCategory.TaskDiscussion]: () => null,
                [IssueCategory.KeepClient]: () => ({
                    lead_pk: randomElementPk(leads),
                    client_pk: randomElementPk(clients),
                    old_expires_at: dateToUnixTimestamp(faker.date.soon()),
                }),
                [IssueCategory.ClosingReason]: () => ({
                    lead_pk: randomElementPk(leads),
                }),
                [IssueCategory.PriceDrop]: () => ({
                    pnr: 'SASDASDe123',
                    agent_pk: randomElementPk(agents),
                }),
                [IssueCategory.AltExtra]: () => ({
                    agent_pk: randomElementPk(agents),
                }),

                // Verification
                [IssueCategory.VerificationTA]: () => getPnrsData(),
                [IssueCategory.VerificationOther]: () => getPnrsData(),

                // Airline reimbursement
                [IssueCategory.AirlineReimbursement]: () => getPnrsData(),
                [IssueCategory.AirlineReimbursementRefund]: () => getPnrsData(),
                [IssueCategory.AirlineReimbursementCredit]: () => getPnrsData(),
                [IssueCategory.AirlineReimbursementCancellation]: () => getPnrsData(),
                [IssueCategory.PnrSchedule]: () => {
                    return {
                        sale_pk: randomElementPk(sales),
                        pnr_schedule_pk: randomElementPk(pnrSchedules),
                    }
                },
                //
                [IssueCategory.Voucher]: () => null,
            } satisfies {
                [Key in keyof Content]: () => Content[Key]
            },
        )

        // ==============

        return makeModelRecord(modelName, {
            category,
            status,
            data,

            is_deleted: false,

            urgency: randomEnumValue(IssueUrgency),
            created_at: dateToUnixTimestamp(new Date()),
            created_by_pk: randomElementPk(agents),
            expires_at: withChance(
                dateToUnixTimestamp(new Date()) + randomInt(0, Timespan.hours(12).inSeconds),
                0.5,
            ),

            members: members.map((agent) => {
                const role = randomElement(['Agent', 'Curator', 'Boss'])

                return {
                    pk: usePk(agent),
                    role: role,
                    is_hidden: randomBoolean(),
                    permissions: {
                        vote: true,
                        apply: true,
                        extend: true,
                        deleteTask: role === 'Curator',
                    },
                }
            }),

            chat_pk: 'undefined', // Will be set later

            // Other
            project_pk: usePk('Project', MockProjectID.TBC),
            executor_pk: null,
            executor_start_at: null,

            //
            ...attributes,
        })
    }
}
