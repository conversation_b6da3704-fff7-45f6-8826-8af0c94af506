import ModelDatabaseSeeder from '~mock/seeds/ModelDatabaseSeeder'
import MarketingCostFactory from '~mock/factories/Marketing/MarketingCostFactory'
import { CalendarMonth } from '~/api/models/Marketing/MarketingCost'
import { randomBoolean } from '~mock/lib/Helper/SeedHelper'

const modelName = 'MarketingCost'

export default class MarketingCostSeeder extends ModelDatabaseSeeder<typeof modelName> {
    public readonly modelName = modelName
    public readonly relatedModels = []

    public readonly factories = {
        marketingCost: new MarketingCostFactory(),
    }

    public async seed() {
        const years = [2024, 2025, 2026]

        const months: Array<CalendarMonth> = Object.keys(CalendarMonth)
            .filter(key => isNaN(Number(key)))
            .map(key => CalendarMonth[key as keyof typeof CalendarMonth])

        for (const year of years) {
            for (const month of months) {
                const create = randomBoolean()

                if (create) {
                    await this.factories.marketingCost.create({
                        year,
                        month,
                    })
                }
            }
        }
    }
}
