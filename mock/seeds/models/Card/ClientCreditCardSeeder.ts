import ModelDatabaseSeeder from '~mock/seeds/ModelDatabaseSeeder'
import ClientCreditCardFactory from '~mock/factories/Card/ClientCreditCardFactory'

const modelName = 'ClientCreditCard'

export default class ClientCreditCardSeeder extends ModelDatabaseSeeder<typeof modelName> {
    public modelName = modelName

    public readonly factories = {
        clientCreditCard: new ClientCreditCardFactory(),
    }

    public async seed() {
        for (let i = 0; i < 10; i++) {
            await this.factories.clientCreditCard.create()
        }
    }
}
