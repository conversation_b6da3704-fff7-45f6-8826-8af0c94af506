import ModelDatabaseSeeder from '~mock/seeds/ModelDatabaseSeeder'
import { ChatGatewayDriver } from '~/api/models/Chat/ChatGateway'
import { MockProjectID } from '~mock/seeds/models/Project/ProjectSeeder'
import { usePk } from '~/composables/usePk'

const modelName = 'ChatGateway'

export default class ChatGatewaySeeder extends ModelDatabaseSeeder<typeof modelName> {
    public modelName = modelName

    public async data() {
        const project_pk = usePk('Project', MockProjectID.TBC)

        return [
            {
                id: 1,
                driver: ChatGatewayDriver.Internal,
                config: null,
                project_pk,
            },
            {
                id: 2,
                driver: ChatGatewayDriver.InternalWithGroups,
                config: null,
                project_pk,
            },
            {
                id: 5,
                driver: ChatGatewayDriver.RingCentral,
                config: {
                    'server': 'https://platform.ringcentral.com',
                    'clientId': 'bRwZdJJFoezdtGANQwDSd4',
                    'clientSecret': 'dYrtaxUM4r2exDCvyITCp25TXgJw2yBR7beiZw6VwNq2',
                    'acceptFiles': '.jpg,.jpeg,.png,.pdf,.doc,.docx,.rtf,.odt,.odf,.ods,.odp,.txt,.xls,.xlsx,.xlsm,.xlsb,.csv,.mp3,.wav,.mpeg4,.webm',
                },
                project_pk,
            },

            {
                id: 7,
                driver: ChatGatewayDriver.Assistant,
                config: null,
                project_pk,
            },
            // {
            //     id: 6,
            //     driver: ChatGatewayDriver.RingCentral,
            //     config: {
            //         'server': 'https://platform.ringcentral.com',
            //         'clientId': 'bRwZdJJFoezdtGANQwDSd4',
            //         'clientSecret': 'dYrtaxUM4r2exDCvyITCp25TXgJw2yBR7beiZw6VwNq2',
            //         'acceptFiles': '.jpg,.jpeg',
            //     },
            //     project_pk,
            // },
        ]
    }
}
