import LeaveRequestFactory from '~mock/factories/LeaveManagement/LeaveRequestFactory'
import ModelDatabaseSeeder from '~mock/seeds/ModelDatabaseSeeder'
import { LeaveRequestStatus } from '~/api/models/LeaveManagement/LeaveRequest'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'

const modelName = 'LeaveRequest'

export default class LeaveRequestSeeder extends ModelDatabaseSeeder<typeof modelName> {
    public readonly modelName = modelName
    public readonly relatedModels = []

    public readonly factories = {
        leaveRequestFactory: new LeaveRequestFactory(),
    }

    public async seed() {
        const beneficiary_pk = '3'

        for (let i = 0; i < 10; i++) {
            const tmpDate = new Date()
            await this.factories.leaveRequestFactory.create({ status: LeaveRequestStatus.Approved, beneficiary_pk, dates: { leave_start: dateToUnixTimestamp(tmpDate), leave_end: dateToUnixTimestamp(tmpDate) } })
        }
        for (let i = 0; i < 10; i++) {
            await this.factories.leaveRequestFactory.create({ status: LeaveRequestStatus.Pending, beneficiary_pk })
        }
        for (let i = 0; i < 10; i++) {
            await this.factories.leaveRequestFactory.create({ status: LeaveRequestStatus.Rejected, beneficiary_pk })
        }

        for (let i = 0; i < 30; i++) {
            await this.factories.leaveRequestFactory.create({ status: LeaveRequestStatus.Approved })
        }
    }
}
