import ModelDatabaseSeeder from '~mock/seeds/ModelDatabaseSeeder'
import LeaveTransactionFactory from '~mock/factories/LeaveManagement/LeaveTransactionFactory'
import { findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { LeaveRequestStatus } from '~/api/models/LeaveManagement/LeaveRequest'

const modelName = 'LeaveTransaction'

export default class LeaveTransactionSeeder extends ModelDatabaseSeeder<typeof modelName> {
    public modelName = modelName

    public readonly factories = {
        leaveTransaction: new LeaveTransactionFactory(),
    }

    public async seed() {
        const approvedLeaveRequests = await findModelRecords('LeaveRequest', { status: LeaveRequestStatus.Approved })

        for (const approvedLeaveRequest of approvedLeaveRequests) {
            await this.factories.leaveTransaction.create({
                beneficiary_pk: approvedLeaveRequest.beneficiary_pk,
                request_pk: usePk(approvedLeaveRequest),
            })
        }

        for (let i = 0; i < 40; i++) {
            await this.factories.leaveTransaction.create({})
        }
    }
}
