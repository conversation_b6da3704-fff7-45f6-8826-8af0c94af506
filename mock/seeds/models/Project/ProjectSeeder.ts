import ModelDatabaseSeeder from '~mock/seeds/ModelDatabaseSeeder'
import type { ModelFields } from '~types/lib/Model'

const modelName = 'Project'

export enum MockProjectID {
    'TBC' = 1,
    'ABT' = 2,
    'BCF' = 5,
    'ABF' = 6,
    'BCS' = 10,
    'LFS' = 11,
}

export default class ProjectSeeder extends ModelDatabaseSeeder<typeof modelName> {
    public modelName = modelName

    public async data() {
        return [
            // TBC =============
            {
                id: MockProjectID.TBC,
                name: 'Travel Business Class',
                abbreviation: 'TBC',
                host: 'travelbusinessclass.com',
                pay_host: 'localhost:3000',
                is_main: true,
                general_line_phone_number: ['+188877766655', '+***********'],
                features: {
                    client_cabinet: {
                        enabled: true,
                    },
                    email_footer_generator: {
                        enabled: true,
                    },
                },
            },
            {
                id: MockProjectID.ABT,
                name: 'Air Business Travel',
                abbreviation: 'ABT',
                host: 'airbusinesstravel.com',
                pay_host: 'localhost:3000',
                is_main: false,
                general_line_phone_number: ['+188877766655', '+***********'],
                features: {
                    client_cabinet: {
                        enabled: false,
                    },
                    email_footer_generator: {
                        enabled: true,
                    },
                },
            },

            // BCF =============
            {
                id: MockProjectID.BCF,
                name: 'Business Class Flights',
                abbreviation: 'BCF',
                host: 'businessclassflights.com',
                pay_host: 'localhost:3000',
                is_main: true,
                general_line_phone_number: ['+188877766655', '+***********'],
                features: {
                    client_cabinet: {
                        enabled: false,
                    },
                    email_footer_generator: {
                        enabled: false,
                    },
                },
            },
            {
                id: MockProjectID.ABF,
                abbreviation: 'ABF',
                name: 'Air Business Flights',
                host: 'airbusinessflights.com',
                pay_host: 'localhost:3000',
                is_main: false,
                general_line_phone_number: ['+188877766655', '+***********'],
                features: {
                    client_cabinet: {
                        enabled: false,
                    },
                    email_footer_generator: {
                        enabled: false,
                    },
                },
            },

            // BCS =============
            {
                id: MockProjectID.BCS,
                name: 'BCFlights',
                abbreviation: 'BCS',
                host: 'bcflights.com',
                pay_host: 'localhost:3000',
                is_main: true,
                general_line_phone_number: [], // @todo
                features: {
                    client_cabinet: {
                        enabled: false,
                    },
                    email_footer_generator: {
                        enabled: true,
                    },
                },
            },
            {
                id: MockProjectID.LFS,
                name: 'Lux Flights',
                abbreviation: 'LFS',
                host: 'lux-flights.com',
                pay_host: 'localhost:3000',
                is_main: false,
                general_line_phone_number: [],
                features: {
                    client_cabinet: {
                        enabled: false,
                    },
                    email_footer_generator: {
                        enabled: true,
                    },
                },
            },
        ] satisfies ModelFields<typeof modelName>[]
    }
}
