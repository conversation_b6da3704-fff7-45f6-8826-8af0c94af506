import ModelDatabaseSeeder from '~mock/seeds/ModelDatabaseSeeder'
import { ProductType } from '~/api/models/Product/Product'
import { usePk } from '~/composables/usePk'
import ProductFactory from '~mock/factories/Product/ProductFactory'
import { findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import ProductClientApproveFactory from '~mock/factories/Product/ProductClientApproveFactory'
import AdditionalExpenseFactory from '~mock/factories/AdditionalExpense/AdditionalExpenseFactory'
import IncentiveSaleFactory from '~mock/factories/IncentiveSale/IncentiveSaleFactory'

const modelName = 'Product'

export default class ProductSeeder extends ModelDatabaseSeeder<typeof modelName> {
    public modelName = modelName

    public readonly factories = {
        product: new ProductFactory(),
        productApprove: new ProductClientApproveFactory(),
        additionalExpense: new AdditionalExpenseFactory(),
        incentiveSale: new IncentiveSaleFactory(),
    }

    public async seed() {
        const sales = await findModelRecords('Sale')
        const productTypes = Object.keys(ProductType)

        for (const sale of sales.slice(0, 10)) {
            for (const key of productTypes) {
                const product = await this.factories.product.create({
                    item_type: ProductType[key as keyof typeof ProductType],
                    sale_pk: usePk(sale),
                    sale_version_pk: sale.sale_version_pk,
                })

                if ([ProductType.Insurance, ProductType.Tips].includes(key)) {
                    await this.factories.additionalExpense.create({
                        sale_version_pk: sale.sale_version_pk,
                        product_pk: usePk(product),
                    })
                }

                if ([ProductType.Insurance, ProductType.Tips].includes(key)) {
                    await this.factories.incentiveSale.create({
                        sale_version_pk: sale.sale_version_pk,
                        product_pk: usePk(product),
                    })
                }

                const cl_appr = await this.factories.productApprove.create({ product_pk: usePk(product) })
            }
        }
    }
}
