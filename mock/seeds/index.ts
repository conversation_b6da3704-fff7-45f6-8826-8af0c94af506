import type DatabaseSeeder from '~mock/seeds/DatabaseSeeder'
import AgentSeeder from '~mock/seeds/models/Agent/AgentSeeder'
import TeamSeeder from '~mock/seeds/models/Team/TeamSeeder'
import AirlineSeeder from '~mock/seeds/models/Airline/AirlineSeeder'
import ProjectSeeder from '~mock/seeds/models/Project/ProjectSeeder'
import DepartmentSeeder from '~mock/seeds/models/Department/DepartmentSeeder'
import PositionSeeder from '~mock/seeds/models/Position/PositionSeeder'
import AssignLogSeeder from './models/AssignLog/AssignLogSeeder'
import PaymentGatewaySeeder from '~mock/seeds/models/PaymentGateway/PaymentGatewaySeeder'
import BookkeepingInvoiceSeeder from '~mock/seeds/models/Bookkeeping/BookeepingInvoiceSeeder'
import SaleVersionCardSeeder from '~mock/seeds/models/Sale/SaleVersionCardSeeder'
import SaleSeeder from '~mock/seeds/models/Sale/SaleSeeder'
import IssueSeeder from '~mock/seeds/models/Issue/IssueSeeder'
import ChatGatewaySeeder from '~mock/seeds/models/Chat/ChatGatewaySeeder'
import ClientSeeder from '~mock/seeds/models/Client/ClientSeeder'
import ClientStatusSeeder from '~mock/seeds/models/Client/ClientStatusSeeder'
import LeadSeeder from '~mock/seeds/models/Lead/LeadSeeder'
import ChatTestSystemMessagesSeederSeeder from '~mock/seeds/models/Chat/ChatTestSystemMessagesSeederSeeder'
import ClientPassportSeeder from '~mock/seeds/models/Client/ClientPassportSeeder'
import ConsolidatorSeeder from '~mock/seeds/models/Consolidator/ConsolidatorSeeder'
import FileSeeder from '~mock/seeds/models/FileSeeder'
import CurrencySeeder from '~mock/seeds/models/Currency/CurrencySeeder'
import ProductSeeder from '~mock/seeds/models/Product/ProductSeeder'
import BookkeepingTransactionSeeder from '~mock/seeds/models/Bookkeeping/BookkeepingTransactionSeeder'
import BookkeepingPaymentGatewaySeeder from '~mock/seeds/models/BookkeepingPaymentGatewaySeeder'
import BookkeepingTransactionAssignmentSeeder
    from '~mock/seeds/models/Bookkeeping/BookkeepingTransactionAssignmentSeeder'
import CountrySeeder from '~mock/seeds/models/Country/CountrySeeder'

import MilePriceProgramSeeder from '~mock/seeds/models/MilePriceProgram/MilePriceProgramSeeder'
import AwardAccountSeeder from '~mock/seeds/models/AwardAccount/AwardAccountSeeder'
import TicketSeeder from '~mock/seeds/models/Ticket/TicketSeeder'
import IataSeeder from '~mock/seeds/models/Iata/IataSeeder'
import TicketExtraPreferenceSeeder from '~mock/seeds/models/TicketExtraPreference/TicketExtraPreferenceSeeder'
import CheckInReminderSeeder from '~mock/seeds/models/CheckInReminder/CheckInReminderSeeder'
import PriceQuoteSeeder from '~mock/seeds/models/PriceQuote/PriceQuoteSeeder'
import ConsolidatorAreaSeeder from '~mock/seeds/models/Consolidator/ConsolidatorAreaSeeder'
import LeadStatusSeeder from '~mock/seeds/models/Lead/LeadStatusSeeder'
import ActivityLogSeeder from '~mock/seeds/models/ActivityLog/ActivityLogSeeder'
import AirlineReportSeeder from '~mock/seeds/models/AirlineReport/AirlineReportSeeder'
import TaskAssignmentsSeeder from '~mock/seeds/models/Task/TaskAssignmentsSeeder'
import AgentReportSeeder from '~mock/seeds/models/AgentReport/AgentReportSeeder'
import SaleVoucherSeeder from '~mock/seeds/models/Sale/SaleVoucherSeeder'
import SaleExtraProfitSeeder from '~mock/seeds/models/Sale/SaleExtraProfitSeeder'
import CompanyContactSeeder from '~mock/seeds/models/CompanyContact/CompanyContactSeeder'
import ActivityLogDetailedSeeder from '~mock/seeds/models/ActivityLog/ActivityLogDetailedSeeder'
import ExpectedAmountSeeder from '~mock/seeds/models/ExpectedAmount/ExpectedAmountSeeder'
import SkillSeeder from '~mock/seeds/models/Skill/SkillSeeder'
import AgentSkillSeeder from '~mock/seeds/models/Agent/AgentSkillSeeder'
import RegionSeeder from '~mock/seeds/models/Region/RegionSeeder'
import ShiftSeeder from '~mock/seeds/models/Shift/ShiftSeeder'
import TravelerSeeder from '~mock/seeds/models/Traveler/TravelerSeeder'
import AirlineCaseSeeder from '~mock/seeds/models/Airline/AirlineCaseSeeder'
import ReleasePostSeeder from '~mock/seeds/models/ReleasePost/ReleasePostSeeder'
import TaskChangeSaleTypeSeeder from '~mock/seeds/models/Task/TaskChangeSaleTypeSeeder'
import EmailContactAttemptSeeder from '~mock/seeds/models/ContactAttempt/EmailContactAttemptSeeder'
import PriceDropCheckSeeder from '~mock/seeds/models/PriceDrop/PriceDropCheckSeeder'
import IssueGroupSeeder from '~mock/seeds/models/Issue/IssueGroupSeeder'
import ExternalUserAgentSeeder from '~mock/seeds/models/ExternalUserAgent/ExternalUserAgentSeeder'
import VoucherSeeder from '~mock/seeds/models/Voucher/VoucherSeeder'
import OfficeSeeder from '~mock/seeds/models/Office/OfficeSeeder'
import ElrFrtCheckSeeder from '~mock/seeds/models/ElrFrtCheck/ElrFrtCheckSeeder'
import GamblingLotSeeder from '~mock/seeds/models/GamblingLot/GamblingLotSeeder'
import CallContactAttemptSeeder from '~mock/seeds/models/ContactAttempt/CallContactAttemptSeeder'
import CitySeeder from '~mock/seeds/models/City/CitySeeder'
import TerminalHotkeySeeder from '~mock/seeds/models/TerminalHotkey/TerminalHotkeySeeder'
import SaleTransactionSeeder from '~mock/seeds/models/Sale/SaleTransactionSeeder'
import PnrScheduleSeeder from '~mock/seeds/models/PnrSchedule/PnrScheduleSeeder'
import ProjectCardSeeder from '~mock/seeds/models/Card/ProjectCardSeeder '
import VccProjectCardSummarySeeder from '~mock/seeds/models/Card/VccProjectCardSummarySeeder'
import SaleClientRequestSeeder from '~mock/seeds/models/Client/SaleClientRequestSeeder'
import SignedDocumentSeeder from '~mock/seeds/models/SignedDocument/SignedDocumentSeeder'
import PerformanceFeedbackSeeder from '~mock/seeds/models/PerformanceFeedback/PerformanceFeedbackSeeder'
import PerformanceFeedbackEventSeeder from '~mock/seeds/models/PerformanceFeedback/PerformanceFeedbackEventSeeder'
import SaleRequestHelpSeeder from '~mock/seeds/models/Sale/SaleRequestHelpSeeder'
import LeaveRequestSeeder from '~mock/seeds/models/LeaveManagement/LeaveRequestSeeder'
import LeaveTransactionSeeder from '~mock/seeds/models/LeaveManagement/LeaveTransactionSeeder'
import ClientCreditCardSeeder from '~mock/seeds/models/Card/ClientCreditCardSeeder'
import MarketingCostSeeder from '~mock/seeds/models/Marketing/MarketingCostSeeder'

export default [
    CountrySeeder,
    CitySeeder,
    ProjectSeeder,
    AirlineSeeder,
    PaymentGatewaySeeder,
    CurrencySeeder,
    DepartmentSeeder,
    PositionSeeder,
    TeamSeeder,
    AgentSeeder,
    ChatGatewaySeeder,
    ConsolidatorSeeder,
    ConsolidatorAreaSeeder,
    MilePriceProgramSeeder,
    ClientStatusSeeder,
    ClientSeeder,
    IataSeeder,
    ClientPassportSeeder,
    LeadStatusSeeder,
    LeadSeeder,
    FileSeeder,
    LeaveRequestSeeder,
    LeaveTransactionSeeder,
    TicketExtraPreferenceSeeder,
    SaleSeeder,
    VoucherSeeder,
    IssueSeeder,
    TaskAssignmentsSeeder,
    ChatTestSystemMessagesSeederSeeder,
    CheckInReminderSeeder,
    PriceQuoteSeeder,
    ClientCreditCardSeeder,
    SaleVersionCardSeeder,
    BookkeepingPaymentGatewaySeeder,
    BookkeepingInvoiceSeeder,
    BookkeepingTransactionSeeder,
    BookkeepingTransactionAssignmentSeeder,
    ExternalUserAgentSeeder,

    ProjectCardSeeder,
    ProductSeeder,
    TicketSeeder,
    VccProjectCardSummarySeeder,

    AirlineReportSeeder,
    AssignLogSeeder,
    ActivityLogSeeder,
    ActivityLogDetailedSeeder,
    AgentReportSeeder,
    SaleVoucherSeeder,
    SaleExtraProfitSeeder,
    CompanyContactSeeder,
    AirlineCaseSeeder,
    ExpectedAmountSeeder,
    SkillSeeder,
    AgentSkillSeeder,
    RegionSeeder,
    ShiftSeeder,
    TravelerSeeder,
    ReleasePostSeeder,
    TaskChangeSaleTypeSeeder,
    EmailContactAttemptSeeder,
    IssueGroupSeeder,
    PriceDropCheckSeeder,
    OfficeSeeder,
    ElrFrtCheckSeeder,
    GamblingLotSeeder,
    CallContactAttemptSeeder,
    TerminalHotkeySeeder,
    SaleTransactionSeeder,
    PnrScheduleSeeder,
    SaleClientRequestSeeder,
    SignedDocumentSeeder,
    PerformanceFeedbackSeeder,
    PerformanceFeedbackEventSeeder,
    AwardAccountSeeder,
    SaleRequestHelpSeeder,
    MarketingCostSeeder,
] satisfies Array<typeof DatabaseSeeder>
