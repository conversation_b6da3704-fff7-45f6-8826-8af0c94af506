import { defineResourceHandler } from '~mock/utils/define'
import { findModelRecord, createModelIdentification } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelAttributes } from '~types/lib/Model'

export default defineResourceHandler(async (resourceName, pk): Promise<ModelAttributes<'LeaveCounter'>> => {
    const leaveCounter = await findModelRecord('LeaveCounter', pk)

    return {
        ...createModelIdentification('LeaveCounter', pk),
        agent_pk: pk,
        approved_future_days: leaveCounter.approved_future_days,
        available_days: leaveCounter.available_days,
        pending_requests: leaveCounter.pending_requests,
        sick_days: leaveCounter.sick_days,
        used_days_in_current_year: leaveCounter.used_days_in_current_year,
    }
})
