import { defineResource<PERSON>and<PERSON> } from '~mock/utils/define'
import { createModelIdentification } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelAttributes } from '~types/lib/Model'
import { randomBoolean } from '~mock/lib/Helper/SeedHelper'
import { faker } from '@faker-js/faker'

export default defineResourceHandler(async (resourceName, composedPk): Promise<ModelAttributes<'FraudInfo'>> => {
    const [model_name, model_pk] = composedPk.split(':')
    const is_fraud = randomBoolean(0.2)

    return {
        ...createModelIdentification('FraudInfo', composedPk),
        model_name,
        model_pk,
        is_fraud,
        remark: is_fraud ? faker.lorem.sentence() : null,
    }
})
