import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import ProjectCardFactory from '~mock/factories/Card/ProjectCardFactory'
import { ProjectCardCategory } from '~/api/models/Card/ProjectCard'
import {
    deleteModelRecords,
    findModelRecord,
    findModelRecords,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import { getFullName } from '~/lib/Helper/PersonHelper'
import ProjectCardAdditionalVccFactory from '~mock/factories/Card/ProjectCardAdditionalVccFactory'
import { randomInt } from '~/lib/Helper/NumberHelper'
import ProjectCardTransactionFactory from '~mock/factories/Card/ProjectCardTransactionFactory'
import { usePk } from '~/composables/usePk'
import { randomElement } from '~/lib/Helper/ArrayHelper'
import { CreditCardType } from '~/api/models/Sale/SaleVersionCard'

export default defineModelHandler<'ProjectCard'>({
    actions: {
        createVirtualCard: async function({ amount_limit, sale_pk, client_pk, airline_pk, usage_area }) {
            const client = await findModelRecord('Client', client_pk)
            const card_name = getFullName(client)
            const card = await new ProjectCardFactory().create({
                card_name,
                sale_pk,
                category: ProjectCardCategory.ProjectVirtualCard,
            })

            await new ProjectCardAdditionalVccFactory().create({
                id: card.id,
                amount_limit,
                airline_pk,
                usage_area,
            })
        },

        getBillingAddressForNewCard: async function({ sale_pk }) {
            return this.response({
                country: 'US',
                city: 'Elmhurst',
                state: 'IL',
                street: '725 S Euclid Ave',
                zip: '60126',
                bank_phone: '+***********',
            })
        },

        getCardBillingAddress: async function({ pk }) {
            return this.response({
                country: 'US',
                city: 'Elmhurst',
                state: 'IL',
                street: '725 S Euclid Ave',
                zip: '60126',
                bank_phone: '+***********',
            })
        },

        updateVirtualCardAmount: async function({ pk, amount_limit }) {
            await updateModelRecord('ProjectCardAdditionalVcc', pk, {
                amount_limit,
            })
        },

        updateTransactions: async function({ pk }) {
            await deleteModelRecords('ProjectCardTransaction', {
                card_pk: pk,
            })

            const transactionFactory = new ProjectCardTransactionFactory()

            let amount_used = 0
            for (let i = 0; i < randomInt(1, 15); i++) {
                const transaction = await transactionFactory.create({
                    card_pk: pk,
                })
                amount_used += transaction.amount
            }

            await updateModelRecord('ProjectCardAdditional', pk, {
                amount_used,
            })
        },

        updateAllTransactions: async function() {
            const cards = await findModelRecords('ProjectCard', {
                category: ProjectCardCategory.ProjectVirtualCard,
            })

            for (const card of cards) {
                const pk = usePk(card)
                const transactionFactory = new ProjectCardTransactionFactory()

                let amount_used = 0
                for (let i = 0; i < randomInt(1, 15); i++) {
                    const transaction = await transactionFactory.create({
                        card_pk: pk,
                    })
                    amount_used += transaction.amount
                }

                await updateModelRecord('ProjectCardAdditional', pk, {
                    amount_used,
                })
            }
        },

        getCardInfo: async function({ pk }) {
            const types = [CreditCardType.Master, CreditCardType.Visa]
            const numbers = ['****************', '******************']
            const holder_names = ['SARAH JONES', 'JONATHAN BLACK']
            const expirations = ['04/27', '05/26']
            const cvvs = ['123', '345']
            const info = {
                card_type: randomElement(types),
                card_number: randomElement(numbers),
                holder_name: randomElement(holder_names),
                expiration: randomElement(expirations),
                cvv: randomElement(cvvs),
            }

            return this.response(info)
        },

        getCredentialsUrl: async function({ pk }) {
            return this.response({
                url: 'https://staging-bo.travelbusinessclass.com/login?back=/leads',
            })
        },
    },

})
