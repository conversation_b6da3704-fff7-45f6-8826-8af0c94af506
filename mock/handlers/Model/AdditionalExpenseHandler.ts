import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { deleteModelRecord, findModelRecordOrFail, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import ProductFactory from '~mock/factories/Product/ProductFactory'
import AdditionalExpenseFactory from '~mock/factories/AdditionalExpense/AdditionalExpenseFactory'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'

const modelName = 'AdditionalExpense'

export default defineModelHandler<typeof modelName>({
    actions: {
        create: async function({ sale_version_pk, product }) {
            const productForExpense = await new ProductFactory().create({
                ...product,
                created_at: dateToUnixTimestamp(new Date()),
                created_by_pk: useMockAuth().pk,
            })

            const additional = await new AdditionalExpenseFactory().create({
                sale_version_pk,
                product_pk: usePk(productForExpense),
                executor_pk: product.executor_pk,
            })

            return this.response({
                pk: usePk(additional),
            })
        },
        update: async function({ pk, product }) {
            const expense = await findModelRecordOrFail('AdditionalExpense', pk)

            if (!expense) {

            }

            await updateModelRecord('Product', expense.product_pk, product)
        },
        delete: async function({ pk }) {
            await deleteModelRecord('AdditionalExpense', pk)
        },
    },
    searchFields: {},
    observers: {
        afterDelete: async function(record) {
            await deleteModelRecord('Product', record.product_pk)
        },
    },
})
