import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { createModelRecord, findModelRecords, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ActionResponse, ModelAttributes } from '~types/lib/Model'
import { dateToUnixTimestamp, items, randomBoolean } from '~mock/lib/Helper/SeedHelper'
import AgentShiftFactory from '~mock/factories/Agent/AgentShiftFactory'
import { usePk } from '~/composables/usePk'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { faker } from '@faker-js/faker'
import { pluckPks } from '~/lib/Helper/ArrayHelper'

export default defineModelHandler<'Agent'>({
    actions: {
        changeTeam: async function({
                                        pk,
                                        team_pk,
                                    }) {
            const item = await updateModelRecord('Agent', pk, { team_pk })

            return this.response(item)
        },

        disable: async function({ pk }) {
            await updateModelRecord('Agent', pk, { is_enabled: false })
        },
        requestedDisable: async function({ pk }) {
            await updateModelRecord('Agent', pk, { is_enabled: false })
        },

        confirmDismissal: async function({
                                              pk,
                                              date,
                                          }) {
            await updateModelRecord('Agent', pk, { dismissed_at: date || dateToUnixTimestamp(new Date()) })
        },

        confirmHire: async function({
                                         pk,
                                         date,
                                     }) {
            await updateModelRecord('Agent', pk, { hired_at: date || dateToUnixTimestamp(new Date()) })
        },

        enable: async function({ pk }) {
            await updateModelRecord('Agent', pk, { is_enabled: true })
        },
        suspiciousActivityMonitoring: async function({ pk }) {
            //do nothing
        },

        getExpertStatistics: async function({
                                                 period,
                                                 project_pk,
                                             }) {
            const agents = await findModelRecords('Agent')
            const result = agents
                .splice(12)
                .map((agent) => {
                    const takenLeadsManuallyDayCount = randomInt(0, 10)
                    const takenLeadsFromQueueDayCount = randomInt(0, 10)

                    const takenLeadsManuallyMonthCount = takenLeadsManuallyDayCount * randomInt(5, 10)
                    const takenLeadsFromQueueMonthCount = takenLeadsFromQueueDayCount * randomInt(5, 10)

                    return {
                        agent_pk: usePk(agent),
                        takenLeadsMonthCount: takenLeadsManuallyMonthCount + takenLeadsFromQueueMonthCount,
                        takenLeadsManuallyMonthCount,
                        takenLeadsFromQueueMonthCount,

                        takenLeadsDayCount: takenLeadsManuallyDayCount * takenLeadsFromQueueDayCount,
                        takenLeadsManuallyDayCount,
                        takenLeadsFromQueueDayCount,

                        soldLeadsCount: 3,
                        soldSalesCount: 6,
                        averageLeadsDay: 4,
                        total_gp: 123,
                        targetAchievedPs: 333,
                        target: 3434,
                        pq_gp: 123123,
                        lead_gp: 6756575.657,
                    } satisfies ActionResponse<'Agent', 'getExpertStatistics'>[number]
                })

            return this.response(result)
        },

        getExpertStatisticDetails: async function({
                                                       period,
                                                       agent_pk,
                                                       field,
                                                   }) {
            const lead_pks = (await findModelRecords('Lead'))
                .splice(12)
                .map((lead) => usePk(lead))

            return this.response({
                lead_pks,
            })
        },

        getSaleStatistics: async function({
                                               period,
                                               project_pk,
                                           }) {
            const agents = await findModelRecords('Agent')
            const result = agents
                .splice(12)
                .map((agent) => {
                    return {
                        agent_pk: usePk(agent),
                        target: randomInt(1000, 5000),
                        is_closed: {
                            bonus_leads_count: randomInt(1, 3),
                            new_leads_count: randomInt(1, 3),
                            leads_moved_to_nq: randomInt(1, 3),
                            leads_moved_to_bq: randomInt(1, 3),
                            leads_returns_or_referrals_count: randomInt(1, 3),
                            bonus_clients_count: randomInt(1, 3),
                            new_clients_count: randomInt(1, 3),
                            clients_moved_to_nq: randomInt(1, 3),
                            clients_moved_to_bq: randomInt(1, 3),
                            clients_returns_or_referrals_count: randomInt(1, 3),
                            non_tbc_clients: 2,
                            gp_exchange_refunds: 5.55,
                            gp_from_leads_returns_or_referrals: 3.23,
                            gp_from_new_clients: 4.44,
                            sales_number: 15,
                            tp: 123,
                            tips: 23232.2222,
                            total_gp: 2323.123,
                        },
                        is_adjusted: {
                            bonus_leads_count: randomInt(1, 3),
                            new_leads_count: randomInt(1, 3),
                            leads_moved_to_nq: randomInt(1, 3),
                            leads_moved_to_bq: randomInt(1, 3),
                            leads_returns_or_referrals_count: randomInt(1, 3),
                            bonus_clients_count: randomInt(1, 3),
                            new_clients_count: randomInt(1, 3),
                            clients_moved_to_nq: randomInt(1, 3),
                            clients_moved_to_bq: randomInt(1, 3),
                            clients_returns_or_referrals_count: randomInt(1, 3),
                            non_tbc_clients: 2,
                            gp_exchange_refunds: 5.55,
                            gp_from_leads_returns_or_referrals: 3.23,
                            gp_from_new_clients: 4.44,
                            sales_number: 15,
                            tp: 123,
                            tips: 13232.2222,
                            total_gp: 1323.123,
                        },
                    } satisfies ActionResponse<'Agent', 'getSaleStatistics'>[number]
                })

            return this.response(result)
        },

        getLeadManagementStatistics: async function({
                                                         period,
                                                         project_pk,
                                                     }) {
            const agents = await findModelRecords('Agent')
            const result = agents
                .splice(12)
                .map((agent) => {
                    return {
                        agent_pk: usePk(agent),
                        // month
                        total_month_closing_count: randomInt(1, 10),
                        approved_month_closing_count: randomInt(1, 10),
                        declined_month_closing_count: randomInt(1, 10),
                        //today
                        total_today_closing_count: randomInt(1, 10),
                        approved_today_closing_count: randomInt(1, 10),
                        declined_today_closing_count: randomInt(1, 10),

                        //month
                        total_month_keep_client_count: randomInt(1, 10),
                        approved_month_keep_client_count: randomInt(1, 10),
                        declined_month_keep_client_count: randomInt(1, 10),
                        //today
                        total_today_keep_client_count: randomInt(1, 10),
                        approved_today_keep_client_count: randomInt(1, 10),
                        declined_today_keep_client_count: randomInt(1, 10),

                        // month
                        total_month_follow_up_count: randomInt(1, 10),
                        processed_well_month_follow_up_count: randomInt(1, 10),
                        need_attention_month_follow_up_count: randomInt(1, 10),
                        //today
                        total_today_follow_up_count: randomInt(1, 10),
                        processed_well_today_follow_up_count: randomInt(1, 10),
                        need_attention_today_follow_up_count: randomInt(1, 10),

                        // month
                        total_month_created_count: randomInt(1, 10),
                        created_month_jivo_count: randomInt(1, 10),
                        created_month_general_line_count: randomInt(1, 10),
                        //today
                        total_today_created_count: randomInt(1, 10),
                        created_today_jivo_count: randomInt(1, 10),
                        created_today_general_line_count: randomInt(1, 10),

                        gp: randomInt(10, 1000),
                    } satisfies ActionResponse<'Agent', 'getLeadManagementStatistics'>[number]
                })

            return this.response(result)
        },

        getLeadManagementStatisticsDetails: async function() {
            const lead_pks = (await findModelRecords('Lead'))
                .splice(12)
                .map((lead) => usePk(lead))

            return this.response({
                lead_pks,
            })
        },

        getSaleStatisticDetails: async function({
                                                     period,
                                                     agent_pk,
                                                     field,
                                                 }) {
            const lead_pks = (await findModelRecords('Lead'))
                .splice(12)
                .map((lead) => usePk(lead))

            return this.response({
                lead_pks,
            })
        },
        getSalesFromStatisticDetails: async function({
                                                          period,
                                                          agent_pk,
                                                          saleStage,
                                                          field,
                                                      }) {
            const sale_pks = (await findModelRecords('Sale'))
                .splice(8)
                .map((sale) => usePk(sale))

            return this.response({
                sale_pks,
            })
        },
        setIsBeginner: async function({
                                           pk,
                                           is_beginner,
                                       }) {
            await updateModelRecord('Agent', pk, { is_beginner })
        },
        updateBonus: async function({
                                         pk,
                                         rate,
                                     }) {
            await updateModelRecord('AgentAdditionalBonusInfo', pk, { rate: rate })
        },
        updateShiftType: async function({
                                             agent_pk,
                                             shift_type,
                                         }) {
            await updateModelRecord('Agent', agent_pk, { shift_type: shift_type })
        },
        linkToRingcentral: async function({ ringcentral_id }) {
            await updateModelRecord('Agent', useMockAuth().pk, { ringcentral_id })
        },
        agentAvatarCompress: async function() {
            return this.response({
                avatar: 'https://avatars.githubusercontent.com/u/48316232',
            })
        },

        getAbilities: async function() {
            const abilityLead = {
                pk: 'lead-management',
                name: 'Lead Management',
                description: 'Ability to manage leads',
                access_description: [
                    'View leads',
                    'Edit leads',
                    'Create leads',
                    'Delete leads',
                    'Assign to leads',
                ],
            }

            const abilitySale = {
                pk: 'sale-management',
                name: 'Sale Management',
                description: 'Ability to manage sales',
                access_description: [
                    'View sales',
                    'Edit sales',
                    'Create sales',
                    'Delete sales',
                    'Assign to sales',
                ],
            }

            return this.response(
                items(15).flatMap((n) => {
                    return [
                        {
                            ...abilityLead,
                            pk: `${abilityLead.pk}-${n}`,
                            name: `${abilityLead.name} ${n}`,
                            is_enabled: randomBoolean(),
                            description: faker.lorem.sentence(randomInt(5, 40)),
                        },
                        {
                            ...abilitySale,
                            pk: `${abilitySale.pk}-${n}`,
                            name: `${abilitySale.name} ${n}`,
                            is_enabled: randomBoolean(),
                            description: faker.lorem.sentence(),
                        },
                    ]
                }),
            )
        },

        setAbility: async function(params) {
            return this.response({
                is_enabled: params.is_enabled,
            })
        },

        getAgentsMightBeTop: async function({}) {
            const agents = await findModelRecords('Agent')

            return this.response(pluckPks(agents).splice(0, 2))
        },
    },

    searchFields: {
        fullname(record: ModelAttributes<'Agent'>) {
            return record.first_name + ' ' + record.last_name + ' ' + record.first_name
        },
        can_ba_added_to_agent_report(record) {
            return record.pk
        },
        position_pk(record: ModelAttributes<'Agent'>) {
            return record.position_pk
        },
        department_pk(record: ModelAttributes<'Agent'>) {
            return record.department_pk
        },
        is_ai_agent(record: ModelAttributes<'Agent'>) {
            return record.is_ai_agent ? 1 : 0
        },
    },

    observers: {
        afterCreate: async function(request) {
            await createModelRecord('AgentAdditionalSalaryInfo', {
                id: request.id,
                value: randomInt(100, 200),
            })

            await new AgentShiftFactory().create({
                agent_pk: String(request.id),
            })

            const chat = await createModelRecord('Chat', {
                name: 'Assistant',
                gateway_pk: '7', // TODO: hardcoded
                type: 'assistant',
                is_readonly: false,
                is_archived: false,
                model_name: 'Agent',
                model_pk: String(request.id),
                chat_group_pks: [],
            })

            await createModelRecord('ChatAdditionalInfo', {
                id: chat.id,
                last_message_pk: null,
                last_user_message_pk: null,
                new_messages_count: 0,
                messages_count: 0,
                user_messages_count: 0,
                new_user_messages_count: 0,
                last_read_at: null,
                last_pinned_message_pk: null,

                // @ts-ignore
                auth_pk: String(request.id),
            })
        },
    },
})
