import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import IssueSeeder from '~mock/seeds/models/Issue/IssueSeeder'
import { IssueCategory } from '~/api/models/Issue/Issue'
import {
    createModelIdentification,
    deleteModelRecord,
    deleteModelRecords,
    emitResourceEvent,
    findModelRecord,
    findModelRecordOrFail,
    findModelRecords,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import { DepartmentName } from '~/api/models/Department/Department'
import {
mapAsync,
randomElement,
randomElements,
randomElementPk,
pluckPks,
} from '~/lib/Helper/ArrayHelper'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { faker } from '@faker-js/faker'
import type { ActionResponse } from '~types/lib/Model'
import { getFullName } from '~/lib/Helper/PersonHelper'
import { CounterCategory } from '~/api/models/Counter/Counter'
import { usePk } from '~/composables/usePk'
import type { StarColor } from '~/api/dictionaries/Static/Lead/LeadStarTypeDictionary'
import { randomBoolean, randomEnumValue, withChance } from '~mock/lib/Helper/SeedHelper'
import { followUpMaxPoints } from '~/api/models/Lead/Lead'
import searchAwardOffersData from '../../data/searchAwardOffers.json'
import { Timespan } from '~/lib/Helper/TimespanHelper'
import { ExternalResource } from '~/api/dictionaries/Static/ExternalResourceDictionary'
import { wait } from '~/lib/Helper/PromiseHelper'

const modelName = 'Lead'

export default defineModelHandler<typeof modelName>({
    actions: {
        createClientStatusChangeIssue: async function(data) {
            const lead = await findModelRecordOrFail('Lead', data.pk)

            const issue = await (new IssueSeeder()).create({
                model_name: modelName,
                model_pk: usePk(lead),
                category: IssueCategory.ClientStatus,
                data: {
                    old_client_status_pk: lead.client_status_pk,
                    client_pk: lead.client_pk,
                    executor_pk: lead.executor_pk as PrimaryKey, // We give access to this action for executors. So it will be always defined
                },
                result: {
                    client_status_pk: data.client_status_pk,
                    referral_client_pk: data.referral_client_pk,
                    return_from_sale_pk: data.return_from_sale_pk,
                },
            })

            const itDepartment = await findModelRecordOrFail('Department', {
                system_name: DepartmentName.IT,
            })

            if (useMockAuth().department_pk === usePk(itDepartment)) {
                return this.response({
                    issue_pk: null,
                })
            }

            return this.response({
                issue_pk: usePk(issue),
            })
        },
        createKeepClientIssue: async function() {
            //do nothing
        },

        claimSale: async function(data) {
            await (new IssueSeeder()).create({
                model_name: 'Sale',
                model_pk: data.sale_pk,
                category: IssueCategory.SplitSale,
                data: {
                    agent_pk: useMockAuth().pk,
                    lead_pk: data.lead_pk,
                    sale_lead_pk: data.lead_pk,
                },
                result: {
                    ticket_profit: 50,
                    ticket_protection: 50,
                    tips: 50,
                },
            })
        },

        findLead: async function({ query }) {
            let leads = await findModelRecords('Lead')

            if (query === 'no') {
                return this.response([])
            }

            if (query !== 'all') {
                leads = randomElements(leads, randomInt(2, 10))
            }

            return this.response(
                await mapAsync(leads, async (lead) => {
                    return {
                        pk: usePk(lead),
                        client_fullname: faker.person.fullName(),
                        client_phone: faker.phone.number(),
                        client_email: faker.internet.email(),
                        created_at: lead.created_at,
                        executor_fullname: lead.executor_pk ? getFullName(await findModelRecordOrFail('Agent', lead.executor_pk)) : null,
                        from_iata_code: lead.from_iata_code,
                        to_iata_code: lead.to_iata_code,
                    }
                }) satisfies ActionResponse<'Lead', 'findLead'>,
            )
        },

        takeOwnLead: async function() {
            // do nothing, it's just a mock
        },

        requestLead: async function() {
            await emitResourceEvent('update', 'Counter', {
                ...createModelIdentification('Counter', composeResourcePk('Counter', {
                    workspace: useMockWorkspace(),
                    category: CounterCategory.LeadTakeRequests,
                    auth_pk: useMockAuth().pk,
                })),
                value: 1,
            })
        },

        takeNewLead: async function() {
            const agentPk = useMockAuth().pk

            const leads = await findModelRecords('Lead', (record) => {
                return record.executor_pk !== agentPk
            })

            if (!leads.length) {
                throw new Error('No new leads found')
            }

            return this.response({
                lead_pks: randomElements(leads, randomInt(1, 3)).map(usePk),
            })
        },

        changeStatusBulk: async function({ pks, status_pk }) {
            for (const pk of pks) {
                await updateModelRecord(modelName, pk, { status_pk })
            }
        },

        searchLeadsByAgentForShare: async function() {
            const leads = await findModelRecords('Lead')

            const leadsResult = leads.map((lead) => {
                return {
                    ...lead,
                    pk: usePk(lead),
                }
            })

            return this.response(randomElements(leadsResult, randomInt(0, 5)))
        },

        dropClient: async function() {
            // do nothing
        },

        addLeadToQueue: async function({ lead_pk }) {
            await updateModelRecord('LeadAdditionalManagementStatus', lead_pk, {
                is_in_queue: true,
            })
        },

        closeLead: async function({ lead_pk, closing_reason, closing_reason_remark }) {
            await updateModelRecord('Lead', lead_pk, {
                closing_reason: closing_reason,
                closing_reason_remark: closing_reason_remark,
            })
        },

        sendFollowUp: async function() {
            // do nothing
        },

        changeLeadStarTypeForAgent: async function({ lead_pk, star_type }) {
            await updateModelRecord('LeadAdditionalAgentInfo', `${lead_pk}:3`, { star_type: star_type as StarColor })

            return this.response(await findModelRecordOrFail('LeadAdditionalAgentInfo', `${lead_pk}:3`))
        },

        setPinned: async function() {
            //do nothing
        },

        setDefaultPhone: async function({ lead_pk, phone_pk }) {
            await updateModelRecord('Lead', lead_pk, { client_phone_pk: phone_pk })
        },

        setDefaultEmail: async function({ lead_pk, email_pk }) {
            await updateModelRecord('Lead', lead_pk, { client_email_pk: email_pk })
        },

        toggleUnsoldPriceQuotes: async function() {
            //do nothing
        },
        assignExecutor: async function({ lead_pk, executor_pk }) {
            await updateModelRecord(modelName, lead_pk, { executor_pk })
        },
        assignExpert: async function({ lead_pk, expert_pk }) {
            await updateModelRecord(modelName, lead_pk, { expert_pk })
        },
        getLeadStatistics: async function({}) {
            const leads = await findModelRecords('Lead')

            return this.response(randomElements(leads, randomInt(5, 30)).map(usePk))
        },

        checkLead: async function({ jivo_link }) {
            if (jivo_link?.includes('none')) {
                return this.response({ lead_pk: null, leads_info: []})
            }

            const leads = randomElements(await findModelRecords('Lead'), randomInt(5, 10))

            const mappedLeads: ActionResponse<'Lead', 'checkLead'>['leads_info'] = leads.map(lead => {
                return {
                    ...lead,
                    pk: usePk(lead),
                    pq_count: randomInt(1, 10),
                }
            })

            return this.response({
                lead_pk: usePk(randomElement(leads)),
                leads_info: mappedLeads,
            })
        },
        createFromCheckLead: async function({}) {
            return this.response('1')
        },
        getLeadFollowUpProgress: async function() {
            const dailyProgress = []
            for (let i = 0; i < 14; i++) {
                dailyProgress.push({
                    first_phone_call: withChance(randomBoolean(), 0.3),
                    first_voice_message: withChance(randomBoolean(), 0.3),
                    second_phone_call: withChance(randomBoolean(), 0.3),
                    second_voice_message: withChance(randomBoolean(), 0.3),
                    text_massage: withChance(randomBoolean(), 0.3),
                    reply_to_email: withChance(randomBoolean(), 0.3),
                    manually_created_email: withChance(randomBoolean(), 0.3),
                    follow_up_template: withChance(randomBoolean(), 0.3),
                })
            }

            return this.response({
                completed_points: randomInt(0, followUpMaxPoints),
                daily_progress: dailyProgress,
            })
        },

        getLeadOfferPreview: async function() {
            return this.response({
                url: 'https://travelbusinessclass.com/best-deals/region/oceania',
            })
        },

        sendLeadOffer: async function() {
        },
        sendLeadOfferToMe: async function() {
        },

        getClientReachedTaskPk: async function(record) {
            const taskPk = usePk(await findModelRecordOrFail('Task', record.pk))

            return this.response({ task_pk: taskPk })
        },
        attachVoucher: async function({ lead_pk, voucher_pk }) {
            const lead = await findModelRecordOrFail('Lead', lead_pk)
            const voucher_pks = lead.voucher_pks || []
            await updateModelRecord('Lead', lead_pk, { voucher_pks: [...voucher_pks, voucher_pk]})
        },

        getLeadClientSessionAgent: async function() {
            return this.response(
                withChance({
                    agent_pk: randomElementPk(await findModelRecords('Agent')),
                }),
            )
        },

        create: async function() {
            // Do nothing. Logic is too complex to mock

            return this.response({
                lead_pk: randomElementPk(await findModelRecords('Lead')),
            })
        },

        update: async function() {
            // Do nothing. Logic is too complex to mock
        },

        updateRemark: async function({ lead_pk, remark }) {
            return updateModelRecord(modelName, lead_pk, { remark: remark })
        },

        sendNewLead: async function({ agent_pk }) {
            const leads = await findModelRecords('Lead', { executor_pk: null })
            const lead_pk = randomElementPk(leads)
            await updateModelRecord('Lead', lead_pk, { executor_pk: agent_pk })

            return this.response({
                lead_pk,
            })
        },
        searchAwardOffers: async function() {
            return this.response(searchAwardOffersData)
        },

        getNewQueueStatisticsPerDay: async function({ date_range, group_per_hours, team_pks }) {
            const teamList = await findModelRecords('Team')

            const days = getAllDaysOfLastYear()
            const hours = getAllHoursInDay()
            const month = getAllDaysInMonth()

            if ((date_range.end - date_range.start) <= Timespan.days(2).inSeconds) {
                return this.response(Array.from({ length: 1 }, (_, i) => ({
                    dataset: hours.map((h, index) => {
                        return {
                            value: Math.floor(Math.random() * (i > 0 ? 150 : 500)),
                            timestamp: h.getTime(),
                            data: Array.from({ length: 3 }, (_, i) => ({
                                external_resource: randomEnumValue(ExternalResource),
                                count: faker.number.int({ min: 0, max: 20 }),
                            })),
                        }
                    }),
                    data: i !== 0 ? { team_pk: randomElementPk(teamList) } : null,
                })))
            }

            if ((date_range.end - date_range.start) <= (Timespan.month().inSeconds + Timespan.days(1).inSeconds)) {
                return this.response(Array.from({ length: 20 }, (_, i) => ({
                    dataset: month.map((d, index) => {
                        return {
                            value: Math.floor(Math.random() * (i > 0 ? 150 : 500)),
                            timestamp: d.getTime(),
                            data: Array.from({ length: 3 }, (_, i) => ({
                                external_resource: randomEnumValue(ExternalResource),
                                count: faker.number.int({ min: 0, max: 20 }),
                            })),
                        }
                    }),
                    data: i !== 0 ? { team_pk: randomElementPk(teamList) } : null,
                })))
            }

            return this.response(Array.from({ length: 2 }, (_, i) => ({
                dataset: days.map((d, index) => {
                    return {
                        value: Math.floor(Math.random() * (i > 0 ? 150 : 500)),
                        timestamp: d.getTime(),
                        data: Array.from({ length: 3 }, (_, i) => ({
                            external_resource: randomEnumValue(ExternalResource),
                            count: faker.number.int({ min: 0, max: 20 }),
                        })),
                    }
                }),
                data: i !== 0 ? { team_pk: randomElementPk(teamList) } : null,
            })))
        },

        getNewQueueStatisticsPerUser: async function({ date_range }) {
            const agents = await  findModelRecords('Agent')

            return this.response(
                agents.map((item, index) => ({
                    agent_pk: usePk(item),
                    conversion_value: index,
                    value: index,
                })),
            )
        },

        getNewQueueSettings: async function({}) {
            return this.response({
                top_agent_max_count: 12,
                prediction_threshold: 50,
            })
        },

        updateNewQueueSettings: async function({ top_agent_max_count, prediction_threshold }) {

        },
    },

    searchFields: {
        is_client_reached: () => {
            return randomBoolean() ? '1' : '0'
        },
        client_fullname: async (record) => {
            const client = await findModelRecord('Client', record.client_pk)

            return client ? getFullName(client).toLowerCase() : ''
        },
        client_email: async (record) => {
            if (!record.client_email_pk) {
                return
            }
            const email = await findModelRecord('Email', record.client_email_pk)

            return email?.value.toLowerCase()
        },
        client_phone: async (record) => {
            if (!record.client_phone_pk) {
                return
            }
            const phone = await findModelRecord('Phone', record.client_phone_pk)

            return phone?.value
        },
        utm_source: async (record) => {
            const utmModel = await findModelRecord('LeadAdditionalUTM', String(record.id))

            return utmModel ? utmModel.utm_source.toLowerCase() : ''
        },
        status: async (record) => {
            const status = await findModelRecord('LeadStatus', String(record.status_pk))

            return status?.id
        },
        is_in_queue: async () => {
            return randomBoolean(0.5)
        },
        can_add_pq: async () => {
            return true
        },
        prediction_score: async (record) => {
            const prediction = await findModelRecord('LeadAdditionalPrediction', usePk(record))

            return prediction?.lead_score
        },
        can_merge: async () => {
            return true
        },
    },
    observers: {
        async afterDelete(record) {
            const pk = usePk(record)

            await deleteModelRecord('LeadAdditionalUTM', pk)
            await deleteModelRecords('LeadAdditionalListInformation', pk)
            await deleteModelRecord('LeadAdditionalAgentInfo', pk)
            await deleteModelRecord('LeadAdditionalManagementStatus', pk)
            await deleteModelRecord('LeadOffer', pk)
        },
    },
})

function getAllDaysInMonth(): Date[] {
    const start = new Date()
    const end = new Date()

    start.setDate(1)
    end.setDate(31)

    const result: Date[] = []
    const current = new Date(start)

    while (current <= end) {
        result.push(new Date(current))
        current.setDate(current.getDate() + 1)
    }

    return result
}

function getAllHoursInDay(): Date[] {
    const start = new Date()
    const end = new Date()

    start.setHours(0)
    start.setMinutes(0)
    end.setHours(23)
    end.setMinutes(59)

    const result: Date[] = []
    const current = new Date(start)

    while (current <= end) {
        result.push(new Date(current))
        current.setHours(current.getHours() + 1)
    }

    return result
}

function getAllDaysOfLastYear(): Date[] {
    const now = new Date()
    const lastYear = now.getFullYear() - 1

    const start = new Date(lastYear, 0, 1) // Jan 1, 00:00:00
    const end = new Date(lastYear, 11, 31) // Dec 31

    const result: Date[] = []
    const current = new Date(start)

    while (current <= end) {
        result.push(new Date(current))
        current.setMonth(current.getMonth() + 1)
    }

    return result
}
