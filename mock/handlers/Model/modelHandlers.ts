// This file is auto generatedby vite-plugin-import-model-handlers
// Do not edit this file manually

import type { MockModelHandler } from '~mock/types'

import AdditionalExpenseHandler from '#/mock/handlers/Model/AdditionalExpenseHandler'
import AgentAdditionalSalaryInfoHandler from '#/mock/handlers/Model/AgentAdditionalSalaryInfoHandler'
import AgentAppSettingsHandler from '#/mock/handlers/Model/AgentAppSettingsHandler'
import AgentClientTransferHandler from '#/mock/handlers/Model/AgentClientTransferHandler'
import AgentHandler from '#/mock/handlers/Model/AgentHandler'
import Agent<PERSON>eportAdditionalHandler from '#/mock/handlers/Model/AgentReportAdditionalHandler'
import <PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON> from '#/mock/handlers/Model/AgentReportHandler'
import AgentReportInternalProfitHandler from '#/mock/handlers/Model/AgentReportInternalProfitHandler'
import AgentReportInvoiceHandler from '#/mock/handlers/Model/AgentReportInvoiceHandler'
import <PERSON>Report<PERSON>aleHand<PERSON> from '#/mock/handlers/Model/AgentReportSaleHandler'
import <PERSON><PERSON><PERSON><PERSON><PERSON>ick<PERSON><PERSON>andler from '#/mock/handlers/Model/AgentReportTicketingHandler'
import AgentShiftHandler from '#/mock/handlers/Model/AgentShiftHandler'
import AgentSkillHandler from '#/mock/handlers/Model/AgentSkillHandler'
import AgentTargetHandler from '#/mock/handlers/Model/AgentTargetHandler'
import AirlineCaseHandler from '#/mock/handlers/Model/AirlineCaseHandler'
import AirlineHandler from '#/mock/handlers/Model/AirlineHandler'
import AirlineReportHandler from '#/mock/handlers/Model/AirlineReportHandler'
import AirlineReportVersionHandler from '#/mock/handlers/Model/AirlineReportVersionHandler'
import AudioTranscriptionHandler from '#/mock/handlers/Model/AudioTranscriptionHandler'
import AwardAccountHandler from '#/mock/handlers/Model/AwardAccountHandler'
import AwardAccountHolderHandler from '#/mock/handlers/Model/AwardAccountHolderHandler'
import AwardAccountIncomingRequestHandler from '#/mock/handlers/Model/AwardAccountIncomingRequestHandler'
import AwardAccountOutgoingRequestHandler from '#/mock/handlers/Model/AwardAccountOutgoingRequestHandler'
import BonusLeadHandler from '#/mock/handlers/Model/BonusLeadHandler'
import BookkeepingInvoiceDocumentHandler from '#/mock/handlers/Model/BookkeepingInvoiceDocumentHandler'
import BookkeepingInvoiceHandler from '#/mock/handlers/Model/BookkeepingInvoiceHandler'
import BookkeepingTransactionAdditionalPaymentHandler from '#/mock/handlers/Model/BookkeepingTransactionAdditionalPaymentHandler'
import BookkeepingTransactionAssignmentHandler from '#/mock/handlers/Model/BookkeepingTransactionAssignmentHandler'
import BookkeepingTransactionHandler from '#/mock/handlers/Model/BookkeepingTransactionHandler'
import CallContactAttemptHandler from '#/mock/handlers/Model/CallContactAttemptHandler'
import ChatHandler from '#/mock/handlers/Model/ChatHandler'
import ChatMessageHandler from '#/mock/handlers/Model/ChatMessageHandler'
import CheckInReminderHandler from '#/mock/handlers/Model/CheckInReminderHandler'
import ClientAdditionalInfoHandler from '#/mock/handlers/Model/ClientAdditionalInfoHandler'
import ClientCreditCardHandler from '#/mock/handlers/Model/ClientCreditCardHandler'
import ClientEmailDataHandler from '#/mock/handlers/Model/ClientEmailDataHandler'
import ClientHandler from '#/mock/handlers/Model/ClientHandler'
import ClientPassportHandler from '#/mock/handlers/Model/ClientPassportHandler'
import ClientPhoneDataHandler from '#/mock/handlers/Model/ClientPhoneDataHandler'
import CompanyContactHandler from '#/mock/handlers/Model/CompanyContactHandler'
import ConsolidatorAdditionalParserSettingsHandler from '#/mock/handlers/Model/ConsolidatorAdditionalParserSettingsHandler'
import ConsolidatorAreaHandler from '#/mock/handlers/Model/ConsolidatorAreaHandler'
import ConsolidatorHandler from '#/mock/handlers/Model/ConsolidatorHandler'
import ContactRuleHandler from '#/mock/handlers/Model/ContactRuleHandler'
import ElrFrtCheckHandler from '#/mock/handlers/Model/ElrFrtCheckHandler'
import EmailContactAttemptConversationHandler from '#/mock/handlers/Model/EmailContactAttemptConversationHandler'
import EmailContactAttemptHandler from '#/mock/handlers/Model/EmailContactAttemptHandler'
import EmailHandler from '#/mock/handlers/Model/EmailHandler'
import EmailTemplateHandler from '#/mock/handlers/Model/EmailTemplateHandler'
import ExpectedAmountHandler from '#/mock/handlers/Model/ExpectedAmountHandler'
import ExternalUserAgentHandler from '#/mock/handlers/Model/ExternalUserAgentHandler'
import ExternalUserAgentProxyHandler from '#/mock/handlers/Model/ExternalUserAgentProxyHandler'
import ExternalUserAgentQuickProfileLogHandler from '#/mock/handlers/Model/ExternalUserAgentQuickProfileLogHandler'
import FileHandler from '#/mock/handlers/Model/FileHandler'
import GamblingLotHandler from '#/mock/handlers/Model/GamblingLotHandler'
import IataHandler from '#/mock/handlers/Model/IataHandler'
import IncentiveSaleHandler from '#/mock/handlers/Model/IncentiveSaleHandler'
import IssueHandler from '#/mock/handlers/Model/IssueHandler'
import LeadAdditionalExpertInformationHandler from '#/mock/handlers/Model/LeadAdditionalExpertInformationHandler'
import LeadAdditionalManagementStatusHandler from '#/mock/handlers/Model/LeadAdditionalManagementStatusHandler'
import LeadAdditionalProfitsHandler from '#/mock/handlers/Model/LeadAdditionalProfitsHandler'
import LeadExpertProfitsHandler from '#/mock/handlers/Model/LeadExpertProfitsHandler'
import LeadHandler from '#/mock/handlers/Model/LeadHandler'
import LeaveRequestHandler from '#/mock/handlers/Model/LeaveRequestHandler'
import MarketingCostHandler from '#/mock/handlers/Model/MarketingCostHandler'
import MilePriceProgramHandler from '#/mock/handlers/Model/MilePriceProgramHandler'
import OfficeHandler from '#/mock/handlers/Model/OfficeHandler'
import PaymentGatewayHandler from '#/mock/handlers/Model/PaymentGatewayHandler'
import PerformanceFeedbackHandler from '#/mock/handlers/Model/PerformanceFeedbackHandler'
import PhoneHandler from '#/mock/handlers/Model/PhoneHandler'
import PnrScheduleHandler from '#/mock/handlers/Model/PnrScheduleHandler'
import PollHandler from '#/mock/handlers/Model/PollHandler'
import PriceDropCheckHandler from '#/mock/handlers/Model/PriceDropCheckHandler'
import PriceDropOfferHandler from '#/mock/handlers/Model/PriceDropOfferHandler'
import PriceQuoteHandler from '#/mock/handlers/Model/PriceQuoteHandler'
import ProductClientApproveHandler from '#/mock/handlers/Model/ProductClientApproveHandler'
import ProductHandler from '#/mock/handlers/Model/ProductHandler'
import ProjectCardHandler from '#/mock/handlers/Model/ProjectCardHandler'
import ReleasePostHandler from '#/mock/handlers/Model/ReleasePostHandler'
import ReleasePostItemFeedbackHandler from '#/mock/handlers/Model/ReleasePostItemFeedbackHandler'
import ReleasePostItemFeedbackReplyHandler from '#/mock/handlers/Model/ReleasePostItemFeedbackReplyHandler'
import ReleasePostItemHandler from '#/mock/handlers/Model/ReleasePostItemHandler'
import SaleClientRequestHandler from '#/mock/handlers/Model/SaleClientRequestHandler'
import SaleExtraProfitHandler from '#/mock/handlers/Model/SaleExtraProfitHandler'
import SaleHandler from '#/mock/handlers/Model/SaleHandler'
import SaleRequestHelpHandler from '#/mock/handlers/Model/SaleRequestHelpHandler'
import SaleTransactionHandler from '#/mock/handlers/Model/SaleTransactionHandler'
import SaleVersionCardHandler from '#/mock/handlers/Model/SaleVersionCardHandler'
import SaleVersionHandler from '#/mock/handlers/Model/SaleVersionHandler'
import SaleVersionPassengerHandler from '#/mock/handlers/Model/SaleVersionPassengerHandler'
import SaleVersionPassengerSegmentInfoHandler from '#/mock/handlers/Model/SaleVersionPassengerSegmentInfoHandler'
import SaleVersionPnrHandler from '#/mock/handlers/Model/SaleVersionPnrHandler'
import SaleVersionPnrInfoHandler from '#/mock/handlers/Model/SaleVersionPnrInfoHandler'
import ShiftHandler from '#/mock/handlers/Model/ShiftHandler'
import SignedDocumentHandler from '#/mock/handlers/Model/SignedDocumentHandler'
import SkillHandler from '#/mock/handlers/Model/SkillHandler'
import TaskGroupHandler from '#/mock/handlers/Model/TaskGroupHandler'
import TaskHandler from '#/mock/handlers/Model/TaskHandler'
import TeamHandler from '#/mock/handlers/Model/TeamHandler'
import TerminalHotkeyHandler from '#/mock/handlers/Model/TerminalHotkeyHandler'
import TicketHandler from '#/mock/handlers/Model/TicketHandler'
import TravelerHandler from '#/mock/handlers/Model/TravelerHandler'
import VoucherHandler from '#/mock/handlers/Model/VoucherHandler'

const handlers: { [TModelName in ModelName]?: MockModelHandler<TModelName> } = {
    AdditionalExpense: AdditionalExpenseHandler,
    AgentAdditionalSalaryInfo: AgentAdditionalSalaryInfoHandler,
    AgentAppSettings: AgentAppSettingsHandler,
    AgentClientTransfer: AgentClientTransferHandler,
    Agent: AgentHandler,
    AgentReportAdditional: AgentReportAdditionalHandler,
    AgentReport: AgentReportHandler,
    AgentReportInternalProfit: AgentReportInternalProfitHandler,
    AgentReportInvoice: AgentReportInvoiceHandler,
    AgentReportSale: AgentReportSaleHandler,
    AgentReportTicketing: AgentReportTicketingHandler,
    AgentShift: AgentShiftHandler,
    AgentSkill: AgentSkillHandler,
    AgentTarget: AgentTargetHandler,
    AirlineCase: AirlineCaseHandler,
    Airline: AirlineHandler,
    AirlineReport: AirlineReportHandler,
    AirlineReportVersion: AirlineReportVersionHandler,
    AudioTranscription: AudioTranscriptionHandler,
    AwardAccount: AwardAccountHandler,
    AwardAccountHolder: AwardAccountHolderHandler,
    AwardAccountIncomingRequest: AwardAccountIncomingRequestHandler,
    AwardAccountOutgoingRequest: AwardAccountOutgoingRequestHandler,
    BonusLead: BonusLeadHandler,
    BookkeepingInvoiceDocument: BookkeepingInvoiceDocumentHandler,
    BookkeepingInvoice: BookkeepingInvoiceHandler,
    BookkeepingTransactionAdditionalPayment: BookkeepingTransactionAdditionalPaymentHandler,
    BookkeepingTransactionAssignment: BookkeepingTransactionAssignmentHandler,
    BookkeepingTransaction: BookkeepingTransactionHandler,
    CallContactAttempt: CallContactAttemptHandler,
    Chat: ChatHandler,
    ChatMessage: ChatMessageHandler,
    CheckInReminder: CheckInReminderHandler,
    ClientAdditionalInfo: ClientAdditionalInfoHandler,
    ClientCreditCard: ClientCreditCardHandler,
    ClientEmailData: ClientEmailDataHandler,
    Client: ClientHandler,
    ClientPassport: ClientPassportHandler,
    ClientPhoneData: ClientPhoneDataHandler,
    CompanyContact: CompanyContactHandler,
    ConsolidatorAdditionalParserSettings: ConsolidatorAdditionalParserSettingsHandler,
    ConsolidatorArea: ConsolidatorAreaHandler,
    Consolidator: ConsolidatorHandler,
    ContactRule: ContactRuleHandler,
    ElrFrtCheck: ElrFrtCheckHandler,
    EmailContactAttemptConversation: EmailContactAttemptConversationHandler,
    EmailContactAttempt: EmailContactAttemptHandler,
    Email: EmailHandler,
    EmailTemplate: EmailTemplateHandler,
    ExpectedAmount: ExpectedAmountHandler,
    ExternalUserAgent: ExternalUserAgentHandler,
    ExternalUserAgentProxy: ExternalUserAgentProxyHandler,
    ExternalUserAgentQuickProfileLog: ExternalUserAgentQuickProfileLogHandler,
    File: FileHandler,
    GamblingLot: GamblingLotHandler,
    Iata: IataHandler,
    IncentiveSale: IncentiveSaleHandler,
    Issue: IssueHandler,
    LeadAdditionalExpertInformation: LeadAdditionalExpertInformationHandler,
    LeadAdditionalManagementStatus: LeadAdditionalManagementStatusHandler,
    LeadAdditionalProfits: LeadAdditionalProfitsHandler,
    LeadExpertProfits: LeadExpertProfitsHandler,
    Lead: LeadHandler,
    LeaveRequest: LeaveRequestHandler,
    MarketingCost: MarketingCostHandler,
    MilePriceProgram: MilePriceProgramHandler,
    Office: OfficeHandler,
    PaymentGateway: PaymentGatewayHandler,
    PerformanceFeedback: PerformanceFeedbackHandler,
    Phone: PhoneHandler,
    PnrSchedule: PnrScheduleHandler,
    Poll: PollHandler,
    PriceDropCheck: PriceDropCheckHandler,
    PriceDropOffer: PriceDropOfferHandler,
    PriceQuote: PriceQuoteHandler,
    ProductClientApprove: ProductClientApproveHandler,
    Product: ProductHandler,
    ProjectCard: ProjectCardHandler,
    ReleasePost: ReleasePostHandler,
    ReleasePostItemFeedback: ReleasePostItemFeedbackHandler,
    ReleasePostItemFeedbackReply: ReleasePostItemFeedbackReplyHandler,
    ReleasePostItem: ReleasePostItemHandler,
    SaleClientRequest: SaleClientRequestHandler,
    SaleExtraProfit: SaleExtraProfitHandler,
    Sale: SaleHandler,
    SaleRequestHelp: SaleRequestHelpHandler,
    SaleTransaction: SaleTransactionHandler,
    SaleVersionCard: SaleVersionCardHandler,
    SaleVersion: SaleVersionHandler,
    SaleVersionPassenger: SaleVersionPassengerHandler,
    SaleVersionPassengerSegmentInfo: SaleVersionPassengerSegmentInfoHandler,
    SaleVersionPnr: SaleVersionPnrHandler,
    SaleVersionPnrInfo: SaleVersionPnrInfoHandler,
    Shift: ShiftHandler,
    SignedDocument: SignedDocumentHandler,
    Skill: SkillHandler,
    TaskGroup: TaskGroupHandler,
    Task: TaskHandler,
    Team: TeamHandler,
    TerminalHotkey: TerminalHotkeyHandler,
    Ticket: TicketHandler,
    Traveler: TravelerHandler,
    Voucher: VoucherHandler,
}

export default handlers