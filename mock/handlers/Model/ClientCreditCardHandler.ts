import { findModelRecord, updateModelRecord, createModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'

const modelName = 'ClientCreditCard'

export default defineModelHandler<typeof modelName>({
    actions: {
        setFraud: async function({ pk, value, remark }) {
            const fraudPk = `${modelName}:${pk}`

            const fraudModel = await findModelRecord('FraudInfo', fraudPk)

            if (fraudModel) {
                await updateModelRecord('FraudInfo', fraudPk, {
                    is_fraud: value,
                    remark: remark,
                })
            } else {
                await createModelRecord('FraudInfo', {
                    model_name: modelName,
                    model_pk: pk,
                    is_fraud: value,
                    remark: remark,
                })
            }
        },
        checkIfFraud: async function({ card_number, expiration }) {
            return this.response(true)
        },
    },
})
