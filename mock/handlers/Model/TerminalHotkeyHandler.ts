import { defineModel<PERSON>and<PERSON> } from '~mock/utils/define'
import {
createModelRecord,
deleteModelRecord,
emitResourceHandlerEvent,
findModelRecordOrFail,
findModelRecords,
updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { PrimaryKey } from '@/types'

const modelName = 'TerminalHotkey'

export default defineModelHandler<typeof modelName>({
    actions: {
        createOrUpdate: async function({ data }) {
            for (let i = 0; i < data.length; i++) {
                const { pk, ...params } = data[i]

                if (pk) {
                    await updateModelRecord(modelName, pk, {
                        ...params,
                    })
                    await emitResourceHandlerEvent('TerminalHotkeyList', 'update', useMockAuth().pk)
                } else {
                    await createModelRecord(modelName, {
                        ...params,
                    })
                    await emitResourceHandlerEvent('TerminalHotkeyList', 'insert', useMockAuth().pk)
                }
            }
        },
        delete: async function({ pk }) {
            await deleteModelRecord(modelName, pk)
            await emitResourceHandlerEvent('TerminalHotkeyList', 'delete', useMockAuth().pk)
        },
    },
    searchFields: {

    },
})
