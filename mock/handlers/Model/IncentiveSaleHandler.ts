import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import {
    deleteModelRecord,
    findModelRecord,
    findModelRecordOrFail,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import ProductFactory from '~mock/factories/Product/ProductFactory'
import IncentiveSaleFactory from '~mock/factories/IncentiveSale/IncentiveSaleFactory'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'

const modelName = 'IncentiveSale'

export default defineModelHandler<typeof modelName>({
    actions: {
        create: async function({ sale_version_pk, product }) {
            const productForExpense = await new ProductFactory().create({
                ...product,
                created_at: dateToUnixTimestamp(new Date()),
                created_by_pk: useMockAuth().pk,
            })

            const incentive = await new IncentiveSaleFactory().create({
                sale_version_pk,
                product_pk: usePk(productForExpense),
            })

            return this.response({
                pk: usePk(incentive),
            })
        },
        update: async function({ pk, product }) {
            const expense = await findModelRecordOrFail('IncentiveSale', pk)

            if (!expense) {
                return
            }

            await updateModelRecord('Product', expense.product_pk, product)
        },
        delete: async function({ pk }) {
            await deleteModelRecord('IncentiveSale', pk)
        },
    },
    searchFields: {},
    observers: {
        afterDelete: async function(record) {
            await deleteModelRecord('Product', record.product_pk)
        },
    },
})
