import { defineModelHandler } from '~mock/utils/define'
import { createModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'

const modelName = 'PerformanceFeedback'

export default defineModelHandler<typeof modelName>({
    actions: {
        leaveFeedback: async function({ rating, remark }) {
            await createModelRecord('PerformanceFeedback', {
                rating: rating,
                remark: remark,
                created_at: dateToUnixTimestamp(new Date()),
                agent_pk: useMockAuth().pk,
            })
            //
        },
    },
})
