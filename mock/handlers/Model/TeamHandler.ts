import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { usePk } from '~/composables/usePk'
import {
    createModelRecord,
    deleteModelRecord,
    findModelRecords,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'

export default defineModelHandler<'Team'>({
    actions: {
        create: async function({ data }) {
            const item = await createModelRecord('Team', data)

            return this.response(item)
        },
        update: async function({ pk, name, department_pk, pnr_queue }) {
            const item = await updateModelRecord('Team', pk,  {
                name,
                department_pk,
                pnr_queue,
            })

            return this.response(item)
        },
        delete: async function({ pk }) {
            await deleteModelRecord('Team', pk)

            // @todo On delete set null/cascade relation

            const agents = await findModelRecords('Agent', {
                team_pk: pk,
            })

            for (const agent of agents) {
                await update<PERSON>odelR<PERSON>ord('Agent', usePk(agent), {
                    team_pk: null,
                })
            }
        },
    },
})
