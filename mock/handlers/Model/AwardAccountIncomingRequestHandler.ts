import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { recalculateAccountBalance } from '~mock/handlers/Model/AwardAccountHandler'

const modelName = 'AwardAccountIncomingRequest'

export default defineModelHandler<typeof modelName>({
    actions: {
        editRemark: async function({ pk, remark }) {
            await updateModelRecord('AwardAccountIncomingRequest', pk, { remark })
        },

        writeOff: async function({ pk, amount, remark }) {
            //
        },
    },

    observers: {
        afterCreate: async function(request) {
            await recalculateAccountBalance(request.award_account_pk)
        },

        afterUpdate: async function(request) {
            await recalculateAccountBalance(request.award_account_pk)
        },

        afterDelete: async function(request) {
            await recalculateAccountBalance(request.award_account_pk)
        },
    },
})
