import { deleteModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { dateToUnixTimestamp, randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { SaleClientRequestCategory, SaleClientRequestStatus } from '~/api/models/Client/SaleClientRequest'
import { randomFloat } from '~/lib/Helper/NumberHelper'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'

const modelName = 'SaleClientRequest'

export default defineModelHandler<typeof modelName>({
    actions: {
        getProtectionOfferPreview: async function({ sale_version_pk, subject, message, plan1, plan2, plan3, baggage_protection_price }) {
            return this.response({
                url: 'https://www.google.com/url?sa=i&url=https%3A%2F%2Ftheallever.fandom.com%2Fwiki%2FTHE_PREVIEW&psig=AOvVaw27COcMEX9y3_ya18PG2PSw&ust=1749122592084000&source=images&cd=vfe&opi=89978449&ved=0CBUQjRxqFwoTCMCk-8jT140DFQAAAAAdAAAAABAE',
            })
        },

        sendProtectionOffer: async function({ sale_version_pk, message, subject, email, plan1, plan2, plan3, baggage_protection_price, card_pks }) {
            makeModelRecord(modelName, {
                category: randomEnumValue(SaleClientRequestCategory),
                created_at: dateToUnixTimestamp(new Date()),
                created_by_pk: randomElementPk(await findModelRecords('Agent')),
                sale_version_pk: sale_version_pk,
                status: randomEnumValue(SaleClientRequestStatus),
                offered_price: randomFloat(200, 1000),
            })
        },

        getProtectionFormInfo: async function({ sale_version_pk }) {
            return this.response({
                plan1: randomFloat(200, 1000),
                plan2: randomFloat(200, 1000),
                plan3: randomFloat(200, 1000),
                baggage_protection_price: randomFloat(200, 1000),
            })
        },

        remove: async function({ pk }) {
            await deleteModelRecord('SaleClientRequest', pk)
        },
    },
})
