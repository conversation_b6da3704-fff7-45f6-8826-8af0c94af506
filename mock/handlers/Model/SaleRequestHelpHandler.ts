import { define<PERSON><PERSON>l<PERSON><PERSON><PERSON> } from '~mock/utils/define'
import SaleRequestHelpFactory from '~mock/factories/Sale/SaleRequestHelpFactory'
import { deleteModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'

const modelName = 'SaleRequestHelp'

export default defineModelHandler<typeof modelName>({
    actions: {
        createRequest: async function({ sale_pk, reason }) {
            await (new SaleRequestHelpFactory()).create({ sale_pk, reason })
        },
        takeRequest: async function({ request_pk }) {
            await deleteModelRecord('SaleRequestHelp', request_pk)
        },
    },
})
