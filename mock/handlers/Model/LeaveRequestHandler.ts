import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import type { ModelAttributes } from '~types/lib/Model'
import {
createModelRecord,
deleteModelRecord,
findModelRecord,
findModelRecordOrFail,
findModelRecords,
updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import LeaveRequestFactory from '~mock/factories/LeaveManagement/LeaveRequestFactory'
import { LeaveRequestStatus } from '~/api/models/LeaveManagement/LeaveRequest'
import { faker } from '@faker-js/faker'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'

export default defineModelHandler<'LeaveRequest'>({
    searchFields: {
        beneficiary_pk: async (record: ModelAttributes<'LeaveRequest'>) => {
            const agent = await findModelRecordOrFail('Agent', record.beneficiary_pk)

            return agent._pk
        },
        beneficiary_team_pk: async (record: ModelAttributes<'LeaveRequest'>) => {
            const agent = await findModelRecordOrFail('Agent', record.beneficiary_pk)

            if (agent.team_pk) {
                const team = await findModelRecord('Team', agent.team_pk)

                return String(team?.id)
            }

            return  undefined
        },
        beneficiary_department_pk: async (record: ModelAttributes<'LeaveRequest'>) => {
            const agent = await findModelRecordOrFail('Agent', record.beneficiary_pk)
            const department = await findModelRecordOrFail('Department', agent.department_pk)

            return String(department.id)
        },
        dates: async (record: ModelAttributes<'LeaveRequest'>) => {
            return record.dates
        },
        comment: async (record: ModelAttributes<'LeaveRequest'>) => {
            return record.comment
        },
        status: async (record: ModelAttributes<'LeaveRequest'>) => {
            return record.status
        },
    },
    actions: {
        create: async function(data) {
            const request = await new LeaveRequestFactory().create({
                ...data,
                status: LeaveRequestStatus.Pending,
                remark: null,
            })

            const agents = await findModelRecords('Agent')

            // mock simulation for uploading files
            for (const file of data.file_pks) {
                const fileExist = await findModelRecord('File', file)

                if (!fileExist) {
                    const name = faker.system.commonFileName('png')

                    await createModelRecord('File', {
                        id: Number(file),
                        name: name.split('.')[0],
                        extension: name.split('.')[1],
                        created_by: randomElementPk(agents),
                        created_at: Math.floor(Date.now() / 1000),
                        url: 'https://files.tmgbo.com/devStorage/bo/airline-report.xls',
                    })
                }
            }

            if (data.auto_approve) {
                await updateModelRecord('LeaveRequest', usePk(request), {
                    status: LeaveRequestStatus.Approved,
                    remark: null,
                })

                await createModelRecord('LeaveTransaction', {
                    beneficiary_pk: data.beneficiary_pk,
                    created_at: Date.now(),
                    request_pk: usePk(request),
                    amount: data.days_count,
                })
            }
        },
        update: async function({ pk, ...data }) {
            await updateModelRecord('LeaveRequest', pk, data)
        },
        delete: async function({ pk }) {
            await deleteModelRecord('LeaveRequest', pk)
        },
        changeStatus: async function({ pk, status, remark, dates, days_count }) {
            await updateModelRecord('LeaveRequest', pk, { status, remark, dates, days_count })
        },

        getSummaryChartData: async function({ dates }) {
            const now = new Date()
            const currentYear = now.getFullYear()

            let fromTs, toTs

            if (dates?.from != null && dates?.to != null) {
                fromTs =  dates.from * 1000
                toTs   =  dates.to * 1000
            } else {
                fromTs = new Date(Date.UTC(currentYear, 0, 1)).getTime()
                toTs   = new Date(Date.UTC(currentYear, 11, 31, 23, 59, 59, 999)).getTime()
            }

            const fromDate = new Date(fromTs)
            const toDate = new Date(toTs)

            const daysInMonth = (month: number, year: number) => {
                return new Date(year, month + 1, 0).getDate()
            }

            const monthlyBaseRanges = [
                [0, 5], [2, 8], [5, 15], [5, 18], [10, 20],
                [15, 25], [20, 35], [15, 30], [10, 20], [5, 15],
                [2, 10], [1, 8],
            ]

            const data = []

            let y = fromDate.getFullYear()
            let m = fromDate.getMonth()

            while (y < toDate.getFullYear() || (y === toDate.getFullYear() && m <= toDate.getMonth())) {
                const days = daysInMonth(m, y)
                const [min, max] = monthlyBaseRanges[m]
                const monthData = []

                for (let d = 1; d <= days; d++) {
                    const ts = new Date(Date.UTC(y, m, d)).getTime()

                    if (ts >= fromTs && ts <= toTs) {
                        const base      = Math.floor(Math.random() * (max - min + 1)) + min
                        const variation = Math.floor(Math.random() * 5) - 2
                        monthData.push(Math.max(0, base + variation))
                    }
                }

                if (monthData.length) {
                    data.push(monthData)
                }
                m++

                if (m > 11) {
                    m = 0
                    y++
                }
            }

            return this.response({
                data: data,
            })
        },
        getHolidays: async function({}) {
            return this.response({
                data: [
                    // 1704067200, // 2024-01-01 – Новый год
                    // 1704585600, // 2024-01-07 – Рождество (по юлианскому календарю)
                    // 1704672000, // 2024-01-08 – Рождественский выходной
                    // 1709856000, // 2024-03-08 – Международный женский день
                    // 1714867200, // 2024-05-05 – Пасха (православная)
                    // 1714953600, // 2024-05-06 – Пасхальный понедельник
                    // 1715212800, // 2024-05-09 – День Победы
                    // 1714521600, // 2024-05-01 – День труда
                    // 1717200000, // 2024-06-01 – День защиты детей
                    // 1724716800, // 2024-08-27 – День независимости
                    // 1725043200, // 2024-08-31 – День национального языка
                    // 1735084800, // 2024-12-25 – Рождество (по григорианскому календарю)
                    // 1735689600, // 2024-12-31 – Канун Нового года
                    1735689600, // 2025-01-01 – Новый год
                    1736208000, // 2025-01-07 – Рождество (по юлианскому календарю)
                    1736294400, // 2025-01-08 – Рождественский выходной
                    1741372800, // 2025-03-08 – Международный женский день
                    1745107200, // 2025-04-20 – Пасха (православная)
                    1745193600, // 2025-04-21 – Пасхальный понедельник
                    1745884800, // 2025-05-01 – День труда
                    1746576000, // 2025-05-09 – День Победы
                    1749331200, // 2025-06-01 – День защиты детей
                    1756252800, // 2025-08-27 – День независимости
                    1756579200, // 2025-08-31 – День национального языка
                    1766601600,  // 2025-12-25 – Рождество (по григорианскому календарю)
                ],
            })
        },
    },
    observers: {
        afterUpdate: async function(record) {
            await createModelRecord('LeaveTransaction', {
                beneficiary_pk: record.beneficiary_pk,
                created_at: Date.now(),
                request_pk: record._pk,
                amount: record.days_count,
            })
        },
    },
})
