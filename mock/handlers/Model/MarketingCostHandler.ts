import { define<PERSON>odelHand<PERSON> } from '~mock/utils/define'
import { updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import MarketingCostFactory from '~mock/factories/Marketing/MarketingCostFactory'

const modelName = 'MarketingCost'

export default defineModelHandler<typeof modelName>({
    actions: {
        create: async function({ year, month, value }) {
            await (new MarketingCostFactory()).create({
                year,
                month,
                value,
            })
        },
        update: async function({ pk, value }) {
            await updateModelRecord(modelName, pk, { value })
        },
    },
})
