import { useEventQueued } from '~mock/composables/useEvent'
import type { PublicationContext } from '~/service/WebSocketEventService/WebSocketEventServiceInterface'
import type { AvailabilityTableRow, Delta, WPNCAllTableRow } from '#/modules/gds-terminal/src/helpers/DeltaHelper'
import { WPNIAllTableRowType } from '#/modules/gds-terminal/src/lib/delta/WPNIAllDelta/WPNIAllStateDelta'
import { wait } from '~/lib/Helper/PromiseHelper'
import { ensureArray } from '~/lib/Helper/ArrayHelper'
import  { FlightHackType }  from '#/modules/gds-terminal/src/lib/delta/FlightHacksDelta/FlightHackRowDelta'

type Output = {
    type: 'output',
    processId: string,
    deltas: Delta[],
    final?: boolean,
    unsubscribe?: boolean,
}

type Command = {
    type: 'command',
    processId: string,
    payload: {
        command: string,
    },
}

export default defineEventHandler(async (event) => {
    const {
        channel,
        data,
    } = await readBody(event) as PublicationContext

    useEventQueued(`${channel}`, 'publish', data)

    await wait(200)

    if (channel === 'gds-terminal-channel') {
        if (data.type === 'command') {
            const event = data as Command

            const command = event.payload.command

            const handler = commandHandlers.find(({ matcher }) => matcher(command))

            let outputs: Output[] | undefined

            if (handler) {
                const context = {
                    makeOutput(deltas: Delta[], options: {
                        final?: boolean,
                        unsubscribe?: boolean
                    }) {
                        return {
                            type: 'output',
                            processId: event.processId,
                            deltas,
                            ...options,
                        }
                    },
                    makeError(deltas: Delta[]) {
                        return {
                            type: 'error',
                            processId: event.processId,
                            deltas,
                        }
                    },
                }

                outputs = ensureArray(await handler.handler.bind(context)(command, channel, event.processId))
            } else {
                outputs = [{
                    type: 'output',
                    processId: event.processId,
                    deltas: [{
                        type: 'text',
                        raw: `Command "${command}" not found`,
                    }],
                    final: true,
                    unsubscribe: true,
                }]
            }

            for (const output of outputs) {
                useEventQueued(channel, 'output', output)
            }

            if (command.toLowerCase() === 'expire') {
                useEventQueued(channel, 'sessionExpired', {
                    type: 'sessionExpired',
                })
            }
        }
    }

    return {
        ok: true,
    }
})

const commandHandlers = [
    {
        matcher: (command: string) => command.toLowerCase() === 'colored',
        handler(command: string) {
            const deltas: Delta[] = []

            deltas.push({
                type: 'text',
                raw: 'red',
                payload: { color: 'red' },
            })
            deltas.push({
                type: 'text',
                raw: ' and ',
            })
            deltas.push({
                type: 'text',
                raw: 'green',
                payload: { color: 'green' },
            })
            deltas.push({
                type: 'text',
                raw: ' and ',
            })
            deltas.push({
                type: 'text',
                raw: 'yellow',
                payload: { color: 'yellow' },
            })
            deltas.push({
                type: 'text',
                raw: ' and ',
            })
            deltas.push({
                type: 'text',
                raw: 'blue\n',
                payload: { color: 'blue' },
            })

            return this.makeOutput(deltas, {
                final: true,
                unsubscribe: true,
            })
        },
    },
    {
        matcher: (command: string) => command.toLowerCase() === 'typography',
        handler(command: string) {
            const deltas: Delta[] = []

            deltas.push({
                type: 'text',
                raw: ' bold\n',
                payload: { bold: true },
            })

            return this.makeOutput(deltas, {
                final: true,
                unsubscribe: true,
            })
        },
    },
    {
        matcher: (command: string) => ['wpncall', 'wpncallh'].includes(command.toLowerCase()),
        handler(command: string, channel: string) {
            const pccs = [
                'PCC1',
                'PCC2',
                'PCC3',
                command.toLowerCase() == 'wpncallh' ? 'FLIGHT HACK' : undefined,
            ].filter(Boolean)

            const rows: WPNCAllTableRow[] = pccs.map(pcc => ({
                gds: 'Sabre',
                pcc,
            }));

            (async () => {
                for (let i = 0; i < rows.length; i++) {
                    const row = rows[i]

                    await wait(200)

                    if (row.pcc === 'FLIGHT HACK') {
                        useEventQueued(channel, 'output', this.makeOutput([{
                            type: 'flightHackState',
                            raw: '',
                            payload: {
                                source: row.pcc,
                                gds: 'SABER',
                                loading: true,
                            },
                            id: `state-${row.pcc}`,
                        }], {
                            final: true,
                            unsubscribe: false,
                        }))
                    } else {
                        row.priceAdt = {
                            amount: 100,
                            currency: 'USD',
                        }

                        if (i === 0) {
                            row.priceChd = {
                                amount: 75,
                                currency: 'USD',
                            }
                        }

                        if (i === 1) {
                            row.priceAdt = {
                                amount: 200,
                                currency: 'USD',
                            }

                            row.priceInf = {
                                amount: 0,
                                currency: 'USD',
                            }
                        }

                        if (i === 2) {
                            row.error = 'Long error message that can take multiple lines and surely should be truncated to fit the screen'
                        }

                        row.ptc = 'ADT'
                        row.fareType = 'PUB'
                        row.baggage = '1PC'

                        const deltas: Delta[] = [{
                            id: row.pcc,
                            type: 'wpncAllTableRow',
                            raw: `${row.gds} ${row.pcc}\n`,
                            payload: row,
                        }]
                        useEventQueued(channel, 'output', this.makeOutput(deltas, {}))
                    }
                }

                if (command.toLowerCase() == 'wpncallh') {
                    await wait(2000)

                    const segment = ['EWR', '2025-03-20T23:30:00Z', 'LHR', '2025-03-21T05:40:00Z', '934', '1', 'K', '763', 'LH', '7818', 'UA', 'Y']

                    useEventQueued(channel, 'output', this.makeOutput([{
                        type: 'flightHackState',
                        raw: '',
                        payload: {
                            source: 'FLIGHT HACK',
                            gds: 'SABER',
                            loading: false,
                        },
                        id: `state-FLIGHT HACK`,
                    }], {}))

                    useEventQueued(channel, 'output', this.makeOutput([{
                        type: 'flightHackTableRow',
                        raw: '',
                        payload: {
                            source: 'FLIGHT HACK',
                            segments: [segment, segment],
                            optionNumber: 1,
                            flownMiles: 1200,
                            validatingCarrier: 'AF',
                            stopsCount: 3,
                            flightDuration: {
                                flightTime: 29000,
                                waitingTime: 1000,
                                totalTime: 30000,
                            },
                            price: {
                                amount: 200,
                                currency: 'USD',
                            },
                            flightHackType: FlightHackType.ELR,
                        },
                        id: '',
                    }], {}))
                }

                useEventQueued(channel, 'output', this.makeOutput([], {
                    final: true,
                    unsubscribe: true,
                }))
            })()

            return this.makeOutput(rows.filter(row => row.pcc != 'FLIGHT HACK').map(row => ({
                id: row.pcc,
                type: 'wpncAllTableRow',
                raw: `${row.gds} ${row.pcc}\n`,
                payload: row,
            })), { final: true })
        },
    },
    {
        matcher: (command: string) => ['wpniall', 'wpniallx', 'wpniallh'].includes(command.toLowerCase()),
        handler(command: string, channel: string) {
            (async () => {
                const segment = ['EWR', '2025-03-20T23:30:00Z', 'LHR', '2025-03-21T05:40:00Z', '934', '1', 'K', '763', 'LH', '7818', 'UA', 'Y']

                const sources = [
                    'PCC1',
                    'PCC2',
                    'PCC3',
                    'AIRCANADA',
                    'FLIGHT HACK',
                ]

                sources.forEach((source) => {
                    if (source === 'FLIGHT HACK') {
                        useEventQueued(channel, 'output', this.makeOutput([{
                            type: 'flightHackState',
                            raw: '',
                            payload: {
                                source,
                                gds: 'SABER',
                                loading: true,
                            },
                            id: `state-${source}`,
                        }], {
                            final: true,
                            unsubscribe: false,
                        }))
                    } else {
                        useEventQueued(channel, 'output', this.makeOutput([{
                            type: 'wpniAllState',
                            raw: '',
                            payload: {
                                source,
                                gds: 'SABER',
                                loading: true,
                                type: source === 'AIRCANADA' ? WPNIAllTableRowType.Award : WPNIAllTableRowType.Gds,
                            },
                            id: `state-${source}`,
                        }], {
                            final: true,
                            unsubscribe: false,
                        }))
                    }
                })

                await wait(2000)

                sources.forEach((source, index) => {
                    if (source === 'AIRCANADA') {
                        useEventQueued(channel, 'output', this.makeOutput([{
                            type: 'wpniAllTableRow',
                            raw: '',
                            payload: {
                                source: source,
                                segments: [segment, segment],
                                optionNumber: 1,
                                flownMiles: 1200,
                                validatingCarrier: 'AF',
                                stopsCount: index,
                                flightDuration: {
                                    flightTime: 29000,
                                    waitingTime: 1000,
                                    totalTime: 30000,
                                },
                                price: {
                                    amount: 200,
                                    currency: 'USD',
                                },
                                priceAward: {
                                    amount: 300,
                                    currency: 'MILES',
                                },
                                type: WPNIAllTableRowType.Award,
                            },
                            id: '',
                        }], {}))
                    } else if (source === 'FLIGHT HACK') {
                        useEventQueued(channel, 'output', this.makeOutput([{
                            type: 'flightHackTableRow',
                            raw: '',
                            payload: {
                                source: source,
                                segments: [segment, segment],
                                optionNumber: 1,
                                flownMiles: 1200,
                                validatingCarrier: 'AF',
                                stopsCount: index,
                                flightDuration: {
                                    flightTime: 29000,
                                    waitingTime: 1000,
                                    totalTime: 30000,
                                },
                                price: {
                                    amount: 200,
                                    currency: 'USD',
                                },
                                flightHackType: FlightHackType.ELR,
                            },
                            id: '',
                        }], {}))
                    } else {
                        useEventQueued(channel, 'output', this.makeOutput([{
                            type: 'wpniAllTableRow',
                            raw: '',
                            payload: {
                                source,
                                segments: [segment, segment],
                                optionNumber: 1,
                                flownMiles: 1200,
                                validatingCarrier: 'AF',
                                stopsCount: index,
                                flightDuration: {
                                    flightTime: 29000,
                                    waitingTime: 1000,
                                    totalTime: 30000,
                                },
                                price: {
                                    amount: 200,
                                    currency: 'USD',
                                },
                                type: WPNIAllTableRowType.Gds,
                            },
                            id: '',
                        }], {}))
                    }

                    if (source === 'FLIGHT HACK') {
                        useEventQueued(channel, 'output', this.makeOutput([{
                            type: 'flightHackState',
                            raw: '',
                            payload: {
                                source,
                                gds: 'SABER',
                                loading: false,
                            },
                            id: `state-${source}`,
                        }], {}))
                    } else {
                        useEventQueued(channel, 'output', this.makeOutput([{
                            type: 'wpniAllState',
                            raw: '',
                            payload: {
                                source,
                                gds: 'SABER',
                                loading: false,
                                type: source === 'AIRCANADA' ? WPNIAllTableRowType.Award : WPNIAllTableRowType.Gds,
                            },
                            id: `state-${source}`,
                        }], {}))
                    }
                })

                useEventQueued(channel, 'output', this.makeOutput([], {
                    final: true,
                    unsubscribe: true,
                }))
            })()

            return []
        },
    },
    {
        matcher: (command: string) => command.toLowerCase() === 'broken',
        handler() {
            return this.makeOutput([{
                type: 'text',
                raw: 'This command does not have a newline character at the end',
            }], {
                final: true,
                unsubscribe: true,
            })
        },
    },
    // Long execution command
    {
        matcher: (command: string) => command.toLowerCase() === 'wait',
        async handler() {
            await wait(20000)

            return this.makeOutput([{
                type: 'text',
                raw: 'This command took 20 seconds to execute\n',
            }], {
                final: true,
                unsubscribe: true,
            })
        },
    },
    {
        matcher: (command: string) => command.toLowerCase().split(' ')[0] === 'long',
        async handler(command) {
            const args = command.split(' ')

            const numberOfLines = parseInt(args[1], 10) || 50

            return this.makeOutput(
                Array.from({ length: numberOfLines }, (_, i) => {
                    return {
                        type: 'text',
                        raw: `Line ${i + 1}\n`,
                    }
                }),
                {
                    final: true,
                    unsubscribe: true,
                },
            )
        },
    },
    {
        matcher: (input: string) => input.toLowerCase() === 'error',
        handler(input: string) {
            return this.makeError([{
                type: 'error',
                raw: `Error message for command "${input}"\n`,
            }])
        },
    },
    {
        matcher: (input: string) => input.toLowerCase() === 'availability',
        handler(input: string) {
            const rows: AvailabilityTableRow[] = [
                {
                    dl: 1,
                    flightNumber: 2345,
                    bookingCodes: ['J9', 'C9', '09', '19', '29', '89', 'S9', 'Y9', '89', 'M9', 'H9', '09', 'K9', '19', '09', '19', 'X9', 'V9', 'E9'],
                    departureAirport: 'FLL',
                    arrivalAirport: 'ATL',
                    departureTime: '6:10 PM',
                    arrivalTime: '8:24 PM',
                    aircraftType: '757',
                    flightDuration: '2H 14MIN',
                    carrier: 'AZ',
                    departureDateAdjustment: '+4',
                    arrivalDateAdjustment: '+2',
                    isConnection: false,
                },
                {
                    dl: 2,
                    flightNumber: 218,
                    bookingCodes: ['J9', 'C9', '09', '19', '29', 'P9', 'A9', 'G9', 'W9', 'S9', '19', '89', 'M9', 'H9', '09', 'K9', '19', '19', '19', 'X9', 'V9', 'E9'],
                    departureAirport: 'CPT',
                    arrivalAirport: 'CPT',
                    departureTime: '9:45 PM',
                    arrivalTime: '7:15 PM',
                    aircraftType: '359',
                    flightDuration: '14H 30MIN',
                    carrier: 'NH',
                    departureDateAdjustment: '+3',
                    arrivalDateAdjustment: '+1',
                    isConnection: true,
                },
                {
                    dl: 3,
                    flightNumber: 1413,
                    bookingCodes: ['J9', 'C9', '09', '19', '29', 'W9', 'S0', 'Y0', '80', 'M0', 'H9', '09', 'K9', '19', '09', '10', 'X0', 'V9', 'E9'],
                    departureAirport: 'FLL',
                    arrivalAirport: 'ATL',
                    departureTime: '4:38 PM',
                    arrivalTime: '6:40 PM',
                    aircraftType: '757',
                    flightDuration: '2H 10MIN',
                    carrier: 'LX',
                    departureDateAdjustment: '+2',
                    arrivalDateAdjustment: '+5',
                    isConnection: false,
                },
                {
                    dl: 4,
                    flightNumber: 210,
                    bookingCodes: ['J9', 'C9', '09', '19', '29', 'P9', 'A9', 'G9', 'W9', '59', '19', '89', 'M9', 'H9', '09', 'K9', '19', '19', '19', 'X9', 'VO', 'EQ'],
                    departureAirport: 'CPT',
                    arrivalAirport: 'CPT',
                    departureTime: '9:45 PM',
                    arrivalTime: '7:15 PM',
                    aircraftType: '359',
                    flightDuration: '14H 30MIN',
                    carrier: 'CI',
                    departureDateAdjustment: '+1',
                    arrivalDateAdjustment: '+6',
                    isConnection: true,
                },
                {
                    dl: 5,
                    flightNumber: 1554,
                    bookingCodes: ['J9', 'C9', 'D9', '19', '29', 'W9', 'S9', 'Y9', '89', 'M9', 'H9', '09', 'K9', '19', '09', 'T9', 'X9', 'V9', 'E9'],
                    departureAirport: 'FLL',
                    arrivalAirport: 'ATL',
                    departureTime: '3:28 PM',
                    arrivalTime: '5:34 PM',
                    aircraftType: '757',
                    flightDuration: '2H 14MIN',
                    carrier: 'AZ',
                    departureDateAdjustment: '+5',
                    arrivalDateAdjustment: '+3',
                    isConnection: false,
                },
                {
                    dl: 6,
                    flightNumber: 210,
                    bookingCodes: ['J9', 'C9', '09', '19', '29', 'P9', 'A9', 'G9', 'W9', '59', '19', '89', 'M9', 'H9', '09', 'K9', '19', '19', '19', 'X9', 'V9', 'E9'],
                    departureAirport: 'CPT',
                    arrivalAirport: 'CPT',
                    departureTime: '9:45 PM',
                    arrivalTime: '7:15 PM',
                    aircraftType: '359',
                    flightDuration: '14H 30MIN',
                    carrier: 'NH',
                    departureDateAdjustment: '+3',
                    arrivalDateAdjustment: '+4',
                    isConnection: true,
                },
                {
                    dl: 7,
                    flightNumber: 1512,
                    bookingCodes: ['J9', 'C9', '09', '19', '29', 'W9', '89', '19', '89', 'M9', 'H9', '09', 'K9', '19', '09', 'T9', 'X9', 'V9', 'E9'],
                    departureAirport: 'FLL',
                    arrivalAirport: 'ATL',
                    departureTime: '1:34 PM',
                    arrivalTime: '3:38 PM',
                    aircraftType: '757',
                    flightDuration: '2H 04MIN',
                    carrier: 'LX',
                    departureDateAdjustment: '+1',
                    arrivalDateAdjustment: '+2',
                    isConnection: false,
                },
                {
                    dl: 8,
                    flightNumber: 210,
                    bookingCodes: ['J9', 'C9', '09', '19', '29', 'P9', 'A9', 'G9', 'WO', 'S9', 'Y9', '89', 'M9', 'H9', '09', 'K9', '19', '19', '19', 'X9', 'V9', 'EQ'],
                    departureAirport: 'CPT',
                    arrivalAirport: 'CPT',
                    departureTime: '9:45 PM',
                    arrivalTime: '7:15 PM',
                    aircraftType: '359',
                    flightDuration: '14H 30MIN',
                    carrier: 'CI',
                    departureDateAdjustment: '+6',
                    arrivalDateAdjustment: '+1',
                    isConnection: true,
                },
            ]

            return this.makeOutput([
                {
                    type: 'text',
                    raw: ' 20AUG  WED  NYC/EDT\n',
                },
                ...rows.map(row => ({
                    type: 'availabilityTableRow',
                    raw: `qweqw\n`,
                    payload: row,
                })),
            ], { final: true })
        },
    },
    {
        matcher: (command: string) => ['wpnc', 'wpnch'].includes(command.toLowerCase()),
        async handler(command: string, channel: string) {
            const deltas: Delta[] = []

            const source = 'FLIGHT HACK'

            useEventQueued(channel, 'output', this.makeOutput([{
                type: 'createPqButton',
                raw: 'Create PQ\n',
                payload: {
                    totalPrice: {
                        amount: 11,
                        currency: 'USD',
                    },
                    commissionValue: {
                        amount: 11,
                        currency: 'USD',
                    },
                    baseFarePrice: {
                        amount: 1,
                        currency: 'USD',
                    },
                    validatingCarrier: 'AA',
                },
            }], {}))

            useEventQueued(channel, 'output', this.makeOutput([{
                type: 'flightHackState',
                raw: '',
                payload: {
                    source,
                    gds: 'SABER',
                    loading: true,
                },
                id: `state-${source}`,
            }], {}))

            const segment = ['EWR', '2025-03-20T23:30:00Z', 'LHR', '2025-03-21T05:40:00Z', '934', '1', 'K', '763', 'LH', '7818', 'UA', 'Y']

            useEventQueued(channel, 'output', this.makeOutput([
                {
                    type: 'flightHackTableRow',
                    raw: '',
                    payload: {
                        source: source,
                        segments: [segment, segment],
                        optionNumber: 1,
                        flownMiles: 1200,
                        validatingCarrier: 'AF',
                        stopsCount: 1,
                        flightDuration: {
                            flightTime: 29000,
                            waitingTime: 1000,
                            totalTime: 30000,
                        },
                        price: {
                            amount: 200,
                            currency: 'USD',
                        },
                        flightHackType: FlightHackType.ELR,
                    },
                    id: '',
                }], {}))

            await wait(5000)

            useEventQueued(channel, 'output', this.makeOutput([{
                type: 'flightHackState',
                raw: '',
                payload: {
                    source,
                    gds: 'SABER',
                    loading: false,
                },
                id: `state-${source}`,
            }], {}))

            return this.makeOutput(deltas, {
                final: true,
                unsubscribe: true,
            })
        },
    },
    {
        matcher: () => true,
        handler(command: string) {
            return this.makeOutput([{
                type: 'text',
                raw: `You typed "${command}"\n`,
            }], {
                final: true,
                unsubscribe: true,
            })
        },
    },
] satisfies Array<{
    matcher: (command: string) => boolean,
    handler: (this: {
        makeOutput(delta: Delta[], options: {
            final?: boolean,
            unsubscribe?: boolean
        }): Output,
        makeError(delta: Delta): Output,
    }, command: string, channel: string) => Promise<Output[]> | Output[],
}>
