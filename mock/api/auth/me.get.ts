import type { Model } from '~types/lib/Model'
import type { WorkspaceInfo } from '~/service/WorkspaceService'
import { MockProjectID } from '~mock/seeds/models/Project/ProjectSeeder'

export default defineAuthenticatedEventHandler(async (request, user) => {
    return {
        abilities: getAbilities(user),
        workspaces: getWorkspacesInfo(user),
        _oldData: oldUserInfo(user), // @deprecated
    }
})

const getWorkspacePermissions = (user: Model.Attributes<'Agent'>) => {
    const workspaces = ['ANY']

    if (user.email.includes('tbc')) {
        workspaces.push('TBC')
    } else if (user.email.includes('bcf')) {
        workspaces.push('BCF')
    } else if (user.email.includes('bcs')) {
        workspaces.push('BCS')
    } else if (user.email.includes('tmg')) {
        workspaces.push('TBC')
        workspaces.push('BCF')
        workspaces.push('BCS')
    } else {
        workspaces.push('TBC')
    }

    return {
        'abilityWorkspace': {
            '$in': workspaces,
        },
    }
}

const getAbilities = (user: Model.Attributes<'Agent'>) => [
    [
        'manage',
        'all',
        {
            ...getWorkspacePermissions(user),
        },
    ],
    [
        'openPageLead',
        'all',
        {
            ...getWorkspacePermissions(user),
        },
    ],
]

const getWorkspacesInfo = (user: Model.Attributes<'Agent'>) => {
    const tmgAccountPk = '3'

    const tbc: WorkspaceInfo = {
        pk: String(MockProjectID.TBC),
        title: 'TBC',
        account_pk: user.email.includes('tmg') ? tmgAccountPk : usePk(user),
        is_default: true,
        project_pks: [String(MockProjectID.TBC), String(MockProjectID.ABT)],
    }

    const bcf: WorkspaceInfo = {
        pk: String(MockProjectID.BCF),
        title: 'BCF',
        account_pk: user.email.includes('tmg') ? tmgAccountPk : usePk(user),
        is_default: false,
        project_pks: [String(MockProjectID.BCF), String(MockProjectID.ABF)],
    }

    const bcs: WorkspaceInfo = {
        pk: String(MockProjectID.BCS),
        title: 'BCS',
        account_pk: user.email.includes('tmg') ? tmgAccountPk : usePk(user),
        is_default: false,
        project_pks: [String(MockProjectID.BCS)],
    }

    const result = []

    if (user.email.includes('tbc')) {
        result.push(tbc)
    } else if (user.email.includes('bcf')) {
        result.push(bcf)
    } else if (user.email.includes('bcs')) {
        result.push(bcs)
    } else if (user.email.includes('tmg')) {
        result.push(tbc)
        result.push(bcf)
        result.push(bcs)
    }

    return result
}

const oldUserInfo = (user: Model.Attributes<'Agent'>) => {
    const TBC = {
        'id': MockProjectID.TBC,
        'project_name': 'Travel Business Class',
        'project_abbr': 'TBC',
        'account': 1,
        'list': [
            MockProjectID.TBC,
            MockProjectID.ABT,
        ],
        'projects': [
            {
                'id': MockProjectID.TBC,
                'project_abbr': 'TBC',
                'project_name': 'Travel Business Class',
                'project_host': 'travelbusinessclass.com',
                'is_main': 1,
                'enabledTrustPilot': 1,
            },
            {
                'id': MockProjectID.ABT,
                'project_abbr': 'ABT',
                'project_name': 'Air Business Travel',
                'project_host': 'airbusinesstravel.com',
                'is_main': 0,
                'enabledTrustPilot': 0,
            },
        ],
        'is_default': 1,
    }

    const BCF = {
        'id': MockProjectID.BCF,
        'project_name': 'Business Class Flights',
        'project_abbr': 'BCF',
        'account': 51,
        'list': [
            MockProjectID.BCF,
            MockProjectID.ABF,
        ],
        'projects': [
            {
                'id': MockProjectID.BCF,
                'project_abbr': 'BCF',
                'project_name': 'Business Class Flights',
                'project_host': 'businessclassflights.com',
                'is_main': 1,
                'enabledTrustPilot': 1,
            },
            {
                'id': MockProjectID.ABF,
                'project_abbr': 'ABF',
                'project_name': 'Air Business Flights',
                'project_host': 'airbusinessflights.com',
                'is_main': 0,
                'enabledTrustPilot': 0,
            },
        ],
        'is_default': 0,
    }

    const BCS = {
        'id': 3,
        'project_name': 'BCFlights',
        'project_abbr': 'BCS',
        'account': 3,
        'list': [
            3,
        ],
        'projects': [
            {
                'id': 3,
                'project_abbr': 'BCS',
                'project_name': 'BCFlights',
                'project_host': 'bcflights.com',
                'is_main': 1,
                'enabledTrustPilot': 0,
            },
            {
                'id': MockProjectID.LFS,
                'project_abbr': 'LFS',
                'project_name': 'Lux-Flights',
                'project_host': 'lux-flights.com',
                'is_main': 0,
                'enabledTrustPilot': 0,
            },
        ],
        'is_default': 0,
    }

    const workspaces: any = {
        'TBC': TBC,
        'BCF': BCF,
        'BCS': BCS,
    }

    return {
        'id': user.id,
        'avatar': 'https://staging-bo.tmgbo.com/storage/avatar/27ecb03f-381d-48e4-831c-18ba1ae040dd_online.png',
        'fio': user.first_name + ' ' + user.last_name,
        'sex': null,
        'department': {
            'id': 10,
            'name': 'IT',
        },
        'position': {
            'id': 3,
            'name': 'Manager',
        },
        'team': {
            'id': 10,
            'name': 'Sale Team',
        },
        'workspaces': workspaces,
        'permissions': getAbilities(user),
        'modifiedAt': 1681123617,
        'ringcentral_id': 317013004,
        'version': 2,
    }
}
