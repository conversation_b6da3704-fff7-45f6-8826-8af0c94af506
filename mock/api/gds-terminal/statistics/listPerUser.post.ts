import { findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { faker } from '@faker-js/faker'

export default defineEventHandler(async () => {
    const agents = await findModelRecords('Agent')

    return Array.from({ length: faker.number.int({ min: 10, max: 30 }) }, (_, i) => ({
        agent_pk: randomElementPk(agents),
        command_count: faker.number.int({ min: 10, max: 100 }),
        custom_command_count: faker.number.int({ min: 10, max: 100 }),
        wpniall_count: faker.number.int({ min: 10, max: 100 }),
        wpniallx_count: faker.number.int({ min: 10, max: 100 }),
        wpniallh_count: faker.number.int({ min: 10, max: 100 }),
        wpncall_count: faker.number.int({ min: 10, max: 100 }),
        wpncallh_count: faker.number.int({ min: 10, max: 100 }),
        wpnch_count: faker.number.int({ min: 10, max: 100 }),
        pnr_count: faker.number.int({ min: 10, max: 100 }),
        pq_count: faker.number.int({ min: 10, max: 100 }),
    }))
})
/*
{
            agent_name: string,
            command_count: number,
            custom_command_count: number,
            wpniAll_count: number,
            wpniAllX_count: number,
            wpncAll_count: number,
            pnr_count: number,
            pq_count: number,
        }
 */
