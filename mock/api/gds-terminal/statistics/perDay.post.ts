import { faker } from '@faker-js/faker'

export default defineEventHandler(async (request) => {
    const payload = (await readBody(request)) as {
        from: number // timestamp (в секундах)
        to: number   // timestamp (в секундах)
        utc_offset: number
    }

    const result = []

    const offsetMs = payload.utc_offset * 1000

    const fromDate = new Date(payload.from * 1000)
    const toDate = new Date(payload.to * 1000 + offsetMs)

    const msPerDay = 1000 * 60 * 60 * 24
    const diffInDays = Math.ceil((toDate.getTime() - fromDate.getTime()) / msPerDay)

    for (let i = 0; i < diffInDays; i++) {
        const date = new Date(fromDate)
        date.setDate(fromDate.getDate() + i)

        const total = faker.number.int({ min: 10, max: 100 })

        result.push({
            day_at: Math.floor(date.getTime() / 1000), // UNIX timestamp
            total_count: total,
            tooltip: [
                faker.hacker.phrase(),
                faker.company.catchPhrase(),
                faker.lorem.sentence(),
            ],
        })
    }

    return result

    // return [
    //         {
    //             day_at: faker.date.recent().getTime(),
    //             total_count: 20,
    //             custom_count: 5,
    //             tooltip: 'Start of tracking',
    //         },
    //         {
    //             day_at: faker.date.recent().getTime(),
    //             total_count: 35,
    //             custom_count: 8,
    //             tooltip: 'Good growth',
    //         },
    //         {
    //             day_at: 1719266400,
    //             total_count: 40,
    //             custom_count: 10,
    //             tooltip: 'Improved performance',
    //         },
    //         {
    //             day_at: 1719352800,
    //             total_count: 30,
    //             custom_count: 6,
    //             tooltip: 'Minor drop',
    //         },
    //         {
    //             day_at: 1719439200,
    //             total_count: 50,
    //             custom_count: 15,
    //             tooltip: 'Best day so far',
    //         },
    //         {
    //             day_at: 1719525600,
    //             total_count: 45,
    //             custom_count: 12,
    //             tooltip: 'Still strong',
    //         },
    //         {
    //             day_at: 1719612000,
    //             total_count: 25,
    //             custom_count: 7,
    //             tooltip: 'Weekend slowdown',
    //         },
    //         {
    //             day_at: 1719698400,
    //             total_count: 28,
    //             custom_count: 9,
    //             tooltip: 'Slight recovery',
    //         },
    //         {
    //             day_at: 1719784800,
    //             total_count: 33,
    //             custom_count: 11,
    //             tooltip: 'Steady progress',
    //         },
    //         {
    //             day_at: 1719871200,
    //             total_count: 37,
    //             custom_count: 13,
    //             tooltip: 'New peak',
    //         },
    //         {
    //             day_at: 1719957600,
    //             total_count: 42,
    //             custom_count: 14,
    //             tooltip: 'Holding strong',
    //         },
    //         {
    //             day_at: 1720044000,
    //             total_count: 38,
    //             custom_count: 10,
    //             tooltip: 'Mid-week plateau',
    //         },
    //         {
    //             day_at: 1720130400,
    //             total_count: 41,
    //             custom_count: 12,
    //             tooltip: 'Uptick again',
    //         },
    //         {
    //             day_at: 1720216800,
    //             total_count: 47,
    //             custom_count: 16,
    //             tooltip: 'Second best performance',
    //         },
    // ]
})

/*
{
    day_at: number,
    total_count: number,
    custom_count: number,
}
 */
