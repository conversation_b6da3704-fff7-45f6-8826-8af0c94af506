import type {
    ProductMetricCategory,
} from '#/packages/@tmg/product-metrics-tool-frontend/src/lib/dictionary/ProductMetricsDictionary'
import { randomInt } from '~/lib/Helper/NumberHelper'
import type { ProductMetrics } from '#/packages/@tmg/product-metrics-tool-frontend/src/types/ProductMetrics'
import type { ProductMetricFetchCategories } from '~/api/models/Sale/Sale'

export function getMetrics(category: ProductMetricFetchCategories) {
    const data = generateMetricsData()

    if (category === 'all') {
        return data
    } else {
        return {
            [category]: data[category] || {},
        }
    }
}

function generateMetricsData(): ProductMetrics.MetricsResponse {
    return {
        'acquisition': {
            'total_visitors': {
                'value': { 'unit': 'number', 'value': randomInt(100, 10000) },
                'nested': {
                    'total_visitors_paid': {
                        'value': { 'unit': 'number', 'value': randomInt(100, 10000) },
                        'contents': {
                            'search_ads': {
                                'value': { 'unit': 'number', 'value': randomInt(100, 10000) },
                            },
                            'remarketing': {
                                'value': { 'unit': 'number', 'value': randomInt(100, 10000) },
                            },
                            'display_ads': {
                                'value': { 'unit': 'number', 'value': randomInt(100, 10000) },
                            },
                            'google': {
                                'value': { 'unit': 'number', 'value': randomInt(100, 10000) },
                            },
                            'kayak': {
                                'value': { 'unit': 'number', 'value': randomInt(100, 10000) },
                            },
                            'other': {
                                'value': { 'unit': 'number', 'value': randomInt(100, 10000) },
                            },
                        },
                    },
                    'total_visitors_organic': {
                        'value': { 'unit': 'number', 'value': randomInt(100, 10000) },
                        'contents': {
                            'search_ads': {
                                'value': { 'unit': 'number', 'value': randomInt(100, 10000) },
                            },
                            'remarketing': {
                                'value': { 'unit': 'number', 'value': randomInt(100, 10000) },
                            },
                            'display_ads': {
                                'value': { 'unit': 'number', 'value': randomInt(100, 10000) },
                            },
                            'google': {
                                'value': { 'unit': 'number', 'value': randomInt(100, 10000) },
                            },
                            'kayak': {
                                'value': { 'unit': 'number', 'value': randomInt(100, 10000) },
                            },
                            'other': {
                                'value': { 'unit': 'number', 'value': randomInt(100, 10000) },
                            },
                        },
                    },
                },
            },
            'cost_per_visitor': { 'value': { 'unit': 'money', 'value': randomInt(100, 10000) } },
        },
        'activation': {
            'activation_rate': {
                'value': { 'unit': 'percent', 'value': randomInt(0, 100) },
                'nested': {
                    'activation_rate_jivo_initiated': { 'value': { 'unit': 'number', 'value': randomInt(100, 10000) } },
                    'activation_rate_viewed_pseudo_offer': { 'value': { 'unit': 'number', 'value': randomInt(100, 10000) } },
                    'activation_rate_form_submitted': { 'value': { 'unit': 'number', 'value': randomInt(100, 10000) } },
                },
            },
            'cost_per_acquisition': { 'value': { 'unit': 'money', 'value': randomInt(100, 10000) } },
            'total_leads_generated': { 'value': { 'unit': 'number', 'value': randomInt(100, 10000) } },
            'avg_first_response_time': { 'value': { 'unit': 'seconds', 'value': randomInt(100, 10000) } },
        },
        'conversion': {
            'lead_to_purchase_rate': { 'value': { 'unit': 'percent', 'value': randomInt(0, 100) } },
            'avg_time_from_lead_to_purchase': { 'value': { 'unit': 'number', 'value': randomInt(100, 10000) } },
            'customer_acquisition_cost': { 'value': { 'unit': 'money', 'value': randomInt(100, 10000) } },
        },
        'retention': {
            'customer_retention_rate': { 'value': { 'unit': 'percent', 'value': randomInt(0, 100) } },
            'repeat_purchase_rate': { 'value': { 'unit': 'percent', 'value': randomInt(100, 100) } },
            'avg_purchases_per_customer': { 'value': { 'unit': 'number', 'value': randomInt(100, 10000) } },
            'loyalty_program_engagement': { 'value': { 'unit': 'percent', 'value': randomInt(0, 100) } },
        },
        'referral': {
            'new_customers_via_referral': {
                'value': { 'unit': 'number', 'value': randomInt(100, 10000) },
                'nested': {
                    'new_customers_via_referral_link': { 'value': { 'unit': 'number', 'value': randomInt(100, 10000) } },
                    'new_customers_via_referral_client': { 'value': { 'unit': 'number', 'value': randomInt(100, 10000) } },
                },
            },
            'percent_users_sharing_referrals': { 'value': { 'unit': 'percent', 'value': randomInt(0, 100) } },
            'customer_reviews': { 'value': { 'unit': 'number', 'value': randomInt(100, 10000) } },
        },
        'revenue': {
            'total_revenue': { 'value': { 'unit': 'money', 'value': randomInt(100, 10000) } },
            'arppu': { 'value': { 'unit': 'money', 'value': randomInt(100, 10000) } },
            'orders_with_upsells_rate': { 'value': { 'unit': 'percent', 'value': randomInt(0, 100) } },
            'avg_order_value': { 'value': { 'unit': 'money', 'value': randomInt(100, 10000) } },
            'gross_profit': { 'value': { 'unit': 'money', 'value': randomInt(100, 10000) } },
            'profit_margin': { 'value': { 'unit': 'money', 'value': randomInt(100, 10000) } },
        },
        'resurrection': {
            'dormant_users': { 'value': { 'unit': 'number', 'value': randomInt(100, 10000) } },
            'reactivation_rate': {
                'value': { 'unit': 'percent', 'value': randomInt(0, 100) },
                'nested': {
                    'reactivation_returned': { 'value': { 'unit': 'number', 'value': randomInt(100, 10000) } },
                },
            },
            'avg_time_to_reactivation': { 'value': { 'unit': 'number', 'value': randomInt(100, 10000) } },
        },
        'satisfaction': {
            'nps': { 'value': { 'unit': 'number', 'value': randomInt(100, 10000) } },
        },
        'northstar': {
            'ltv': {
                'value': { 'unit': 'money', 'value': randomInt(100, 10000) },
                'nested': {
                    'ltv_arppu': { 'value': { 'unit': 'money', 'value': randomInt(100, 10000) } },
                    'ltv_avg_purchases': { 'value': { 'unit': 'number', 'value': randomInt(100, 10000) } },
                },
            },
        },
    }
}

