import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelAttributes, ModelFields } from '~types/lib/Model'
import { faker } from '@faker-js/faker'
import { ReleasePostStatus } from '~/api/models/ReleasePost/ReleasePost'
import { dateToUnixTimestamp, randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import ReleasePostItemFactory from '~mock/factories/ReleasePost/ReleasePostItemFactory'
import { randomInt } from '~/lib/Helper/NumberHelper'
import ReleasePostItemFeedbackFactory from '~mock/factories/ReleasePost/ReleasePostItemFeedbackFactory'
import ReleasePostItemFeedbackReplyFactory from '~mock/factories/ReleasePost/ReleasePostItemFeedbackReplyFactory'

const modelName = 'ReleasePost'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class ReleasePostFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public factories = {
        postItem: new ReleasePostItemFactory(),
        releasePostItemFeedback: new ReleasePostItemFeedbackFactory(),
        ReleasePostItemFeedbackReply: new ReleasePostItemFeedbackReplyFactory(),
    }

    public async create(
        attributes: Attributes = {},
        options: {
            withContents?: boolean,
            withFeedbacks?: boolean | number,
        } = {},
    ) {
        const release = await createModelRecord(modelName, await this.make(attributes))

        const agents = await findModelRecords('Agent')

        const items: ModelAttributes<'ReleasePostItem'>[] = []

        if (options.withContents) {
            for (let i = 0; i < randomInt(1, 5); i++) {
                const item = await this.factories.postItem.create({
                    release_post_pk: usePk(release),
                })

                items.push(item)
            }
        }

        if (options.withFeedbacks) {
            for (const item of items) {
                const count = typeof options.withFeedbacks === 'number' ? options.withFeedbacks : randomInt(1, 3)

                for (let i = 0; i < count; i++) {
                    const feedback = await this.factories.releasePostItemFeedback.create({
                        release_post_item_pk: usePk(item),
                    })

                    const totalComments = randomInt(1, 4)
                    const participants = [ feedback.created_by_pk, '3']

                    for (let i = 1; i < totalComments; i++) {
                        await this.factories.ReleasePostItemFeedbackReply.create({
                            release_post_item_feedback_pk: usePk(feedback),
                            agent_pk: participants[i % 2],
                            message: faker.lorem.words(10),
                        })
                    }
                }
            }
        }

        return release
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const created_at = dateToUnixTimestamp(faker.date.past())

        const dateFormatted = new Date(created_at * 1000).toISOString().split('T')[0].replace('-', '.')

        return makeModelRecord(modelName, {
            title: `Release ${dateFormatted}`,
            description: faker.lorem.paragraph(),
            status: randomEnumValue(ReleasePostStatus),
            published_at: created_at,
            approved_at: created_at,
            created_at: created_at,
            ...attributes,
        })
    }
}
