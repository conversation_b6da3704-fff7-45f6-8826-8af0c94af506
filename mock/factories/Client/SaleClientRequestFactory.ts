import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { dateToUnixTimestamp, randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { SaleClientRequestCategory, SaleClientRequestStatus } from '~/api/models/Client/SaleClientRequest'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { randomFloat } from '~/lib/Helper/NumberHelper'

const modelName = 'SaleClientRequest'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class SaleClientRequestFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const saleVersionList = await findModelRecords('SaleVersion')

        return makeModelRecord(modelName, {
            category: randomEnumValue(SaleClientRequestCategory),
            created_at: dateToUnixTimestamp(new Date()),
            created_by_pk: randomElementPk(await findModelRecords('Agent')),
            sale_version_pk: randomElementPk(saleVersionList),
            status: randomEnumValue(SaleClientRequestStatus),
            offered_price: randomFloat(500, 1000),
            offer_link: '#',
            ...attributes,
        })
    }
}
