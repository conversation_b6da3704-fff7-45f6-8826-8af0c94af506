import { faker } from '@faker-js/faker'
import Factory from '~mock/factories/Factory'
import {
    createModelRecord,
    findModelRecords,
    makeModelRecord,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import {
dateToUnixTimestamp,
items,
randomBoolean,
randomEnumValue,
withChance,
} from '~mock/lib/Helper/SeedHelper'
import { mapAsync, randomElement, randomElementPk } from '~/lib/Helper/ArrayHelper'
import { ExternalResource } from '~/api/dictionaries/Static/ExternalResourceDictionary'
import PhoneFactory from '~mock/factories/Phone/PhoneFactory'
import EmailFactory from '~mock/factories/Email/EmailFactory'
import ClientAdditionalInfoFactory from '~mock/factories/Client/ClientAdditionalInfoFactory'
import { randomInt } from '~/lib/Helper/NumberHelper'
import ClientPhoneDataFactory from '~mock/factories/Client/ClientPhoneDataFactory'
import ClientEmailDataFactory from '~mock/factories/Client/ClientEmailDataFactory'
import { usePk } from '~/composables/usePk'
import { MockProjectID } from '~mock/seeds/models/Project/ProjectSeeder'

const modelName = 'Client'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class ClientFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName, 'Phone', 'Email']

    protected factories = {
        phone: new PhoneFactory(),
        email: new EmailFactory(),
        info: new ClientAdditionalInfoFactory(),
        clientPhone: new ClientPhoneDataFactory(),
        clientEmail: new ClientEmailDataFactory(),
    }

    public async create(
        attributes: Attributes = {},
        options: Options = {},
    ) {
        const clientData = await this.make(attributes)

        const phones = await mapAsync(items(options.phoneCount ?? 1), () => {
            return this.factories.phone.create()
        })

        const emails = await mapAsync(items(options.emailCount ?? 1), () => {
            return this.factories.email.create({ value: clientData.email })
        })

        const client = await createModelRecord(modelName, {
            ...clientData,
            phone: randomElement(phones).value,
            email: randomElement(emails).value,
        })

        for (const phone of phones) {
            await this.factories.clientPhone.create({
                client_pk: usePk(client),
                phone_pk: usePk(phone),
            })
        }

        for (const email of emails) {
            await this.factories.clientEmail.create({
                client_pk: usePk(client),
                email_pk: usePk(email),
            })
        }

        const clientDefaultPhone = (await findModelRecords('ClientPhoneData', { client_pk: usePk(client) }))[0].phone_pk
        const clientDefaultEmail = (await findModelRecords('ClientEmailData', { client_pk: usePk(client) }))[0].email_pk

        await updateModelRecord('Client', usePk(client), {
            client_phone_pk: clientDefaultPhone,
            client_email_pk: clientDefaultEmail,
        })

        await this.factories.info.create({
            id: client.id,
            last_lead_pk: withChance(String(randomInt(1, 10))),
            pending_status_pk: withChance(randomElementPk(await findModelRecords('ClientStatus'))),
            project_pk: client.project_pk,
            has_enriched_info: client.has_enriched_info,
        })

        return client
    }

    public async make(
        attributes: Attributes,
    ) {
        const agents = await findModelRecords('Agent')

        const names = {
            first_name: faker.person.firstName(),
            last_name: faker.person.lastName(),
        }

        const is_fixed = randomBoolean()
        const is_fixed_by = is_fixed ? randomElementPk(agents) : null

        return makeModelRecord(modelName, {
            ...names,

            client_cabinet_id: withChance(randomInt(1, 10)),
            created_at: dateToUnixTimestamp(new Date()),
            from_resource: randomEnumValue(ExternalResource),
            curator_pk: withChance(randomElementPk(agents)),
            email: faker.internet.email({ firstName: names.first_name, lastName: names.last_name }),
            phone: faker.phone.number(),
            is_fixed: is_fixed,
            is_fixed_by_pk: is_fixed_by,
            status_pk: randomElementPk(await findModelRecords('ClientStatus')),
            project_pk: usePk('Project', MockProjectID.TBC),
            is_fraud: randomBoolean(0.2),

            has_enriched_info: true,

            //
            ...attributes,
        })
    }
}

type Options = {
    phoneCount?: number
    emailCount?: number
}
