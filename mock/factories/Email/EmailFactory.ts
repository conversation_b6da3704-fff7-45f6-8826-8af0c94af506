import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { faker } from '@faker-js/faker'
import { randomInt } from '~/lib/Helper/NumberHelper'

const modelName = 'Email'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class EmailFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
        options: Options = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes, options))
    }

    public async make(
        attributes: Attributes = {},
        options: Options = {},
    ) {
        return makeModelRecord(modelName, {
            value: faker.internet.email({ firstName: options.first_name, lastName: options.last_name }),
            fraud_info_pk: `${modelName}:${String(randomInt(1, 10))}`, // Random int, because of custom handler

            //
            ...attributes,
        })
    }
}

type Options = {
    first_name?: string
    last_name?: string
}
