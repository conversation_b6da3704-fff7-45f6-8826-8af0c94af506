import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { faker } from '@faker-js/faker'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'

const modelName = 'SaleRequestHelp'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class SaleRequestHelpFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
        options: Options = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const sales = await findModelRecords('Sale')
        const agents = await findModelRecords('Agent')

        return makeModelRecord(modelName, {
            reason: faker.lorem.sentence(),
            sale_pk: randomElementPk(sales),
            created_by_pk: randomElementPk(agents),
            executor_pk: null,
            ...attributes,
        })
    }
}

type Options = {
    //
}
