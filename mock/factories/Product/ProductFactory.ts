import Factory from '~mock/factories/Factory'
import {
    createModelRecord,
    findModelRecord,
    findModelRecords,
    makeModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { ProductPayType, ProductType } from '~/api/models/Product/Product'
import { randomElement, randomElementPk } from '~/lib/Helper/ArrayHelper'
import { faker } from '@faker-js/faker'
import { usePk } from '~/composables/usePk'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { dateToUnixTimestamp, randomBoolean, randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { SaleInsuranceType } from '~/api/dictionaries/Static/Sale/SaleInsuranceTypeDictionary'

const modelName = 'Product'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class ProductFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const sales = await findModelRecords('Sale')
        const agents = await findModelRecords('Agent')
        const consolidators = await findModelRecords('Consolidator')
        const productTypes = Object.keys(ProductType)
        const consolidator = randomElement(consolidators)
        const consolidator_pk = usePk(consolidator)
        const isAward = consolidator.is_award
        const mile_price = 0.014 // todo: from selected award account
        const net_price_currency_pk = isAward ? '2' : '1'  // todo: maybe remove

        const net_price = isAward ? randomInt(20000, 50000) : randomInt(100, 1000)

        const fare = isAward ? net_price * mile_price : net_price
        const tax = randomInt(10, 100)

        const check_payment_ps = randomBoolean() ? 0 : 3.5
        //
        const net_price_base = fare + tax
        const sell_price = net_price_base
        //
        const commission_ps = 1 // todo:
        const commission = fare / 100 * commission_ps

        const consolidatorArea = await findModelRecord('ConsolidatorArea', { consolidator_pk }) || randomElement(await findModelRecords('ConsolidatorArea'))

        const issuing_fee = 2

        const pay_type = randomEnumValue(ProductPayType)
        const card_identity = randomElementPk(await findModelRecords('ProjectCard'))

        const item_type = attributes.item_type || ProductType[randomElement(productTypes) as keyof typeof ProductType]

        return makeModelRecord(modelName, {
            item_type: item_type,
            sub_type: item_type === ProductType.Insurance ? randomEnumValue(SaleInsuranceType) : null,
            net_price: roundToMoney(net_price),
            net_price_base: roundToMoney(net_price_base),
            net_price_currency_pk,
            sell_price: roundToMoney(sell_price),
            fare: roundToMoney(fare),
            tax: roundToMoney(tax),
            commission: roundToMoney(commission),
            commission_ps,
            issuing_fee: roundToMoney(issuing_fee),
            check_payment: ((sell_price - net_price_base) / 100) * check_payment_ps,
            check_payment_ps,
            balance: roundToMoney(net_price_base + issuing_fee - commission),
            fee: 0,
            consolidator_pk,
            consolidator_area_pk: usePk(consolidatorArea),
            profit: sell_price - net_price_base,
            order_number: faker.string.alphanumeric(5),
            external_number: faker.string.alphanumeric(5),
            is_award: isAward,
            remark: faker.lorem.words(2),
            sale_pk: randomElementPk(sales),
            created_at: dateToUnixTimestamp(faker.date.past()),
            created_by_pk: randomBoolean() ? randomElementPk(agents) : null,
            pay_type,
            card_identity,
            assignee_pk: randomBoolean() ? randomElementPk(agents) : null,
            chat_branch_name: 'product-' + randomInt(1, 10),
            //
            ...attributes,
        })
    }
}

const roundToMoney = (value: number) => {
    return parseFloat(value.toFixed(2))
}
