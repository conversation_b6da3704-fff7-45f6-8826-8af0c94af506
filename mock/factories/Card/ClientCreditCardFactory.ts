import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomInt } from '~/lib/Helper/NumberHelper'

const modelName = 'ClientCreditCard'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class ClientCreditCardFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        return makeModelRecord(modelName, {
            fraud_info_pk: `${modelName}:${String(randomInt(1, 10))}`, // Random int, because of custom handler
            ...attributes,
        })
    }
}

type Options = {
    //
}
