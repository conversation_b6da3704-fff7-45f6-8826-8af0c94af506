import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomBoolean, randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { CreditCardType, SaleCardVerifyStatus } from '~/api/models/Sale/SaleVersionCard'
import { faker } from '@faker-js/faker'
import type { PrimaryKey } from '@/types'
import { randomInt } from '~/lib/Helper/NumberHelper'

const modelName = 'SaleVersionCard'

type Attributes = Partial<ModelFields<typeof modelName>> & {sale_pk: PrimaryKey, sale_version_pk: PrimaryKey, client_card_pk: PrimaryKey}

export default class SaleVersionCardFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes,
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes,
    ) {
        const card = faker.finance.creditCardNumber()
        const month = randomInt(1, 12)
        const fixMonth = month < 10 ? '0' + month : month

        return makeModelRecord(modelName, {
            expiration: `${fixMonth}${randomInt(25, 30)}`,
            is_verified: randomBoolean(),
            verify_status: randomEnumValue(SaleCardVerifyStatus),
            strip: `${card.slice(0, 4)} - ${card.slice(-4)}`,
            credit_card_type: randomEnumValue(CreditCardType),
            amount: randomInt(1000, 9999),
            postal_code: faker.location.zipCode(),
            country: faker.location.countryCode(),
            street: faker.location.street(),
            city: faker.location.city(),
            state: faker.location.state(),
            email: faker.internet.email(),
            first_name: faker.person.firstName(),
            last_name: faker.person.lastName(),
            bank_phone: faker.phone.number(),
            is_wrong: randomBoolean(),
            invoice_number: 'qweqw',
            ...attributes,
        })
    }
}
