import Factory from '~mock/factories/Factory'
import {
    createModelRecord,
    findModelRecords,
    makeModelRecord,
    nextIncrementValue,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'

const modelName = 'AdditionalExpense'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class AdditionalExpenseFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        return makeModelRecord(modelName, {
            id: nextIncrementValue(modelName),
            sale_version_pk: randomElementPk(await findModelRecords('SaleVersion')),
            product_pk: randomElementPk(await findModelRecords('Product')),
            executor_pk: null,
            ...attributes,
        })
    }
}

