import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import type { CalendarMonth } from '~/api/models/Marketing/MarketingCost'
import { randomFloat } from '~/lib/Helper/NumberHelper'

const modelName = 'MarketingCost'

type Attributes = Partial<ModelFields<typeof modelName>> & {
    year: number,
    month: CalendarMonth,
}

export default class MarketingCostFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public factories = {}

    public async create(
        attributes: Attributes,
    ) {
        return await createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes,
    ) {
        return makeModelRecord(modelName, {
            value: randomFloat(1000, 5000),
            ...attributes,
        })
    }
}
