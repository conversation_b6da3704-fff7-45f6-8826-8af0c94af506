import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { faker } from '@faker-js/faker'
import { dateToUnixTimestamp, randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { LeaveRequestStatus, LeaveRequestType } from '~/api/models/LeaveManagement/LeaveRequest'
import { randomElementPk, randomElements } from '~/lib/Helper/ArrayHelper'

const modelName = 'LeaveRequest'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class LeaveRequestFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public factories = {

    }

    public async create(
        attributes: Attributes = {},
    ) {
        return await createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const status = attributes.status ?? randomEnumValue(LeaveRequestStatus)

        const files = randomElements(await findModelRecords('File'), 5).map((file) => String(file.id))

        const now = new Date()
        const startOfYearUTC = new Date(Date.UTC(now.getUTCFullYear(), 0, 1, 0, 0, 0))
        const endOfYearUTC = new Date(Date.UTC(now.getUTCFullYear(), 11, 31, 23, 59, 59, 999))

        const leave_start_date = faker.date.between({ from: startOfYearUTC, to: endOfYearUTC })

        return makeModelRecord(modelName, {
            beneficiary_pk: randomElementPk(await findModelRecords('Agent')),
            dates: {
                leave_start: dateToUnixTimestamp(leave_start_date),
                leave_end: dateToUnixTimestamp(faker.date.between({ from: leave_start_date, to: endOfYearUTC })),
            },
            comment: faker.lorem.paragraph(),
            days_count: faker.number.int({ min: 1, max: 5 }),
            status: status,
            remark: status != LeaveRequestStatus.Pending ? faker.lorem.paragraph() : null,
            file_pks: files,
            created_at: dateToUnixTimestamp(new Date()),
            request_type: randomEnumValue(LeaveRequestType),
            ...attributes,
        })
    }
}
