import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { faker } from '@faker-js/faker'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import FakeTable from '@/api/mock/lib/FakeTable'
import FakeHelper from '@/api/mock/lib/FakeHelper'

const modelName = 'LeaveTransaction'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class LeaveTransactionFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes,
    ) {
        return  await createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const randomValue = faker.number.float({ min: -2, max: 2 })
        const roundedValue = Math.round(randomValue * 2) / 2

        return makeModelRecord(modelName, {
            beneficiary_pk: randomElementPk(await findModelRecords('Agent')),
            amount: roundedValue,
            created_at: FakeTable.currentTimeStamp() - FakeHelper.randomMaxMinInt(1000, 86400),
            ...attributes,
        })
    }
}
