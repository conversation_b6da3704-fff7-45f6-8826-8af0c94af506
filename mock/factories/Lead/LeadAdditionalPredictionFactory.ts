import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomInt } from '~/lib/Helper/NumberHelper'
import type { ModelFields } from '~types/lib/Model'

const modelName = 'LeadAdditionalPrediction'

type Attributes = Partial<ModelFields<typeof modelName>> & {
    id: number
}

export default class LeadAdditionalPredictionFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(attributes: Attributes) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(attributes: Attributes) {
        return makeModelRecord(modelName, {
            id: attributes.id,
            lead_score: randomInt(10, 90),
        })
    }
}
