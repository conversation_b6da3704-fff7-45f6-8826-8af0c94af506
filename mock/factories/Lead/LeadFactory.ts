import Factory from '~mock/factories/Factory'
import {
    createModelRecord,
    findModelRecords,
    makeModelRecord,
    nextIncrementValue,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields, ModelLinkIdentification } from '~types/lib/Model'
import { dateToUnixTimestamp, randomBoolean, randomEnumValue, withChance } from '~mock/lib/Helper/SeedHelper'
import { randomElement, randomElementPk } from '~/lib/Helper/ArrayHelper'
import { usePk } from '~/composables/usePk'
import { faker } from '@faker-js/faker'
import { ClosingReasons, DuplicateStatus, ItineraryType } from '~/api/models/Lead/Lead'
import { ExternalResource } from '~/api/dictionaries/Static/ExternalResourceDictionary'
import { MockProjectID } from '~mock/seeds/models/Project/ProjectSeeder'
import { ItineraryClass } from '~/api/dictionaries/Static/ItineraryClassDictionary'
import { randomInt } from '~/lib/Helper/NumberHelper'
import TaskGroupFactory from '~mock/factories/Task/TaskGroupFactory'
import { TaskCategory } from '~/api/models/Task/TaskGroup'
import LeadAdditionalUTMFactory from '~mock/factories/Lead/LeadAdditionalUTMFactory'
import LeadAdditionalListInformationFactory from '~mock/factories/Lead/LeadAdditionalListInformationFactory'
import LeadAdditionalAgentInfoFactory from '~mock/factories/Lead/LeadAdditionalAgentInfoFactory'
import LeadAdditionalManagementStatusFactory from '~mock/factories/Lead/LeadAdditionalManagementStatus'
import LeadAdditionalProfitsFactory from '~mock/factories/Lead/LeadAdditionalProfitsFactory'
import LeadAdditionalCallCounterInfoFactory from '~mock/factories/Lead/LeadAdditionalCallCounterInfoFactory'
import LeadAdditionalMailCounterInfoFactory from '~mock/factories/Lead/LeadAdditionalMailCounterInfoFactory'
import LeadAdditionalExpertInformationFactory from '~mock/factories/Lead/LeadAdditionalExpertInformationFactory'
import LeadOfferFactory from '~mock/factories/Lead/LeadOfferFactory'
import CallContactAttemptFactory from '~mock/factories/Call/CallContactAttemptFactory'
import LeadAdditionalPredictionFactory from '~mock/factories/Lead/LeadAdditionalPredictionFactory'

const modelName = 'Lead'

type Attributes = Partial<ModelFields<typeof modelName>>
type Options = {
    withTasks?: boolean
    withOffers?: boolean,
}

export default class LeadFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public factories = {
        taskGroup: new TaskGroupFactory(),
        offers: new LeadOfferFactory(),
        utm: new LeadAdditionalUTMFactory(),
        listInformation: new LeadAdditionalListInformationFactory(),
        additionalPrediction: new LeadAdditionalPredictionFactory(),
        agentLeadInfo: new LeadAdditionalAgentInfoFactory(),
        expertLeadInfo: new LeadAdditionalExpertInformationFactory(),
        leadManagementStatus: new LeadAdditionalManagementStatusFactory(),
        profits: new LeadAdditionalProfitsFactory(),
        callCounter: new LeadAdditionalCallCounterInfoFactory(),
        mailCounter: new LeadAdditionalMailCounterInfoFactory(),
        callContactAttempt: new CallContactAttemptFactory(),
    }

    public async create(
        attributes: Attributes = {},
        options: Options = {},
    ) {
        const lead = await createModelRecord(modelName, await this.make(attributes))

        if (options.withTasks) {
            const modelInfo: ModelLinkIdentification = {
                model_name: modelName,
                model_pk: usePk(lead),
            }

            await this.factories.taskGroup.create({
                category: TaskCategory.General,
                ...modelInfo,
            }, {
                withTasks: true,
                withFollowupTasks: true,
            })

            await this.factories.taskGroup.create({
                category: TaskCategory.CustomerSupport,
                ...modelInfo,
            }, {
                withTasks: true,
            })
        }

        if (options.withOffers) {
            for (let i = 0; i < randomInt(1, 3); i++) {
                await this.factories.offers.create({
                    lead_pk: usePk(lead),
                })
            }
        }

        await this.factories.utm.create({
            id: lead.id,
        })
        await this.factories.listInformation.create({
            id: lead.id,
        })
        await this.factories.additionalPrediction.create({
            id: lead.id,
        })
        await this.factories.profits.create({
            id: lead.id,
        })
        await this.factories.agentLeadInfo.create({
            id: lead.id,
        })
        await this.factories.expertLeadInfo.create({
            id: lead.id,
        })
        await this.factories.leadManagementStatus.create({
            id: lead.id,
        })

        await this.factories.mailCounter.create({
            id: lead.id,
        })

        await this.factories.callCounter.create({
            id: lead.id,
        })

        await this.factories.callContactAttempt.create()
        await this.factories.callContactAttempt.create()
        await this.factories.callContactAttempt.create()

        return lead
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const itineraryType = randomEnumValue(ItineraryType)
        const duplicateStatus = randomEnumValue(DuplicateStatus)

        const iatas = await findModelRecords('Iata')
        const leadStatuses = await findModelRecords('LeadStatus')

        const currentDate = new Date()
        const futureDate = new Date(currentDate)
        futureDate.setDate(currentDate.getDate() + 3)

        return makeModelRecord(modelName, {
            id: nextIncrementValue(modelName),
            is_deleted: randomBoolean(0.1),
            created_at: dateToUnixTimestamp(faker.date.past()),

            status_pk: randomElementPk(leadStatuses),
            is_bonus: randomBoolean(),
            external_resource: randomEnumValue(ExternalResource),
            itinerary_class: randomEnumValue(ItineraryClass),
            duplicate_status: withChance(duplicateStatus),
            itinerary_type: itineraryType,
            itinerary_plan: [''],
            from_iata_code: randomElement(iatas).code,
            to_iata_code: randomElement(iatas).code,
            from_iata_timezone: faker.location.timeZone(),
            to_iata_timezone: faker.location.timeZone(),
            departure_date: dateToUnixTimestamp(faker.date.future()),
            return_date: itineraryType === ItineraryType.RoundTrip ? dateToUnixTimestamp(faker.date.future()) : null,
            taken_date: withChance(dateToUnixTimestamp(faker.date.past())),
            closing_reason: randomEnumValue(ClosingReasons),
            closing_reason_remark: faker.lorem.words(),
            remark: withChance(faker.lorem.sentence()),
            adult_count: randomInt(0, 3),
            child_count: randomInt(0, 3),
            infant_count: randomInt(0, 3),
            is_award: randomBoolean(),
            client_phone_pk: withChance(randomElementPk(await findModelRecords('Phone'))),
            client_email_pk: randomElementPk(await findModelRecords('Email')),
            executor_pk: withChance(randomElementPk(await findModelRecords('Agent'))),
            client_pk: randomElementPk(await findModelRecords('Client')),

            created_by_pk: withChance(randomElementPk(await findModelRecords('Agent'))),
            project_pk: usePk('Project', MockProjectID.TBC),
            first_expert_agent_pk: withChance(randomElementPk(await findModelRecords('Agent'))),
            is_cs_lead: randomBoolean(),
            is_possible_to_add_pq: randomBoolean(),
            active_keep_client_request_pk: null,
            client_session_activity_pk: withChance(randomElementPk(await findModelRecords('ClientSessionActivity'))),

            voucher_pks: [],
            is_test: randomBoolean(),
            //
            chat_pk: '0',
            ...attributes,
        })
    }
}
