import Factory from '~mock/factories/Factory'
import {
    createModelRecord,
    findModelRecord,
    findModelRecords,
    makeModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelAttributes, ModelFields } from '~types/lib/Model'
import {
dateToUnixTimestamp,
items,
randomBoolean,
randomEnumValue,
withChance,
} from '~mock/lib/Helper/SeedHelper'
import { AwardAccountStatus, AwardAccountType, AwardAccountWarningFlag } from '~/api/models/AwardAccount/AwardAccount'
import { faker } from '@faker-js/faker'
import { randomFloat, randomInt } from '~/lib/Helper/NumberHelper'
import { mapAsync, randomElement, randomElementPk, tryRandomElementPk } from '~/lib/Helper/ArrayHelper'
import { composeResourcePk, usePk } from '~/composables/usePk'
import AwardAccountHolderFactory from '~mock/factories/AwardAccount/AwardAccountHolderFactory'
import { AwardAccountRelations } from '~/api/dictionaries/Static/AwardAccount/AwardAccountRelationDictionary'
import { DepartmentName } from '~/api/models/Department/Department'
import { CompanyContactCategory } from '~/api/models/CompanyContact/CompanyContact'

const modelName = 'AwardAccount'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class AwardAccountFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public readonly factories = {
        holderFactory: new AwardAccountHolderFactory(),
    }

    public async create(
        attributes: Attributes = {},
    ) {
        const accountData = await this.make(attributes)
        const accountPk = composeResourcePk(modelName, accountData)

        const tickets_award_department = await findModelRecord('Department', {
            system_name: DepartmentName.TicketingAward,
        }) as ModelAttributes<'Department'>

        const contacts = await findModelRecords('CompanyContact', {
            department_pk: usePk(tickets_award_department),
        })

        const emails = contacts.filter(contact => contact.category === CompanyContactCategory.Email)
        const phones = contacts.filter(contact => contact.category === CompanyContactCategory.Phone)

        const holders = await mapAsync(items(randomInt(1, 3)), async (n) => {
            return await this.factories.holderFactory.create({
                award_account_pk: accountPk,
                is_default: n === 1,
            })
        })

        const account = await createModelRecord(modelName, {
            ...accountData,
            holder_pk: usePk(holders[0]),
        })

        await createModelRecord('AwardAccountAdditionalCredentials', {
            id: account.id,

            password: faker.internet.password(),

            phone: randomBoolean() ? randomElement(phones).value : null,
            is_our_phone: randomBoolean(),
            email: randomElement(emails).value,
            is_our_email: randomBoolean(),

            city: faker.location.city(),
            country: faker.location.country(),
            state: faker.location.state(),
            street: faker.location.street(),
            zip: faker.location.zipCode(),

            old_data: [{
                email: '<EMAIL>',
                phone: '****** 567 8900 (Is our)',
                status: 'Active',
                type: 'Main',
                remark: 'Main communication account',
            }],

        })

        return account
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const milePricePrograms = await findModelRecords('MilePriceProgram')
        const consolidators = await findModelRecords('Consolidator')
        const consolidatorAreas = await findModelRecords('ConsolidatorArea')
        const projects = await findModelRecords('Project')
        const existingawardAccount = await findModelRecords('AwardAccount')
        const externalUserAgents = await findModelRecords('ExternalUserAgent')

        const parentAccountPk = withChance(tryRandomElementPk(existingawardAccount))

        const status = attributes.status || randomEnumValue(AwardAccountStatus)

        const type = randomEnumValue(AwardAccountType)

        const externalUserAgentPk = withChance(randomElementPk(externalUserAgents))
        const milePriceProgramPk = randomElementPk(milePricePrograms)

        return makeModelRecord(modelName, {
            status,
            type,
            is_full_editable: randomBoolean(),

            account_number: faker.internet.userName(),

            vpn: faker.string.alphanumeric(4),
            // award_account_email_pk: '_tmp',

            remark: faker.lorem.sentence(randomInt(3, 20)),

            balance: randomFloat(100, 10000),
            cpm: randomInt(1, 100) / 1000,
            rcpm: randomInt(1, 100) / 1000,

            holder_pk: '_tmp',
            mile_price_program_pk: milePriceProgramPk,
            consolidator_pk: randomElementPk(consolidators),
            consolidator_area_pk: type === AwardAccountType.Inhouse ? randomElementPk(consolidatorAreas) : null,

            parent_award_account_pk: parentAccountPk,
            parent_award_account_relation: withChance(randomElement(AwardAccountRelations)),

            in_use_till_date: status === AwardAccountStatus.InUse ? dateToUnixTimestamp(faker.date.past({ years: 1 })) : null,
            last_sale_pk: status === AwardAccountStatus.InUse ? tryRandomElementPk(await findModelRecords('Sale')) : null,
            active_sale_count: status === AwardAccountStatus.InUse ? randomInt(0, 10) : 0,
            refund_sale_count: randomInt(0, 100),
            exchange_sale_count: randomInt(0, 100),
            total_sale_count: randomInt(0, 100),
            top_up_count: randomInt(0, 100),

            is_booking_type_online: randomBoolean(),
            is_booking_type_phone: randomBoolean(),
            is_booking_type_special: randomBoolean(),

            warning_flag: withChance(randomEnumValue(AwardAccountWarningFlag)),

            is_reserved: randomBoolean(),
            group_pk: parentAccountPk,

            project_pk: randomElementPk(projects),
            external_user_agent_pk: externalUserAgentPk,
            audit_sale_pk: randomBoolean() ? tryRandomElementPk(await findModelRecords('Sale')) : null,

            is_to_be_deleted: false,
            to_be_deleted_remark: null,
            deleted_at: null,
            //
            ...attributes,
        })
    }
}
