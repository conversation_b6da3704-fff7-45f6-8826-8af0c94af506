import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { faker } from '@faker-js/faker'
import { randomFloat, randomInt } from '~/lib/Helper/NumberHelper'
import { randomElementPk, tryRandomElementPk } from '~/lib/Helper/ArrayHelper'
import { dateToUnixTimestamp, withChance } from '~mock/lib/Helper/SeedHelper'

const modelName = 'AwardAccountOutgoingRequest'

type Attributes = Partial<ModelFields<typeof modelName>>

type Options = {
    withActivityLogs?: boolean | number
}

export default class AwardAccountOutgoingRequestFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public factories = {}

    public async create(
        attributes: Attributes = {},
        options?: Options,
    ) {
        const accountRequest = await createModelRecord(modelName, await this.make(attributes))

        return accountRequest
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const accounts = await findModelRecords('AwardAccount')
        const products = await findModelRecords('Product', {
            'net_price_currency_pk': '2', // @todo
        })
        const refundedPk = withChance(tryRandomElementPk(await findModelRecords(modelName, {
            refunded_pk: null,
            is_refund: false,
        })))

        return makeModelRecord(modelName, {
            award_account_pk: randomElementPk(accounts),
            miles_amount: randomInt(1000, 10000),
            price: randomFloat(10, 1000),
            remark: faker.lorem.sentence(),
            product_pk: randomElementPk(products),
            parent_pk: null,
            created_at: dateToUnixTimestamp(faker.date.past()),
            is_blocked: false,
            refunded_pk: refundedPk,
            is_refund: refundedPk ? false : faker.datatype.boolean(),
            rcpm: randomFloat(0.01, 1),
            //
            ...attributes,
        })
    }
}
