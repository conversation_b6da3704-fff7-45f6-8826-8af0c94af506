import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { faker } from '@faker-js/faker'
import { randomFloat, randomInt } from '~/lib/Helper/NumberHelper'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'

const modelName = 'AwardAccountIncomingRequest'

type Attributes = Partial<ModelFields<typeof modelName>>

type Options = {
    withActivityLogs?: boolean | number
}

export default class AwardAccountIncomingRequestFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public factories = {}

    public async create(
        attributes: Attributes = {},
        options?: Options,
    ) {
        const accountRequest = await createModelRecord(modelName, await this.make(attributes))

        return accountRequest
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const accounts = await findModelRecords('AwardAccount')
        const consolidators = await findModelRecords('Consolidator', { is_award: true })
        const amount = randomInt(1000, 10000)
        const products = await findModelRecords('Product', {
            'net_price_currency_pk': '2', // @todo
        })

        return makeModelRecord(modelName, {
            award_account_pk: randomElementPk(accounts),
            miles_amount: amount,
            balance: amount,
            price: randomFloat(10, 1000),
            product_pk: randomElementPk(products),
            remark: faker.lorem.sentence(),
            created_at: dateToUnixTimestamp(faker.date.past()),
            consolidator_pk: randomElementPk(consolidators),
            is_deleted: false,
            rcpm: randomFloat(0.01, 1),
            //
            ...attributes,
        })
    }
}
