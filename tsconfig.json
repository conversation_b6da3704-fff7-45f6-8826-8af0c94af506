{"extends": "@vue/tsconfig/tsconfig.json", "compilerOptions": {"moduleResolution": "Node", "allowSyntheticDefaultImports": true, "verbatimModuleSyntax": false, "strict": true, "checkJs": false, "allowJs": true, "noEmit": true, "baseUrl": "./", "paths": {"#/*": ["./*"], "@/*": ["./src/*"], "~/*": ["./src-new/*"], "~types/*": ["./types/*"], "~assets/*": ["./src-new/assets/*"], "~modules/*": ["./modules/*"], "~service-worker/*": ["./service-worker/*"]}}, "include": ["./src-new/**/*", "./src/**/*", "./src-cli/**/*", "./types/**/*", "./tests/**/*", "./node/**/*", "./service-worker/**/*", "./auto-imports.d.ts", "./auto-components.d.ts", "./modules/gds-terminal/**/*", "./packages/@tmg/markup-tool-frontend/**/*", "./packages/@tmg/consolidator-tool-frontend/**/*", "./packages/@tmg/award-offers-tool-frontend/**/*", "./packages/@tmg/product-metrics-tool-frontend/**/*"], "exclude": ["./node_modules/**/*"]}