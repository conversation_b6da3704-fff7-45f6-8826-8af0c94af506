
import type { components, operations } from './vendor/cmt'

export namespace ConsolidatorTool {
    export type FlightHacksRule = components['schemas']['FlightHacksRule']
    export type HackType = components['schemas']['HackType']
    export type TripType = components['schemas']['TripType']
    export type RegionsType = components['schemas']['RegionsType']
    export type FakeSegmentType = components['schemas']['FakeSegmentType']
    export type LayoverType = components['schemas']['LayoverType']
    export type BookingType = components['schemas']['BookingType']
    export type CabinType = components['schemas']['FakeSegmentCabinType']
    export type FlightHacksRulePayload = (components['schemas']['BasePayloadDTO'] | components['schemas']['ElrPayloadDTO'] | components['schemas']['FrtPayloadDTO'] | components['schemas']['OwRevOwMlgPayloadDTO'] | components['schemas']['OwMlgOwRevPayloadDTO'] | components['schemas']['OwRevFrtOwMlgPayloadDTO'] | components['schemas']['OwMlgOwRevFrtPayloadDTO'] | components['schemas']['OwRevFrtOwRevFrtPayloadDTO'] | components['schemas']['OriginOpenJawPayloadDTO'] | components['schemas']['TwoOneWaysPayloadDTO']) | null

    export namespace Api {
        export namespace FlightHacksRule {
            export namespace List {
                export type Response = operations['FlightHacksRule_list']['responses']['200']['content']['application/json']
                export type Payload = operations['FlightHacksRule_list']['parameters']['query']
            }

            export namespace Create {
                export type Response = operations['FlightHacksRule_create']['responses']['201']['content']['application/json']
                export type Payload = operations['FlightHacksRule_create']['requestBody']['content']['application/json']
            }

            export namespace Update {
                export type Response = operations['FlightHacksRule_update']['responses']['201']['content']['application/json']
                export type Payload = operations['FlightHacksRule_update']['requestBody']['content']['application/json']
            }
        }
        export namespace Consolidator {
            export namespace Create {
                export type Response = operations['PseudoCityCodes_create']['responses']['201']['content']['application/json']
                export type Payload = operations['PseudoCityCodes_create']['requestBody']['content']['application/json']
            }
            export namespace Update {
                export type Response = operations['PseudoCityCodes_update']['responses']['201']['content']['application/json']
                export type Payload = operations['PseudoCityCodes_update']['requestBody']['content']['application/json']
            }
            export namespace Delete {
                export type Response = operations['PseudoCityCodes_delete']['responses']['204']['content']
                export type Payload = operations['PseudoCityCodes_delete']['requestBody']
            }
        }

        export namespace Rule {
            export namespace Create {
                export type Response = operations['Rule_create']['responses']['201']['content']['application/json']
                export type Payload = operations['Rule_create']['requestBody']['content']['application/json']
            }
            export namespace Update {
                export type Response = operations['Rule_update']['responses']['201']['content']['application/json']
                export type Payload = operations['Rule_update']['requestBody']['content']['application/json']
            }
            export namespace Delete {
                export type Response = operations['Rule_delete']['responses']['201']['content']['application/json']
                export type Payload = operations['Rule_delete']['requestBody']['content']['application/json']
            }
        }
    }
}
