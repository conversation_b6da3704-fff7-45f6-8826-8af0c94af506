import type { RouteDefinition } from '~/router'
import { RouteGroup } from '~types/enums/RouteGroup'
import { defineRoutePermission } from '~/utils/define'
import PlaneIcon from '~/assets/icons/PlaneIcon.svg?component'

export default ([
    {
        path: '/markup',
        name: 'markup',
        redirect: () => {
            return {
                name: 'markup.list',
                params: { category: 'offer' },
            }
        },
        meta: {
            inSidebar: true,
            permission: defineRoutePermission('all', 'openPageMarkup'),
            title: 'Markups',
            icon: DollarSignIcon,
            group: RouteGroup.SalesFinances,
        },
    },
    {
        path: '/markup/:category(offer|baggage)',
        name: 'markup.list',
        component: () => import('./pages/MarkupListPage.vue'),
        props: true,
        meta: {
            permission: defineRoutePermission('all', 'openPageMarkup'),
            title: 'Markups',
        },
    },
    {
        path: '/markup/:category(offer|baggage)/:id',
        name: 'markup.edit',
        component: () => import('./pages/MarkupListPage.vue'),
        props: true,
        meta: {
            permission: defineRoutePermission('all', 'openPageMarkup'),
            title: 'Markups',
        },
    },
    {
        path: '/markup/:category(offer|baggage)/create',
        name: 'markup.create',
        component: () => import('./pages/MarkupListPage.vue'),
        props: true,
        meta: {
            permission: defineRoutePermission('all', 'openPageMarkup'),
            title: 'Markups',
        },
    },
    {
        path: '/flight-hack',
        name: 'flight-hack',
        redirect: () => {
            return {
                name: 'flight-hack.list',
            }
        },
        meta: {
            inSidebar: true,
            permission: defineRoutePermission('all', 'openPageFlightHack'),
            title: 'Flight Hacks',
            icon: PlaneIcon,
            group: RouteGroup.SalesFinances,
        },
    },
    {
        path: '/flight-hack',
        name: 'flight-hack.list',
        component: () => import('./pages/FlightHackListPage.vue'),
        meta: {
            permission: defineRoutePermission('all', 'openPageFlightHack'),
            title: 'Flight Hacks',
        },
    },
    {
        path: '/flight-hack/:id',
        name: 'flight-hack.edit',
        component: () => import('./pages/FlightHackListPage.vue'),
        props: true,
        meta: {
            permission: defineRoutePermission('all', 'openPageFlightHack'),
            title: 'Flight Hack',
        },
    },
    {
        path: '/flight-hack/create',
        name: 'flight-hack.create',
        component: () => import('./pages/FlightHackListPage.vue'),
        meta: {
            permission: defineRoutePermission('all', 'openPageFlightHack'),
            title: 'Flight Hack',
        },
    },
] satisfies RouteDefinition[])
