<template>
    <div>
        <div class="card">
            <div class="flex items-center gap-4 w-full p-2">
                <RouterLink :to="routeToMarkupBaggageCreateRule()">
                    <AppButton class="--small">
                        Create baggage rule
                        <PlusIcon />
                    </AppButton>
                </RouterLink>

                <Loader v-if="suspense.silentLoading.value" />

                <div class="ml-auto flex items-center gap-4 flex-none">
                    <AppPaginationInfo :pagination="pagination" />
                    <AppPaginationCompact :pagination="pagination" />
                    <AppPageSize :pagination="pagination" :options="[10, 40, 100, 500]" />
                </div>
            </div>
            <div class="list-table-v2 table-fixed relative">
                <SuspenseManual :state="suspense">
                    <AppTable
                        :items="items"
                        :columns="columns"
                        zebra
                    >
                        <template #body>
                            <tr
                                v-for="item in items"
                                :key="item.baggage_rule_id"
                                :class="{
                                    'table-tr--highlighted-primary': item.baggage_rule_id === id,
                                }"
                            >
                                <TableCellLink :to="routeToMarkupBaggageRule(item.baggage_rule_id)">
                                    <Edit3Icon class="mr-2 icon --small" />
                                    Edit
                                </TableCellLink>
                                <td>
                                    <CheckIcon v-if="item.is_active" class="mx-auto" />
                                </td>
                                <td>
                                    {{ dictionary.findTypeOption(item.markup_type).title }}
                                </td>
                                <td
                                    :class="{
                                        'text-success': item.markup_value.HAND > 0,
                                        'text-danger': item.markup_value.HAND < 0,
                                    }"
                                >
                                    {{ item.markup_value.HAND > 0 ? '+' : '' }}{{ item.markup_type === 'FIXED' ? $format.money(item.markup_value.HAND, { withCurrency: true }) : item.markup_value.HAND + '%' }}
                                </td>
                                <td
                                    :class="{
                                        'text-success': item.markup_value.HOLD > 0,
                                        'text-danger': item.markup_value.HOLD < 0,
                                    }"
                                >
                                    {{ item.markup_value.HOLD > 0 ? '+' : '' }}{{ item.markup_type === 'FIXED' ? $format.money(item.markup_value.HOLD, { withCurrency: true }) : item.markup_value.HOLD + '%' }}
                                </td>
                                <td class="w-[200px] whitespace-normal">
                                    {{ item.criteria.depart?.map((value) => value.code).join(', ') }}
                                </td>
                                <td class="w-[200px] whitespace-normal">
                                    {{ item.criteria.dest?.map((value) => value.code).join(', ') }}
                                </td>
                                <td class="w-[200px] whitespace-normal">
                                    {{ item.criteria.validating_carrier?.join(', ') }}
                                </td>
                                <td>
                                    {{ item.start_applying_at ? $format.datetime(new Date(item.start_applying_at)) : '' }}
                                </td>
                                <td>
                                    {{ item.end_applying_at ? $format.datetime(new Date(item.end_applying_at)) : '' }}
                                </td>
                                <td>
                                    {{ item.criteria.cabin_class }}
                                </td>
                                <td>
                                    {{ item.criteria.itinerary_type }}
                                </td>
                                <td>
                                    {{ item.criteria.provider }}
                                </td>
                                <td class="text-center">
                                    <AppButton class="--small --square --danger --soft mx-auto -my-1.5" @click="remove(item.baggage_rule_id)">
                                        <Trash2Icon />
                                    </AppButton>
                                </td>
                            </tr>
                        </template>
                    </AppTable>
                    <template #fallback>
                        <PlaceholderBlock class="w-full h-64" />
                    </template>
                </SuspenseManual>
            </div>
        </div>
        <AppTablePagination class="mt-6 px-6" :pagination="pagination" />
    </div>
</template>

<script setup lang="ts">
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import TableCellLink from '~/components/Table/TableCellLink.vue'
import AppTable from '~/components/Table/AppTable.vue'
import { FrontendPagination } from '~/lib/Model/FrontendPagination'
import type { Mixer } from './../types/Mixer'
import { useMarkupSdk } from './../lib/useMarkupSdk'
import { useRouter } from 'vue-router'
import { $confirmDelete } from '@/plugins/ConfirmPlugin'
import { useMarkupDictionary } from '#/packages/@tmg/markup-tool-frontend/src/lib/useMarkupDictionary'
import { routeToMarkupBaggageCreateRule } from '@/lib/core/helper/RouteNavigationHelper'

defineOptions({
    name: 'MarkupBaggageListTableSection',
})

const router = useRouter()

const id = computed(() => {
    return router.currentRoute.value.params.id
})

//

const columns = useTableColumns({
    id: {
        width: 5,
    },
    active: {
        label: 'Active',
        center: true,
        width: 'min',
    },
    markup_type: {
        label: 'Type',
        width: 14,
    },
    markup_value_hand: {
        label: 'Value HAND',
        width: 14,
    },
    markup_value_hold: {
        label: 'Value HOLD',
        width: 14,
    },
    from: {
        label: 'From',
        width: 'min',
    },
    to: {
        label: 'To',
        width: 'min',
    },
    carrier: {
        label: 'Carrier',
        width: 'min',
    },
    start_applying_at: {
        label: 'Start applying',
        width: 20,
    },
    end_applying_at: {
        label: 'End applying',
        width: 20,
    },
    cabin_class: {
        label: 'Cabin class',
        width: 15,
    },
    itinerary_type: {
        label: 'Itinerary type',
        width: 15,
    },
    provider: {
        label: 'Provider',
        width: 15,
    },
    actions: {
        width: 'min',
    },
})

const items = ref<Mixer.MarkupBaggageRule[]>([])

const markupSdk = useMarkupSdk()
const dictionary = useMarkupDictionary()

const suspense = useSuspensableComponent(async () => {
    const { rows, total_rows_count } = await markupSdk.getBaggageRules({
        limit: pagination.page.size,
        offset: pagination.items.from,
    })

    items.value = rows

    if (paginationItems.value.length !== total_rows_count) {
        paginationItems.value = Array.from({ length: total_rows_count }, (_, i) => i)
    }
})

const paginationItems = ref([])

const pagination = new FrontendPagination(paginationItems, {
    pageSize: 18,
})

function onPageChange() {
    suspense.fetch()
}

onMounted(() => {
    pagination.eventBus.on('pageChange', onPageChange)
})

onUnmounted(() => {
    pagination.eventBus.off('pageChange', onPageChange)
})

async function onMarkupBaggageRuleUpdated({ id, event }: { id: string, event: 'update' | 'insert' | 'delete' }) {
    if (event === 'update') {
        if (items.value.some((item) => item.baggage_rule_id === id)) {
            const item = await markupSdk.getBaggageRule(id)

            const index = items.value.findIndex((item) => item.baggage_rule_id === id)

            items.value.splice(index, 1, item)
        }
    } else if (event === 'insert') {
        await suspense.fetch()
    } else if (event === 'delete') {
        const index = items.value.findIndex((item) => item.baggage_rule_id === id)

        items.value.splice(index, 1)
    }
}

onMounted(() => {
    useService('event').on('markupBaggageRuleUpdated', onMarkupBaggageRuleUpdated)
})

onUnmounted(() => {
    useService('event').off('markupBaggageRuleUpdated', onMarkupBaggageRuleUpdated)
})

//

async function remove(id: string) {
    await $confirmDelete('Are you sure you want to delete this baggage rule?')

    await markupSdk.deleteBaggageRule(id)

    useService('event').emit('markupBaggageRuleUpdated', { id, event: 'delete' } as const)
}
</script>
