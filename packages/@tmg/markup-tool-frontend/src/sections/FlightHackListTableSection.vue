<template>
    <div>
        <div class="card">
            <div class="flex items-center gap-4 w-full p-2">
                <RouterLink :to="routeToCreateFlightHack()">
                    <AppButton class="--small">
                        Create Flight Hack
                        <PlusIcon />
                    </AppButton>
                </RouterLink>

                <Loader v-if="suspense.silentLoading.value" />
            </div>
            <div class="list-table-v2 table-fixed relative">
                <SuspenseManual :state="suspense">
                    <AppTable
                        :items="items"
                        :columns="columns"
                        zebra
                    >
                        <template #body>
                            <tr
                                v-for="(item, index) in items"
                                :key="index"
                            >
                                <TableCellLink :to="routeToFlightHack(item.id)">
                                    <Edit3Icon class="mr-2 icon --small" />
                                    Edit
                                </TableCellLink>
                                <td>
                                    {{ consolidatorDictionary.findTripTypeOption(item.flight_pattern.trip_type).title }}
                                </td>
                                <td class="whitespace-normal">
                                    {{ item.flight_pattern.airline.join(', ') }}
                                </td>
                                <td>
                                    {{ consolidatorDictionary.findBookingTypeOption(item.booking_type).title }}
                                </td>
                                <td class="whitespace-normal">
                                    {{ item.flight_pattern.departure.join(', ') }}
                                </td>
                                <td class="whitespace-normal">
                                    {{ item.flight_pattern.arrival.join(', ') }}
                                </td>
                                <td class="whitespace-normal">
                                    {{ item.flight_pattern.destination.join(', ') }}
                                </td>
                                <td>
                                    {{ item.type }}
                                </td>
                                <component :is="detectFlightHackTableCellComponent(item)" :rule="item" />
                                <td class="text-center">
                                    <AppButton class="--small --square --danger --soft mx-auto -my-1.5" @click="remove(item.id)">
                                        <Trash2Icon />
                                    </AppButton>
                                </td>
                            </tr>
                        </template>
                    </AppTable>
                    <template #fallback>
                        <PlaceholderBlock class="w-full h-64" />
                    </template>
                </SuspenseManual>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import AppTable from '~/components/Table/AppTable.vue'
import { $confirmDelete } from '@/plugins/ConfirmPlugin'
import { useRouter } from 'vue-router'
import { routeToCreateFlightHack, routeToFlightHack } from '@/lib/core/helper/RouteNavigationHelper'
import { useConsolidatorSdk } from '#/packages/@tmg/markup-tool-frontend/src/lib/useConsolidatorSdk'
import type { ConsolidatorTool } from '#/packages/@tmg/markup-tool-frontend/src/types/ConsolidatorTool'
import { useConsolidatorDictionary } from '#/packages/@tmg/markup-tool-frontend/src/lib/useConsolidatorDictionary'
import { detectFlightHackTableCellComponent } from '#/packages/@tmg/markup-tool-frontend/src/lib/FlightHacksHelper'

defineOptions({
    name: 'FlightHackListTableSection',
})

const router = useRouter()
const consolidatorSdk = useConsolidatorSdk()
const consolidatorDictionary = useConsolidatorDictionary()

const id = computed(() => {
    return router.currentRoute.value.params.id
})

const items = ref<ConsolidatorTool.Api.FlightHacksRule.List.Response>([])

const suspense = useSuspensableComponent(async () => {
    items.value = await consolidatorSdk.getFlightHacks()
})

const columns = useTableColumns({
    id: {
        width: 5,
    },
    flight_type: {
        label: 'Flight Type',
    },
    airline: {
        label: 'Airline',
    },
    type_of_booking: {
        label: 'Type of booking',
    },
    departure: {
        label: 'Departure',
    },
    return_arrival: {
        label: 'Return Arrival',
    },
    destination: {
        label: 'Destination',
    },
    product_type: {
        label: 'Product type',
    },
    rule: {
        center: true,
        label: 'Rule',
        width: 'max',
    },
    actions: {
        width: 'auto',
    },
})

async function onFlightHackUpdated({ id, event }: { id: string, event: 'update' | 'insert' | 'delete' }) {
    if (event === 'update') {
        if (items.value.some((item) => item.id === id)) {
            const item = await consolidatorSdk.getFlightHack(id)

            const index = items.value.findIndex((item) => item.id === id)

            items.value.splice(index, 1, item)
        }
    } else if (event === 'insert') {
        await suspense.fetch()
    } else if (event === 'delete') {
        const index = items.value.findIndex((item) => item.id === id)

        items.value.splice(index, 1)
    }
}

onMounted(() => {
    useService('event').on('flightHackUpdated', onFlightHackUpdated)
})

onUnmounted(() => {
    useService('event').off('flightHackUpdated', onFlightHackUpdated)
})

async function remove(id: string) {
    await $confirmDelete('Are you sure you want to delete this rule?')

    await consolidatorSdk.deleteFlightHack(id)

    useService('event').emit('flightHackUpdated', { id, event: 'delete' } as const)
}
</script>
