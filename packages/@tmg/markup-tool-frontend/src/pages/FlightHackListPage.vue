<template>
    <div class="layout__content layout__content--list">
        <div class="flex items-center justify-between gap-4 mb-4">
            <div class="font-bold text-xl">
                Flight Hacks
            </div>
        </div>

        <div class="relative">
            <FlightHackListTableSection />
        </div>
    </div>
</template>

<script setup lang="ts">
import FlightHackListTableSection from '../sections/FlightHackListTableSection.vue'

defineOptions({
    name: 'FlightHackListPage',
})

//

useNewContext('selected')
</script>
