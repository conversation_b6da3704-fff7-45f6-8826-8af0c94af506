<template>
    <AppModalWrapper
        class="!min-w-[1000px] app-modal__container--fixed-body app-modal__container--flex "
        :header="id ? 'Edit Flight Hack' : 'Create Flight Hack'"
        close-button
        outer-button
        stick-to-side
    >
        <form class="p-4 flex flex-col gap-8 h-flex py-4 overflow-auto fancy-scroll" @submit.prevent="submit">
            <div class="grid grid-cols-4 gap-4">
                <FormField
                    :form="form"
                    field="type"
                    label="Type"
                >
                    <InputSelect
                        v-model="form.data.type"
                        :options="consolidatorDictionary.hackTypeOptions"
                        with-empty
                    />
                </FormField>

                <FormField
                    :form="form"
                    field="booking_type"
                    label="Booking Type"
                >
                    <InputSelect
                        v-model="form.data.booking_type"
                        :options="consolidatorDictionary.bookingType"
                        with-empty
                    />
                </FormField>

                <FormField
                    :form="form"
                    class="col-span-2"
                    field="description"
                    label="Description"
                >
                    <InputTextarea v-model="form.data.description" />
                </FormField>
            </div>

            <div class="border-t dark:border-secondary-400 py-2 pt-4 relative grid gap-4">
                <div class="absolute -top-2.5 left-0 flex gap-x-2">
                    <div class="badge --neutral --outline !bg-white">
                        Flight pattern
                    </div>
                </div>

                <div class="grid grid-cols-4 gap-4">
                    <FormField
                        :form="form"
                        field="flight_pattern.trip_type"
                        label="Trip type"
                    >
                        <InputSelect
                            v-model="form.data.flight_pattern.trip_type"
                            :options="consolidatorDictionary.tripTypeOptions"
                            with-empty
                        />
                    </FormField>

                    <div class="col-span-4" />

                    <FormField
                        :form="form"
                        field="flight_pattern.airlines"
                        label="Airlines"
                    >
                        <InputSelect
                            :options="aeroPlacesController.airlineOptions.value"
                            :model-value="undefined"
                            with-empty
                            select-without-close
                            :debounce="200"
                            search
                            @search="aeroPlacesController.searchAirlines($event)"
                            @update:model-value="addFlightPatternAirlines($event)"
                        />

                        <div class="flex flex-wrap gap-1 mt-4">
                            <div
                                v-for="(airline, index) in form.data.flight_pattern.airlines"
                                :key="index"
                                class="w-fit badge --primary flex gap-1 items-center cursor-pointer justify-center select-none"
                                @click="removeFlightPatternAirlines(airline)"
                            >
                                <span>{{ airline }}</span><CloseIcon class="icon --small" />
                            </div>
                        </div>
                    </FormField>

                    <FormField
                        :form="form"
                        field="flight_pattern.departure"
                        label="Departure"
                    >
                        <InputSelect
                            :options="aeroPlacesController.placesFromOptions.value"
                            :model-value="undefined"
                            with-empty
                            select-without-close
                            :debounce="200"
                            search
                            @search="aeroPlacesController.searchDeparture($event)"
                            @update:model-value="addFlightPatternDeparture($event.code)"
                        />
                        <div class="flex flex-wrap gap-1 mt-4">
                            <div
                                v-for="(code, index) in form.data.flight_pattern.departure"
                                :key="index"
                                class="w-fit badge --primary flex gap-1 items-center cursor-pointer justify-center select-none"
                                @click="removeFlightPatternDeparture(code)"
                            >
                                <span>{{ code }}</span><CloseIcon class="icon --small" />
                            </div>
                        </div>
                    </FormField>

                    <FormField
                        :form="form"
                        field="flight_pattern.arrival"
                        label="Arrival"
                    >
                        <InputSelect
                            :options="aeroAirportsController.placesToOptions.value"
                            :model-value="undefined"
                            with-empty
                            select-without-close
                            :debounce="200"
                            search
                            @search="aeroAirportsController.searchDestination($event)"
                            @update:model-value="addFlightPatternArrival($event.code)"
                        />

                        <div class="flex flex-wrap gap-1 mt-4">
                            <div
                                v-for="(code, index) in form.data.flight_pattern.arrival"
                                :key="index"
                                class="w-fit badge --primary flex gap-1 items-center cursor-pointer justify-center select-none"
                                @click="removeFlightPatternArrival(code)"
                            >
                                <span>{{ code }}</span><CloseIcon class="icon --small" />
                            </div>
                        </div>
                    </FormField>

                    <FormField
                        :form="form"
                        field="flight_pattern.destination"
                        label="Destination"
                    >
                        <InputSelect
                            :options="aeroPlacesController.placesToOptions.value"
                            :model-value="undefined"
                            with-empty
                            select-without-close
                            :debounce="200"
                            search
                            @search="aeroPlacesController.searchDestination($event)"
                            @update:model-value="addFlightPatternDestination($event.code)"
                        />

                        <div class="flex flex-wrap gap-1 mt-4">
                            <div
                                v-for="(code, index) in form.data.flight_pattern.destination"
                                :key="index"
                                class="w-fit badge --primary flex gap-1 items-center cursor-pointer justify-center select-none"
                                @click="removeFlightPatternDestination(code)"
                            >
                                <span>{{ code }}</span><CloseIcon class="icon --small" />
                            </div>
                        </div>
                    </FormField>
                </div>
            </div>

            <div class="border-t dark:border-secondary-400 py-2 pt-4 relative grid gap-4">
                <div class="absolute -top-2.5 left-0 flex gap-x-2">
                    <div class="badge --neutral --outline !bg-white">
                        Fake segment
                    </div>
                </div>

                <div class="grid grid-cols-4 gap-4">
                    <div class="flex flex-col gap-4">
                        <FormField
                            :form="form"
                            field="fake_segment.type"
                            label="Type"
                        >
                            <InputSelect
                                v-model="form.data.fake_segment.type"
                                :options="consolidatorDictionary.fakeSegmentType"
                                with-empty
                            />
                        </FormField>
                        <FormField
                            :form="form"
                            field="fake_segment.layover"
                            label="Layover"
                        >
                            <InputSelect
                                v-model="form.data.fake_segment.layover"
                                :options="consolidatorDictionary.layoverType"
                                with-empty
                            />
                        </FormField>
                        <FormField
                            :form="form"
                            field="fake_segment.cabin_type"
                            label="Cabin type"
                        >
                            <InputSelect
                                v-model="form.data.fake_segment.cabin_type"
                                :options="consolidatorDictionary.cabinType"
                                with-empty
                            />
                        </FormField>
                    </div>

                    <FormField
                        :form="form"
                        field="fake_segment.airlines"
                        label="Airlines"
                    >
                        <InputSelect
                            :model-value="undefined"
                            :options="aeroPlacesController.airlineOptions.value"
                            with-empty
                            :debounce="200"
                            search
                            @search="aeroPlacesController.searchAirlines($event)"
                            @update:model-value="addFakeSegmentAirlines($event)"
                        />

                        <div class="flex flex-wrap gap-1 mt-4">
                            <div
                                v-for="(code, index) in form.data.fake_segment.airlines"
                                :key="index"
                                class="w-fit badge --primary flex gap-1 items-center cursor-pointer justify-center select-none"
                                @click="removeFakeSegmentAirlines(code)"
                            >
                                <span>{{ code }}</span><CloseIcon class="icon --small" />
                            </div>
                        </div>
                    </FormField>

                    <FormField
                        :form="form"
                        field="fake_segment.departure"
                        label="Departure"
                    >
                        <InputSelect
                            :options="aeroAirportsController.placesToOptions.value"
                            :model-value="undefined"
                            with-empty
                            select-without-close
                            :debounce="200"
                            search
                            @search="aeroAirportsController.searchDestination($event)"
                            @update:model-value="addFakeSegmentDeparture($event.code)"
                        />

                        <div class="flex flex-wrap gap-1 mt-4">
                            <div
                                v-for="(code, index) in form.data.fake_segment.departure"
                                :key="index"
                                class="w-fit badge --primary flex gap-1 items-center cursor-pointer justify-center select-none"
                                @click="removeFakeSegmentDeparture(code)"
                            >
                                <span>{{ code }}</span><CloseIcon class="icon --small" />
                            </div>
                        </div>
                    </FormField>

                    <FormField
                        :form="form"
                        field="fake_segment.arrival"
                        label="Arrival"
                    >
                        <InputSelect
                            :options="aeroAirportsController.placesToOptions.value"
                            :model-value="undefined"
                            with-empty
                            select-without-close
                            :debounce="200"
                            search
                            @search="aeroAirportsController.searchDestination($event)"
                            @update:model-value="addFakeSegmentArrival($event.code)"
                        />

                        <div class="flex flex-wrap gap-1 mt-4">
                            <div
                                v-for="(code, index) in form.data.fake_segment.arrival"
                                :key="index"
                                class="w-fit badge --primary flex gap-1 items-center cursor-pointer justify-center select-none"
                                @click="removeFakeSegmentArrival(code)"
                            >
                                <span>{{ code }}</span><CloseIcon class="icon --small" />
                            </div>
                        </div>
                    </FormField>
                </div>
            </div>

            <Component
                :is="flightHackPayloadFormComponent"
                ref="currentFlightHackPayloadFormComponent"
                :type="form.data.type"
                :initial-value="flightHack"
            />

            <div class="flex items-center gap-4">
                <AppButton
                    type="submit"
                    class="--success"
                    :loading="form.loading.value"
                >
                    <SaveIcon />
                    {{ id ? 'Update' : 'Create' }}
                </AppButton>
                <AppButton
                    type="button"
                    @click="reset"
                >
                    Discard changes
                </AppButton>
                <AppButton
                    v-if="id"
                    type="button"
                    class="--danger-lite ml-auto"
                    @click="remove"
                >
                    <Trash2Icon />
                    Delete
                </AppButton>
            </div>
        </form>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import { useConsolidatorSdk } from '#/packages/@tmg/markup-tool-frontend/src/lib/useConsolidatorSdk'
import FormField from '~/components/Form/FormField.vue'
import { useConsolidatorDictionary } from '#/packages/@tmg/markup-tool-frontend/src/lib/useConsolidatorDictionary'
import { useAeroPlaces } from '#/packages/@tmg/markup-tool-frontend/src/lib/useAeroPlaces'
import CloseIcon from '@/assets/icons/CloseIcon.svg?component'
import { goTo, goToFlightHackList, routeToFlightHackList } from '@/lib/core/helper/RouteNavigationHelper'
import { $confirmDelete } from '@/plugins/ConfirmPlugin'
import type { ConsolidatorTool } from '#/packages/@tmg/markup-tool-frontend/src/types/ConsolidatorTool'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import Errors from '~/lib/Model/Errors'
import { detectFlightHackPayloadFormComponent } from '#/packages/@tmg/markup-tool-frontend/src/lib/FlightHacksHelper'
import type FlightHackDefaultPayloadForm
    from '#/packages/@tmg/markup-tool-frontend/src/modals/sections/FlightHackDefaultPayloadForm.vue'

defineOptions({
    name: 'FlightHackModal',
    modal: {
        position: 'right',
    },
})

const props = defineProps<{
    id?: string
}>()

const consolidatorSdk = useConsolidatorSdk()
const consolidatorDictionary = useConsolidatorDictionary()
const aeroPlacesController = useAeroPlaces()
const aeroAirportsController = useAeroPlaces(['airport'])

const flightHack = props.id ? await consolidatorSdk.getFlightHack(props.id) : undefined

const form = useForm<{
    type: string,
    booking_type: string,
    description: string | undefined,

    flight_pattern: {
        trip_type: string | undefined
        airlines: string[]
        departure: string[]
        arrival: string[]
        destination: string[]
    },

    fake_segment: {
        type: string | undefined
        layover: string | undefined
        airlines: string[]
        departure: string[]
        arrival: string[]
        cabin_type: string | undefined,
    }

    payload: ConsolidatorTool.FlightHacksRulePayload | undefined

}>({
    type: '',
    booking_type: '',
    description: '',

    flight_pattern: {
        trip_type: undefined,
        airlines: [],
        departure: [],
        arrival: [],
        destination: [],
    },

    fake_segment: {
        type: undefined,
        layover: undefined,
        airlines: [],
        departure: [],
        arrival: [],
        cabin_type: undefined,
    },

    payload: undefined,
}, {
    booking_type: ValidationRules.Required(),
    flight_pattern: {
        trip_type: ValidationRules.Required(),
        airlines: ValidationRules.Required(),
    },

    fake_segment: {
        type: ValidationRules.Required(),
        layover: ValidationRules.Required(),
        airlines: ValidationRules.Required(),
        cabin_type: ValidationRules.Required(),
    },
    payload: (value, field, form) => {
        if (currentFlightHackPayloadFormComponent.value?.form) {
            return currentFlightHackPayloadFormComponent.value.form.validate() ? undefined : 'incorrect payload'
        }
    },
})

// Flight Hack payload
const flightHackPayloadFormComponent = computed(() => {
    return detectFlightHackPayloadFormComponent(form.data.type)
})

const currentFlightHackPayloadFormComponent = ref<InstanceType<typeof FlightHackDefaultPayloadForm>>()

const submit = form.useSubmit(async (data) => {
    let payload = undefined

    if (currentFlightHackPayloadFormComponent.value?.form) {
        payload = { data: currentFlightHackPayloadFormComponent.value.form.data }
    }

    if (flightHack?.id) {
        await consolidatorSdk.updateFlightHack(flightHack.id, {
            type: data.type,
            booking_type: data.booking_type,
            description: data.description,
            flight_pattern: {
                trip_type: data.flight_pattern.trip_type,
                airline: data.flight_pattern.airlines,
                departure: data.flight_pattern.departure,
                arrival: data.flight_pattern.arrival,
                destination: data.flight_pattern.destination,
            },
            fake_segment: {
                type: data.fake_segment.type,
                layover: data.fake_segment.layover,
                airline: data.fake_segment.airlines,
                departure: data.fake_segment.departure,
                arrival: data.fake_segment.arrival,
                cabin_type: data.fake_segment.cabin_type,
            },
            hack_payload: payload,
        })

        await useService('event').emit('flightHackUpdated', { id: flightHack.id, event: 'update' } as const)

        toastSuccess('Flight hack updated')
    } else {
        const { id } = await consolidatorSdk.createFlightHack({
            type: data.type,
            booking_type: data.booking_type,
            description: data.description,
            flight_pattern: {
                trip_type: data.flight_pattern.trip_type,
                airline: data.flight_pattern.airlines,
                departure: data.flight_pattern.departure,
                arrival: data.flight_pattern.arrival,
                destination: data.flight_pattern.destination,
            },
            fake_segment: {
                type: data.fake_segment.type,
                layover: data.fake_segment.layover,
                airline: data.fake_segment.airlines,
                departure: data.fake_segment.departure,
                arrival: data.fake_segment.arrival,
                cabin_type: data.fake_segment.cabin_type,
            },
            hack_payload: payload,
        })

        await useService('event').emit('flightHackUpdated', { id: id!, event: 'insert' } as const)

        await goToFlightHackList()

        toastSuccess('Flight hack created')
    }

    form.updateInitialData()
}, {
    resetOnSuccess: false,
    onError: (error) => {
        let details

        try {
            // @ts-ignore
            details = error.data.errors
        } catch (error) {
            //
        }

        if (details) {
            let errors = new Errors(details)

            if (!props.id) {
                errors = errors.slice('rules_to_create.0')
            }

            form.errors.record(errors.all())
        }
    },
})

if (flightHack) {
    form.updateInitialData({
        type: flightHack.type,
        booking_type: flightHack.booking_type,
        description: flightHack.description ?? undefined,

        flight_pattern: {
            trip_type: flightHack.flight_pattern.trip_type,
            airlines: flightHack.flight_pattern.airline,
            departure: flightHack.flight_pattern.departure,
            arrival: flightHack.flight_pattern.arrival,
            destination: flightHack.flight_pattern.destination,
        },

        fake_segment: {
            type: flightHack.fake_segment.type,
            layover: flightHack.fake_segment.layover,
            airlines: flightHack.fake_segment.airline,
            departure: flightHack.fake_segment.departure,
            arrival: flightHack.fake_segment.arrival,
            cabin_type: flightHack.fake_segment.cabin_type,
        },
        payload: flightHack.hack_payload,
    })
}

const remove = async () => {
    if (!props.id) {
        return
    }

    await $confirmDelete('Are you sure you want to delete this flight hack ?')

    await consolidatorSdk.deleteFlightHack(props.id)

    useService('event').emit('flightHackUpdated', { id: props.id, event: 'delete' } as const)

    await goTo(routeToFlightHackList())
}

// FlightPattern

const addFlightPatternAirlines = (code: string) => {
    if (form.data.flight_pattern.airlines.includes(code)) {
        return
    }

    form.data.flight_pattern.airlines.push(code)
}

const removeFlightPatternAirlines = (code: string) => {
    form.data.flight_pattern.airlines = form.data.flight_pattern.airlines.filter((airline) => airline !== code)
}

//

const addFlightPatternDeparture = (code: string) => {
    if (form.data.flight_pattern.departure.includes(code)) {
        return
    }
    form.data.flight_pattern.departure.push(code)
}

const removeFlightPatternDeparture = (code: string) => {
    form.data.flight_pattern.departure = form.data.flight_pattern.departure.filter((formCode) => formCode !== code)
}

//

const addFlightPatternArrival = (code: string) => {
    if (form.data.flight_pattern.arrival.includes(code)) {
        return
    }
    form.data.flight_pattern.arrival.push(code)
}

const removeFlightPatternArrival = (code: string) => {
    form.data.flight_pattern.arrival = form.data.flight_pattern.arrival.filter((formCode) => formCode !== code)
}

const addFlightPatternDestination = (code: string) => {
    if (form.data.flight_pattern.destination.includes(code)) {
        return
    }
    form.data.flight_pattern.destination.push(code)
}

const removeFlightPatternDestination = (code: string) => {
    form.data.flight_pattern.destination = form.data.flight_pattern.destination.filter((formCode) => formCode !== code)
}

// FakeSegment

const addFakeSegmentAirlines = (code: string) => {
    if (form.data.fake_segment.airlines.includes(code)) {
        return
    }

    form.data.fake_segment.airlines.push(code)
}

const removeFakeSegmentAirlines = (code: string) => {
    form.data.fake_segment.airlines = form.data.fake_segment.airlines.filter((formCode) => formCode !== code)
}

//

const addFakeSegmentDeparture = (code: string) => {
    if (form.data.fake_segment.departure.includes(code)) {
        return
    }

    form.data.fake_segment.departure.push(code)
}

const removeFakeSegmentDeparture = (code: string) => {
    form.data.fake_segment.departure = form.data.fake_segment.departure.filter((formCode) => formCode !== code)
}

//

const addFakeSegmentArrival = (code: string) => {
    if (form.data.fake_segment.arrival.includes(code)) {
        return
    }

    form.data.fake_segment.arrival.push(code)
}

const removeFakeSegmentArrival = (code: string) => {
    form.data.fake_segment.arrival = form.data.fake_segment.arrival.filter((formCode) => formCode !== code)
}

//

const reset = () => {
    form.reset()

    if (currentFlightHackPayloadFormComponent.value) {
        currentFlightHackPayloadFormComponent.value.form.reset()
    }
}
</script>

<script lang="ts">
declare global {
    interface ApplicationEvents {
        flightHackUpdated(data: { id: string, event: 'update' | 'insert' | 'delete' }): void
    }
}
</script>
