<template>
    <div class="border-t dark:border-secondary-400 py-2 pt-4 relative grid gap-4">
        <div class="absolute -top-2.5 left-0 flex gap-x-2">
            <div class="badge --neutral --outline !bg-white">
                {{ consolidatorDictionary.findTypeOption(type).title }}
            </div>
        </div>
        <FormField
            :form="form"
            field="title"
            label="Title"
        >
            <InputText v-model="form.data.title" />
        </FormField>
    </div>
</template>

<script setup lang="ts">
import FormField from '~/components/Form/FormField.vue'
import type { ConsolidatorTool } from '#/packages/@tmg/markup-tool-frontend/src/types/ConsolidatorTool'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import { useConsolidatorDictionary } from '#/packages/@tmg/markup-tool-frontend/src/lib/useConsolidatorDictionary'

const props = defineProps<{
    type: ConsolidatorTool.HackType
    initialValue?: ConsolidatorTool.FlightHacksRule
}>()

const consolidatorDictionary = useConsolidatorDictionary()

const form = useForm<{
    title: string
}>({
    title: '',
}, {
    title: ValidationRules.Required(),
})

if (props.initialValue) {
    form.updateInitialData({
        title: props.initialValue.type,
    })
}

defineExpose({
    form,
})
</script>
