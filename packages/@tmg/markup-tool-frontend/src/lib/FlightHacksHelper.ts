import type { ConsolidatorTool } from '#/packages/@tmg/markup-tool-frontend/src/types/ConsolidatorTool'
import Default from '#/packages/@tmg/markup-tool-frontend/src/sections/RuleAdditionalRenderByType/Default.vue'
import FlightHackDefaultPayloadForm
    from '#/packages/@tmg/markup-tool-frontend/src/modals/sections/FlightHackDefaultPayloadForm.vue'

export function detectFlightHackTableCellComponent(flightHack: ConsolidatorTool.FlightHacksRule) {
    switch (flightHack.type) {
        case 'ELR':
        case 'FRT':
        case 'OW_REV_OW_MLG':
        case 'OW_MLG_OW_REV':
        case 'OW_REV_FRT_OW_MLG':
        case 'OW_MLG_OW_REV_FRT':
        case 'OW_REV_FRT_OW_REV_FRT':
        case 'ORIGIN_OPEN_JAW':
        case 'TWO_ONE_WAYS':
        default:
            return Default
    }
}

export function detectFlightHackPayloadFormComponent(type: ConsolidatorTool.HackType) {
    switch (type) {
        case 'ELR':
        case 'FRT':
        case 'OW_REV_OW_MLG':
        case 'OW_MLG_OW_REV':
        case 'OW_REV_FRT_OW_MLG':
        case 'OW_MLG_OW_REV_FRT':
        case 'OW_REV_FRT_OW_REV_FRT':
        case 'ORIGIN_OPEN_JAW':
        case 'TWO_ONE_WAYS':
        default:
            return undefined
    }
}
