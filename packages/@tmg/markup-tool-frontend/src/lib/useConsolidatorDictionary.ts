import type SelectOption from '~types/structures/SelectOption'
import type { ConsolidatorTool } from '#/packages/@tmg/markup-tool-frontend/src/types/ConsolidatorTool'

export function useConsolidatorDictionary() {
    const hackTypeOptions: SelectOption<ConsolidatorTool.HackType>[] = [
        {
            title: 'ELR',
            value: 'ELR',
        },
        {
            title: 'FRT',
            value: 'FRT',
        },
        {
            title: 'OW_REV_OW_MLG',
            value: 'OW_REV_OW_MLG',
        },
        {
            title: 'OW_MLG_OW_REV',
            value: 'OW_MLG_OW_REV',
        },
        {
            title: 'OW_REV_FRT_OW_MLG',
            value: 'OW_REV_FRT_OW_MLG',
        },
        {
            title: 'OW_MLG_OW_REV_FRT',
            value: 'OW_MLG_OW_REV_FRT',
        },
        {
            title: 'OW_REV_FRT_OW_REV_FRT',
            value: 'OW_REV_FRT_OW_REV_FRT',
        },
        {
            title: 'ORIGIN_OPEN_JAW',
            value: 'ORIGIN_OPEN_JAW',
        },
        {
            title: 'TWO_ONE_WAYS',
            value: 'TWO_ONE_WAYS',
        },
    ]

    const tripTypeOptions: SelectOption<ConsolidatorTool.TripType>[] = [
        {
            title: 'One way',
            value: 'one_way',
        },
        {
            title: 'Round trip',
            value: 'round_trip',
        },
        {
            title: 'Any',
            value: 'any',
        },
        {
            title: 'Eastbound',
            value: 'eastbound',
        },
        {
            title: 'Westbound',
            value: 'westbound',
        },
    ]

    const regionsType: SelectOption<ConsolidatorTool.RegionsType>[] = [
        {
            title: 'World',
            value: 'world',
        },
        {
            title: 'EMEA',
            value: 'europe_india_middle_east_africa',
        },
        {
            title: 'India',
            value: 'india',
        },
        {
            title: 'Airports',
            value: 'airports',
        },
    ]

    const fakeSegmentType: SelectOption<ConsolidatorTool.FakeSegmentType>[] = [
        {
            title: 'From same destination city',
            value: 'from_same_destination_city',
        },
        {
            title: 'From same destination airport',
            value: 'from_same_destination_airport',
        },
        {
            title: 'To airport',
            value: 'to_airport',
        },
        {
            title: 'Route',
            value: 'route',
        },
        {
            title: 'Not applicable',
            value: 'not_applicable',
        },
    ]

    const layoverType: SelectOption<ConsolidatorTool.LayoverType>[] = [
        {
            title: 'Any time',
            value: 'any_time',
        },
        {
            title: 'Next day less 24 hours',
            value: 'next_day_less_24_hours',
        },
        {
            title: 'After 7 days minimum',
            value: 'after_7_days_minimum',
        },
    ]

    const bookingType: SelectOption<ConsolidatorTool.BookingType>[] = [
        {
            title: 'Revenue',
            value: 'revenue',
        },
        {
            title: 'Mileage',
            value: 'mileage',
        },
        {
            title: 'Mixed',
            value: 'mixed',
        },
    ]

    const cabinType: SelectOption<ConsolidatorTool.CabinType>[] = [
        {
            title: 'Eco',
            value: 'eco',
        },
        {
            title: 'Pre',
            value: 'pre',
        },
        {
            title: 'Biz',
            value: 'biz',
        },
        {
            title: 'Same or higher',
            value: 'same_or_higher',
        },
        {
            title: 'Any',
            value: 'any',
        },
    ]

    function findTypeOption(value: ConsolidatorTool.HackType) {
        return hackTypeOptions.find(option => option.value === value)!
    }

    function findTripTypeOption(value: ConsolidatorTool.TripType) {
        return tripTypeOptions.find(option => option.value === value)!
    }

    function findRegionsTypeOption(value: ConsolidatorTool.RegionsType) {
        return regionsType.find(option => option.value === value)!
    }

    function findFakeSegmentTypeOption(value: ConsolidatorTool.FakeSegmentType) {
        return fakeSegmentType.find(option => option.value === value)!
    }

    function findLayoverTypeOption(value: ConsolidatorTool.LayoverType) {
        return layoverType.find(option => option.value === value)!
    }

    function findBookingTypeOption(value: ConsolidatorTool.BookingType) {
        return bookingType.find(option => option.value === value)!
    }

    function findCabinTypeOption(value: ConsolidatorTool.CabinType) {
        return cabinType.find(option => option.value === value)!
    }

    return {
        hackTypeOptions,
        tripTypeOptions,
        regionsType,
        fakeSegmentType,
        layoverType,
        bookingType,
        cabinType,
        findTypeOption,
        findTripTypeOption,
        findRegionsTypeOption,
        findFakeSegmentTypeOption,
        findLayoverTypeOption,
        findBookingTypeOption,
        findCabinTypeOption,
    }
}
