import type { FetchOptions } from 'ofetch'
import type { ConsolidatorTool } from '#/packages/@tmg/markup-tool-frontend/src/types/ConsolidatorTool'

export function useConsolidatorSdk() {
    const callAction = useContext().useModel('ConsolidatorArea').actions.consolidatorRequest

    const fetch = <TResponse>(url: string, options: FetchOptions = {}, method?: string) => {
        return callAction({
            url,
            options,
        }) as Promise<TResponse>
    }

    async function getFlightHacks() {
        return await fetch<ConsolidatorTool.Api.FlightHacksRule.List.Response>('/api/flight-hacks-rule')
    }

    async function getFlightHack(id: string) {
        return await fetch<ConsolidatorTool.FlightHacksRule>('/api/flight-hacks-rule/' + id)
    }

    async function createFlightHack(data: ConsolidatorTool.Api.FlightHacksRule.Create.Payload) {
        return await fetch<ConsolidatorTool.Api.FlightHacksRule.Create.Response>('/api/flight-hacks-rule/', {
            method: 'POST',
            body: data,
        })
    }

    async function updateFlightHack(id: string, data: ConsolidatorTool.Api.FlightHacksRule.Update.Payload) {
        return await fetch<ConsolidatorTool.Api.FlightHacksRule.Update.Response>('/api/flight-hacks-rule/' + id, {
            method: 'PUT',
            body: data,
        })
    }

    async function deleteFlightHack(id: string) {
        return await fetch<void>('/api/flight-hacks-rule/' + id, {
            method: 'DELETE',
        })
    }

    return {
        getFlightHacks,
        getFlightHack,
        createFlightHack,
        updateFlightHack,
        deleteFlightHack,
    }
}
