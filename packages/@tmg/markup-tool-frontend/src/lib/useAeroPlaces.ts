import type { Aero } from '#/packages/@tmg/markup-tool-frontend/src/types/Aero'
import { useAeroDictionary } from '#/packages/@tmg/markup-tool-frontend/src/lib/useAeroDictionary'
import { useMarkupDictionary } from '#/packages/@tmg/markup-tool-frontend/src/lib/useMarkupDictionary'
import { useAeroData } from '#/packages/@tmg/markup-tool-frontend/src/lib/useAeroData'

export function useAeroPlaces(usedTypeOfPlace: Aero.TypeOfPlace[] = ['airport', 'city', 'country', 'region']) {
    const aeroData = useAeroData()

    const aeroDictionary = useAeroDictionary()
    const markupDictionary = useMarkupDictionary()

    //

    const placesListFrom = ref<Aero.SuggestionsPlace[]>([])
    const placesListTo = ref<Aero.SuggestionsPlace[]>([])

    const placesFromOptions = computed(() => placesMapperForSelect(placesListFrom.value))
    const placesToOptions = computed(() => placesMapperForSelect(placesListTo.value))

    const placesMapperForSelect = (list: Aero.SuggestionsPlace[]) => {
        return list.map((place) => {
            const subtitle = []

            subtitle.push(aeroDictionary.findPlaceTypeOptions(place.type)?.title)

            if (place.city?.name) {
                subtitle.push(place.city.name)
            }

            if (place.country?.name) {
                subtitle.push(place.country.name)
            }

            return {
                title: place.name,
                subtitle: subtitle.join(', '),
                value: {
                    type: markupDictionary.findPointTypeOptionByAeroValue(place.type)?.value,
                    code: place.code,
                },
            }
        })
    }

    const searchDestination = async (query: string) => {
        placesListTo.value = await aeroData.searchPlaces(query, usedTypeOfPlace)
    }

    const searchDeparture = async (query: string) => {
        placesListFrom.value = await aeroData.searchPlaces(query, usedTypeOfPlace)
    }

    //

    const airlineList = ref<Aero.Airline[]>([])

    const airlineOptions = computed(() => airlineMapperForSelect(airlineList.value))

    const airlineMapperForSelect = (list: Aero.Airline[]) => {
        return list.map((airline) => {
            return {
                title: airline.name,
                value: airline.code,
            }
        })
    }

    const searchAirlines = async (query: string) => {
        airlineList.value = await aeroData.searchAirlines(query)
    }

    return {
        placesListFrom,
        placesListTo,

        placesFromOptions,
        placesToOptions,

        searchDestination,
        searchDeparture,

        airlineList,
        airlineOptions,
        searchAirlines,
    }
}
