{"name": "@tmg/markup-tool-frontend", "version": "0.0.0", "private": true, "exports": {"./routes": "./src/routes.ts", "./modals/*": "./src/modals/*"}, "scripts": {"create-types:mixer": "openapi-typescript https://mixer.stage.tmgclick.com/api-docs-json -o ./src/types/vendor/mixer.ts", "create-types:aero": "openapi-typescript https://aero.stage.tmgclick.com/docs/api-docs.json -o ./src/types/vendor/aero.ts", "create-types:cmt": "openapi-typescript https://cmt.test.tmgclick.com/docs/api-docs.json -o ./src/types/vendor/cmt.ts"}, "devDependencies": {"openapi-typescript": "^7.3.0"}, "dependencies": {}}