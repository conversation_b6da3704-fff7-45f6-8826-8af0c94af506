import type { components, operations } from './vendor/aot'

type AwardOffersTool = components['schemas']

export namespace AwardOffersTool {
    export type Itinerary = AwardOffersTool['ProviderItineraryDto']
    export type Part = AwardOffersTool['ProviderItineraryPartDto']

    export namespace Api {
        export namespace AwardOffers {
            export namespace Search {
                export type Response = operations['flights-search_flights']['responses']['200']['content']['application/json']
                export type Payload = operations['flights-search_flights']['requestBody']['content']['application/json']
            }
        }
    }
}
