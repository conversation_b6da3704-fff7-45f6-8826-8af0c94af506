/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    '/search': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Search Flights
         * @description Search for flights.
         *
         *     Args:
         *         session: Database session
         *         request: FastAPI request object
         *         current_user: Authenticated user (via JWT or legacy auth)
         *         query: Search query parameters containing cabin classes and other criteria
         *         crawler_service: Crawler service instance
         *
         *     Returns:
         *         Search results matching the query criteria
         */
        post: operations['flights-search_flights'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/search-airfrance': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Search Flights Airfrance
         * @description Search for flights on Air France.
         */
        post: operations['flights-search_flights_airfrance'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/v1/browser-profiles/': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Browser Profiles
         * @description List browser profiles with optional filtering.
         */
        get: operations['browser-profiles-list_browser_profiles'];
        put?: never;
        /**
         * Create Browser Profile
         * @description Create new browser profile.
         */
        post: operations['browser-profiles-create_browser_profile'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/v1/browser-profiles/available': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Available Profile
         * @description Get an available browser profile.
         */
        get: operations['browser-profiles-get_available_profile'];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/v1/browser-profiles/{profile_id}': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Browser Profile
         * @description Get browser profile by ID.
         */
        get: operations['browser-profiles-get_browser_profile'];
        /**
         * Update Browser Profile
         * @description Update browser profile.
         */
        put: operations['browser-profiles-update_browser_profile'];
        post?: never;
        /**
         * Delete Browser Profile
         * @description Delete browser profile.
         */
        delete: operations['browser-profiles-delete_browser_profile'];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/v1/browser-profiles/{profile_id}/block': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Block Browser Profile
         * @description Mark browser profile as blocked.
         */
        post: operations['browser-profiles-block_browser_profile'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/v1/browser-profiles/{profile_id}/unblock': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Unblock Browser Profile
         * @description Mark browser profile as unblocked.
         */
        post: operations['browser-profiles-unblock_browser_profile'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/v1/browser-profiles/register-aircanada-account': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Register Aircanada Account
         * @description Register a new Air Canada account (admin only).
         */
        post: operations['browser-profiles-register_aircanada_account'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/v1/private/users/': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create User
         * @description Create a new user.
         */
        post: operations['private-create_user'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/v1/login/access-token': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Login Access Token
         * @description OAuth2 compatible token login, get an access token for future requests
         */
        post: operations['login-login_access_token'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/v1/login/test-token': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Test Token
         * @description Test access token
         */
        post: operations['login-test_token'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/v1/password-recovery/{email}': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Recover Password
         * @description Password Recovery
         */
        post: operations['login-recover_password'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/v1/reset-password/': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Reset Password
         * @description Reset password
         */
        post: operations['login-reset_password'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/v1/password-recovery-html-content/{email}': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Recover Password Html Content
         * @description HTML Content for Password Recovery
         */
        post: operations['login-recover_password_html_content'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/v1/users/': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Users
         * @description Retrieve users.
         */
        get: operations['users-read_users'];
        put?: never;
        /**
         * Create User
         * @description Create new user.
         */
        post: operations['users-create_user'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/v1/users/me': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read User Me
         * @description Get current user.
         */
        get: operations['users-read_user_me'];
        put?: never;
        post?: never;
        /**
         * Delete User Me
         * @description Delete own user.
         */
        delete: operations['users-delete_user_me'];
        options?: never;
        head?: never;
        /**
         * Update User Me
         * @description Update own user.
         */
        patch: operations['users-update_user_me'];
        trace?: never;
    };
    '/api/v1/users/me/password': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        /**
         * Update Password Me
         * @description Update own password.
         */
        patch: operations['users-update_password_me'];
        trace?: never;
    };
    '/api/v1/users/signup': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Register User
         * @description Create new user without the need to be logged in.
         */
        post: operations['users-register_user'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/v1/users/{user_id}': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read User By Id
         * @description Get a specific user by id.
         */
        get: operations['users-read_user_by_id'];
        put?: never;
        post?: never;
        /**
         * Delete User
         * @description Delete a user.
         */
        delete: operations['users-delete_user'];
        options?: never;
        head?: never;
        /**
         * Update User
         * @description Update a user.
         */
        patch: operations['users-update_user'];
        trace?: never;
    };
    '/api/v1/utils/test-email/': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Test Email
         * @description Test emails.
         */
        post: operations['utils-test_email'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/v1/utils/health-check/': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Health Check */
        get: operations['utils-health_check'];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/v1/utils/transcribe-audio/': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Transcribe Audio
         * @description Transcribe an uploaded audio file using Wit.ai.
         */
        post: operations['utils-transcribe_audio'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/v1/utils/classify-audio/': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Classify Audio
         * @description Classify segments of an uploaded audio file using SoundFinder (YAMNet) and answer the question using LLM.
         */
        post: operations['utils-classify_audio'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        /** BaggageCountByTypeMapDto */
        BaggageCountByTypeMapDto: {
            /**
             * Personal
             * @description Personal baggage.
             */
            PERSONAL?: number | null;
            /**
             * Hand
             * @description Hand baggage.
             */
            HAND?: number | null;
            /**
             * Hold
             * @description Hold baggage.
             */
            HOLD?: number | null;
        };
        /** BaggageCountDto */
        BaggageCountDto: {
            /** @description Contains the number of included (free) baggage. */
            included: components['schemas']['BaggageCountByTypeMapDto'];
        };
        /** Body_login-login_access_token */
        'Body_login-login_access_token': {
            /** Grant Type */
            grant_type?: string | null;
            /** Username */
            username: string;
            /** Password */
            password: string;
            /**
             * Scope
             * @default
             */
            scope: string;
            /** Client Id */
            client_id?: string | null;
            /** Client Secret */
            client_secret?: string | null;
        };
        /** Body_utils-classify_audio */
        'Body_utils-classify_audio': {
            /**
             * Audio File
             * Format: binary
             */
            audio_file: string;
            /** Question */
            question: string;
        };
        /** Body_utils-transcribe_audio */
        'Body_utils-transcribe_audio': {
            /**
             * Audio File
             * Format: binary
             */
            audio_file: string;
        };
        /**
         * BrowserProfile
         * @description Schema for browser profile.
         */
        BrowserProfile: {
            /** Name */
            name?: string | null;
            /** Ac Username */
            ac_username?: string | null;
            /** Ac Password */
            ac_password?: string | null;
            /** @default none */
            proxy_type: components['schemas']['ProxyType'];
            /**
             * Proxy Config
             * @description Proxy-specific configuration (host, port, session_id, etc.)
             */
            proxy_config?: Record<string, never> | null;
            /**
             * Is Blocked
             * @default false
             */
            is_blocked: boolean;
            /** Id */
            id: number;
            /** Last Used */
            last_used?: string | null;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
        };
        /**
         * BrowserProfileCreate
         * @description Schema for creating a browser profile.
         */
        BrowserProfileCreate: {
            /** Name */
            name?: string | null;
            /** Ac Username */
            ac_username?: string | null;
            /** Ac Password */
            ac_password?: string | null;
            /** @default none */
            proxy_type: components['schemas']['ProxyType'];
            /**
             * Proxy Config
             * @description Proxy-specific configuration (host, port, session_id, etc.)
             */
            proxy_config?: Record<string, never> | null;
            /**
             * Is Blocked
             * @default false
             */
            is_blocked: boolean;
        };
        /**
         * BrowserProfileUpdate
         * @description Schema for updating a browser profile.
         */
        BrowserProfileUpdate: {
            /** Name */
            name?: string | null;
            /** Ac Username */
            ac_username?: string | null;
            /** Ac Password */
            ac_password?: string | null;
            proxy_type?: components['schemas']['ProxyType'] | null;
            /** Proxy Config */
            proxy_config?: Record<string, never> | null;
            /** Is Blocked */
            is_blocked?: boolean | null;
        };
        /**
         * CabinClass
         * @description Cabin class.
         * @enum {string}
         */
        CabinClass: 'ECONOMY' | 'PREMIUM_ECONOMY' | 'BUSINESS' | 'PREMIUM_BUSINESS' | 'FIRST' | 'PREMIUM_FIRST';
        /** CarrierDescDto */
        CarrierDescDto: {
            /**
             * Code
             * @description Carrier code.
             */
            code: string;
            /**
             * Flight Number
             * @description Flight number.
             */
            flight_number: number;
        };
        /** CarrierDescNullableDto */
        CarrierDescNullableDto: {
            /**
             * Code
             * @description Carrier code.
             */
            code: string | null;
            /**
             * Flight Number
             * @description Flight number.
             */
            flight_number: number | null;
        };
        /** CarrierDto */
        CarrierDto: {
            /** @description Validating carrier (marketing). */
            validating: components['schemas']['CarrierDescDto'];
            /** @description Operating carrier. */
            operating: components['schemas']['CarrierDescNullableDto'];
        };
        /** FlightDurationDto */
        FlightDurationDto: {
            /**
             * Flight Time
             * @description Flight time (seconds).
             */
            flight_time: number;
            /**
             * Waiting Time
             * @description Waiting time (seconds).
             */
            waiting_time: number;
            /**
             * Total
             * @description Calculates total time (seconds).
             */
            readonly total: number;
        };
        /** HTTPValidationError */
        HTTPValidationError: {
            /** Detail */
            detail?: components['schemas']['ValidationError'][];
        };
        /** HiddenStopDto */
        HiddenStopDto: {
            /**
             * Airport Code
             * @description Location IATA code.
             */
            airport_code: string;
            /**
             * City Code
             * @description The city IATA code.
             */
            city_code?: string | null;
            /**
             * Country Code
             * @description The country code (ISO 3166-1 alpha-2 format).
             */
            country_code?: string | null;
            /**
             * State Code
             * @description The state code (alpha-2 format).
             */
            state_code?: string | null;
            /**
             * Arrival Time
             * @description Arrival local time.
             */
            arrival_time: string | null;
            /**
             * Depart Time
             * @description Departure local time.
             */
            depart_time: string | null;
            /**
             * Vehicle Code
             * @description Vehicle code.
             */
            vehicle_code: string | null;
            vehicle_type?: components['schemas']['VehicleType'] | null;
            /** @description Duration info of the hidden stop. */
            duration: components['schemas']['FlightDurationDto'];
            /**
             * Flown Miles
             * @description Flown miles.
             */
            flown_miles?: number | null;
        };
        /**
         * ItinerarySortType
         * @description Sorting.
         * @enum {string}
         */
        ItinerarySortType: 'BEST' | 'FASTEST' | 'CHEAPEST';
        /**
         * ItineraryType
         * @description Itinerary type.
         * @enum {string}
         */
        ItineraryType: 'ONEWAY' | 'RETURN' | 'MULTICITY';
        /** Message */
        Message: {
            /** Message */
            message: string;
        };
        /** MoneyDto */
        MoneyDto: {
            /**
             * Amount
             * @description Amount of money.
             */
            amount: number;
            /**
             * Currency
             * @description The currency (ISO 4217).
             */
            currency: string;
        };
        /** NewPassword */
        NewPassword: {
            /** Token */
            token: string;
            /** New Password */
            new_password: string;
        };
        /** PartPreference */
        PartPreference: {
            /**
             * Part Idx
             * @description Part index.
             */
            part_idx: number;
            /** @description Cabin preference. */
            cabin_class: components['schemas']['CabinClass'];
        };
        /** PrivateUserCreate */
        PrivateUserCreate: {
            /** Email */
            email: string;
            /** Password */
            password: string;
            /** Full Name */
            full_name: string;
            /**
             * Is Verified
             * @default false
             */
            is_verified: boolean;
        };
        /** ProviderAdditionalPricingInfoDto */
        ProviderAdditionalPricingInfoDto: {
            /** @description Total fare related to the entire itinerary. */
            total_fare: components['schemas']['ProviderPricingInfoTotalFareDto'];
            /** @description The fare related to each passenger type in part. */
            pax_fares: components['schemas']['ProviderPricingInfoPaxFareMapDto'];
            /**
             * Total Price Diff
             * @description The total price difference between the main offer price and actual additional price.
             */
            total_price_diff: number;
        };
        /** ProviderAdditionalPricingInfoMapDto */
        ProviderAdditionalPricingInfoMapDto: {
            /** @description Regular pricing details. Will be set when the main offer price is an exclusive price. (ex.: a snap code is applied) and the regular price is found. */
            regular_pricing?: components['schemas']['ProviderAdditionalPricingInfoDto'] | null;
        };
        /** ProviderItineraryDto */
        ProviderItineraryDto: {
            /**
             * Cmp Id
             * @description Internal itinerary comparison ID (can change over time).
             */
            cmp_id?: string | null;
            type: components['schemas']['ItineraryType'];
            /**
             * Parts
             * @description Itinerary parts.
             */
            parts: components['schemas']['ProviderItineraryPartDto'][];
            /**
             * Throw Away Ticketing
             * @description There are cases where the return flight is cheaper than the one-way flight. In these cases, we offer a one-way flight with a "hidden" return flight.
             */
            throw_away_ticketing: boolean;
            /**
             * Hidden City Ticketing
             * @description There are cases when an itinerary with more legs is cheaper than the itineraries with less legs. In these cases, we hide the last legs from the itinerary and return only the needed legs.
             */
            hidden_city_ticketing: boolean;
            /**
             * Virtual Interlining
             * @description True if the route is provided by airlines that do not cooperate together.
             */
            virtual_interlining: boolean;
            /**
             * Min Availability
             * @description Minimum number of available seats from all parts.
             */
            min_availability: number | null;
            /** @description Pricing information. */
            pricing_info: components['schemas']['ProviderPricingInfoDto'];
            /** @description Count the number of baggage. */
            baggage_count: components['schemas']['BaggageCountDto'];
            /**
             * Id
             * @description Itinerary id.
             */
            id: string;
            /**
             * Pnr Count
             * @description Calculates the number of PNRs (tickets) based on the number of parts.
             */
            readonly pnr_count: number;
            /**
             * Airlines
             * @description Returns a unique list of airlines from all itinerary parts.
             */
            readonly airlines: string[];
            /**
             * Max Stops
             * @description Returns the maximum number of stops among all parts.
             */
            readonly max_stops: number;
            /** @description Calculates total itinerary duration by summing all part durations. */
            readonly duration: components['schemas']['FlightDurationDto'];
        };
        /** ProviderItineraryPartDto */
        ProviderItineraryPartDto: {
            /**
             * Segments
             * @description Part segments.
             */
            segments: components['schemas']['ProviderItinerarySegmentDto'][];
            /** @description Number of stops by type. */
            stops_count: components['schemas']['StopsCountDto'];
            /** @description Returns a major cabin from all segments. */
            readonly cabin: components['schemas']['CabinClass'];
            /**
             * Airlines
             * @description Returns a unique list of airlines from all segments.
             */
            readonly airlines: string[];
            /** @description Calculates total part duration by summing all segment durations. */
            readonly duration: components['schemas']['FlightDurationDto'];
        };
        /** ProviderItinerarySegmentDto */
        ProviderItinerarySegmentDto: {
            /** @description Departure point. */
            depart: components['schemas']['SegmentPointDto'];
            /** @description Arrival point. */
            arrival: components['schemas']['SegmentPointDto'];
            /** @description Carrier details. */
            carrier: components['schemas']['CarrierDto'];
            /** @description Segment duration.<br />Waiting time is calculated from the next segment (layover time).<br />Note: Flight time includes hidden stops. */
            duration: components['schemas']['FlightDurationDto'];
            /** @description Segment vehicle details. */
            vehicle: components['schemas']['VehicleDto'];
            cabin_class: components['schemas']['CabinClass'];
            /**
             * Hidden Stops
             * @description Hidden (technical) stops.
             */
            hidden_stops: components['schemas']['HiddenStopDto'][];
            /**
             * Bags Recheck Required
             * @description If true, then the pax needs to recheck the baggage.
             */
            bags_recheck_required: boolean;
            /**
             * Vi Connection
             * @description Information whether the connection between two segments is virtually interlined.
             */
            vi_connection: boolean;
            /**
             * Guarantee
             * @description Identifies if the segment is covered by guarantee.
             */
            guarantee: boolean | null;
            /**
             * Flown Miles
             * @description Flown miles.
             */
            flown_miles?: number | null;
        };
        /** ProviderPricingInfoDto */
        ProviderPricingInfoDto: {
            /** @description Total fare related to the entire itinerary. */
            total_fare: components['schemas']['ProviderPricingInfoTotalFareDto'];
            /** @description The fare related to each passenger type in part. */
            pax_fares: components['schemas']['ProviderPricingInfoPaxFareMapDto'];
            /**
             * Pcc
             * @description PCC (if available).
             */
            pcc?: string | null;
            /**
             * Validating Carrier Code
             * @description Validating carrier code.
             */
            validating_carrier_code?: string | null;
            /** @description Additional pricing infos. */
            additional_pricing_infos?: components['schemas']['ProviderAdditionalPricingInfoMapDto'] | null;
        };
        /** ProviderPricingInfoMilesDto */
        ProviderPricingInfoMilesDto: {
            /**
             * Amount
             * @description Amount of miles.
             */
            amount: number;
        };
        /** ProviderPricingInfoPaxCarrierCommissionDto */
        ProviderPricingInfoPaxCarrierCommissionDto: {
            /**
             * Validating Carrier
             * @description Validating carrier code.
             */
            validating_carrier?: string | null;
            /**
             * Commission Amount
             * @description Commission amount.
             */
            commission_amount: number;
            /**
             * Earned Commission Amount
             * @description Earned commission amount.
             */
            earned_commission_amount?: number | null;
            /**
             * Commission Contract Qualifier
             * @description Commission contract qualifier.
             */
            commission_contract_qualifier?: string | null;
            /**
             * Source Pcc
             * @description Source PCC.
             */
            source_pcc?: string | null;
            /**
             * Commission Percent
             * @description Commission percent.
             */
            commission_percent?: number | null;
        };
        /** ProviderPricingInfoPaxFareFlightAmenitiesDto */
        ProviderPricingInfoPaxFareFlightAmenitiesDto: {
            /** @description Seat details. */
            seat?: components['schemas']['ProviderPricingInfoSeatAmenityDto'] | null;
        };
        /** ProviderPricingInfoPaxFareItemDto */
        ProviderPricingInfoPaxFareItemDto: {
            /**
             * Pax Count
             * @description Number of passengers.
             */
            pax_count: number;
            /**
             * Provider Pax Type
             * @description The provider passenger type.
             */
            provider_pax_type?: string | null;
            /** @description Total fare related to the passenger type. */
            total_fare: components['schemas']['ProviderPricingInfoTotalFareDto'];
            /**
             * Fare Components
             * @description Fare components.
             */
            fare_components?: components['schemas']['ProviderPricingInfoPaxPartFareComponentDto'][] | null;
            /**
             * Validating Carrier Commissions
             * @description Carrier commissions.
             */
            validating_carrier_commissions?: components['schemas']['ProviderPricingInfoPaxCarrierCommissionDto'][] | null;
            /** @description Selected commission. */
            selected_commission?: components['schemas']['ProviderPricingInfoPaxFareSelectedCommissionDto'] | null;
            /**
             * Cmt Rule Id
             * @description ID of applied CMT rule.
             */
            cmt_rule_id?: string | null;
        };
        /** ProviderPricingInfoPaxFareMapDto */
        ProviderPricingInfoPaxFareMapDto: {
            /** @description Adult. */
            ADT: components['schemas']['ProviderPricingInfoPaxFareItemDto'];
            /** @description Children (if present). */
            CHD?: components['schemas']['ProviderPricingInfoPaxFareItemDto'] | null;
            /** @description Infant (if present). */
            INF?: components['schemas']['ProviderPricingInfoPaxFareItemDto'] | null;
        };
        /** ProviderPricingInfoPaxFareSelectedCommissionDto */
        ProviderPricingInfoPaxFareSelectedCommissionDto: {
            /**
             * Commission Idx
             * @description The commission index.
             */
            commission_idx: number;
        };
        /** ProviderPricingInfoPaxPartFareComponentDto */
        ProviderPricingInfoPaxPartFareComponentDto: {
            /**
             * Part Idx
             * @deprecated
             * @description The part index.
             */
            part_idx?: number | null;
            /**
             * Begin Airport
             * @description Begin airport.
             */
            begin_airport?: string | null;
            /**
             * End Airport
             * @description End airport.
             */
            end_airport?: string | null;
            /** @description Fare details. */
            fare_details: components['schemas']['ProviderPricingInfoPaxPartFareDetailsDto'];
            /**
             * Segments
             * @description Segment details.
             */
            segments: components['schemas']['ProviderPricingInfoPaxSegmentFareComponentDto'][];
        };
        /** ProviderPricingInfoPaxPartFareDetailsDto */
        ProviderPricingInfoPaxPartFareDetailsDto: {
            /**
             * Fare Basic Code
             * @description Fare basic code.
             */
            fare_basic_code?: string | null;
            /**
             * Matched Account Code
             * @description Matched account code (snap code).
             */
            matched_account_code?: string | null;
            /**
             * Cabin Code
             * @description Cabin code.
             */
            cabin_code?: string | null;
            /**
             * Private Fare
             * @description If true, the fare is a private fare.
             */
            private_fare?: boolean | null;
            /**
             * Negotiated Fare
             * @description If true, the fare is negotiated
             */
            negotiated_fare?: boolean | null;
        };
        /** ProviderPricingInfoPaxSegmentFareComponentDto */
        ProviderPricingInfoPaxSegmentFareComponentDto: {
            /**
             * Segment Idx
             * @description The segment index.
             */
            segment_idx: number;
            /** @description The segment details. */
            segment_details: components['schemas']['ProviderPricingInfoPaxSegmentFareDetailsDto'];
        };
        /** ProviderPricingInfoPaxSegmentFareDetailsDto */
        ProviderPricingInfoPaxSegmentFareDetailsDto: {
            cabin_class: components['schemas']['CabinClass'];
            /**
             * Booking Class
             * @description Booking class.
             */
            booking_class: string | null;
            /**
             * Meal Code
             * @description Meal code.
             */
            meal_code: string | null;
            /**
             * Seats Available
             * @description Number of available seats.
             */
            seats_available: number | null;
            /**
             * Availability Break
             * @description Availability break (GDS specific).
             */
            availability_break: boolean | null;
            /** @description Flight amenities. */
            flight_amenities?: components['schemas']['ProviderPricingInfoPaxFareFlightAmenitiesDto'] | null;
        };
        /** ProviderPricingInfoSeatAmenityDto */
        ProviderPricingInfoSeatAmenityDto: {
            /**
             * Pitch
             * @description Pitch size in inches.
             */
            pitch: number;
            /**
             * Type
             * @description Seat type.
             */
            type: string;
        };
        /** ProviderPricingInfoTotalFareDto */
        ProviderPricingInfoTotalFareDto: {
            /** @description The fare. */
            fare: components['schemas']['MoneyDto'];
            /** @description The fax. */
            tax: components['schemas']['MoneyDto'];
            /** @description The commission (if present). */
            commission?: components['schemas']['MoneyDto'] | null;
            /** @description The issue fee (if present). */
            issue_fee?: components['schemas']['MoneyDto'] | null;
            /** @description The miles (if provider). */
            miles?: components['schemas']['ProviderPricingInfoMilesDto'] | null;
            /** @description Calculates total. */
            readonly total: components['schemas']['MoneyDto'];
        };
        /** ProviderSearchItinerariesCommonOptionsDto */
        ProviderSearchItinerariesCommonOptionsDto: {
            /**
             * Limit
             * @description Limit the number of returned itineraries.
             */
            limit?: number | null;
            sort_by?: components['schemas']['ItinerarySortType'] | null;
            /**
             * Timeout
             * @description Request timeout in milliseconds.
             */
            timeout?: number | null;
            /**
             * Cache Id
             * @description If used, the search result will be cached under this key for a short time.
             */
            cache_id?: string | null;
        };
        /** ProviderSearchItinerariesProviderOptionsDto */
        ProviderSearchItinerariesProviderOptionsDto: {
            /**
             * Pcc
             * @description PCC.
             */
            pcc?: string | null;
            /**
             * Part Preferences
             * @description List of part preferences, each containing part index and cabin preference.
             */
            part_preferences?: components['schemas']['PartPreference'][] | null;
        };
        /** ProviderSearchItinerariesRequestDto */
        ProviderSearchItinerariesRequestDto: {
            /** @description The search query parameters. */
            query: components['schemas']['SearchItinerariesQueryDto'];
            /** @description Options common to all providers. */
            common_options?: components['schemas']['ProviderSearchItinerariesCommonOptionsDto'] | null;
            /** @description Provider specific options. */
            provider_options?: components['schemas']['ProviderSearchItinerariesProviderOptionsDto'] | null;
        };
        /** ProviderSearchItinerariesResponseDto */
        ProviderSearchItinerariesResponseDto: {
            /**
             * Search Id
             * @description Search id.
             */
            search_id: string;
            /**
             * Itineraries
             * @description The list of available itineraries.
             */
            itineraries: components['schemas']['ProviderItineraryDto'][];
        };
        /**
         * ProxyType
         * @description Type of proxy service.
         * @enum {string}
         */
        ProxyType: 'none' | 'oxylabs_datacenter' | 'oxylabs_residential' | 'oxylabs_residential_sticky' | 'mlx' | 'simple';
        /** SearchItinerariesItemDto */
        SearchItinerariesItemDto: {
            /**
             * Origin
             * @description Origin locations (IATA codes).
             */
            origin: string[];
            /**
             * Dest
             * @description Destination locations (IATA codes).
             */
            dest: string[];
            /** @description Departure date range. */
            depart_date: components['schemas']['SearchItineraryDateRangeDto'];
            /** @description Return date range. */
            return_date?: components['schemas']['SearchItineraryDateRangeDto'] | null;
        };
        /** SearchItinerariesPaxCountMapDto */
        SearchItinerariesPaxCountMapDto: {
            /**
             * Adt
             * @description Number of adults.
             */
            ADT: number;
            /**
             * Chd
             * @description Number of children.
             */
            CHD?: number | null;
            /**
             * Inf
             * @description Number of infants.<br>Should not be greater than number of adults.
             */
            INF?: number | null;
        };
        /** SearchItinerariesQueryDto */
        SearchItinerariesQueryDto: {
            /**
             * Itineraries
             * @description The list of itineraries.<br>For ONEWAY send only one itinerary without return date.<br>For RETURN send only one itinerary with return date.<br>For MULTICITY send multiple itineraries without return date.
             */
            itineraries: components['schemas']['SearchItinerariesItemDto'][];
            /** @description The number of passengers by type.<br>The sum of passengers cannot be greater than 9. */
            passengers: components['schemas']['SearchItinerariesPaxCountMapDto'];
            /** Cabin Classes */
            cabin_classes: components['schemas']['CabinClass'][];
        };
        /** SearchItineraryDateRangeDto */
        SearchItineraryDateRangeDto: {
            /**
             * Start
             * Format: date
             * @description Start date.
             */
            start: string;
            /**
             * End
             * Format: date
             * @description End date.
             */
            end: string;
        };
        /** SegmentPointDto */
        SegmentPointDto: {
            /**
             * Date
             * Format: date-time
             * @description The local date and time.
             */
            date: string;
            /**
             * Airport
             * @description The airport IATA code.
             */
            airport: string;
            /**
             * Terminal
             * @description Terminal.
             */
            terminal: string | null;
            /**
             * City
             * @description The city IATA code.
             */
            city?: string | null;
            /**
             * Country
             * @description The country code (ISO 3166-1 alpha-2 format).
             */
            country?: string | null;
            /**
             * State
             * @description The state code (alpha-2 format).
             */
            state?: string | null;
        };
        /** StopsCountDto */
        StopsCountDto: {
            /**
             * Regular
             * @description Number of regular stops (between segments).
             */
            regular: number;
            /**
             * Hidden
             * @description Number of hidden stops (technical stops).
             */
            hidden: number;
            /**
             * Total
             * @description Calculates total number of stops.
             */
            readonly total: number;
        };
        /** Token */
        Token: {
            /** Access Token */
            access_token: string;
            /**
             * Token Type
             * @default bearer
             */
            token_type: string;
        };
        /** UpdatePassword */
        UpdatePassword: {
            /** Current Password */
            current_password: string;
            /** New Password */
            new_password: string;
        };
        /** UserCreate */
        UserCreate: {
            /**
             * Email
             * Format: email
             */
            email: string;
            /**
             * Is Active
             * @default true
             */
            is_active: boolean;
            /**
             * Is Superuser
             * @default false
             */
            is_superuser: boolean;
            /** Full Name */
            full_name?: string | null;
            /** Password */
            password: string;
        };
        /** UserPublic */
        UserPublic: {
            /**
             * Email
             * Format: email
             */
            email: string;
            /**
             * Is Active
             * @default true
             */
            is_active: boolean;
            /**
             * Is Superuser
             * @default false
             */
            is_superuser: boolean;
            /** Full Name */
            full_name?: string | null;
            /**
             * Id
             * Format: uuid
             */
            id: string;
        };
        /** UserRegister */
        UserRegister: {
            /**
             * Email
             * Format: email
             */
            email: string;
            /** Password */
            password: string;
            /** Full Name */
            full_name?: string | null;
        };
        /** UserUpdate */
        UserUpdate: {
            /** Email */
            email?: string | null;
            /**
             * Is Active
             * @default true
             */
            is_active: boolean;
            /**
             * Is Superuser
             * @default false
             */
            is_superuser: boolean;
            /** Full Name */
            full_name?: string | null;
            /** Password */
            password?: string | null;
        };
        /** UserUpdateMe */
        UserUpdateMe: {
            /** Full Name */
            full_name?: string | null;
            /** Email */
            email?: string | null;
        };
        /** UsersPublic */
        UsersPublic: {
            /** Data */
            data: components['schemas']['UserPublic'][];
            /** Count */
            count: number;
        };
        /** ValidationError */
        ValidationError: {
            /** Location */
            loc: (string | number)[];
            /** Message */
            msg: string;
            /** Error Type */
            type: string;
        };
        /** VehicleDto */
        VehicleDto: {
            /**
             * Code
             * @description Vehicle code (aircraft code).
             */
            code: string | null;
            /**
             * Name
             * @description Vehicle name.
             */
            name: string | null;
            type: components['schemas']['VehicleType'] | null;
        };
        /**
         * VehicleType
         * @description Vehicle type.
         * @enum {string}
         */
        VehicleType: 'AIRCRAFT' | 'TRAIN' | 'BUS' | 'SHIP';
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    'flights-search_flights': {
        parameters: {
            query?: {
                /** @description The timestamp when the hash was generated. */
                timestamp?: string;
                /** @description SHA256 HEX hash "timestamp"_"secret". */
                token?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                'application/json': components['schemas']['ProviderSearchItinerariesRequestDto'];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ProviderSearchItinerariesResponseDto'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'flights-search_flights_airfrance': {
        parameters: {
            query?: {
                /** @description The timestamp when the hash was generated. */
                timestamp?: string;
                /** @description SHA256 HEX hash "timestamp"_"secret". */
                token?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                'application/json': components['schemas']['ProviderSearchItinerariesRequestDto'];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ProviderSearchItinerariesResponseDto'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'browser-profiles-list_browser_profiles': {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
                proxy_type?: components['schemas']['ProxyType'] | null;
                include_blocked?: boolean;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['BrowserProfile'][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'browser-profiles-create_browser_profile': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                'application/json': components['schemas']['BrowserProfileCreate'];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['BrowserProfile'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'browser-profiles-get_available_profile': {
        parameters: {
            query?: {
                proxy_type?: components['schemas']['ProxyType'] | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['BrowserProfile'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'browser-profiles-get_browser_profile': {
        parameters: {
            query?: never;
            header?: never;
            path: {
                profile_id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['BrowserProfile'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'browser-profiles-update_browser_profile': {
        parameters: {
            query?: never;
            header?: never;
            path: {
                profile_id: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                'application/json': components['schemas']['BrowserProfileUpdate'];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['BrowserProfile'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'browser-profiles-delete_browser_profile': {
        parameters: {
            query?: never;
            header?: never;
            path: {
                profile_id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['BrowserProfile'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'browser-profiles-block_browser_profile': {
        parameters: {
            query?: never;
            header?: never;
            path: {
                profile_id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['BrowserProfile'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'browser-profiles-unblock_browser_profile': {
        parameters: {
            query?: never;
            header?: never;
            path: {
                profile_id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['BrowserProfile'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'browser-profiles-register_aircanada_account': {
        parameters: {
            query?: {
                proxy_type?: components['schemas']['ProxyType'] | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['BrowserProfile'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'private-create_user': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                'application/json': components['schemas']['PrivateUserCreate'];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['UserPublic'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'login-login_access_token': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                'application/x-www-form-urlencoded': components['schemas']['Body_login-login_access_token'];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['Token'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'login-test_token': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['UserPublic'];
                };
            };
        };
    };
    'login-recover_password': {
        parameters: {
            query?: never;
            header?: never;
            path: {
                email: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['Message'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'login-reset_password': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                'application/json': components['schemas']['NewPassword'];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['Message'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'login-recover_password_html_content': {
        parameters: {
            query?: never;
            header?: never;
            path: {
                email: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'text/html': string;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'users-read_users': {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['UsersPublic'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'users-create_user': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                'application/json': components['schemas']['UserCreate'];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['UserPublic'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'users-read_user_me': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['UserPublic'];
                };
            };
        };
    };
    'users-delete_user_me': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['Message'];
                };
            };
        };
    };
    'users-update_user_me': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                'application/json': components['schemas']['UserUpdateMe'];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['UserPublic'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'users-update_password_me': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                'application/json': components['schemas']['UpdatePassword'];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['Message'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'users-register_user': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                'application/json': components['schemas']['UserRegister'];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['UserPublic'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'users-read_user_by_id': {
        parameters: {
            query?: never;
            header?: never;
            path: {
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['UserPublic'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'users-delete_user': {
        parameters: {
            query?: never;
            header?: never;
            path: {
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['Message'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'users-update_user': {
        parameters: {
            query?: never;
            header?: never;
            path: {
                user_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                'application/json': components['schemas']['UserUpdate'];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['UserPublic'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'utils-test_email': {
        parameters: {
            query: {
                email_to: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['Message'];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'utils-health_check': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': boolean;
                };
            };
        };
    };
    'utils-transcribe_audio': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                'multipart/form-data': components['schemas']['Body_utils-transcribe_audio'];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
    'utils-classify_audio': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                'multipart/form-data': components['schemas']['Body_utils-classify_audio'];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['HTTPValidationError'];
                };
            };
        };
    };
}
