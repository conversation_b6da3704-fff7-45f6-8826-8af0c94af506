<template>
    <div class="layout__content layout__content--list flex flex-col gap-y-1 h-full mb-0">
        <AwardOffersSearchSection />
        <AwardOffersListSection />
    </div>
</template>

<script setup lang="ts">
import AwardOffersSearchSection from '../sections/AwardOffersSearchSection.vue'
import AwardOffersListSection from '../sections/AwardOffersListSection.vue'

defineOptions({
    name: 'AwardOffersPage',
})

useNewContext('selected')
</script>
