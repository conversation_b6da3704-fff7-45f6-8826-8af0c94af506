import { defineStore } from 'pinia'
import { useAwardOffersSdk } from '#/packages/@tmg/award-offers-tool-frontend/src/lib/sdk/useAwardOffersSdk'
import type { AwardOffersTool } from '#/packages/@tmg/award-offers-tool-frontend/src/types/AwardOffersTool'
import { FlightFormData, FlightRoute, TripType } from '@tmg/flight-form'
import type { TripClass } from '@tmg/flight-form'
import { groupBy } from '~/lib/Helper/ArrayHelper'

export type GroupedRecords = {
    itinerariesByClass: Record<TripClass, AwardOffersTool.Itinerary>
}

export enum AwardOffersSortOptions {
    BestPrice = 'best_price',
    BestTime = 'best_time',
}

export const useAwardOffersStore = defineStore('award-offers', () => {
    const sdk = useAwardOffersSdk()
    const isLoading = ref(false)
    const isBeforeFirstRequest = ref(true)
    const isEmptyResult = ref(false)
    const selectedSortOption = ref<'best_price' | 'best_time'>()

    const fetchedFromIataCode = ref()
    const fetchedToIataCode = ref()

    const formData = new FlightFormData({
        routes: [new FlightRoute()],
        tripType: TripType.OneWay,
    })

    const flightFormData = reactive(formData)

    const awardOfferRecords = ref<AwardOffersTool.Itinerary[]>([])

    const groupedAwardOfferRecords = computed(() => {
        if (!awardOfferRecords.value) {
            return undefined
        }

        if (selectedSortOption.value) {
            const sortedRecords = awardOfferRecords.value.toSorted((a, b) => {
                if (selectedSortOption.value === 'best_price') {
                    return (
                        a.pricing_info.total_fare.total.amount -
                        b.pricing_info.total_fare.total.amount
                    )
                }

                if (selectedSortOption.value === 'best_time') {
                    return (
                        a.duration.total - b.duration.total
                    )
                }

                return 0
            })

            return groupRecords(sortedRecords)
        }

        return groupRecords(awardOfferRecords.value)
    })

    async function getAwardOfferRecords(data: AwardOffersTool.Api.AwardOffers.Search.Payload) {
        isBeforeFirstRequest.value = false
        isLoading.value = true
        awardOfferRecords.value = []
        selectedSortOption.value = undefined

        fetchedFromIataCode.value = flightFormData.routes[0].from?.code
        fetchedToIataCode.value = flightFormData.routes[0].to?.code

        try {
            const response = await sdk.search(data)

            if (flightFormData.tripType === TripType.OneWay) {
                awardOfferRecords.value = response.itineraries.map((record) => {
                    return { ...record, cmp_id: `${record.cmp_id}_${record.cmp_id}` }
                })
            } else {
                awardOfferRecords.value = response.itineraries
            }
            isEmptyResult.value = response.itineraries.length === 0
        } catch (e) {
            isEmptyResult.value = false
            throw e
        } finally {
            isLoading.value = false
        }
    }

    function groupRecords(itineraries: AwardOffersTool.Itinerary[]) {
       return groupBy(itineraries, 'cmp_id')
    }

    function sortRecords(sortType: AwardOffersSortOptions | undefined) {
        selectedSortOption.value = sortType
    }

    return {
        awardOfferRecords,
        getAwardOfferRecords,
        flightFormData,
        groupedAwardOfferRecords,
        isLoading,
        isBeforeFirstRequest,
        isEmptyResult,
        sortRecords,
        selectedSortOption,
        fetchedFromIataCode,
        fetchedToIataCode,
    }
})
