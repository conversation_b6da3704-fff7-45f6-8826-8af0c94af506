import type { RouteDefinition } from '~/router'
import { RouteGroup } from '~types/enums/RouteGroup'

export default ([
    {
        path: '/award-offers',
        name: 'award.offers',
        component: () => import('./pages/AwardOffersPage.vue'),
        meta: {
            permission: defineRoutePermission('all', 'openPageAwardOffers'),
            title: 'Award Offers',
            icon: SearchIcon,
            inSidebar: true,
            group: RouteGroup.ToolsSettings,
        },
    },
] satisfies RouteDefinition[])
