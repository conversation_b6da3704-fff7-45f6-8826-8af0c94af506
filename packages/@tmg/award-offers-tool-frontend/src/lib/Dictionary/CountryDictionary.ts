import Dictionary from './Dictionary'
import type { Aero } from '@tmg/aero-data-sdk'

type CountryIdentification = {
    code: string
}

export default class CountryDictionary extends Dictionary<CountryIdentification, Aero.Country> {
    protected getKey(item: CountryIdentification) {
        return `${item.code}`
    }

    public override all() {
        return super.all().slice().sort((a, b) => a.name.localeCompare(b.name))
    }
}
