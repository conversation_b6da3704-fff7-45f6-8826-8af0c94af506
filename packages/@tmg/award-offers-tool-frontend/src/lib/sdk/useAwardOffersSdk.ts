import type { FetchOptions } from 'ofetch'
import type { AwardOffersTool } from '../../types/AwardOffersTool'

export type RowSegmentsResponseType = {
    segments: string
}

export function useAwardOffersSdk() {
    const callFlightsAction = useContext().useModel('Lead').actions.searchAwardOffers
    const callRawSegmentsAction = useContext().useModel('PriceDropCheck').actions.getRawSegments

    const fetch = <TResponse>(url: string, options: FetchOptions = {}, method?: string) => {
        return callFlightsAction({
            url,
            options,
        }) as Promise<TResponse>
    }

    const fetchRawSegments = <TResponse>(url: string, options: FetchOptions = {}, method?: string) => {
        return callRawSegmentsAction({
            url,
            options,
        }) as Promise<TResponse>
    }

    async function search(data: AwardOffersTool.Api.AwardOffers.Search.Payload) {
        return await fetch<AwardOffersTool.Api.AwardOffers.Search.Response>('/api/search', {
            method: 'POST',
            body: data,
        })
    }

    async function getRawSegments(data: AwardOffersTool.Itinerary) {
        return await fetchRawSegments<RowSegmentsResponseType>('/award/offers', {
            method: 'POST',
            body: data,
        })
    }

    return {
        search,
        getRawSegments,
    }
}
