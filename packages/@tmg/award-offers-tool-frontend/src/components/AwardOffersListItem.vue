<template>
    <div class="w-full grid grid-cols-3 gap-x-1">
        <div v-if="itineraryInfo" class="col-span-2 flex flex-col justify-center gap-y-5 items-center w-full bg-white dark:bg-dark-2 rounded py-4 px-5 h-full">
            <div
                v-for="(item, index) in itineraryInfo.parts"
                :key="index"
                class="grid grid-cols-6 gap-x-5 w-full"
            >
                <div v-if="index === 0" class="flex items-center gap-x-1.5 w-full border-r">
                    <OutboundIcon />
                    <span class="text-xs font-medium text-secondary dark:text-secondary-100">Outbound</span>
                </div>
                <div v-else class="flex items-center gap-x-1.5 w-full border-r">
                    <InboundIcon />
                    <span class="text-xs font-medium text-secondary dark:text-secondary-100">Inbound</span>
                </div>
                <div class="flex items-center justify-center gap-x-2 border-r">
                    <div class="p-1.5 bg-white dark:bg-dark-2 rounded-lg border w-fit">
                        <AirCanadaIcon />
                    </div>
                    <span class="text-xs font-medium text-secondary dark:text-secondary-100">Air Canada</span>
                </div>
                <div class="col-span-3 flex items-center justify-center gap-x-6 border-r">
                    <div class="flex flex-col gap-y-0.5">
                        <span class="text-sm text-secondary-900 dark:text-secondary-100 font-semibold">
                            {{ getTime(item.segments[0].depart.date) }}
                        </span>
                        <span class="text-right text-xs text-primary dark:text-primary-200 font-semibold">
                            {{ item.segments[0].depart.city }}
                        </span>
                    </div>
                    <div class="w-[230px] flex flex-col gap-y-0.5">
                        <div class="relative w-full py-2 flex justify-between items-center">
                            <div class="p-1 bg-white dark:bg-dark-2 border border-secondary-200 dark:border-secondary-400 rounded-full relative" style="z-index: 2" />
                            <div class="h-px w-full border-t border-dashed absolute z-0" />
                            <div
                                v-if="item.stops_count.total > 0"
                                v-tooltip="{ content: layoverTooltips[index], html: true }"
                                class="p-1 border-t left-[50%] rounded-full bg-primary dark:bg-primary-400 absolute z-0"
                            />
                            <div class="p-1 bg-white dark:bg-dark-2 border border-secondary-200 dark:border-secondary-400 rounded-full relative z-2" />
                        </div>
                        <div class="w-full flex items-center justify-center text-xs font-semibold text-secondary-900 dark:text-secondary-100">
                            {{ formatDuration(item.duration.total) }}
                        </div>
                    </div>
                    <div class="flex flex-col gap-y-0.5">
                        <span class="text-sm text-secondary-900 dark:text-secondary-100 font-semibold">
                            {{ getTime(item.segments.at(-1).arrival.date) }}
                        </span>
                        <span class="text-left text-xs text-primary dark:text-primary-200 font-semibold">
                            {{ item.segments.at(-1).arrival.city }}
                        </span>
                    </div>
                </div>
                <div class="flex items-center gap-x-1.5 justify-end">
                    <div class="text-secondary dark:text-secondary-100">
                        <RouteIcon />
                    </div>
                    <span v-if="item.stops_count.total > 0" class="text-center text-secondary dark:text-secondary-100 text-xs">
                        Layover, {{ item.stops_count.total }} stops
                    </span>
                    <span v-else class="text-secondary dark:text-secondary-100 text-xs">
                        Direct
                    </span>
                </div>
            </div>
        </div>
        <div class="bg-white dark:bg-dark-2 rounded grid grid-cols-4 gap-x-1 p-1">
            <div
                v-for="{ cls, data } in itinerariesForClass"
                :key="cls"
                class="h-full"
            >
                <div v-if="data" class="h-full px-4 py-2 flex flex-col gap-y-2 bg-primary-50 dark:bg-dark-3 rounded">
                    <div class="my-auto">
                        <div class="flex flex-col gap-y-1">
                            <div class="flex items-center justify-between pb-0.5 border-b dark:border-secondary-400">
                                <span class="text-secondary dark:text-secondary-100 text-xs">Miles:</span>
                                <span class="text-secondary-900 dark:text-secondary-200 text-xs font-semibold">
                                    {{ data.pricing_info.total_fare.miles?.amount }}
                                </span>
                            </div>
                            <div class="flex items-center justify-between pb-0.5 border-b dark:border-secondary-400">
                                <span class="text-secondary dark:text-secondary-100 text-xs">USD:</span>
                                <span class="text-secondary-900 dark:text-secondary-200 text-xs">
                                    {{ data.pricing_info.total_fare.total.amount }}
                                </span>
                            </div>
                            <div class="flex items-center justify-between pb-0.5 border-b  dark:border-secondary-400">
                                <span class="text-secondary dark:text-secondary-100 text-xs">Availability:</span>
                                <span class="text-secondary-900 dark:text-secondary-200 text-xs">
                                    {{ data.min_availability }} seat
                                </span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <AppButton
                                class="--xs w-full dark:bg-dark-3 dark:text-white dark:border dark:border-secondary-300"
                                :loading="createPqLoading"
                                @click="createPq(cls)"
                            >
                                <PlusIcon />
                                Create PQ
                            </AppButton>
                        </div>
                    </div>
                </div>
                <div v-else class="flex items-center justify-center h-full w-full">
                    <MinusIcon />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import OutboundIcon from '#/packages/@tmg/award-offers-tool-frontend/src/assets/icons/OutboundIcon.svg?component'
import InboundIcon from '#/packages/@tmg/award-offers-tool-frontend/src/assets/icons/InboundIcon.svg?component'
import AirCanadaIcon from '#/packages/@tmg/award-offers-tool-frontend/src/assets/icons/AirCanadaIcon.svg?component'
import RouteIcon from '~/assets/icons/RouteIcon.svg?component'
import type { AwardOffersTool } from '#/packages/@tmg/award-offers-tool-frontend/src/types/AwardOffersTool'
import LeadSelectModal from '~modules/gds-terminal/src/modals/LeadSelectModal.vue'
import { useAwardOffersSdk } from '#/packages/@tmg/award-offers-tool-frontend/src/lib/sdk/useAwardOffersSdk'
import type { GroupedRecords } from '#/packages/@tmg/award-offers-tool-frontend/src/stores/useAwardOffersStore'
import { TripClass } from '@tmg/flight-form'
import { useAeroDataStore } from '#/packages/@tmg/award-offers-tool-frontend/src/stores/useAeroDataStore'
import type { PlaceIdentification } from '@tmg/aero-data-sdk'
import { preventDuplication } from '~/lib/Helper/PromiseHelper'

const props = defineProps<{
    record: AwardOffersTool.Itinerary[],
}>()

function getTime(date: string) {
    return new Date(date).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'UTC',
    }).replace(/^0/, '')
}

function formatDuration(seconds: number) {
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)

    const parts = []

    if (days > 0) parts.push(`${days}d`)

    if (hours > 0 || days > 0) parts.push(`${hours}h`)
    parts.push(`${minutes}m`)

    return parts.join(' ')
}

const selectLeadModal = useModal(LeadSelectModal)
const awardOffersSdk = useAwardOffersSdk()

const mappedData = computed((): GroupedRecords => {
    const itinerariesByClass: Partial<Record<TripClass, AwardOffersTool.Itinerary>> = {}

    for (const record of props.record) {
        const segments = record.parts[0].segments

        if (!segments?.length) continue

        const longestSegment = segments.reduce((max, s) =>
            s.duration.flight_time > max.duration.flight_time ? s : max,
        )

        const cabinClass = longestSegment.cabin_class as TripClass

        if (!(cabinClass in itinerariesByClass)) {
            itinerariesByClass[cabinClass] = record
        }
    }

    return {
        itinerariesByClass: itinerariesByClass as Record<TripClass, AwardOffersTool.Itinerary>,
    }
})

const itineraryInfo = computed(() => {
    const values = Object.values(mappedData.value.itinerariesByClass)

    return values.find(v => !!v)
})

//

const tripClasses = [TripClass.Economy, TripClass.PremiumEconomy, TripClass.Business, TripClass.First]

const itinerariesForClass = computed(() =>
    tripClasses.map((cls) => ({
        cls,
        data: mappedData.value.itinerariesByClass[cls],
    })),
)

//

const createPqLoading = ref(false)

async function createPq(tripClass: TripClass) {
    await preventDuplication(async () => {
        const itineraryForTripClass: AwardOffersTool.Itinerary = mappedData.value.itinerariesByClass[tripClass]
        const leadPk = await selectLeadModal.open()

        const { segments, validating_carrier } = await awardOffersSdk.getRawSegments(itineraryForTripClass)

        const modal = useModal((await import('@/components/Modals/priceQuote/PriceQuoteModal.vue')).default)

        await modal.open({
            lead_id: Number(leadPk),
            prepopulateFrom: {
                option: segments,
                consolidator_area_name: itineraryForTripClass.pricing_info.pcc,
                carrier_code: validating_carrier,
                net_price: itineraryForTripClass.pricing_info.total_fare.total.amount,
                fare_amount: itineraryForTripClass.pricing_info.total_fare.fare.amount,
                commission: itineraryForTripClass.pricing_info.total_fare.commission?.amount,
            },
        })
    }, createPqLoading)
}

//

const aeroDataStore = useAeroDataStore()

const layoverTooltips = ref<Record<number, string>>({})

async function getLayoversTooltip(part: AwardOffersTool.Part): Promise<string> {
    const placeCodes = part.segments.slice(0, -1).map(segment => segment.arrival.city)

    await aeroDataStore.fetchPlacesInfo(
        placeCodes.map(code => ({ code } as PlaceIdentification)),
    )

    const layoverDescriptions = part.segments.slice(0, -1).map((segment, index) => {
        const arrival = segment.arrival

        const place = aeroDataStore.getPlaceInfo({ code: arrival.city } as PlaceIdentification)
        const cityName = place?.name || arrival.city

        return `<strong>Layover ${index + 1}:</strong> ${cityName}(${segment.arrival.city})`
    })

    const total = `<strong>Total layover time:</strong> ${formatDuration(part.duration.waiting_time)}`

    return [...layoverDescriptions, total].join('<br>')
}

onMounted(async () => {
    if (!itineraryInfo.value) return

    for (let i = 0; i < itineraryInfo.value.parts.length; i++) {
        const part = itineraryInfo.value.parts[i]

        if (part.segments.length > 1) {
            layoverTooltips.value[i] = await getLayoversTooltip(part)
        } else {
            layoverTooltips.value[i] = 'Direct flight'
        }
    }
})
</script>
