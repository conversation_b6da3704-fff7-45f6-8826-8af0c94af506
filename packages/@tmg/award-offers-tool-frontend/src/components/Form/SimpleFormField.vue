<template>
    <div
        class="form-field"
        :class="{
            'has-error': error,
            'form-inline': inline,
        }"
    >
        <label v-if="label" class="form-label">
            {{ label }}&nbsp;<span v-if="required" class="text-danger text-xs">*</span>
        </label>

        <div class="form-field__body">
            <slot />
        </div>

        <div v-if="error" class="form-error">
            {{ error }}
        </div>
    </div>
</template>

<script setup lang="ts">
defineOptions({
    name: 'SimpleFormField',
})

//

defineProps<{
    label?: string,
    error?: string,
    required?: boolean,
    inline?: boolean,
}>()
</script>
