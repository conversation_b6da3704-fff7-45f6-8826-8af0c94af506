<template>
    <div class="p-5 bg-white dark:bg-dark-3 flex items-start justify-between rounded">
        <SimpleFormField
            label="Flight Type"
            class="text-xs flex flex-col font-semibold"
        >
            <div class="button-group">
                <AppButton
                    :class="{
                        '--primary': flightFormData.tripType === TripType.OneWay
                    }"
                    @click="()=>{
                        flightFormData.tripType = TripType.OneWay
                        flightFormData.routes[0].return = undefined
                    }"
                >
                    One Way
                </AppButton>
                <AppButton
                    class=""
                    :class="{
                        '--primary': flightFormData.tripType === TripType.Return
                    }"
                    @click="()=>{
                        flightFormData.tripType = TripType.Return
                    }"
                >
                    Round Trip
                </AppButton>
            </div>
        </SimpleFormField>
        <SimpleFormField
            label="Airline"
            class="text-xs font-semibold"
        >
            <InputSelect
                v-model="form.data.airline"
                class="w-[100px]"
                :disabled="true"
                :options="[{ value: 'aircanada', title: 'Air Canada' }, { value: 'airfrance', title: 'Air France' }]"
            />
        </SimpleFormField>
        <SimpleFormField
            label="From"
            class="text-xs font-semibold"
            :error="fromError"
        >
            <InputSelect
                v-model="flightFormData.routes[0].from"
                class="w-[180px]"
                search
                :options="departureSelectOptions"
                :debounce="200"
                @search="updateDepartureSelectOptions"
            />
        </SimpleFormField>
        <SimpleFormField
            label="To"
            class="text-xs font-semibold"
            :error="toError"
        >
            <InputSelect
                v-model="flightFormData.routes[0].to"
                class="w-[180px]"
                search
                :options="destinationSelectOptions"
                :debounce="200"
                @search="updateDestinationSelectOptions"
            />
        </SimpleFormField>
        <SimpleFormField
            label="Departure date"
            class="text-xs font-semibold w-[190px]"
            :error="departureDateError"
        >
            <InputDate v-model="flightFormData.routes[0].departure" />
        </SimpleFormField>
        <SimpleFormField
            v-tooltip="{ content: flightFormData.tripType === TripType.OneWay ? 'Switch trip type to Round Trip' : '' }"
            label="Return date"
            class="text-xs font-semibold w-[190px]"
            :error="returnDateError"
        >
            <InputDate
                v-model="flightFormData.routes[0].return"
                :disabled="flightFormData.tripType === TripType.OneWay"
                :min-date="minReturnDate"
            />
        </SimpleFormField>
        <SimpleFormField
            label="Passengers"
            class="text-xs font-semibold max-w-[370px]"
        >
            <InputPassengers
                :model-value="form.data.passengers"
                with-buttons
                @update:model-value="(val)=>{
                    form.data.passengers = val
                    flightFormData.passengers.adults = val.adult_count
                    flightFormData.passengers.children = val.child_count
                    flightFormData.passengers.infants = val.infant_count
                }"
            />
        </SimpleFormField>
        <AppButton
            class="--primary mt-5"
            :loading="formLoading"
            @click="submit"
        >
            <SearchIcon />
            Search Flights
        </AppButton>
    </div>
</template>

<script setup lang="ts">
import { TripType, TripClass } from '@tmg/flight-form'
import { useAeroDataStore } from '#/packages/@tmg/award-offers-tool-frontend/src/stores/useAeroDataStore'
import type { Aero } from '@tmg/aero-data-sdk'
import type SelectOption from '~types/structures/SelectOption'
import type { AwardOffersTool } from '#/packages/@tmg/award-offers-tool-frontend/src/types/AwardOffersTool'
import SimpleFormField from '../components/Form/SimpleFormField.vue'
import Errors from '~/lib/Model/Errors'
import { useAwardOffersStore } from '#/packages/@tmg/award-offers-tool-frontend/src/stores/useAwardOffersStore'

const { useModel } = useContext()

const aeroDataStore = useAeroDataStore()
const { flightFormData, getAwardOfferRecords } = useAwardOffersStore()

const departureSelectOptions = ref([])
const destinationSelectOptions = ref([])

async function updateDepartureSelectOptions(query: string) {
    departureSelectOptions.value = await handleSearch(query)
}

async function updateDestinationSelectOptions(query: string) {
    destinationSelectOptions.value = await handleSearch(query)
}

async function handleSearch(query: string) {
    const fetchedPlaces = await aeroDataStore.searchPlaces(query)

    return searchOptionsMapper(fetchedPlaces)
}

type SearchOptionValueType = {
    code: string,
    type: Aero.TypeOfPlace,
}

function searchOptionsMapper(searchOptions: Aero.PlaceSuggestion[]) {
    return searchOptions.map((option): SelectOption<SearchOptionValueType> => {
        return {
            title: `${option.code}, ${option.name}`,
            subtitle: option.type === 'airport' ? `${option.city?.name}, ${option.country?.name}` : `${option.country?.name}`,
            value: {
                code: option.code,
                type: option.type,
            },
        }
    })
}

function getSearchPayload(): AwardOffersTool.Api.AwardOffers.Search.Payload {
    return {
        'query': {
            'itineraries': flightFormData.routes.map(route => {
                if (flightFormData.tripType === TripType.OneWay) {
                    if (!route.from || !route.to || !route.departure) {
                        useLogger('search').error('Invalid route:', route)

                        return
                    }

                    return {
                        'origin': [route.from.code],
                        'dest': [route.to.code],
                        'depart_date': {
                            'start': route.departure.toISOString().split('T')[0],
                            'end': route.departure.toISOString().split('T')[0],
                        },
                    }
                }

                if (flightFormData.tripType === TripType.Return) {
                    if (!route.from || !route.to || !route.departure || !route.return) {
                        useLogger('search').warn('Invalid route:', route)

                        return
                    }

                    return {
                        'origin': [route.from.code],
                        'dest': [route.to.code],
                        'depart_date': {
                            'start': route.departure.toISOString().split('T')[0],
                            'end': route.departure.toISOString().split('T')[0],
                        },
                        'return_date': {
                            'start': route.return.toISOString().split('T')[0],
                            'end': route.return.toISOString().split('T')[0],
                        },
                    }
                }
            }).filter(Boolean),

            'passengers': {
                'ADT': flightFormData.passengers.adults,
                'CHD': flightFormData.passengers.children,
                'INF': flightFormData.passengers.infants,
            },

            'cabin_classes': [
                TripClass.Economy,
                TripClass.PremiumEconomy,
                TripClass.Business,
                TripClass.First,
            ],
        },
        'provider_options': {
            'pcc': 'Air Canada',
        },
    }
}

const form = useForm({
    airline: 'aircanada',
    passengers: {
        adult_count: 1,
        child_count: 0,
        infant_count: 0,
    },
})

const flightFormErrors = ref<{
    general: Errors,
    routes: Errors[]
}>({
    general: new Errors(),
    routes: [],
})

// return date

const minReturnDate = computed(() => {
    return flightFormData.routes[0].departure
})

watch(() => flightFormData.routes[0].departure, () => {
    if (new Date(flightFormData.routes[0].departure).getTime() > new Date(flightFormData.routes[0].return).getTime()) {
        flightFormData.routes[0].return = flightFormData.routes[0].departure
    }
}, {
    immediate: true,
})

//

const fromError = computed(() => {
    return flightFormErrors.value?.routes.at(0)?.errors.from?.at(0)
})

const toError = computed(() => {
    return flightFormErrors.value?.routes.at(0)?.errors.to?.at(0)
})

const departureDateError = computed(() => {
    return flightFormErrors.value?.routes.at(0)?.errors.departure?.at(0)
})

const returnDateError = computed(() => {
    return flightFormErrors.value?.routes.at(0)?.errors.return?.at(0)
})

const formLoading = computed(() => {
    return form.loading.value
})

const submit = form.useSubmit(async () => {
    flightFormErrors.value = flightFormData.validate()

    if (flightFormData.isValid()) {
        const data = getSearchPayload()
        await getAwardOfferRecords(data)
    }
}, {
    resetOnSuccess: false,
})
</script>
