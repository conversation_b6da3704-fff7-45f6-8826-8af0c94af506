<template>
    <div v-if="isBeforeFirstRequest && !isLoading" class="w-full flex items-center justify-center mt-5">
        <span class="text-secondary dark:text-secondary-100 font-medium text-lg">Fill info to search flights</span>
    </div>
    <div v-else-if="isLoading" class="h-full w-full flex flex-col gap-y-1 pt-2">
        <PlaceholderBlock class="w-full" style="height: 56px" />
        <PlaceholderBlock class="w-full" style="height: 120px" />
        <PlaceholderBlock class="w-full" style="height: 120px" />
        <PlaceholderBlock class="w-full" style="height: 120px" />
        <PlaceholderBlock class="w-full" style="height: 120px" />
        <PlaceholderBlock class="w-full" style="height: 120px" />
    </div>
    <div v-else-if="isEmptyResult" class="w-full flex items-center justify-center mt-5">
        <span class="text-secondary dark:text-secondary-100 font-medium text-lg">No flights was found</span>
    </div>
    <div v-else-if="offersAmount" class="w-full bg-secondary-50 dark:bg-dark-3 p-2 rounded flex flex-col h-full">
        <div class="flex flex-col h-full gap-y-1">
            <div class="w-full grid grid-cols-3 gap-x-1">
                <div class="col-span-2 w-full bg-white dark:bg-dark-2 rounded py-3 px-5 flex items-center justify-between">
                    <div class="flex items-center gap-x-1 text-md">
                        <span class="font-semibold text-secondary-900 dark:text-secondary-50">
                            {{ departureInfo }}
                        </span>
                        <span class="text-secondary dark:text-secondary-200">to</span>
                        <span class="font-semibold text-secondary-900 dark:text-secondary-50">
                            {{ destinationInfo }}
                        </span>
                        <span class="text-secondary dark:text-secondary-200">{{ offersAmount }} results</span>
                    </div>
                    <div class="flex items-center gap-x-4">
                        <span class="whitespace-nowrap text-xs font-medium">Sort by:</span>
                        <InputSelect
                            :model-value="selectedSortOption"
                            class="min-w-[200px]"
                            :options="sortOptions"
                            with-empty
                            @update:model-value="(val)=>{
                                applySort(val)
                            }"
                        />
                    </div>
                </div>
                <div class="w-full bg-white dark:bg-dark-2 rounded grid grid-cols-4 gap-x-1 p-1">
                    <div class="py-2.5 bg-primary-50 dark:bg-dark-3 dark:text-white flex items-center justify-center rounded text-xs font-medium">
                        Economy Class
                    </div>
                    <div class="py-2.5 bg-primary-50 dark:bg-dark-3 dark:text-white flex items-center justify-center rounded text-xs font-medium">
                        Premium Economy
                    </div>
                    <div class="py-2.5 bg-primary-50 dark:bg-dark-3 dark:text-white flex items-center justify-center rounded text-xs font-medium">
                        Business Class
                    </div>
                    <div class="py-2.5 bg-primary-50 dark:bg-dark-3 dark:text-white flex items-center justify-center rounded text-xs font-medium">
                        First Class
                    </div>
                </div>
            </div>
            <div
                v-if="visibleGroups"
                ref="scrollContainer"
                class="flex flex-col h-flex gap-y-1 overflow-y-auto fancy-scroll"
                @scroll="handleScroll"
            >
                <div v-for="(item, index) in visibleGroups" :key="index">
                    <AwardOffersListItem :record="item" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import AwardOffersListItem from '#/packages/@tmg/award-offers-tool-frontend/src/components/AwardOffersListItem.vue'
import { AwardOffersSortOptions, useAwardOffersStore } from '../stores/useAwardOffersStore'
import { useAeroDataStore } from '#/packages/@tmg/award-offers-tool-frontend/src/stores/useAeroDataStore'
import type { PlaceIdentification } from '@tmg/aero-data-sdk'
import type { AwardOffersTool } from '#/packages/@tmg/award-offers-tool-frontend/src/types/AwardOffersTool'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'

const awardOffersStore = useAwardOffersStore()
const aeroDataStore = useAeroDataStore()

const isLoading = computed(() => {
    return awardOffersStore.isLoading
})

const isBeforeFirstRequest = computed(() => {
    return awardOffersStore.isBeforeFirstRequest
})

const isEmptyResult = computed(() => {
    return awardOffersStore.isEmptyResult
})

const sortOptions = computed(() => {
    return [
        { title: 'Best Price', value: AwardOffersSortOptions.BestPrice },
        { title: 'Best Time', value: AwardOffersSortOptions.BestTime },
    ]
})

const selectedSortOption = computed(() => {
    return awardOffersStore.selectedSortOption
})

const awardOffersRecords = computed((): Record<string, AwardOffersTool.Itinerary[]> | undefined => {
    if (!awardOffersStore.groupedAwardOfferRecords) {
        return undefined
    }

    return awardOffersStore.groupedAwardOfferRecords
})

const awardOffersGroups = computed(() => {
    if (!awardOffersRecords.value) {
        return undefined
    }

    return Object.values(awardOffersRecords.value)
})

const offersAmount = computed(() => {
    if (!awardOffersRecords.value) {
        return 0
    }

    return Object.keys(awardOffersRecords.value).length || 0
})

const visibleCount = ref(10)
const visibleGroups = computed(() => {
    if (!awardOffersGroups.value) {
        return undefined
    }

    return awardOffersGroups.value.slice(0, visibleCount.value)
})

const scrollContainer = ref<HTMLElement | null>(null)

const handleScroll = useDebounceFn(() => {
    const el = scrollContainer.value

    if (!el) return

    const atBottom = el.scrollTop + el.clientHeight >= el.scrollHeight - 1

    if (atBottom) {
        visibleCount.value += 10
    }
}, 300)

const departureInfo = ref('')
const destinationInfo = ref('')

watch([() => awardOffersStore.fetchedFromIataCode, () => awardOffersStore.fetchedToIataCode], async () => {
    const from = awardOffersStore.fetchedFromIataCode
    const to = awardOffersStore.fetchedToIataCode

    if (from) {
        await aeroDataStore.fetchPlacesInfo([{ code: from } as PlaceIdentification])
        departureInfo.value = aeroDataStore.getPlaceInfo({ code: from } as PlaceIdentification).name
    }

    if (to) {
        await aeroDataStore.fetchPlacesInfo([{ code: to } as PlaceIdentification])
        destinationInfo.value = aeroDataStore.getPlaceInfo({ code: to } as PlaceIdentification).name
    }
}, { immediate: true, deep: true })

// sorting

function applySort(sortType: AwardOffersSortOptions | undefined) {
    awardOffersStore.sortRecords(sortType)
}
</script>
