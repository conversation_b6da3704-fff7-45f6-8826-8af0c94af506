<template>
    <div class="layout__content layout__content--list flex flex-col h-full mb-0">
        <ProductMetricsPeriodSection />
        <SuspenseManual :state="suspense">
            <template #default>
                <ProductMetricsDashboardSection />
            </template>
            <template #fallback>
                <PlaceholderBlock class="w-full h-[700px]" />
            </template>
        </SuspenseManual>
    </div>
</template>

<script setup lang="ts">
import ProductMetricsPeriodSection
    from '#/packages/@tmg/product-metrics-tool-frontend/src/sections/ProductMetricsPeriodSection.vue'
import ProductMetricsDashboardSection
    from '#/packages/@tmg/product-metrics-tool-frontend/src/sections/ProductMetricsDashboardSection.vue'
import { useProductMetricsStore } from '#/packages/@tmg/product-metrics-tool-frontend/src/stores/useProductMetricsStore'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'

defineOptions({
    name: 'ProductMetricsPage',
})

useNewContext('selected')

const suspense = useSuspensableComponent(async () => {
    await store.fetch('all')
})

const store = useProductMetricsStore()
</script>
