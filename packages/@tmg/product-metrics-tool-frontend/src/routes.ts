import type { RouteDefinition } from '~/router'
import { RouteGroup } from '~types/enums/RouteGroup'
import ChartUpIcon from '../src/assets/icons/ChartUpIcon.svg?component'

export default ([
    {
        path: '/product-metrics',
        name: 'product-metrics',
        component: () => import('./pages/ProductMetricsPage.vue'),
        meta: {
            permission: defineRoutePermission('all', 'openPageProductMetrics'),
            title: 'Product Metrics',
            icon: ChartUpIcon,
            inSidebar: true,
            group: RouteGroup.AnalyticsReports,
        },
    },
] satisfies RouteDefinition[])
