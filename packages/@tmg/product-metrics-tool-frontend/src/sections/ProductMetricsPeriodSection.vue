<template>
    <div class="px-5 pt-2 pb-1 bg-white dark:bg-dark-3 flex items-start justify-between rounded-t">
        <div class="flex flex-col gap-y-1">
            <span class="text-md font-semibold">Product metrics</span>
            <div class="flex items-center gap-x-1">
                <span class="text-xs text-secondary dark:text-secondary-200">Selected period:</span>
                <span class="text-xs font-semibold">{{ $format.dateRange(selectedRangeValue, 'UTC', { full: true }) }}</span>
            </div>
        </div>
        <div class="flex gap-x-4 items-center">
            <div class="button-group">
                <AppButton
                    class="dark:text-secondary-50"
                    :class="{
                        '--primary': selectedRangeType === ProductMetricsRange.Year,
                        'dark:bg-dark-2': selectedRangeType !== ProductMetricsRange.Year,
                    }"
                    @click="setSelectedRange(ProductMetricsRange.Year)"
                >
                    Year
                </AppButton>
                <AppButton
                    class="dark:text-secondary-50"
                    :class="{
                        '--primary': selectedRangeType === ProductMetricsRange.Month,
                        'dark:bg-dark-2': selectedRangeType !== ProductMetricsRange.Month,
                    }"
                    @click="setSelectedRange(ProductMetricsRange.Month)"
                >
                    Month
                </AppButton>
                <AppButton
                    class="dark:text-secondary-50"
                    :class="{
                        '--primary': selectedRangeType === ProductMetricsRange.Week,
                        'dark:bg-dark-2': selectedRangeType !== ProductMetricsRange.Week,
                    }"
                    @click="setSelectedRange(ProductMetricsRange.Week)"
                >
                    Week
                </AppButton>
                <AppButton
                    class="dark:text-secondary-50"
                    :class="{
                        '--primary': selectedRangeType === ProductMetricsRange.Custom,
                        'dark:bg-dark-2': selectedRangeType !== ProductMetricsRange.Custom,
                    }"
                    @click="setSelectedRange(ProductMetricsRange.Custom)"
                >
                    Custom
                </AppButton>
            </div>
            <InputDateRange
                class="max-w-[200px] dark:rounded-md dark:border dark:border-secondary-600"
                :timestamp="store.globalCustomRange"
                timezone="UTC"
                :disabled="selectedRangeType !== ProductMetricsRange.Custom"
                :max-date="new Date()"
                @update:timestamp="(val) =>{
                    setGlobalCustomRangeValue(val)
                }"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import {
    ProductMetricsRange,
    useProductMetricsStore,
} from '#/packages/@tmg/product-metrics-tool-frontend/src/stores/useProductMetricsStore'
import type { TimestampRange } from '@/lib/core/helper/DateHelper'

const store = useProductMetricsStore()

const selectedRangeType = computed(() => {
    return store.globalRange
})

const selectedRangeValue = computed(() => {
    if (selectedRangeType.value === ProductMetricsRange.Custom && store.globalCustomRange) {
        return store.globalCustomRange
    }

    return store.getTimestampRangeFromRange(selectedRangeType.value)
})

const selectedCustomRangeValue = ref()

async function setGlobalCustomRangeValue(range: TimestampRange | undefined) {
    if (range) {
        selectedCustomRangeValue.value = range
        await store.setGlobalCustomRange(range)
    }
}

async function setSelectedRange(range: ProductMetricsRange) {
    await store.setGlobalRange(range)
}
</script>
