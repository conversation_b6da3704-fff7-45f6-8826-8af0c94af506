<template>
    <div class="w-full pb-2 flex flex-col h-full">
        <div class="w-full h-full bg-secondary-50 dark:bg-dark-3 p-2 pb-0 rounded">
            <div class="grid grid-cols-5 w-full h-full gap-2">
                <ProductMetricsCard
                    v-for="record in records"
                    :key="record.category"
                    :item="record.item"
                    :category="record.category"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import ProductMetricsCard from '#/packages/@tmg/product-metrics-tool-frontend/src/components/ProductMetricsCard.vue'
import { useProductMetricsStore } from '#/packages/@tmg/product-metrics-tool-frontend/src/stores/useProductMetricsStore'
import type { ProductMetrics } from '#/packages/@tmg/product-metrics-tool-frontend/src/types/ProductMetrics'
import type {
    ProductMetricCategory,
} from '#/packages/@tmg/product-metrics-tool-frontend/src/lib/dictionary/ProductMetricsDictionary'
import {
    ProductMetricCategoryDisplayOrder,
} from '#/packages/@tmg/product-metrics-tool-frontend/src/lib/order/ProductMetricDisplayOrder'

type ProductMetricsEntry = {
    category: ProductMetricCategory,
    item: ProductMetrics.MetricsResponse[ProductMetricCategory]
}

const store = useProductMetricsStore()

const metrics = computed(() => {
    return store.metrics
})

const records = computed<ProductMetricsEntry[]>(() =>
    ProductMetricCategoryDisplayOrder
        .filter(category => store.metrics[category])
        .map(category => ({
            category,
            item: store.metrics[category],
        })),
)
</script>
