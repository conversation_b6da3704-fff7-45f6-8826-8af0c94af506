import type {
ProductMetricType,
ProductMetricCategory,
MetricUnit,
} from '#/packages/@tmg/product-metrics-tool-frontend/src/lib/dictionary/ProductMetricsDictionary'
export namespace ProductMetrics {
    export type MetricValue = {
        unit: MetricUnit,
        value: number,
    }

    // --- Acquisition ---
    export type Acquisition = {
        [ProductMetricType.TotalVisitors]: {
            value: MetricValue;
            nested: {
                [ProductMetricType.TotalVisitorsPaid]: {
                    value: MetricValue,
                    contents: {
                        [key: string]: {
                            value: MetricValue;
                        };
                    }
                };
                [ProductMetricType.TotalVisitorsOrganic]: {
                    value: MetricValue,
                    contents: {
                        [key: string]: {
                            value: MetricValue;
                        };
                    }
                };
            };
        };
        [ProductMetricType.CostPerVisitor]: {
            value: MetricValue
        };
    };

    // --- Activation ---
    export type Activation = {
        [ProductMetricType.ActivationRate]: {
            value: MetricValue;
            nested: {
                [ProductMetricType.ActivationRateJivoInitiated]: { value: MetricValue };
                [ProductMetricType.ActivationRateViewedPseudoOffer]: { value: MetricValue };
                [ProductMetricType.ActivationRateFormSubmitted]: { value: MetricValue };
            };
        };
        [ProductMetricType.CostPerAcquisition]: { value: MetricValue };
        [ProductMetricType.AvgFirstResponseTime]: { value: MetricValue };
    };

    // --- Conversion ---
    export type Conversion = {
        [ProductMetricType.LeadToPurchaseRate]: { value: MetricValue };
        [ProductMetricType.AvgTimeFromLeadToPurchase]: { value: MetricValue };
        [ProductMetricType.CustomerAcquisitionCost]: { value: MetricValue };
    };

    // --- Retention ---
    export type Retention = {
        [ProductMetricType.CustomerRetentionRate]: { value: MetricValue };
        [ProductMetricType.AvgPurchasesPerCustomer]: { value: MetricValue };
        [ProductMetricType.LoyaltyProgramEngagement]: { value: MetricValue };
    };

    // --- Referral ---
    export type Referral = {
        [ProductMetricType.NewCustomersViaReferral]: {
            value: MetricValue,
            nested: {
                [ProductMetricType.NewCustomersViaReferralLink]: { value: MetricValue };
                [ProductMetricType.NewCustomersViaReferralClient]: { value: MetricValue };
            };
        };
        [ProductMetricType.PercentUsersSharingReferrals]: { value: MetricValue };
        [ProductMetricType.CustomerReviews]: { value: MetricValue };
    };

    // --- Revenue ---
    export type Revenue = {
        [ProductMetricType.TotalRevenue]: { value: MetricValue };
        [ProductMetricType.ARPPU]: { value: MetricValue };
        [ProductMetricType.OrdersWithUpsellsRate]: { value: MetricValue };
        [ProductMetricType.AvgOrderValue]: { value: MetricValue };
        [ProductMetricType.GrossProfit]: { value: MetricValue };
        [ProductMetricType.ProfitMargin]: { value: MetricValue };

    };

    // --- Resurrection ---
    export type Resurrection = {
        [ProductMetricType.DormantUsers]: { value: MetricValue };
        [ProductMetricType.ReactivationRate]: {
            value: MetricValue;
            nested: {
                [ProductMetricType.ReactivationReturned]: { value: MetricValue };
            };
        };
        [ProductMetricType.AvgTimeToReactivation]?: { value: MetricValue };
    };

    // --- Satisfaction ---
    export type Satisfaction = {
        [ProductMetricType.NPS]: { value: MetricValue };
    };

    // --- NorthStar ---
    export type NorthStar = {
        [ProductMetricType.Ltv]: {
            value: MetricValue;
            nested: {
                [ProductMetricType.LtvARPPU]: { value: MetricValue };
                [ProductMetricType.LtvAvgPurchases]: { value: MetricValue };
            };
        };
    };

    export interface MetricsMap {
        [ProductMetricCategory.Acquisition]: Acquisition;
        [ProductMetricCategory.Activation]: Activation;
        [ProductMetricCategory.Conversion]: Conversion;
        [ProductMetricCategory.Retention]: Retention;
        [ProductMetricCategory.Referral]: Referral;
        [ProductMetricCategory.Revenue]: Revenue;
        [ProductMetricCategory.Resurrection]: Resurrection;
        [ProductMetricCategory.Satisfaction]: Satisfaction;
        [ProductMetricCategory.NorthStar]: NorthStar;
    }

    export type MetricsResponse = {
        [K in keyof ProductMetricsMap]: ProductMetricsMap[K];
    };
}
