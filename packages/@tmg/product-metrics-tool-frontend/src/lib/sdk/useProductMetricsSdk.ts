import type { TimestampRange } from '@/lib/core/helper/DateHelper'
import type {
    ProductMetricCategory,
} from '#/packages/@tmg/product-metrics-tool-frontend/src/lib/dictionary/ProductMetricsDictionary'

export function useProductMetricsSdk() {
    const fetchMetricsAction = useContext().useModel('Sale').actions.fetchProductMetrics

    const fetch = <TResponse>(category: 'all' | ProductMetricCategory, range: TimestampRange) => {
        return fetchMetricsAction({
            category,
            range,
        }) as Promise<TResponse>
    }

    async function getData(category: 'all' | ProductMetricCategory, range: TimestampRange) {
        return await fetch(category, range)
    }

    return {
        getData,
    }
}
