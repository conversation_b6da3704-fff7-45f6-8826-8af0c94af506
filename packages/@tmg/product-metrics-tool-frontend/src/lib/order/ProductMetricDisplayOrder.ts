import {
ProductMetricCategory,
ProductMetricType,
} from '#/packages/@tmg/product-metrics-tool-frontend/src/lib/dictionary/ProductMetricsDictionary'

export const ProductMetricCategoryDisplayOrder: ProductMetricCategory[] = [
    ProductMetricCategory.Acquisition,
    ProductMetricCategory.Activation,
    ProductMetricCategory.Conversion,
    ProductMetricCategory.Retention,
    ProductMetricCategory.Referral,
    ProductMetricCategory.Revenue,
    ProductMetricCategory.Resurrection,
    ProductMetricCategory.Satisfaction,
    ProductMetricCategory.NorthStar,
]

export const ProductMetricTypeDisplayOrder: {
    [K in ProductMetricCategory]: {
        types: ProductMetricType[]
        nested?: {
            [T in ProductMetricType]?: ProductMetricType[]
        }
    }
} = {
    [ProductMetricCategory.Acquisition]: {
        types: [
            ProductMetricType.TotalVisitors,
            ProductMetricType.CostPerVisitor,
        ],
        nested: {
            [ProductMetricType.TotalVisitors]: [
                ProductMetricType.TotalVisitorsPaid,
                ProductMetricType.TotalVisitorsOrganic,
            ],
        },
    },

    [ProductMetricCategory.Activation]: {
        types: [
            ProductMetricType.ActivationRate,
            ProductMetricType.CostPerAcquisition,
            ProductMetricType.AvgFirstResponseTime,
        ],
        nested: {
            [ProductMetricType.ActivationRate]: [
                ProductMetricType.ActivationRateJivoInitiated,
                ProductMetricType.ActivationRateViewedPseudoOffer,
                ProductMetricType.ActivationRateFormSubmitted,
            ],
        },
    },

    [ProductMetricCategory.Conversion]: {
        types: [
            ProductMetricType.LeadToPurchaseRate,
            ProductMetricType.AvgTimeFromLeadToPurchase,
            ProductMetricType.CustomerAcquisitionCost,
        ],
    },

    [ProductMetricCategory.Retention]: {
        types: [
            ProductMetricType.CustomerRetentionRate,
            ProductMetricType.AvgPurchasesPerCustomer,
            ProductMetricType.LoyaltyProgramEngagement,
        ],
    },

    [ProductMetricCategory.Referral]: {
        types: [
            ProductMetricType.NewCustomersViaReferral,
            ProductMetricType.PercentUsersSharingReferrals,
            ProductMetricType.CustomerReviews,
        ],
        nested: {
            [ProductMetricType.NewCustomersViaReferral]: [
                ProductMetricType.NewCustomersViaReferralLink,
                ProductMetricType.NewCustomersViaReferralClient,
            ],
        },
    },

    [ProductMetricCategory.Revenue]: {
        types: [
            ProductMetricType.TotalRevenue,
            ProductMetricType.ARPPU,
            ProductMetricType.OrdersWithUpsellsRate,
            ProductMetricType.AvgOrderValue,
            ProductMetricType.GrossProfit,
            ProductMetricType.ProfitMargin,
        ],
    },

    [ProductMetricCategory.Resurrection]: {
        types: [
            ProductMetricType.DormantUsers,
            ProductMetricType.ReactivationRate,
            ProductMetricType.AvgTimeToReactivation,
        ],
        nested: {
            [ProductMetricType.ReactivationRate]: [
                ProductMetricType.ReactivationReturned,
            ],
        },
    },

    [ProductMetricCategory.Satisfaction]: {
        types: [
            ProductMetricType.NPS,
        ],
    },

    [ProductMetricCategory.NorthStar]: {
        types: [
            ProductMetricType.Ltv,
        ],
        nested: {
            [ProductMetricType.Ltv]: [
                ProductMetricType.LtvARPPU,
                ProductMetricType.LtvAvgPurchases,
            ],
        },
    },
}
