import WalletIcon from '#/src-new/assets/icons/WalletIcon.svg?component'
import PackagePlusIcon from '#/src-new/assets/icons/PackagePlusIcon.svg?component'
import SparkleIcon from '#/src-new/assets/icons/SparkleIcon.svg?component'
import UserEntranceIcon from '#/packages/@tmg/product-metrics-tool-frontend/src/assets/icons/UserEntranceIcon.svg?component'
import TargetIcon from '#/packages/@tmg/product-metrics-tool-frontend/src/assets/icons/TargetIcon.svg?component'
import UserMoneyIcon from '#/packages/@tmg/product-metrics-tool-frontend/src/assets/icons/UserMoneyIcon.svg?component'
import CoinsIcon from '#/packages/@tmg/product-metrics-tool-frontend/src/assets/icons/CoinsIcon.svg?component'

export type ProductMetricTypeInfo = {
    title: string,
    tooltip?: string,
    icon?: Component,
    isNested?: boolean
}

export enum ProductMetricCategory {
    Acquisition = 'acquisition',
    Activation = 'activation',
    Conversion = 'conversion',
    Retention = 'retention',
    Referral = 'referral',
    Revenue = 'revenue',
    Resurrection = 'resurrection',
    Satisfaction = 'satisfaction',
    NorthStar = 'northstar',
}

export enum ProductMetricType {
    //
    TotalVisitors = 'total_visitors',
    TotalVisitorsPaid = 'total_visitors_paid',
    TotalVisitorsOrganic = 'total_visitors_organic',
    //
    CostPerVisitor = 'cost_per_visitor',
    //
    ActivationRate = 'activation_rate',
    ActivationRateJivoInitiated = 'activation_rate_jivo_initiated',
    ActivationRateViewedPseudoOffer = 'activation_rate_viewed_pseudo_offer',
    ActivationRateFormSubmitted = 'activation_rate_form_submitted',
    //
    CostPerAcquisition = 'cost_per_acquisition',
    //
    AvgFirstResponseTime = 'avg_first_response_time',
    //
    LeadToPurchaseRate = 'lead_to_purchase_rate',
    //
    AvgTimeFromLeadToPurchase = 'avg_time_from_lead_to_purchase',
    //
    CustomerAcquisitionCost = 'customer_acquisition_cost',
    //
    CustomerRetentionRate = 'customer_retention_rate',
    //
    AvgPurchasesPerCustomer = 'avg_purchases_per_customer',
    //
    LoyaltyProgramEngagement = 'loyalty_program_engagement',
    //
    NewCustomersViaReferral = 'new_customers_via_referral',
    NewCustomersViaReferralLink = 'new_customers_via_referral_link',
    NewCustomersViaReferralClient = 'new_customers_via_referral_client',
    //
    PercentUsersSharingReferrals = 'percent_users_sharing_referrals',
    //
    CustomerReviews = 'customer_reviews',
    //
    TotalRevenue = 'total_revenue',
    //
    ARPPU = 'arppu',
    //
    OrdersWithUpsellsRate = 'orders_with_upsells_rate',
    //
    AvgOrderValue = 'avg_order_value',
    //
    GrossProfit = 'gross_profit',
    //
    ProfitMargin = 'profit_margin',
    //
    DormantUsers = 'dormant_users',
    //
    ReactivationRate = 'reactivation_rate',
    ReactivationReturned = 'reactivation_returned',

    //
    AvgTimeToReactivation = 'avg_time_to_reactivation',
    //
    NPS = 'nps',
    //
    Ltv = 'ltv',
    LtvARPPU = 'ltv_arppu',
    LtvAvgPurchases = 'ltv_avg_purchases',
}

export enum MetricUnit {
    Percent = 'percent',
    Money = 'money',
    Number = 'number',
    TimeSec = 'seconds',
}

export const ProductMetricTypeMap: Record<ProductMetricType, ProductMetricTypeInfo> = {
    [ProductMetricType.TotalVisitors]: {
        title: 'Total Visitors',
        icon: UsersIcon,
        isNested: false,
    },
    [ProductMetricType.TotalVisitorsPaid]: {
        title: 'Paid channel visitors',
        isNested: true,
    },
    [ProductMetricType.TotalVisitorsOrganic]: {
        title: 'Organic visitors',
        isNested: true,
    },
    [ProductMetricType.CostPerVisitor]: {
        title: 'CPV',
        tooltip: 'Total Marketing Spend / Number of Paid Visitors',
        icon: UserEntranceIcon,
        isNested: false,
    },
    [ProductMetricType.ActivationRate]: {
        title: 'Activation Rate',
        tooltip: '(Activated Visitors / Total Visitors) × 100%',
        icon: PercentIcon,
        isNested: false,
    },
    [ProductMetricType.ActivationRateJivoInitiated]: {
        title: 'JivoChat initiated',
        isNested: true,
    },
    [ProductMetricType.ActivationRateViewedPseudoOffer]: {
        title: 'Viewed pseudo-offer',
        isNested: true,
    },
    [ProductMetricType.ActivationRateFormSubmitted]: {
        title: 'Form submitted',
        isNested: true,
    },
    [ProductMetricType.CostPerAcquisition]: {
        title: 'CPA',
        tooltip: 'Total Marketing Spend / Number of Leads',
        icon: TargetIcon,
        isNested: false,
    },
    [ProductMetricType.AvgFirstResponseTime]: {
        title: 'Avg. FRT',
        tooltip: 'Σ( Time of Agent’s First Reply – Lead Submission Time ) / Number of Leads',
        icon: ClockIcon,
        isNested: false,
    },
    [ProductMetricType.LeadToPurchaseRate]: {
        title: 'Lead-to-Purchase',
        tooltip: '(Number of Purchases / Number of Leads) × 100%',
        icon: UserCheckIcon,
        isNested: false,
    },
    [ProductMetricType.AvgTimeFromLeadToPurchase]: {
        title: 'Avg. Time',
        tooltip: 'Σ (Purchase Date – Lead Date) / Number of Purchases',
        icon: ClockIcon,
        isNested: false,
    },
    [ProductMetricType.CustomerAcquisitionCost]: {
        title: 'CAC',
        tooltip: 'Total Marketing & Sales Spend / Number of Paying Customers',
        icon: UserMoneyIcon,
        isNested: false,
    },
    [ProductMetricType.CustomerRetentionRate]: {
        title: 'Retention Rate',
        tooltip: '(Repeat Customers / Total Customers) × 100%',
        icon: RotateCwIcon,
        isNested: false,
    },
    [ProductMetricType.AvgPurchasesPerCustomer]: {
        title: 'Avg. Purchases',
        tooltip: 'Total Purchases / Total Customers',
        icon: ShoppingBagIcon,
        isNested: false,
    },
    [ProductMetricType.LoyaltyProgramEngagement]: {
        title: 'Engagement',
        tooltip: '(Active Loyalty Users / Total Users with App) × 100%',
        icon: ZapIcon,
        isNested: false,
    },
    [ProductMetricType.NewCustomersViaReferral]: {
        title: 'Referral Customers',
        tooltip: 'Customers acquired through referral links',
        icon: UserPlusIcon,
        isNested: false,
    },
    [ProductMetricType.NewCustomersViaReferralLink]: {
        title: 'Referred via link',
        isNested: true,
    },
    [ProductMetricType.NewCustomersViaReferralClient]: {
        title: 'Referred via client',
        isNested: true,
    },
    [ProductMetricType.PercentUsersSharingReferrals]: {
        title: 'Sharing Rate',
        tooltip: '(Users who Shared Referral / Total Active Users) × 100%',
        icon: SendIcon,
        isNested: false,
    },
    [ProductMetricType.CustomerReviews]: {
        title: 'Customer Reviews',
        tooltip: 'Total number of reviews collected',
        icon: MessageSquareIcon,
        isNested: false,
    },
    [ProductMetricType.TotalRevenue]: {
        title: 'Total Revenue',
        tooltip: 'Sell Price',
        icon: WalletIcon,
        isNested: false,
    },
    [ProductMetricType.ARPPU]: {
        title: 'ARPPU',
        tooltip: 'Total Revenue / Paying Customers',
        icon: CoinsIcon,
        isNested: false,
    },
    [ProductMetricType.OrdersWithUpsellsRate]: {
        title: 'Upsell Rate',
        tooltip: '(Orders with Upsells / Total Orders) × 100%',
        icon: PackagePlusIcon,
        isNested: false,
    },
    [ProductMetricType.AvgOrderValue]: {
        title: 'AOV',
        tooltip: 'Total Revenue / Total Orders',
        icon: DollarSignIcon,
        isNested: false,
    },
    [ProductMetricType.GrossProfit]: {
        title: 'Gross Profit',
        tooltip: 'Total Revenue – Direct Costs (tickets, fees, supplier costs)',
        icon: WalletIcon,
        isNested: false,
    },
    [ProductMetricType.ProfitMargin]: {
        title: 'Profit Margin',
        tooltip: '(Gross Profit / Total Revenue) × 100%',
        icon: TrendingUpIcon,
        isNested: false,
    },
    [ProductMetricType.DormantUsers]: {
        title: 'Dormant Users',
        tooltip: 'Users who had no activity (> 6 months inactive)',
        icon: MoonIcon,
        isNested: false,
    },
    [ProductMetricType.ReactivationRate]: {
        title: 'Reactivation Rate',
        tooltip: '(Reactivated Users / Dormant Users) × 100%',
        icon: RefreshCwIcon,
        isNested: false,
    },
    [ProductMetricType.ReactivationReturned]: {
        title: 'Returned',
        isNested: true,
    },
    [ProductMetricType.AvgTimeToReactivation]: {
        title: 'Avg. Time to Reactivation',
        tooltip: 'Σ (Reactivation Date – Dormancy Start Date) / Reactivated Users',
        icon: ClockIcon,
        isNested: false,
    },
    [ProductMetricType.NPS]: {
        title: 'NPS',
        tooltip: '% Promoters (9–10) – % Detractors (0–6)',
        icon: SmileIcon,
        isNested: false,
    },
    [ProductMetricType.Ltv]: {
        title: 'LTV',
        tooltip: 'ARPPU × Avg. Purchases per Customer',
        icon: SparkleIcon,
        isNested: false,
    },
    [ProductMetricType.LtvARPPU]: {
        title: 'ARPPU',
        isNested: true,
    },
    [ProductMetricType.LtvAvgPurchases]: {
        title: 'Avg. Purchases',
        isNested: true,
    },
}

export const ProductMetricCategoryMap: Record<ProductMetricCategory, ProductMetricTypeInfo> = {
    [ProductMetricCategory.Acquisition]: {
        title: 'Acquisition',
    },
    [ProductMetricCategory.Activation]: {
        title: 'Activation',
    },
    [ProductMetricCategory.Conversion]: {
        title: 'Conversion',
    },
    [ProductMetricCategory.Retention]: {
        title: 'Retention',
    },
    [ProductMetricCategory.Referral]: {
        title: 'Referral',
    },
    [ProductMetricCategory.Revenue]: {
        title: 'Revenue',
    },
    [ProductMetricCategory.Resurrection]: {
        title: 'Resurrection',
    },
    [ProductMetricCategory.Satisfaction]: {
        title: 'Satisfaction',
    },
    [ProductMetricCategory.NorthStar]: {
        title: 'North Star',
    },
}

