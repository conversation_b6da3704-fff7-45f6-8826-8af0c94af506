import { MetricUnit } from '#/packages/@tmg/product-metrics-tool-frontend/src/lib/dictionary/ProductMetricsDictionary'

export function getDeltaValue(current: number | undefined | null, previous: number | undefined | null) {
    if (!previous || +previous === 0) {
        return {
            direction: 'unknown',
            percent: null,
        }
    }

    const currentWithOneDecimal = Number(current)
    const previousWithOneDecimal = Number(previous)

    const diff = currentWithOneDecimal! - previousWithOneDecimal!
    const rawPercent = Math.abs(diff / previousWithOneDecimal!) * 100

    let direction: 'increase' | 'decrease' | 'equal' = 'equal'

    if (diff > 0) direction = 'increase'
    else if (diff < 0) direction = 'decrease'

    return {
        direction,
        percent: Math.round(rawPercent * 10) / 10,
    }
}

const format = useService('formatter')

export function formatMetricValue(value: number, unit: MetricUnit) {
    switch (unit) {
        case MetricUnit.Money:
            return format.money(value, {
                fraction: value > 100 ? 0 : 2,
                withCurrency: 'USD',
            })

        case MetricUnit.Percent:
            return value == 100 ? `${Number(value).toFixed(0)}%` : `${Number(value).toFixed(1)}%`

        case MetricUnit.TimeSec:
            return String.diffHoHumanFormat(Math.round(value))

        case MetricUnit.Number:
            return value > 100
                ? `${Math.floor(value)}`
                : `${Number(value).toFixed(1)}`
    }
}
