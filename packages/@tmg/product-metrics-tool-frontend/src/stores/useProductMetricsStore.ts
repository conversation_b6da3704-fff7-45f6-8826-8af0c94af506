import { defineStore } from 'pinia'
import { useProductMetricsSdk } from '#/packages/@tmg/product-metrics-tool-frontend/src/lib/sdk/useProductMetricsSdk'
import type { ProductMetrics } from '#/packages/@tmg/product-metrics-tool-frontend/src/types/ProductMetrics'
import type { TimestampRange } from '@/lib/core/helper/DateHelper'
import { timestampRangeToDateRange } from '@/lib/core/helper/DateHelper'
import { samePeriodInPast } from '@/lib/core/helper/DateHelper'
import {
dateRangeToTimestampRange,
getRangeThisMonth,
getRangeThisWeek,
getRangeThisYear,
} from '@/lib/core/helper/DateHelper'
import type {
ProductMetricType,
ProductMetricTypeContents,
} from '#/packages/@tmg/product-metrics-tool-frontend/src/lib/dictionary/ProductMetricsDictionary'
import {
ProductMetricCategory,
} from '#/packages/@tmg/product-metrics-tool-frontend/src/lib/dictionary/ProductMetricsDictionary'

export enum ProductMetricsRange {
    Year = 'year',
    Month = 'month',
    Week = 'week',
    Custom = 'custom',
}

type FlattenMetricType = {
    [key in ProductMetricType]: {
        value: ProductMetrics.MetricValue,
        contents?: { [key in ProductMetricTypeContents]: ProductMetrics.MetricValue | undefined },
    }
}

type CategoryLoadingState = {
    isLoading: boolean
    loadingRange: TimestampRange | undefined
}

type CategoriesLoadingState = {
    [key in ProductMetricCategory]: CategoryLoadingState
}

export const useProductMetricsStore = defineStore('product-metrics', () => {
    const sdk = useProductMetricsSdk()
    const globalRange = ref<ProductMetricsRange>(ProductMetricsRange.Year)
    const globalCustomRange = ref<TimestampRange>()

    const metrics = reactive({} as ProductMetrics.MetricsResponse)
    const prevPeriodMetrics = reactive({} as FlattenMetricType)

    const categoriesLoadingState = ref<CategoriesLoadingState>({
        [ProductMetricCategory.Acquisition]: {
            isLoading: false,
            loadingRange: undefined,
        },
        [ProductMetricCategory.Activation]: {
            isLoading: false,
            loadingRange: undefined,
        },
        [ProductMetricCategory.Conversion]: {
            isLoading: false,
            loadingRange: undefined,
        },
        [ProductMetricCategory.Retention]: {
            isLoading: false,
            loadingRange: undefined,
        },
        [ProductMetricCategory.Referral]: {
            isLoading: false,
            loadingRange: undefined,
        },
        [ProductMetricCategory.Revenue]: {
            isLoading: false,
            loadingRange: undefined,
        },
        [ProductMetricCategory.Resurrection]: {
            isLoading: false,
            loadingRange: undefined,
        },
        [ProductMetricCategory.Satisfaction]: {
            isLoading: false,
            loadingRange: undefined,
        },
        [ProductMetricCategory.NorthStar]: {
            isLoading: false,
            loadingRange: undefined,
        },
    })

    async function fetch(category: 'all' | ProductMetricCategory = 'all', range?: TimestampRange) {
        if (category === 'all') {
            Object.keys(categoriesLoadingState.value).forEach((key) => {
                categoriesLoadingState.value[key as ProductMetricCategory].isLoading = true
                categoriesLoadingState.value[key as ProductMetricCategory].loadingRange = range
            })
        } else {
            categoriesLoadingState.value[category as ProductMetricCategory].isLoading = true
            categoriesLoadingState.value[category as ProductMetricCategory].loadingRange = range
        }

        const intervalRange = range ? range : getTimestampRangeFromRange()

        const previousRange: TimestampRange = getPreviousPeriod(intervalRange)

        const [current, previous] = await Promise.all([
            sdk.getData(category, intervalRange),
            sdk.getData(category, previousRange),
        ])

        if (category === 'all') {
            Object.assign(metrics, current)
            Object.assign(prevPeriodMetrics, flattenMetrics(previous))
            Object.keys(categoriesLoadingState.value).forEach((key) => {
                categoriesLoadingState.value[key as ProductMetricCategory].isLoading = false
            })
        } else {
            if (categoriesLoadingState.value[category as ProductMetricCategory].loadingRange) {
                const start = categoriesLoadingState.value[category as ProductMetricCategory].loadingRange?.start
                const end = categoriesLoadingState.value[category as ProductMetricCategory].loadingRange?.end

                if (start === range?.start && end === range?.end) {
                    Object.assign(metrics, current)
                    Object.assign(prevPeriodMetrics, flattenMetrics(previous))
                    categoriesLoadingState.value[category as ProductMetricCategory].isLoading = false
                }
            }
        }
    }

    function getPreviousPeriod(timestampRange: TimestampRange): TimestampRange {
        const rangeInDate = timestampRangeToDateRange(timestampRange)

        const pastPeriodInDate = samePeriodInPast(rangeInDate)

        return dateRangeToTimestampRange(pastPeriodInDate)
    }

    function flattenMetrics(metrics: ProductMetrics.MetricsResponse) {
        const flat = {}

        for (const categoryKey of Object.keys(metrics) as Array<keyof ProductMetrics.MetricsResponse>) {
            const categoryMetrics = metrics[categoryKey]

            for (const metricKey of Object.keys(categoryMetrics) as ProductMetricType[]) {
                const metricEntry = categoryMetrics[metricKey]

                if (!metricEntry) continue

                flat[metricKey] = { value: metricEntry.value }

                if ('nested' in metricEntry && metricEntry.nested) {
                    for (const nestedKey of Object.keys(metricEntry.nested) as ProductMetricType[]) {
                        flat[nestedKey] = {
                            value: metricEntry.nested[nestedKey].value,
                            contents: metricEntry.nested[nestedKey].contents,
                        }
                    }
                }
            }
        }

        return flat
    }

    function getTimestampRangeFromRange(
        range?: ProductMetricsRange.Year | ProductMetricsRange.Month | ProductMetricsRange.Week,
    ): TimestampRange {
        if (range === ProductMetricsRange.Year) {
            return dateRangeToTimestampRange(getRangeThisYear())
        } else if (range === ProductMetricsRange.Month) {
            return dateRangeToTimestampRange(getRangeThisMonth())
        } else if (range === ProductMetricsRange.Week) {
            return dateRangeToTimestampRange(getRangeThisWeek())
        } else {
            return dateRangeToTimestampRange(getRangeThisYear())
        }
    }

    async function setGlobalRange(range: ProductMetricsRange) {
        globalRange.value = range

        if (range === ProductMetricsRange.Custom) {
            return
        }

        const intervalRange = getTimestampRangeFromRange(range)

        await fetch('all', intervalRange)
    }

    async function setGlobalCustomRange(range: TimestampRange) {
        globalCustomRange.value = range

        await fetch('all', range)
    }

    async function setCategoryCustomRange(category: ProductMetricCategory, range: TimestampRange) {
        await fetch(category, range)
    }

    return {
        metrics,
        prevPeriodMetrics,
        fetch,
        globalRange,
        globalCustomRange,
        setGlobalRange,
        setGlobalCustomRange,
        getTimestampRangeFromRange,
        setCategoryCustomRange,
        categoriesLoadingState,
    }
})
