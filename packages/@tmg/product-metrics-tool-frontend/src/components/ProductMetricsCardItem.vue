<template>
    <div
        class="w-full overflow-hidden bg-white dark:bg-dark-1"
        :class="{
            'border-t border-b border-secondary-100 dark:border-secondary-700 rounded-lg': !metricTypeValues.isNested
        }"
    >
        <div
            class="px-4 py-2.5"
            :class="{
                '!py-2 bg-[#FCFCFD] dark:bg-dark-4 border-t border-secondary-100 dark:border-secondary-700': metricTypeValues.isNested,
            }"
        >
            <div
                class="flex items-center justify-between"
                :class="{
                    'cursor-pointer': metricSubTypes
                }"
                @click="toggleMetric"
            >
                <div class="flex items-center gap-x-1">
                    <div v-if="metricTypeValues.icon" class="p-2 bg-secondary-50  dark:bg-dark-2 text-secondary-400 rounded-full">
                        <Component
                            :is="metricTypeValues.icon"
                            class="w-4 h-4 "
                        />
                    </div>
                    <div class="text-sm font-semibold">
                        {{ metricTypeValues.title }}
                    </div>
                    <div
                        v-if="metricTypeValues.tooltip"
                        v-tooltip="{content: `${metricTypeValues.tooltip}`}"
                        class="text-secondary dark:text-secondary-300"
                    >
                        <MetricInfoIcon class="h-3.5 w-3.5" />
                    </div>
                </div>
                <div class="flex items-center gap-x-1">
                    <span v-if="metricUnit.value" class="text-2xl font-semibold">{{ formatValue(metricUnit.value, metricUnit.unit) }}</span>
                    <div class="flex items-center mt-1.5">
                        <span v-if="deltaValue.direction === 'unknown'" class="text-secondary-400 dark:text-secondary-300 flex items-center text-xs">
                            <span class="font-medium">N/A</span>
                        </span>
                        <span v-else-if="deltaValue.direction === 'equal'" class="text-secondary flex items-center text-xs">
                            <span class="font-medium">{{ deltaValue.percent }}%</span>
                        </span>
                        <span v-else-if="deltaValue.direction === 'increase'" class="text-success flex items-center text-xs">
                            <MoveUpIcon />
                            <span class="font-medium">{{ deltaValue.percent }}%</span>
                        </span>
                        <span v-else class="text-danger flex items-center text-xs">
                            <MoveDownIcon />
                            <span class="font-medium mt-0.5">{{ deltaValue.percent }}%</span>
                        </span>
                    </div>
                    <div
                        v-if="metricSubTypes"
                        class="text-secondary"
                        :class="{
                            'rotate-180': isOpenedSubTypes,
                        }"
                    >
                        <ChevronDownIcon class="h-4 w-4" />
                    </div>
                </div>
            </div>
        </div>
        <div v-if="metricSubTypes && isOpenedSubTypes">
            <ProductMetricsCardNestedItem
                v-for="(item, index) in metricSubTypes"
                :key="index"
                :type="item.type"
                :value="item.value"
                :category="category"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import type {
    ProductMetricCategory,
    ProductMetricType,
    MetricUnit,
} from '#/packages/@tmg/product-metrics-tool-frontend/src/lib/dictionary/ProductMetricsDictionary'
import {
    ProductMetricTypeMap,
} from '#/packages/@tmg/product-metrics-tool-frontend/src/lib/dictionary/ProductMetricsDictionary'
import type { ProductMetrics } from '#/packages/@tmg/product-metrics-tool-frontend/src/types/ProductMetrics'
import { useProductMetricsStore } from '#/packages/@tmg/product-metrics-tool-frontend/src/stores/useProductMetricsStore'
import MoveUpIcon from '#/packages/@tmg/product-metrics-tool-frontend/src/assets/icons/MoveUpIcon.svg?component'
import MoveDownIcon from '#/packages/@tmg/product-metrics-tool-frontend/src/assets/icons/MoveDownIcon.svg?component'
import MetricInfoIcon from '#/packages/@tmg/product-metrics-tool-frontend/src/assets/icons/MetricInfoIcon.svg?component'
import {
    ProductMetricTypeDisplayOrder,
} from '#/packages/@tmg/product-metrics-tool-frontend/src/lib/order/ProductMetricDisplayOrder'
import ProductMetricsCardNestedItem
    from '#/packages/@tmg/product-metrics-tool-frontend/src/components/ProductMetricsCardNestedItem.vue'
import {
    formatMetricValue,
    getDeltaValue,
} from '#/packages/@tmg/product-metrics-tool-frontend/src/lib/helper/ProductMetricHelper'

const props = defineProps<{
    type: ProductMetricType,
    value: {
        value: ProductMetrics.MetricValue,
        nested?: {
            [key: string]: ProductMetrics.MetricValue
        }
    },
    category: ProductMetricCategory,
}>()

const { prevPeriodMetrics } = useProductMetricsStore()

const deltaValue = computed(() => {
    const current = props.value.value.value
    const previous = prevPeriodMetrics[props.type]?.value.value ? prevPeriodMetrics[props.type]?.value.value : undefined

    return getDeltaValue(current, previous)
})

//

const metricUnit = computed(() => {
    return props.value.value as ProductMetrics.MetricValue
})

const metricTypeValues = computed(() => {
    return ProductMetricTypeMap[props.type]
})

const metricSubTypes = computed(() => {
    const nested = props.value.nested

    if (!nested) return undefined
    const nestedOrder = ProductMetricTypeDisplayOrder[props.category]?.nested?.[props.type] ?? []

    return nestedOrder
        .filter((type) => type in nested)
        .map((type) => ({
            type,
            value: nested[type],
        }))
})

//

function formatValue(val: number, unit: MetricUnit) {
    return formatMetricValue(val, unit)
}

//

const isOpenedSubTypes = ref(true)

function toggleMetric() {
    isOpenedSubTypes.value = !isOpenedSubTypes.value
}
</script>
