<template>
    <div
        class="px-4 pl-12 py-3.5"
        :class="{
            '!py-2 bg-[#FCFCFD] dark:bg-dark-4 border-t border-secondary-100 dark:border-secondary-700': metricTypeValues.isNested,
            'cursor-pointer': contents
        }"
        @click="toggleContentsBlock"
    >
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-x-2">
                <div class="h-[4px] w-[4px] bg-[#242D34] rounded-full dark:bg-secondary" />
                <div class="text-xs font-semibold">
                    {{ metricTypeValues.title }}
                </div>
            </div>
            <div class="flex items-center gap-x-1">
                <span v-if="metricUnit.value" class="text-base font-semibold">{{ formatValue(metricUnit.value, metricUnit.unit) }}</span>
                <div class="flex items-center mt-0.5">
                    <span v-if="deltaValue.direction === 'unknown'" class="text-secondary-400 dark:text-secondary-300 flex items-center text-xs">
                        <span class="font-medium">N/A</span>
                    </span>
                    <span v-else-if="deltaValue.direction === 'equal'" class="text-secondary flex items-center text-xs">
                        <span class="font-medium">{{ deltaValue.percent }}%</span>
                    </span>
                    <span v-else-if="deltaValue.direction === 'increase'" class="text-success flex items-center text-xs">
                        <MoveUpIcon />
                        <span class="font-medium">{{ deltaValue.percent }}%</span>
                    </span>
                    <span v-else class="text-danger flex items-center text-xs">
                        <MoveDownIcon />
                        <span class="font-medium mt-0.5">{{ deltaValue.percent }}%</span>
                    </span>
                </div>
                <div
                    v-if="contents"
                    class="text-secondary"
                    :class="{
                        'rotate-180': isOpenedContentsBlock,
                    }"
                >
                    <ChevronDownIcon class="h-4 w-4" />
                </div>
            </div>
        </div>
        <div v-if="contents && isOpenedContentsBlock" class="mt-1.5 pl-5 flex flex-col gap-y-1.5 w-full">
            <div
                v-for="(item, index) in contents"
                :key="index"
                class="flex w-full items-center justify-between"
            >
                <div class="text-xs text-secondary font-medium dark:text-secondary-300">
                    {{ contentKeyToHumanFormat(item.contentsKey) }}
                </div>
                <div class="flex items-center gap-x-1">
                    <span class="text-xs font-medium text-secondary dark:text-secondary-300">{{ formatValue(item.value.value, item.value.unit) }}</span>
                    <div class="flex items-center">
                        <span v-if="contentsDeltaValues[item.contentsKey].direction === 'unknown'" class="text-secondary dark:text-secondary-100 flex items-center text-xs">
                            <span class="font-medium">N/A</span>
                        </span>
                        <span v-else-if="contentsDeltaValues[item.contentsKey].direction === 'equal'" class="text-secondary flex items-center text-xs">
                            <span class="font-medium">{{ contentsDeltaValues[item.contentsKey].percent }}%</span>
                        </span>
                        <span v-else-if="contentsDeltaValues[item.contentsKey].direction === 'increase'" class="text-success flex items-center text-xs">
                            <MoveUpIcon />
                            <span class="text-2xs font-medium">{{ contentsDeltaValues[item.contentsKey].percent }}%</span>
                        </span>
                        <span v-else class="text-danger flex items-center text-xs">
                            <MoveDownIcon />
                            <span class="text-2xs font-medium">{{ contentsDeltaValues[item.contentsKey].percent }}%</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type {
    ProductMetricCategory,
    ProductMetricType,
    MetricUnit,
} from '#/packages/@tmg/product-metrics-tool-frontend/src/lib/dictionary/ProductMetricsDictionary'
import {
    ProductMetricTypeMap,
} from '#/packages/@tmg/product-metrics-tool-frontend/src/lib/dictionary/ProductMetricsDictionary'
import type { ProductMetrics } from '#/packages/@tmg/product-metrics-tool-frontend/src/types/ProductMetrics'
import MoveUpIcon from '#/packages/@tmg/product-metrics-tool-frontend/src/assets/icons/MoveUpIcon.svg?component'
import MoveDownIcon from '#/packages/@tmg/product-metrics-tool-frontend/src/assets/icons/MoveDownIcon.svg?component'
import { useProductMetricsStore } from '#/packages/@tmg/product-metrics-tool-frontend/src/stores/useProductMetricsStore'
import {
    formatMetricValue,
    getDeltaValue,
} from '#/packages/@tmg/product-metrics-tool-frontend/src/lib/helper/ProductMetricHelper'
import { toHumanPhrase } from '~/lib/Helper/StringHelper'

const props = defineProps<{
    type: ProductMetricType,
    value: {
        value: ProductMetrics.MetricValue,
        contents?: {
            [key: string]: {
                value: ProductMetrics.MetricValue;
            };
        }
    },
    category: ProductMetricCategory,
}>()

const metricTypeValues = computed(() => {
    return ProductMetricTypeMap[props.type]
})

const metricUnit = computed(() => {
    return props.value.value as ProductMetrics.MetricValue
})

//

function formatValue(val: number, unit: MetricUnit) {
    return formatMetricValue(val, unit)
}

//

const { prevPeriodMetrics } = useProductMetricsStore()

const deltaValue = computed(() => {
    const current = props.value.value.value
    const previous = prevPeriodMetrics[props.type]?.value.value ? Number(prevPeriodMetrics[props.type]?.value.value) : undefined

    return getDeltaValue(current, previous)
})

const contentsDeltaValues = computed(() => {
    if (!props.value.contents) {
        return undefined
    }

    const contentsCurrentValue = props.value.contents
    const contentsPrevValue = prevPeriodMetrics[props.type]?.contents

    return Object.fromEntries(
        Object.keys(contentsCurrentValue).map((key) => {
            const current = contentsCurrentValue[key].value.value
            const previous = contentsPrevValue ? contentsPrevValue[key]?.value.value ? contentsPrevValue[key]?.value.value : undefined : undefined

            const deltaContents = getDeltaValue(current, previous)

            return [
                key,
                deltaContents,
            ]
        }),
    )
})

// contents

const contents = computed(() => {
    if (!props.value.contents) {
        return undefined
    }

    return Object.entries(props.value.contents).map((item) => {
        return {
            contentsKey: item[0],
            value: item[1].value,
        }
    })
})

// contents

const isOpenedContentsBlock = ref(false)

function toggleContentsBlock() {
    isOpenedContentsBlock.value = !isOpenedContentsBlock.value
}

function contentKeyToHumanFormat(key: string) {
    return toHumanPhrase(key)
}
</script>
