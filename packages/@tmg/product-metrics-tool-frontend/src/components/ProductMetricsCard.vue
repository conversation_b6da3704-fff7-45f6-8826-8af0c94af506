<template>
    <div v-if="categoryLoading" class="w-full h-full">
        <PlaceholderBlock class="w-full h-full" />
    </div>
    <div v-else class="flex w-full flex-col h-full border border-[#EBEDF3] bg-[#FCFCFD] dark:bg-dark-2 rounded-lg">
        <div class="flex flex-col w-full h-full items-start">
            <div class="w-full px-4 py-2.5 flex items-center justify-between">
                <span class="text-sm font-semibold">{{ categoryTitle }}</span>
                <div class="flex items-center gap-x-2">
                    <InputDateRange
                        :timestamp="metricCustomRangeValue"
                        size="small"
                        timezone="UTC"
                        class="w-[180px] dark:border dark:border-secondary dark:rounded-md"
                        :placeholder-icon="null"
                        :placeholder="category === ProductMetricCategory.NorthStar ? 'Full period' : rangePlaceholder"
                        :max-date="new Date()"
                        @update:timestamp="(val: TimestampRange)=>{
                            updateCustomRangeValue(val)
                        }"
                    />
                </div>
            </div>
            <div class="flex flex-col gap-y-0.5 w-full rounded-lg">
                <ProductMetricsCardItem
                    v-for="(item, index) in orderedMetrics"
                    :key="index"
                    :type="item.type"
                    :value="item.entry"
                    :category="category"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { ProductMetrics } from '#/packages/@tmg/product-metrics-tool-frontend/src/types/ProductMetrics'
import {
    ProductMetricCategory,
    ProductMetricCategoryMap,
} from '#/packages/@tmg/product-metrics-tool-frontend/src/lib/dictionary/ProductMetricsDictionary'
import type { TimestampRange } from '@/lib/core/helper/DateHelper'
import { dateRangeToDateRangeString, timestampRangeToDateRange } from '@/lib/core/helper/DateHelper'
import ProductMetricsCardItem
    from '#/packages/@tmg/product-metrics-tool-frontend/src/components/ProductMetricsCardItem.vue'
import {
    ProductMetricsRange,
    useProductMetricsStore,
} from '#/packages/@tmg/product-metrics-tool-frontend/src/stores/useProductMetricsStore'
import {
    ProductMetricTypeDisplayOrder,
} from '#/packages/@tmg/product-metrics-tool-frontend/src/lib/order/ProductMetricDisplayOrder'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'

const props = defineProps<{
    item: ProductMetrics.MetricsResponse[keyof ProductMetrics.MetricsResponse],
    category: ProductMetricCategory,
}>()

const orderedMetrics = computed(() => {
    const metrics = props.item as ProductMetrics.MetricsMap[typeof props.category]
    const order = ProductMetricTypeDisplayOrder[props.category]?.types ?? []

    return order
        .filter(type => type in metrics)
        .map(type => ({
            type,
            entry: metrics[type],
        }))
})

const store = useProductMetricsStore()
const { setCategoryCustomRange, getTimestampRangeFromRange } = useProductMetricsStore()

const categoryLoading = computed(() => {
    return store.categoriesLoadingState[props.category].isLoading
})

const selectedGlobalRange = computed(() => {
    return store.globalRange
})

const selectedGlobalCustomRange = computed(() => {
    return store.globalCustomRange
})

const metricCustomRangeValue = ref<TimestampRange>()
const isLocalRangeSelected = ref(false)
const rangePlaceholder = ref('')

function updateCustomRangeValue(range: TimestampRange) {
    if (range) {
        isLocalRangeSelected.value = true
        metricCustomRangeValue.value = range
        setCategoryCustomRange(props.category, range)
    }
}

const categoryTitle = computed(() => {
    return ProductMetricCategoryMap[props.category].title
})

const format = useService('formatter')

watch(selectedGlobalRange, (val) => {
    if (val !== ProductMetricsRange.Custom) {
        metricCustomRangeValue.value = undefined
        const timestampRanges = getTimestampRangeFromRange(val)
        const range = timestampRangeToDateRange(timestampRanges)
        rangePlaceholder.value = dateRangeToDateRangeString(range)
        isLocalRangeSelected.value = false
    }
}, {
    immediate: true,
})

watch(selectedGlobalCustomRange, (val: TimestampRange) => {
    if (selectedGlobalRange.value === ProductMetricsRange.Custom) {
        metricCustomRangeValue.value = undefined
        const range = timestampRangeToDateRange(val)
        rangePlaceholder.value = dateRangeToDateRangeString(range)
        isLocalRangeSelected.value = false
    }
}, {
    immediate: true,
})
</script>
