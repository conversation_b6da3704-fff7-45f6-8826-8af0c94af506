/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    '/api/pcc-configuration': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List all PccConfiguration
         * @description Returns a list of all PccConfiguration with their related data
         */
        get: operations['getPccConfiguration_list'];
        put?: never;
        /**
         * Create a new PccConfiguration
         * @description Creates a new PccConfiguration with related data including snap codes, service limitations, and price modifier groups
         */
        post: operations['getPccConfiguration_create'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/pcc-configuration/{id}': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get PccConfiguration by ID
         * @description Returns detailed information about a specific PccConfiguration including related data
         */
        get: operations['getPccConfiguration_get'];
        /**
         * Update PccConfiguration
         * @description Updates an existing PccConfiguration and its related data including snap codes, service limitations, and price modifier groups
         */
        put: operations['getPccConfiguration_update'];
        post?: never;
        /**
         * Delete PccConfiguration
         * @description Soft deletes an existing PccConfiguration and its related data
         */
        delete: operations['getPccConfiguration_delete'];
        options?: never;
        head?: never;
        /**
         * Restore deleted PccConfiguration
         * @description Restores a previously soft-deleted PccConfiguration and its related data
         */
        patch: operations['getPccConfiguration_restore'];
        trace?: never;
    };
    '/api/pcc-configuration/filter': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Filter PccConfigurations
         * @description Returns a filtered list of PccConfiguration based on provided criteria
         */
        post: operations['getPccConfiguration_filter'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/flight-hacks-rule': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get all FlightHacksRule
         * @description Display a listing of the resource.
         */
        get: operations['FlightHacksRule_list'];
        put?: never;
        /** Create a new FlightHacksRule. */
        post: operations['FlightHacksRule_create'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/flight-hacks-rule/{id}': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get one FlightHacksRule. */
        get: operations['FlightHacksRule_get'];
        /** Update existing FlightHacksRule. */
        put: operations['FlightHacksRule_update'];
        post?: never;
        /** Delete FlightHacksRule. */
        delete: operations['FlightHacksRule_delete'];
        options?: never;
        head?: never;
        /** Restore deleted FlightHacksRule. */
        patch: operations['FlightHacksRule_restore'];
        trace?: never;
    };
    '/api/flight-hacks-rule/filter': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Filter FlightHacksRule. */
        post: operations['FlightHacksRule_filter'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/consolidator': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get all Consolidators. */
        get: operations['Consolidator_list'];
        put?: never;
        /** Create a new Consolidator. */
        post: operations['Consolidator_create'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/consolidator/{id}': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get one Consolidator. */
        get: operations['Consolidator_get'];
        /** Update existing Consolidator. */
        put: operations['Consolidator_update'];
        post?: never;
        /** Delete Consolidator. */
        delete: operations['Consolidator_delete'];
        options?: never;
        head?: never;
        /** Restore deteled Consolidator. */
        patch: operations['Consolidator_restore'];
        trace?: never;
    };
    '/api/pseudo-city-codes': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get all PCC. */
        get: operations['PseudoCityCodes_list'];
        put?: never;
        /** Create a new PCC. */
        post: operations['PseudoCityCodes_create'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/pseudo-city-codes/{id}': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get one PCC. */
        get: operations['PseudoCityCodes_get'];
        /** Update existing PCC. */
        put: operations['PseudoCityCodes_update'];
        post?: never;
        /** Delete PCC. */
        delete: operations['PseudoCityCodes_delete'];
        options?: never;
        head?: never;
        /** Restore deleted PCC. */
        patch: operations['PseudoCityCodes_restore'];
        trace?: never;
    };
    '/api/rule': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get all rules. */
        get: operations['Rule_list'];
        put?: never;
        /** Create a new PCC. */
        post: operations['Rule_create'];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/rule/{id}': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get one PCC. */
        get: operations['Rule_get'];
        /** Update existing PCC. */
        put: operations['Rule_update'];
        post?: never;
        /** Delete Rule. */
        delete: operations['Rule_delete'];
        options?: never;
        head?: never;
        /** Restore deleted Rule. */
        patch: operations['Rule_restore'];
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        Consolidator: {
            alias: string;
            name: string | null;
        };
        Error: {
            code?: string;
            message?: string;
        };
        ErrorResponse: {
            error?: components['schemas']['Error'];
        };
        PseudoCityCode: {
            /** @description ID of PseudoCityCode, must be equal with code_consolidator */
            id: string;
            code: string;
            gds_type?: components['schemas']['GdsType'] | null;
            DK: string | null;
            consolidator: components['schemas']['Consolidator'];
            access_J98K: boolean;
            agent_access_info: string | null;
            commission_type: components['schemas']['CommissionType'] | null;
            commission_value: number | null;
            voluntary_changes: components['schemas']['RefundProcessingFee'] | null;
            involuntary_changes: components['schemas']['RefundProcessingFee'] | null;
            rule_type: components['schemas']['PccRuleType'];
            rules: components['schemas']['Rule'][];
            iata?: string | null;
        };
        Rule: {
            /** Format: uuid */
            id: string;
            /** @description ID to PseudoCityCode */
            pseudo_city_code: string;
            type: components['schemas']['RuleType'];
            program: components['schemas']['RuleProgram'];
            trip_type: components['schemas']['TripType'] | null;
            land_pack?: components['schemas']['LandPackType'] | null;
            airlines?: components['schemas']['Airline'][] | null;
            FOP: components['schemas']['FormOfPayment'] | null;
            /** @enum {string|null} */
            markup_type: 'up_to' | 'up_to_publish_fare' | 'n_a' | 'no_markup_allowed' | null;
            markup_value: string | null;
            agent_pricing_info_adt: string | null;
            agent_pricing_info_chd: string | null;
            agent_pricing_info_inf: string | null;
            agent_remark_info: string | null;
            default_fee: components['schemas']['Money'] | null;
            fee: components['schemas']['Fee'][];
        };
        Airline: {
            code: string;
        };
        /** @enum {string} */
        CabinDesignator: 'eco' | 'pre' | 'biz';
        /** @enum {string} */
        CommissionType: 'percent' | 'discussed' | 'as_displayed';
        Fee: {
            class: components['schemas']['CabinDesignator'];
            amount: components['schemas']['Money'];
        };
        /** @enum {string} */
        FormOfPayment: 'CC' | 'CK' | 'only_check' | 'CC/CK';
        /** @enum {string} */
        GdsType: 'SABRE' | 'AMADEUS' | 'GALILEO' | 'ONLINE';
        /** @enum {string} */
        LandPackType: 'mandatory';
        /** @enum {string} */
        MarkupType: 'up_to' | 'up_to_publish_fare' | 'n_a' | 'no_markup_allowed';
        Money: {
            amount: number;
            /** @default USD */
            currency: string;
        };
        /** @enum {string} */
        PccRuleType: 'default_rules' | 'detailed_rules';
        RefundProcessingFee: {
            type: components['schemas']['RefundProcessingType'];
            amount?: components['schemas']['Money'] | null;
        };
        /** @enum {string} */
        RefundProcessingType: 'call_in' | 'reissue_due_to_price_drop' | 'fix' | 'cx';
        /** @enum {string} */
        RuleProgram: 'public_fare' | 'private_fare' | 'corporate_fare' | 'tour_fare' | 'missionary_fare' | 'cruise_fares';
        /** @enum {string} */
        RuleType: 'general_rules' | 'us_domestic' | 'pub_no_comm' | 'pub_with_comm_eco_pre' | 'pub_with_comm_biz' | 'pvt_eco' | 'pvt_premium' | 'pvt_biz' | 'tour_eco_ck' | 'tour_prem_ck' | 'tour_biz_ck';
        /** @enum {string} */
        TripType: 'any' | 'one_way' | 'round_trip' | 'eastbound' | 'westbound';
        /** @description Filter parameters for PCC Configuration search */
        PccConfigurationFilterRequest: {
            /**
             * Format: uuid
             * @description Filter by PCC Configuration ID
             */
            id?: string | null;
            /** @description Filter by PCC (Pseudo City Code) */
            pcc?: string | null;
            /** @description Filter by GDS account identifier */
            gds_account?: string | null;
            /** @description Filter by snap code name */
            snap_code_name?: string | null;
            /** @description Filter by service limitation name */
            service_limitations_name?: string | null;
            /** @description Filter by service limitation description */
            service_limitations_description?: string | null;
            /** @description Filter by service limitation search limit */
            service_limitations_search_limit?: number | null;
            /** @description Filter by service limitation disabled status */
            service_limitations_disabled?: boolean | null;
            /** @description Filter by adult price modifier group */
            price_modifier_groups_ADT?: string | null;
            /** @description Filter by child price modifier group */
            price_modifier_groups_CHD?: string | null;
            /** @description Filter by infant price modifier group */
            price_modifier_groups_INF?: string | null;
        };
        /** @description PCC Configuration request data */
        PccConfigurationRequest: {
            /** @description PCC (Pseudo City Code) identifier */
            pcc: string;
            /** @description GDS (Global Distribution System) account identifier */
            gds_account: string;
            /** @description List of snap codes */
            snap_codes?: {
                /** @description Snap code name */
                name?: string;
            }[];
            /** @description List of price modifier groups */
            price_modifier_groups?: {
                /** @description Adult passenger price modifier */
                ADT?: string | null;
                /** @description Child passenger price modifier */
                CHD?: string | null;
                /** @description Infant passenger price modifier */
                INF?: string | null;
            }[];
            service_limitations?: components['schemas']['ServiceLimitation'][];
        };
        /** @description PCC Configuration resource with related entities */
        PccConfigurationResource: {
            /** @description Unique identifier of the PCC configuration */
            id?: string;
            /** @description PCC (Pseudo City Code) identifier */
            pcc: string;
            /** @description GDS (Global Distribution System) account identifier */
            gdsAccount: string;
            /** @description List of associated snap codes */
            snapCodes?: components['schemas']['SnapCodeResource'][];
            serviceLimitation?: components['schemas']['BFMServiceLimitationDTO'];
            /** @description List of price modifier groups */
            priceModifierGroupResource?: components['schemas']['PriceModifierGroupResource'][];
            /**
             * Format: date-time
             * @description Timestamp when the configuration was created
             */
            createdAt?: string;
            /**
             * Format: date-time
             * @description Timestamp when the configuration was last updated
             */
            updatedAt?: string;
        };
        /** @enum {string} */
        PassengerType: 'ADT' | 'CHD' | 'INF';
        /** @description Price Modifier Group resource containing price modifiers for different passenger types */
        PriceModifierGroupResource: {
            /** @description Price modifier for ADT passengers */
            ADT?: string;
            /** @description Price modifier for CHD passengers */
            CHD?: string;
            /** @description Price modifier for INF passengers */
            INF?: string;
        };
        BFMServiceLimitationDTO: {
            /** @example BFM */
            name: string;
            description?: string | null;
            search_limit?: number | null;
            disabled?: boolean | null;
        };
        ServiceLimitation: {
            name: string;
            description?: string | null;
            search_limit?: number | null;
            disabled?: boolean | null;
        };
        /** @enum {string} */
        NameType: 'BFM';
        /** @description Service Limitation resource */
        ServiceLimitationResource: {
            /** @description Service limitation name */
            name?: string;
            /** @description Indicates if the service limitation is disabled */
            disabled?: boolean | null;
            /** @description Maximum number of search operations allowed */
            searchLimit?: number | null;
            /** @description Detailed description of the service limitation */
            description?: string | null;
        };
        /** @description Snap Code resource */
        SnapCodeResource: {
            /** @description Snap code name */
            name?: string;
        };
        FakeSegment: {
            type?: components['schemas']['FakeSegmentType'] | null;
            /** @example any_time */
            layover: components['schemas']['LayoverType'] | null;
            airline: string[] | null;
            departure: string[] | null;
            arrival: string[] | null;
            /** @example any */
            cabin_type: components['schemas']['FakeSegmentCabinType'] | null;
        };
        FlightHacksRule: {
            /** Format: uuid */
            id?: string;
            type: components['schemas']['HackType'];
            flight_pattern: components['schemas']['FlightPattern'];
            fake_segment?: components['schemas']['FakeSegment'];
            booking_type: components['schemas']['BookingType'] | null;
            pcc?: string | null;
            description?: string | null;
            hack_payload?: (components['schemas']['BasePayloadDTO'] | components['schemas']['ElrPayloadDTO'] | components['schemas']['FrtPayloadDTO'] | components['schemas']['OwRevOwMlgPayloadDTO'] | components['schemas']['OwMlgOwRevPayloadDTO'] | components['schemas']['OwRevFrtOwMlgPayloadDTO'] | components['schemas']['OwMlgOwRevFrtPayloadDTO'] | components['schemas']['OwRevFrtOwRevFrtPayloadDTO'] | components['schemas']['OriginOpenJawPayloadDTO'] | components['schemas']['TwoOneWaysPayloadDTO']) | null;
        };
        FlightPattern: {
            trip_type: components['schemas']['FlightPatternTripType'];
            airline?: string[];
            departure: string[];
            arrival: string[];
            destination: string[];
        };
        BasePayloadDTO: {
            /** @example {
             *       "exampleString": "some string",
             *       "exampleInt": 123
             *     } */
            data?: string | number;
        };
        ElrPayloadDTO: components['schemas']['BasePayloadDTO'];
        FrtPayloadDTO: components['schemas']['BasePayloadDTO'];
        OriginOpenJawPayloadDTO: components['schemas']['BasePayloadDTO'];
        OwMlgOwRevFrtPayloadDTO: components['schemas']['BasePayloadDTO'];
        OwMlgOwRevPayloadDTO: components['schemas']['BasePayloadDTO'];
        OwRevFrtOwMlgPayloadDTO: components['schemas']['BasePayloadDTO'];
        OwRevFrtOwRevFrtPayloadDTO: components['schemas']['BasePayloadDTO'];
        OwRevOwMlgPayloadDTO: components['schemas']['BasePayloadDTO'];
        TwoOneWaysPayloadDTO: components['schemas']['BasePayloadDTO'];
        /** @enum {string} */
        BookingType: 'revenue' | 'mixed' | 'mileage';
        /** @enum {string} */
        FakeSegmentCabinType: 'eco' | 'pre' | 'biz' | 'any' | 'same_or_higher' | 'same_or_lower';
        /** @enum {string} */
        FakeSegmentType: 'from_same_destination_city' | 'from_same_destination_airport' | 'to_airport' | 'route' | 'not_applicable';
        /** @enum {string} */
        FlightPatternTripType: 'one_way' | 'round_trip' | 'none' | 'open_jaw';
        /** @enum {string} */
        HackType: 'ELR' | 'FRT' | 'OW_REV_OW_MLG' | 'OW_MLG_OW_REV' | 'OW_REV_FRT_OW_MLG' | 'OW_MLG_OW_REV_FRT' | 'OW_REV_FRT_OW_REV_FRT' | 'ORIGIN_OPEN_JAW' | 'TWO_ONE_WAYS';
        /** @enum {string} */
        LayoverType: 'any_time' | 'next_day_less_24_hours' | 'after_7_days_minimum';
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    getPccConfiguration_list: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful operation */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': {
                        data?: components['schemas']['PccConfigurationResource'][];
                    };
                };
            };
        };
    };
    getPccConfiguration_create: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                'application/json': components['schemas']['PccConfigurationRequest'];
            };
        };
        responses: {
            /** @description PCC Configuration created successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['PccConfigurationResource'];
                };
            };
            /** @description Validation error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': {
                        /** @description Validation errors */
                        errors?: Record<string, never>;
                    };
                };
            };
        };
    };
    getPccConfiguration_get: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /** @description PccConfiguration ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful operation */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['PccConfigurationResource'];
                };
            };
            /** @description PccConfiguration not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': {
                        /** @example PccConfiguration not found. */
                        message?: string;
                    };
                };
            };
        };
    };
    getPccConfiguration_update: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /** @description PccConfiguration ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                'application/json': components['schemas']['PccConfigurationRequest'];
            };
        };
        responses: {
            /** @description PccConfiguration updated successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['PccConfigurationResource'];
                };
            };
            /** @description PccConfiguration not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': {
                        /** @example PccConfiguration not found. */
                        message?: string;
                    };
                };
            };
            /** @description Validation error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': {
                        /** @example The given data was invalid. */
                        message?: string;
                        /** @description Validation errors */
                        errors?: Record<string, never>;
                    };
                };
            };
        };
    };
    getPccConfiguration_delete: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /** @description PccConfiguration ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description PccConfiguration deleted successfully */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description PccConfiguration not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': {
                        /** @example PccConfiguration not found. */
                        message?: string;
                    };
                };
            };
        };
    };
    getPccConfiguration_restore: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /** @description PccConfiguration ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description PccConfiguration restored successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['PccConfigurationResource'];
                };
            };
            /** @description PccConfiguration not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': {
                        /** @example PccConfiguration not found. */
                        message?: string;
                    };
                };
            };
        };
    };
    getPccConfiguration_filter: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                'application/json': components['schemas']['PccConfigurationFilterRequest'];
            };
        };
        responses: {
            /** @description Successful operation */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': {
                        data?: components['schemas']['PccConfigurationResource'][];
                    };
                };
            };
            /** @description Validation error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': {
                        /** @example The given data was invalid. */
                        message?: string;
                        /** @description Validation errors */
                        errors?: Record<string, never>;
                    };
                };
            };
        };
    };
    FlightHacksRule_list: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The FlightHacksRule rules. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['FlightHacksRule'][];
                };
            };
        };
    };
    FlightHacksRule_create: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                /** @example {
                 *       "type": "ELR",
                 *       "flight_pattern": {
                 *         "trip_type": "round_trip",
                 *         "airlines": [
                 *           "AA"
                 *         ],
                 *         "departure": {
                 *           "region": "airports",
                 *           "value": [
                 *             "JFK",
                 *             "BOS",
                 *             "SFO"
                 *           ]
                 *         },
                 *         "arrival": {
                 *           "region": "world",
                 *           "value": []
                 *         }
                 *       },
                 *       "fake_segment": {
                 *         "type": "from_same_destination_city",
                 *         "layover": "any_time",
                 *         "airline": "UA",
                 *         "departure": [],
                 *         "arrival": [
                 *           "YTO"
                 *         ]
                 *       },
                 *       "hack_payload": {
                 *         "data": {
                 *           "exampleString": "some string",
                 *           "exampleInt": 123
                 *         }
                 *       }
                 *     } */
                'application/json': components['schemas']['FlightHacksRule'];
            };
        };
        responses: {
            /** @description Created FlightHacksRule. */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['FlightHacksRule'];
                };
            };
            /** @description Resource already exists */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
            /** @description Bad Request */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    FlightHacksRule_get: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /**
                 * @description flight-hacks-rule id, UUID format
                 * @example dd5b6216-fafc-46e3-8946-df16a5145044
                 */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The info about the PCC. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['FlightHacksRule'];
                };
            };
            /** @description Not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    FlightHacksRule_update: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /**
                 * @description FlightHacksRule id, UUID format
                 * @example da29524c-e02e-4c96-9447-4f75bb73e69c
                 */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                /** @example {
                 *       "type": "ELR",
                 *       "flight_pattern": {
                 *         "trip_type": "round_trip",
                 *         "airlines": [
                 *           "AA"
                 *         ],
                 *         "departure": {
                 *           "region": "airports",
                 *           "value": [
                 *             "JFK",
                 *             "BOS",
                 *             "SFO"
                 *           ]
                 *         },
                 *         "arrival": {
                 *           "region": "world",
                 *           "value": []
                 *         }
                 *       },
                 *       "fake_segment": {
                 *         "type": "from_same_destination_city",
                 *         "layover": "any_time",
                 *         "airline": "UA",
                 *         "departure": [],
                 *         "arrival": [
                 *           "YTO"
                 *         ]
                 *       },
                 *       "hack_payload": []
                 *     } */
                'application/json': components['schemas']['Rule'];
            };
        };
        responses: {
            /** @description Updated Rules. */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['FlightHacksRule'];
                };
            };
            /** @description Resource already exists */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
            /** @description Bad Request */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    FlightHacksRule_delete: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /**
                 * @description FlightHacksRule id, UUID format
                 * @example da29524c-e02e-4c96-9447-4f75bb73e69c
                 */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successfully deleted. */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    FlightHacksRule_restore: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /**
                 * @description FlightHacksRule id, UUID format
                 * @example da29524c-e02e-4c96-9447-4f75bb73e69c
                 */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Restored Rules. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['FlightHacksRule'];
                };
            };
            /** @description Not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    FlightHacksRule_filter: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                /** @example {
                 *       "trip_type": "round_trip",
                 *       "airlines": [
                 *         "AA"
                 *       ],
                 *       "departure": [
                 *         "JFK",
                 *         "BOS",
                 *         "SFO"
                 *       ],
                 *       "arrival": []
                 *     } */
                'application/json': {
                    /** @enum {string|null} */
                    trip_type?: 'one_way' | 'round_trip' | 'none' | 'open_jaw' | null;
                    airline?: string[] | null;
                    departure?: string[] | null;
                    arrival?: string[] | null;
                };
            };
        };
        responses: {
            /** @description Filter Rules. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['FlightHacksRule'];
                };
            };
            /** @description Not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    Consolidator_list: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
                /** @description Display deleted, if exist (0 or 1, default is 0 to display only active, 1 display only trashed ) */
                trash?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The Consolidator list. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['Consolidator'][];
                };
            };
        };
    };
    Consolidator_create: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                /** @example {
                 *       "alias": "TEST",
                 *       "name": "test"
                 *     } */
                'application/json': components['schemas']['Consolidator'];
            };
        };
        responses: {
            /** @description Created Consolidator. */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['Consolidator'];
                };
            };
            /** @description Resource already exists */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
            /** @description Bad Request */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    Consolidator_get: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /**
                 * @description Consolidator alias
                 * @example test
                 */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The info about the Consolidator. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['Consolidator'];
                };
            };
            /** @description Not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    Consolidator_update: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /**
                 * @description Consolidator alias
                 * @example test
                 */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                /** @example {
                 *       "alias": "TEST",
                 *       "name": "test"
                 *     } */
                'application/json': components['schemas']['Consolidator'];
            };
        };
        responses: {
            /** @description Updated Consolidator. */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['Consolidator'];
                };
            };
            /** @description Resource already exists */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
            /** @description Bad Request */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    Consolidator_delete: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /**
                 * @description Consolidator alias
                 * @example test
                 */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successfully deleted. */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    Consolidator_restore: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /**
                 * @description Consolidator alias
                 * @example test
                 */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The info about the Consolidator. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['Consolidator'];
                };
            };
            /** @description Not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    Consolidator_restore: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /**
                 * @description Consolidator alias
                 * @example test
                 */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The info about the Consolidator. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['Consolidator'];
                };
            };
            /** @description Not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    PseudoCityCodes_list: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
                /**
                 * @description The PCC codes, comma separated to get data about pseudo city codes rules.
                 * @example 2GAC
                 */
                code?: string;
                /**
                 * @description The consolidator aliases, comma separated to get data about pseudo city codes rules from specific consolidators.
                 * @example UPAWAY
                 */
                consolidator?: string;
                /** @description Display deleted, if exist (0 or 1, default is 0 to display only active, 1 display only trashed ) */
                trash?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The PCC rules. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['PseudoCityCode'][];
                };
            };
        };
    };
    PseudoCityCodes_create: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                /** @example {
                 *       "id": "test_test",
                 *       "code": "test",
                 *       "dk": "test",
                 *       "consolidator": {
                 *         "alias": "test"
                 *       },
                 *       "access_J98K": true,
                 *       "agent_access_info": "All agents",
                 *       "commission_type": "percent",
                 *       "commission_value": "85",
                 *       "voluntary_changes": {
                 *         "type": "call_in",
                 *         "amount": {
                 *           "amount": "45.00",
                 *           "currency": "USD"
                 *         }
                 *       },
                 *       "involuntary_changes": {
                 *         "type": "call_in",
                 *         "amount": {
                 *           "amount ": "45.00",
                 *           "currency ": "USD"
                 *         }
                 *       },
                 *       "iata": "TEST"
                 *     } */
                'application/json': components['schemas']['PseudoCityCode'];
            };
        };
        responses: {
            /** @description Created Consolidator. */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['PseudoCityCode'];
                };
            };
            /** @description Resource already exists */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
            /** @description Bad Request */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    PseudoCityCodes_get: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /**
                 * @description PCC id, composed from code_consolidator
                 * @example TEST_TEST
                 */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The info about the PCC. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['PseudoCityCode'];
                };
            };
            /** @description Not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    PseudoCityCodes_update: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /**
                 * @description PCC id
                 * @example test
                 */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                /** @example {
                 *       "id": "test_test",
                 *       "code": "test",
                 *       "dk": "test",
                 *       "consolidator": {
                 *         "alias": "test"
                 *       },
                 *       "access_J98K": true,
                 *       "agent_access_info": "All agents",
                 *       "commission_type": "percent",
                 *       "commission_value": "85",
                 *       "voluntary_changes": {
                 *         "type": "call_in",
                 *         "amount": {
                 *           "amount": "45.00",
                 *           "currency": "USD"
                 *         }
                 *       },
                 *       "involuntary_changes": {
                 *         "type": "call_in",
                 *         "amount": {
                 *           "amount ": "45.00",
                 *           "currency ": "USD"
                 *         }
                 *       },
                 *       "iata": "TEST"
                 *     } */
                'application/json': components['schemas']['PseudoCityCode'];
            };
        };
        responses: {
            /** @description Updated PCC. */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['PseudoCityCode'];
                };
            };
            /** @description Resource already exists */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
            /** @description Bad Request */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    PseudoCityCodes_delete: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /**
                 * @description PCC id, composed from code_consolidator
                 * @example test_test
                 */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successfully deleted. */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    PseudoCityCodes_restore: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /**
                 * @description PCC id, composed from code_consolidator
                 * @example test_test
                 */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The info about the PCC. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['PseudoCityCode'];
                };
            };
            /** @description Not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    PseudoCityCodes_restore: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /**
                 * @description PCC id, composed from code_consolidator
                 * @example test_test
                 */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The info about the PCC. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['PseudoCityCode'];
                };
            };
            /** @description Not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    Rule_list: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
                /**
                 * @description The PCC codes, comma separated to get data about pseudo city codes rules.
                 * @example 2GAC
                 */
                pcc?: string;
                /**
                 * @description The consolidator aliases, comma separated to get data about pseudo city codes rules from specific consolidators.
                 * @example UPAWAY
                 */
                consolidator?: string;
                /** @description Display deleted, if exist (0 or 1, default is 0 to display only active rules, 1 display only trashed ) */
                trash?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The PCC rules. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['Rule'][];
                };
            };
        };
    };
    Rule_create: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                /** @example {
                 *       "pseudo_city_code": "test_test",
                 *       "type": "us_domestic",
                 *       "program": null,
                 *       "airlines": null,
                 *       "FOP": null,
                 *       "markup_type": null,
                 *       "markup_value": null,
                 *       "agent_pricing_info_adt": null,
                 *       "agent_pricing_info_chd": null,
                 *       "agent_pricing_info_inf": null,
                 *       "agent_remark_info": null,
                 *       "fee": [
                 *         {
                 *           "class": "eco",
                 *           "amount": {
                 *             "amount": "5",
                 *             "currency": "USD"
                 *           }
                 *         },
                 *         {
                 *           "class": "pre",
                 *           "amount": {
                 *             "amount": "5",
                 *             "currency": "USD"
                 *           }
                 *         },
                 *         {
                 *           "class": "biz",
                 *           "amount": {
                 *             "amount": "5",
                 *             "currency": "USD"
                 *           }
                 *         }
                 *       ]
                 *     } */
                'application/json': components['schemas']['Rule'];
            };
        };
        responses: {
            /** @description Created Consolidator. */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['Rule'];
                };
            };
            /** @description Resource already exists */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
            /** @description Bad Request */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    Rule_get: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /**
                 * @description Rule id, UUID format
                 * @example dd5b6216-fafc-46e3-8946-df16a5145044
                 */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The info about the PCC. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['Rule'];
                };
            };
            /** @description Not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    Rule_update: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /**
                 * @description Rule id, UUID format
                 * @example test
                 */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                /** @example {
                 *       "id": "ca347b7f-ef38-4488-a445-28a259851aa9",
                 *       "pseudo_city_code": "test_test",
                 *       "type": "us_domestic",
                 *       "program": null,
                 *       "airlines": null,
                 *       "FOP": null,
                 *       "markup_type": null,
                 *       "markup_value": null,
                 *       "agent_pricing_info_adt": null,
                 *       "agent_pricing_info_chd": null,
                 *       "agent_pricing_info_inf": null,
                 *       "agent_remark_info": null,
                 *       "fee": [
                 *         {
                 *           "class": "eco",
                 *           "amount": {
                 *             "amount": "5",
                 *             "currency": "USD"
                 *           }
                 *         },
                 *         {
                 *           "class": "pre",
                 *           "amount": {
                 *             "amount": "5",
                 *             "currency": "USD"
                 *           }
                 *         },
                 *         {
                 *           "class": "biz",
                 *           "amount": {
                 *             "amount": "5",
                 *             "currency": "USD"
                 *           }
                 *         }
                 *       ]
                 *     } */
                'application/json': components['schemas']['Rule'];
            };
        };
        responses: {
            /** @description Updated Rules. */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['Rule'];
                };
            };
            /** @description Resource already exists */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
            /** @description Bad Request */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    Rule_delete: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /**
                 * @description Rule id, UUID format
                 * @example test_test
                 */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successfully deleted. */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
    Rule_restore: {
        parameters: {
            query: {
                /**
                 * @description Token
                 * @example token
                 */
                token: string;
                /** @description timestamp */
                timestamp: string;
            };
            header?: never;
            path: {
                /**
                 * @description Rule id, UUID format
                 * @example test_test
                 */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Restored Rules. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['Rule'];
                };
            };
            /** @description Not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    'application/json': components['schemas']['ErrorResponse'];
                };
            };
        };
    };
}
