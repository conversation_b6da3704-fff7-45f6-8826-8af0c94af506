{"name": "@tmg/service-worker-ringcentral", "version": "1.0.0", "main": "index.ts", "exports": {".": "./dist/index.js", "./dev": "./index.ts", "./types": "./types.d.ts"}, "license": "MIT", "devDependencies": {"vite": "^5.4.9"}, "scripts": {"build": "vite build"}, "dependencies": {"@rc-ex/core": "^1.4.2", "@rc-ex/rcsdk": "^1.1.18", "@rc-ex/ws": "^1.1.18", "@ringcentral/sdk": "link:../../ringcentral-js/sdk", "events": "^3.3.0", "localforage": "^1.10.0", "wait-for-async": "^0.7.0"}}