import type { RouteDefinition } from '~/router'
import { defineOpenRoutePermission, defineRoutePermission } from '~/utils/define'

const routes: RouteDefinition[] = [
    {
        path: '/settings',
        name: 'settings',
        component: () => import('@/views/settings/SettingsController.vue'),
        meta: {
            permission: defineOpenRoutePermission(),
            title: 'Settings',
        },
    },
    {
        path: '/settings/award',
        name: 'settings.award',
        component: () => import('@/views/settings/SettingsAwardController.vue'),
        meta: {
            permission: defineRoutePermission('all', 'openPageSettingsAward'),
            title: 'Settings',
        },
    },
    {
        path: '/settings/project-cards',
        name: 'settings.project.cards',
        component: () => import('@/views/settings/SettingsProjectCardsController.vue'),
        meta: {
            permission: defineRoutePermission('all', 'openPageSettingsProjectCards'),
            title: 'Settings',
        },
    },
    {
        path: '/settings/iata/codes',
        name: 'settings.iata.codes',
        component: () => import('@/views/settings/SettingsIataCodesController.vue'),
        meta: {
            permission: defineRoutePermission('all', 'openPageSettingsIata'),
            title: 'IATA',
        },
    },
    {
        path: '/settings/iata/cities',
        name: 'settings.iata.cities',
        component: () => import('@/views/settings/SettingsIataCitiesController.vue'),
        meta: {
            permission: defineRoutePermission('all', 'openPageSettingsIata'),
            title: 'IATA',
        },
    },
    {
        path: '/settings/iata/airlines',
        name: 'settings.iata.airlines',
        component: () => import('@/views/settings/SettingsIataAirlinesController.vue'),
        meta: {
            permission: defineRoutePermission('all', 'openPageSettingsIata'),
            title: 'IATA',
        },
    },
    {
        path: '/settings/iata/airports',
        name: 'settings.iata.airports',
        component: () => import('@/views/settings/SettingsIataAirportsController.vue'),
        meta: {
            permission: defineRoutePermission('all', 'openPageSettingsIata'),
            title: 'IATA',
        },
    },
    {
        path: '/settings/lead-take-rules',
        name: 'settings.lead.take.rules',
        component: () =>
            import(
                /*webpackChunkName: 'SettingsLeadTakeRules'*/ '@/views/settings/SettingsLeadTakeRulesController.vue'
                ),
        meta: {
            permission: defineRoutePermission('all', 'openPageLeadTakeRules'),
            title: 'Lead Take Rules',
        },
    },
    {
        path: '/settings/expert/invoices',
        name: 'settings.expert.invoices',
        component: () => import('@/views/settings/SettingsExpertInvoicesController.vue'),
        meta: {
            permission: defineRoutePermission('all', 'openPageSettingsExpertInvoices'),
            title: 'Expert invoices',
        },
    },
    {
        path: '/settings/consolidator',
        name: 'settings.consolidator',
        component: () => import('@/views/settings/SettingsConsolidatorController.vue'),
        meta: {
            permission: defineRoutePermission('all', 'openPageSettingsConsolidator'),
            title: 'Settings',
        },
    },
    {
        path: '/settings/festive-events',
        name: 'settings.festive-events',
        component: () => import('@/views/settings/festive-events/SettingsFestiveEventsController.vue'),
        meta: {
            permission: defineOpenRoutePermission(),
            title: 'Festive Events Settings',
        },
    },
    {
        path: '/settings/skills',
        name: 'settings.skills',
        component: () => import('~/pages/Settings/SettingsSkillsPage.vue'),
        meta: {
            permission: defineRoutePermission('all', 'updateSkillList'),
            title: 'Skills',
        },
    },
    {
        path: '/settings/payment-gateway-balance',
        name: 'settings.payment.gateway.balance',
        component: () => import('~/pages/Settings/SettingsPaymentGatewayBalancePage.vue'),
        meta: {
            permission: defineRoutePermission('all', 'manage'),
            title: 'Gateway Balance',
        },
    },
    {
        path: '/settings/office-settings',
        name: 'settings.office.settings',
        component: () => import('~/pages/Settings/SettingsOfficeSettingsPage.vue'),
        meta: {
            permission: defineRoutePermission('all', 'manage'),
            title: 'Office Settings',
        },
    },
    {
        path: '/settings/consolidators',
        name: 'settings.consolidators',
        component: () => import('~/pages/Settings/SettingsConsolidatorPage.vue'),
        meta: {
            permission: defineRoutePermission('all', 'editConsolidatorManagementTool'),
            title: 'Consolidators',
        },
    },
    {
        path: '/settings/marketing',
        name: 'settings.marketing',
        component: () => import('~/pages/Settings/SettingsMarketingCostPage.vue'),
        meta: {
            permission: defineRoutePermission('all', 'manage'),
            title: 'Marketing',
        },
    },
]

export default routes
