import type { RouteDefinition } from '~/router'
import { RouteGroup } from '~types/enums/RouteGroup'
import { defineRoutePermission } from '~/utils/define'

const routes: RouteDefinition[] = [
    {
        path: '/day-off-requests',
        name: 'day.off.requests',
        component: () => import('~/pages/Leave/LeaveManagementPage.vue'),
        meta: {
            permission: defineRoutePermission('LeaveRequest', 'openPage'),
            title: 'Day Off Requests',
            icon: UserMinusIcon,
            inSidebar: true,
            group: RouteGroup.General,
        },
    },
]

export default routes
