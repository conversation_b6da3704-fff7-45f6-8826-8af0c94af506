import type { RouteDefinition } from '~/router'
import type { RouteComponent } from 'vue-router'
import { RouteGroup } from '~types/enums/RouteGroup'

const routes: RouteDefinition[] = [
    {
        path: '/test',
        name: 'test',
        component: () => import('~/pages/Test/TestPage.vue'),
        meta: {
            skipPermissions: true,
            inSidebar: true,
            title: 'Test Page',
            icon: ZapIcon,
            layout: 'full',
            group: RouteGroup.Development,
        },
    },
    {
        path: '/test-frontend',
        name: 'test.frontend',
        component: () => import('~/pages/Test/TestPage.vue'),
        meta: {
            skipPermissions: true,
            title: 'Test Page',
            icon: ZapIcon,
            layout: 'full',
            group: RouteGroup.Development,
        },
    },
    defineCustomTestPage('client-select', () => import('~/pages/Test/TestClientSelectPage.vue')),
    defineCustomTestPage('sale-select', () => import('~/pages/Test/SaleSelectModalTestPage.vue')),
    defineCustomTestPage('file-previewer', () => import('~/pages/Test/FilePreviewerTestPage.vue')),
    defineCustomTestPage('modal', () => import('~/pages/Test/ModalTestPage.vue')),
    defineCustomTestPage('table', () => import('~/pages/Test/TableTestPage.vue')),
    defineCustomTestPage('form', () => import('~/pages/Test/FormTestPage.vue')),
    defineCustomTestPage('chat', () => import('~/pages/Test/ChatTestPage.vue')),
    defineCustomTestPage('full', () => import('~/pages/Test/TestFullPage.vue')),
    defineCustomTestPage('virtual-list', () => import('~/pages/Test/VirtualListTestPage.vue')),
    defineCustomTestPage('infinite-scroll', () => import('~/pages/Test/InfiniteScrollTestPage.vue')),
    defineCustomTestPage('file-uploader', () => import('~/pages/Test/FileUploaderTestPage.vue')),
    defineCustomTestPage('task', () => import('~/pages/Test/TaskTestPage.vue')),
    defineCustomTestPage('prevent-screen-leave', () => import('~/pages/Test/PreventScreenLeaveTestPage.vue')),
    defineCustomTestPage('pagination', () => import('~/pages/Test/PaginationTestPage.vue')),
    defineCustomTestPage('text-editor', () => import('~/pages/Test/TextEditorTestPage.vue')),
    defineCustomTestPage('table-columns', () => import('~/pages/Test/TableColumnsTestPage.vue')),
    defineCustomTestPage('telemetry', () => import('~/pages/Test/TelemetryTestPage.vue')),
    defineCustomTestPage('terminal', () => import('~modules/gds-terminal/src/pages/TerminalTestPage.vue')),
    defineCustomTestPage('chart-page', () => import('~/pages/Test/ChartTestPage.vue')),
    defineCustomTestPage('incentive-sale', () => import('~/pages/Test/IncentiveSaleTestPage.vue')),
    defineCustomTestPage('additional-expense', () => import('~/pages/Test/AdditionalExpenseTestPage.vue')),

]

export default routes

function defineCustomTestPage(name: string, component: RouteComponent): RouteDefinition {
    return {
        path: '/test/' + name,
        name: 'test.' + name,
        component,
        meta: {
            layout: 'full',
            skipPermissions: true,
            title: 'Test Page',
            icon: ZapIcon,
        },
    }
}
