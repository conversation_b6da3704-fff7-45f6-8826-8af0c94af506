import type { ProgressFinisher } from '@marcoschulte/vue3-progress'
import { useProgress } from '@marcoschulte/vue3-progress'

export function useSuspensableComponent(callback?: () => Promise<void>, {
    throwOnError = false,
} = {}) {
    const instance = getCurrentInstance()

    if (!instance) {
        throw new Error('useFetch() must be called within the setup() function')
    }

    const proxy = instance.proxy as {
        fetch?: () => Promise<void>,
    }

    proxy.fetch ||= callback

    if (!proxy.fetch) {
        throw new Error("'fetch' hook is not provided or is not defined in component options")
    }

    const loading = ref(false)
    const silentLoading = ref(false)
    const loadedOnce = ref(false)
    const error = ref<unknown>()

    async function fetch(throwError = true) {
        if (!loadedOnce.value) {
            loading.value = true
        }

        silentLoading.value = true

        try {
            await proxy.fetch?.call(instance)

            error.value = null
        } catch (e) {
            error.value = e

            if (throwError) {
                throw e
            } else {
                console.error(e)
            }
        } finally {
            if (!loadedOnce.value) {
                loading.value = false
                loadedOnce.value = true
            }

            silentLoading.value = false
        }
    }

    function watchWorkspace() {
        const { state } = useContext()

        watch(() => state.workspace, () => {
            // noinspection JSIgnoredPromiseFromCall
            fetch(false)
        })
    }

    function enableGlobalLoading() {
        let progress: ProgressFinisher | undefined

        watch(silentLoading, (value) => {
            if (!loadedOnce.value) {
                return
            }

            if (value) {
                progress = useProgress().start()
            } else {
                progress?.finish()
            }
        })
    }

    // ==============================

    onBeforeMount(() => {
        watchWorkspace()
        // enableGlobalLoading()

        // noinspection JSIgnoredPromiseFromCall
        fetch(throwOnError)
    })

    // ==============================

    function resetLoading() {
        loading.value = false
        silentLoading.value = false
        loadedOnce.value = false
    }

    return {
        fetch,
        loading,
        silentLoading,
        resetLoading,
        error,
    }
}

declare module 'vue' {
    interface ComponentCustomOptions {
        fetch?: () => Promise<void>,
    }
}
