import type { Theme, ThemeNormalized } from '@tmg/chart'
import { DARK_THEME, LIGHT_THEME, normalizeTheme } from '@tmg/chart'

export function useLineChartTheme(
    config?: {
        light: Theme,
        dark: Theme,
    },
) {
    const { isDark } = useDarkMode()

    const theme = computed<ThemeNormalized>(() => {
        if (isDark.value) {
            return config ? normalizeTheme(config.dark) : DARK_THEME
        } else {
            return config ? normalizeTheme(config.light) : LIGHT_THEME
        }
    })

    return {
        theme,
    }
}
