import type { Component, Ref } from 'vue'
import { markRaw, resolveComponent } from 'vue'
import type { ControllablePromise } from '~/composables/useControllablePromise'
import type { ComponentEmits, ComponentProps } from '~types/utils'

export type ModalOptions = {
    key?: ModalKey,
    group?: string,
    position?: ModalPosition,
    place?: string,

    promise?: boolean,

    closeOnOverlay?: boolean,
    closeOnEsc?: boolean,
    overlay?: boolean,
    blur?: boolean,

    bind?: AnyObject,
    workspace?: Workspace,

    onExists?: ((key: ModalKey, modal: Modal) => boolean) | 'replace',
    onClose?: false | ((...rejectPayload: any[]) => boolean | any),
    afterCloseEvent?: () => void,
    onMounted?: () => void,
    onUnmounted?: () => void,
    keepOnRouteChange?: boolean,
}

export type ModalPosition = 'top' | 'center' | 'left' | 'right' | 'right-bottom' | 'context-menu' | 'free'
export type ModalKey = any
export type ModalPayload = AnyObject

export type Modal = {
    key: ModalKey,
    component: Component,
    payload: ModalPayload,
    options: ModalOptions,
    internal: {
        controllablePromise?: ControllablePromise,
        loading?: Ref<boolean>,
    }
}

export const modalsStorage = reactive(new Map<ModalKey, Modal>())

type ResolvePropsFromEmits<Emits> = Emits extends {
    resolve: (...args: infer R) => any
} ? R[0] : never

export function useModal<TComponent extends Component>(
    _component: TComponent,
    _payload?: ComponentProps<TComponent>,
    _options: ModalOptions = {},
) {
    const component = resolveModalComponent(_component)
    const key = resolveModalKey(component, _options)
    const payload = _payload ?? {}
    const options = resolveModalOptions(component, _options)

    const internal: Modal['internal'] = {}

    let workspace: Ref<Workspace> | undefined

    if (!options.workspace) {
        try {
            const context = useContext()

            workspace = computed(() => context.workspace)
        } catch (e) {
            //
        }
    }

    if (isAsyncComponent(component)) {
        internal.loading = ref(true)
    }

    const isOpened = computed(() => modalsStorage.has(key))

    function open<TResult = ResolvePropsFromEmits<ComponentEmits<TComponent>>>(
        _payload?: ComponentProps<TComponent>,
        _options: ModalOptions = {},
    ): Promise<TResult> {
        const usePayload = Object.assign({}, payload, _payload ?? {})
        const useOptions = Object.assign({}, options, _options)

        const existing = modalsStorage.get(key)

        if (existing) {
            if (useOptions.onExists !== 'replace' && (!useOptions.onExists || !useOptions.onExists(key, existing))) {
                useLogger('modal').log('Modal already exists:', prettyKey(key))

                return undefined as any
            }
        }

        if (options.promise) {
            internal.controllablePromise = useControllablePromise({ once: true })
        }

        useOptions.workspace ||= toValue(workspace)

        const modal: Modal = {
            key,
            component,
            payload: usePayload,
            options: useOptions,
            internal,
        }

        useLogger('modal').log('Open modal:', prettyKey(key), modal)

        modalsStorage.set(key, modal)

        if (internal.controllablePromise) {
            return internal.controllablePromise.promise
        }

        return undefined as any
    }

    function close() {
        useLogger('modal').log('Close modal:', prettyKey(key))

        const existing = modalsStorage.get(key)

        existing?.options.afterCloseEvent?.()

        internal.controllablePromise?.reject()

        modalsStorage.delete(key)
    }

    return {
        key,
        component,
        isOpened,
        open,
        close,
    }
}

export function useExistingModal(component: Component) {
    const found = findModal(component)

    if (!found) {
        return
    }

    return useModal(found.component, found.payload, found.options)
}

type ModalFindCondition = ModalKey | {
    group: string
} | {
    place: string
}

function findModal(condition: ModalFindCondition) {
    if (typeof condition === 'string') {
        return modalsStorage.get(condition)
    }

    if (isComponent(condition)) {
        return modalsStorage.get(resolveModalKey(condition))
    }

    for (const modal of modalsStorage.values()) {
        if (condition.group && modal.options.group !== condition.group) {
            continue
        }

        if (condition.place && modal.options.place !== condition.place) {
            continue
        }

        if (condition.key && modal.key !== condition.key) {
            continue
        }

        return modal
    }
}

export function getModalPlaceId(place: string) {
    return `modal-place-${place}`
}

export function getModalPlaceSelector(place: string | undefined) {
    if (!place) {
        return 'body'
    }

    return `#${getModalPlaceId(place)}`
}

export function closeModal(condition: ModalFindCondition) {
    useExistingModal(condition)?.close()
}

export function closeAllModals() {
    for (const [, modal] of modalsStorage.entries()) {
        useExistingModal(modal.key)?.close()
    }
}

const resolveModalKey = (component: Component, options: ModalOptions = {}): ModalKey => {
    return options.key || component
}

const resolveModalComponent = (component: string | Component): Component => {
    const resolved = typeof component === 'string' ? resolveComponent(component) : component

    if (typeof resolved !== 'object') {
        throw new Error(`Modal component "${component}" not found`)
    }

    return markRaw(resolved)
}

const resolveModalOptions = (component: Component, options: ModalOptions = {}) => {
    if (typeof component === 'object' && typeof component.modal === 'object') {
        options = Object.assign({}, component.modal, options)
    }

    return options
}

export function prettyKey(key: ModalKey) {
    if (isComponent(key)) {
        return (key as any).__name || key.name
    }

    return key
}

export function isAnyModalOpened() {
    return modalsStorage.size > 0
}

export function isAnyOverlayModalOpened() {
    return Array.from(modalsStorage.values()).some(modal => modal.options.overlay !== false)
}

/**
 * Get the nearest modal place element from the current element
 * Scan the parent elements children to find the nearest modal place
 */
export function getNearestModalPlace(currentElement: HTMLElement, options: {
    matcher?: (place: string, element: HTMLElement) => boolean,
} = {}): string {
    let element: HTMLElement | null = currentElement.parentElement

    while (element) {
        for (const child of element.children) {
            const id = child.id

            if (id && id.startsWith('modal-place-')) {
                const place = id.replace('modal-place-', '')

                if (options.matcher) {
                    if (options.matcher(place, element)) {
                        return place
                    }
                } else {
                    return place
                }
            }
        }

        element = element.parentElement
    }

    return undefined
}
