import useFloatingElement from '~/composables/useFloatingElement'
import type { DraggableOptions, HTMLElementOption } from '~/composables/useDraggable'
import { type ComputedRefWithControl, useCurrentElement, useDebounceFn } from '@vueuse/core'

type Size = {
    width?: number | string,
    height?: number | string,
}

/**
 * Extended floating composable that also manages element dimensions.
 * - Preserves original behavior (dragging, snapping, viewport fit)
 * - Adds size persistence and helper methods to control height/width
 * - Supports full-viewport-height mode that reacts to window resize
 */
export default function useFloatingDimensionsElement(
    options: {
        initialPosition?: Point,
        key?: string,
        savePosition?: boolean,
        handle?: HTMLElementOption,

        minimalHeight?: number | string,

    } = {},
    draggableOptions?: Pick<Partial<DraggableOptions>, 'dragStart' | 'dragEnd'>,
) {
    const el = useCurrentElement() as ComputedRefWithControl<HTMLElement>

    const base = useFloatingElement({
        initialPosition: options.initialPosition,
        key: options.key,
        savePosition: options.savePosition,
        handle: options.handle,
    }, draggableOptions)

    const isFullHeightMode = ref(false)
    let originalSize: Size | undefined

    const normalizeSizeValue = (value: number | string | undefined): string | undefined => {
        if (value === undefined) return undefined

        if (typeof value === 'number') return `${value}px`

        return value
    }

    const fit = () => {
        // noinspection JSIgnoredPromiseFromCall
        base.fitInViewport()
    }

    const applySize = (size: Size) => {
        if (!originalSize) {
            const style = getComputedStyle(el.value)
            originalSize = {
                width: style.width,
                height: style.height,
            }
        }

        const width = normalizeSizeValue(size.width)
        const height = normalizeSizeValue(size.height)

        if (width !== undefined) {
            el.value.style.width = width
        }

        if (height !== undefined) {
            el.value.style.height = height
        }

        fit()
    }

    const resetSize = () => {
        if (originalSize) {
            if (originalSize.width !== undefined) el.value.style.width = String(originalSize.width)

            if (originalSize.height !== undefined) el.value.style.height = String(originalSize.height)
        } else {
            el.value.style.width = ''
            el.value.style.height = ''
        }
        isFullHeightMode.value = false
        fit()
    }

    const fitSizeToViewport = useDebounceFn(() => {
        if (!el.value) return

        if (isFullHeightMode.value) {
            el.value.style.height = `${window.innerHeight}px`
        } else {
            const rect = el.value.getBoundingClientRect()

            if (rect.height > window.innerHeight) {
                el.value.style.height = `${window.innerHeight}px`
            }
        }

        const rectAfter = el.value.getBoundingClientRect()

        if (rectAfter.width > window.innerWidth) {
            el.value.style.width = `${window.innerWidth}px`
        }

        fit()
    }, 200)

    const enableFullViewportHeight = () => {
        isFullHeightMode.value = true
        applySize({ height: `${window.innerHeight}px` })
    }

    const disableFullViewportHeight = () => {
        isFullHeightMode.value = false
        const minimalHeight = options.minimalHeight || 200

        applySize({ height: normalizeSizeValue(minimalHeight) })
        fit()
    }

    const collapse = () => {
        isFullHeightMode.value = false
        el.value.style.height = ''
        base.init(options.initialPosition)
        fit()
    }

    onMounted(() => {
        window.addEventListener('resize', fitSizeToViewport)
    })

    onUnmounted(() => {
        window.removeEventListener('resize', fitSizeToViewport)
    })

    return {
        ...base,
        applySize,
        resetSize,
        fitSizeToViewport,
        enableFullViewportHeight,
        disableFullViewportHeight,
        collapse,
        isFullHeightMode,
    }
}

