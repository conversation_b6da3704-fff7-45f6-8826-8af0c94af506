import type SearchTag from '~/lib/Search/Tag/SearchTag'
import type { MaybeRefOrGetter } from 'vue'
import type { TooltipOptions } from '~/directives/TooltipDirective'

export type TableColumn = {
    label?: string,
    tooltip?: string | TooltipOptions,
    headingComponent?: string | AnyComponent,
    component?: string | AnyComponent,
    width?: TableColumnWidthValue,
    sortable?: boolean | ((value: any, record: any) => any),
    hideSortArrow?: boolean,
    colspan?: number,
    enabled?: MaybeRefOrGetter<boolean>,
    center?: boolean,
    icon?: AnyComponent,
    iconAfter?: boolean,
    neededRelations?: string[],
}

export type TableColumns = {
    [key: string]: TableColumn,
}

export type TableGroupColumn<TColumn extends string = string> = {
    column: TColumn,
    label?: string,
    inline?: boolean,
}

export type TableGroup<TColumn extends string = string> = {
    columns: Array<TableGroupColumn<TColumn>>,
}

export type TableColumnsDefinition<TColumns extends TableColumns = any> = {
    columns: TColumns,
    groups?: TableGroupsDefinition<Extract<keyof TColumns, string>>,
}

export type TableGroupDefinition<TColumn extends string = string> = {
    columns: Array<TableGroupColumnDefinition<TColumn>>,
    center?: boolean,
    label?: string,
    between?: boolean,
    hideDelimiter?: boolean,
}

export type TableGroupsDefinition<TColumn extends string = string> = {
    [key: string]: TableGroupDefinition<TColumn>,
}

export type TableGroupColumnDefinition<TColumn extends string = string> = TColumn | TableGroupColumn<TColumn>

export function useTableColumns<T extends TableColumns>(
    columns: T,
    groups?: TableGroupsDefinition<Extract<keyof T, string>>,
): TableColumnsDefinition<T> {
    return {
        columns,
        groups,
    }
}

export function useTableColumn(column: TableColumn): TableColumn {
    return column
}

// Width of a columnt should be in "symbols" (characters) and not in pixels.
// The main reason is that in most cases we set column width based on the
// content of the column. For example, if we have a column with a number,
// then we want to set the width of the column based on the maximum number.
// Percentages will be calculated based on that values.
// If you need static width in pixels, then you can use "px" suffix.
// If you don't specify width for a column, then it will be calculated
// based on the content of the column on the first render and will be
// fixed for the rest of the time (unless you rerender parent component).
export type TableColumnWidthValue = number | 'max' | 'min' | 'auto' | `${number}px`

export type TableHeading = {
    field: string,
    label: string,
    component?: string | AnyComponent,
    tooltip?: string | TooltipOptions,
    colspan?: number,
    width?: TableColumnWidthValue,
    sortable?: boolean,
    hideSortArrow?: boolean,
    searchTag?: SearchTag<any>,
    center?: boolean,
    group?: TableGroupDefinition,
    icon?: AnyComponent,
    iconAfter?: boolean,
}

//

export type ColumnSlotProps<T extends TableColumnsDefinition, K extends keyof T['columns']> =
    T['columns'][K] extends {
            component: (...args: infer TArgs) => any
        }
        ? TArgs[0]
        : (
            T['columns'][K] extends {
                    component: {
                        new(): {
                            $props: infer TProps
                        }
                    }
                }
                ? TProps
                : never
            )
