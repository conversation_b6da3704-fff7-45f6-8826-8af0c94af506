import FileData from '@/lib/core/helper/File/FileData'

export type FilesUploadOptions = {
    uploadAfterLoad?: boolean
    onError?: (fileData: FileData) => void
    onUploadAll?: (fileData: UploadFileData[]) => void
}

export type UploadFileData = FileData & { pk: PrimaryKey }

/**
 *  - "Load" - select file in any input source (including drag and drop), but not upload to server yet
 *  - "Upload" - upload file to the server, return file pk
 */
export function useFilesUpload(
    onUpload: (fileData: UploadFileData) => void,
    options: FilesUploadOptions = {},
) {
    const { workspace } = useContext()

    const useOptions = {
        uploadAfterLoad: true,
        ...options,
    } satisfies FilesUploadOptions

    const files = ref<FileData[]>([])

    async function handleLoad(fileList: FileList | File) {
        const filesToWorkWith = fileList instanceof FileList ?  Array.from(fileList) : [fileList]

        const result: Promise<UploadFileData | undefined>[] = []

        for (const fileToWorkWith of filesToWorkWith) {
            const fileToUpload = reactive(FileData.fromFile(fileToWorkWith, {
                project_id: Number(workspace),
            }))

            files.value.push(fileToUpload)

            if (useOptions.uploadAfterLoad) {
                const data = uploadFile(fileToUpload)

                if (data) {
                    result.push(data)
                }
            }
        }

        if (filesToWorkWith.length) {
            const resultData = (await Promise.all(result)).filter(Boolean)

            useOptions.onUploadAll?.(resultData)
        }
    }

    async function upload(filesToUpload?: FileData[]) {
        const promises = (filesToUpload || files.value).filter(canUploadFiledata).map(uploadFile)

        if (!promises.length) {
            return []
        }

        const result = (await Promise.all(promises)).filter(Boolean)

        useOptions.onUploadAll?.(result)

        return result
    }

    const uploadFile = async (fileData: FileData) => {
        if (!canUploadFiledata(fileData)) {
            return
        }

        try {
            await fileData.upload()

            if (fileData.errors.length) {
                useOptions.onError?.(fileData)

                return
            }

            onUpload(fileData as UploadFileData)

            return fileData as UploadFileData
        } catch (e: any) {
            fileData.errors.push(e.text || e.message)

            console.error(e)

            useOptions.onError?.(fileData)
        }
    }

    function clear() {
        files.value = []
    }

    function deleteFile(fileData: FileData) {
        files.value = files.value.filter(file => file.id != fileData.id)
    }

    const isLoading = computed(() => {
        return files.value.length > 0 ? files.value.every(file => file.loading) : false
    })

    return {
        files,
        handleLoad,
        upload,
        clear,
        deleteFile,
        isLoading,
    }
}

export const canUploadFiledata = (fileData: FileData) => {
    return !fileData.errors.length && !fileData.isLoading && !fileData.isLoaded
}
