<template>
    <AppModalWrapper
        header="Closing reason request?"
        class="!max-w-[500px]"
    >
        <div class="p-4 border-t border-gray-200 dark:border-dark-5">
            <IssueResultClosingReasonForm
                ref="component"
                :data="{
                    lead_pk: leadPk
                }"
            />

            <hr class="my-4">

            <IssueAdditionalDataForm
                ref="additionalData"
                with-attachments
                with-urgency
                :required-files-upload="false"
            />
        </div>

        <template #footer="{ close }">
            <div class="flex justify-between ">
                <AppButton @click="close">
                    Cancel
                </AppButton>
                <AppButton class="--primary" @click="submit">
                    Close the lead
                </AppButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script lang="ts" setup>
import { ClosingReasons } from '~/api/models/Lead/Lead'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import IssueAdditionalDataForm from '~/components/Page/Issue/IssueAdditionalDataForm.vue'
import IssueResultClosingReasonForm from '~/components/Page/Issue/Result/ClosingReason/IssueResultClosingReasonForm.vue'
import type Form from '~/lib/Form/Form'

defineOptions({
    name: 'CloseLeadReasonRequestModal',
    modal: {
        promise: true,
    },
})

const props = defineProps<{
    leadPk: PrimaryKey,
}>()

const emit = defineEmits<{
    close: [],
}>()

const { useModel } = useContext()

const modelController = useModel('Lead')

const component = ref<{
    form: Form,
}>()

const additionalData = ref<InstanceType<typeof IssueAdditionalDataForm>>()

const submit = computed(() => {
    if (!component.value) {
        return
    }

    const form = component.value?.form
    const issueForm = additionalData.value?.form

    assertNonNullable(form)

    return form.useSubmit({
        onValidate() {
            return issueForm?.validate()
        },
        async resolver() {
            await modelController.actions.closeLead({
                lead_pk: props.leadPk,
                closing_reason: form.data.closing_reason,
                closing_reason_remark: form.data.closing_reason === ClosingReasons.Other ? form.data.closing_reason_remark : null,
                duplicate_lead_pk: form.data.closing_reason === ClosingReasons.NewRequest ? form.data.duplicate_lead_pk : null,
                issue_data: issueForm.data,
            })

            toastSuccess('Request sent')

            emit('close')
        },
    })
})
</script>
