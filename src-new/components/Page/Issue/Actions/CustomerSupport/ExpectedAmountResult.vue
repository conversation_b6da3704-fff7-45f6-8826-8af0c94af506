<template>
    <span
        v-if="resultRecord"
        class="badge --soft rounded-full h-4"
        :class="`--${resultRecord.style}`"
    >
        {{ resultRecord.title }}
    </span>
</template>

<script setup lang="ts">
import type { ExpectedAmountResult } from '~/api/models/ExpectedAmount/ExpectedAmount'

const props = defineProps<{
    result: ExpectedAmountResult
}>()

const { useDictionary } = useContext()

const expectedAmountResultDictionary = useDictionary('ExpectedAmountResult')

const resultRecord = computed(() => expectedAmountResultDictionary.find(props.result))
</script>
