<template>
    <div>
        <div
            class="flex justify-between"
            :class="{'gap-3':!isModelSale}"
        >
            <AppButton
                v-if="isModelSale"
                class="--xs"
                @click="openChangeRequestModal"
            >
                Create Exchange
            </AppButton>

            <AppButton
                class="--xs"
                :class="{'flex-grow':!isModelSale}"
                @click="createExpectedAmount"
            >
                Create Expected Amount
            </AppButton>

            <AppButton
                class="--primary --xs"
                :class="{'flex-grow':!isModelSale}"
                @click="action"
            >
                Create Case
                <PlusIcon />
            </AppButton>
        </div>
        <div class="flex flex-col gap-3 mt-3">
            <div
                v-for="expectedAmount in expectedAmountRecords"
                :key="expectedAmount.pk"
                class="card p-4 grid grid-cols-2 gap-y-2 gap-x-2 w-full"
            >
                <div class="col-span-2 flex justify-between ">
                    <div class="flex flex-col w-full">
                        <div class="flex items-center gap-1.5">
                            <span class="text-2xs font-medium uppercase">Expected amount</span>
                            <ExpectedAmountResult
                                v-if="expectedAmount.result"
                                :result="expectedAmount.result"
                            />
                        </div>
                    </div>
                    <div class="inline-flex ml-auto">
                        <RouterLink
                            :to="routeToExpectedAmount(expectedAmount.record_locator)"
                            target="_blank"
                        >
                            <ExternalLinkIcon class="w-3.5 h-3.5" />
                        </RouterLink>
                    </div>
                </div>

                <div class="flex flex-col">
                    <span class="text-2xs font-medium text-secondary-400 uppercase">Record Locator</span>
                    <div class="text-xs font-normal truncate">
                        <span v-tooltip="expectedAmount.record_locator">{{ expectedAmount.record_locator }}</span>
                    </div>
                </div>
                <div class="flex flex-col">
                    <span class="text-2xs font-medium text-secondary-400 uppercase">Net Expected</span>
                    <div class="text-xs font-normal truncate">
                        <span v-tooltip="$format.money(expectedAmount.net_expected)">{{ $format.money(expectedAmount.net_expected) }}</span>
                    </div>
                </div>
            </div>

            <div
                v-for="airlineCase in airlineCaseRecords"
                :key="airlineCase.pk"
                class="card p-4 grid grid-cols-2 gap-y-2 gap-x-2 w-full"
            >
                <div class="col-span-2 flex justify-between">
                    <div>
                        <span class="text-2xs font-medium text-secondary-400 uppercase">Case number</span>
                        <br>
                        <span
                            v-tooltip="airlineCase.case_number"
                            class="text-xs font-normal truncate"
                        >{{ airlineCase.case_number }}</span>
                    </div>
                    <div class="inline-flex ml-auto -m-1.5">
                        <AppButton
                            v-tooltip="'Delete case'"
                            class="--only --ghost --neutral"
                            @click="deleteCase(usePk(airlineCase))"
                        >
                            <TrashIcon />
                        </AppButton>
                        <AppButton
                            v-tooltip="'Edit case'"
                            class="--only --ghost --primary"
                            @click="openEditCaseModal(usePk(airlineCase))"
                        >
                            <EditIcon />
                        </AppButton>
                    </div>
                </div>
                <div class="flex flex-col">
                    <span class="text-2xs font-medium text-secondary-400 uppercase">Airline</span>
                    <div class="text-xs font-normal truncate">
                        <span v-tooltip="airlineCase.airline.name">{{ airlineCase.airline.name }}</span>
                    </div>
                </div>
                <div v-if="airlineCase.contact" class="flex flex-col">
                    <span class="text-2xs font-medium text-secondary-400 uppercase">Selected {{
                        airlineCase.contact.category === CompanyContactCategory.Email ? 'email' : 'phone'
                    }}</span>
                    <div class="text-xs font-normal truncate">
                        <span v-tooltip="airlineCase.contact.value">{{ airlineCase.contact.value }}</span>
                    </div>
                </div>
                <div v-if="airlineCase.remark?.length" class="col-span-2 flex flex-col">
                    <span class="text-2xs font-medium text-secondary-400 uppercase">Remark</span>
                    <span class="text-xs font-normal break-words">{{ airlineCase.remark }}</span>
                </div>

                <div v-if="airlineCase.expectedAmount?.length" class="col-span-2 flex flex-col gap-y-2 gap-x-2">
                    <div
                        v-for="expectedAmount in airlineCase.expectedAmount"
                        :key="expectedAmount.pk"
                        class="card py-2.5 px-2 grid grid-cols-2 gap-y-2 gap-x-2 w-full border border-secondary-200 dark:border-secondary-600"
                    >
                        <div class="col-span-2 flex justify-between ">
                            <div class="flex flex-col w-full">
                                <div class="flex items-center gap-1.5">
                                    <span class="text-2xs font-medium uppercase">Expected amount</span>
                                    <ExpectedAmountResult
                                        v-if="expectedAmount.result"
                                        :result="expectedAmount.result"
                                    />
                                </div>
                            </div>
                            <div class="inline-flex ml-auto">
                                <RouterLink
                                    :to="routeToExpectedAmount(expectedAmount.record_locator)"
                                    target="_blank"
                                >
                                    <ExternalLinkIcon class="w-3.5 h-3.5" />
                                </RouterLink>
                            </div>
                        </div>

                        <div class="flex flex-col">
                            <span class="text-2xs font-medium text-secondary-400 uppercase">Record Locator</span>
                            <div class="text-xs font-normal truncate">
                                <span v-tooltip="expectedAmount.record_locator">{{
                                    expectedAmount.record_locator
                                }}</span>
                            </div>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-2xs font-medium text-secondary-400 uppercase">Net Expected</span>
                            <div class="text-xs font-normal truncate">
                                <span v-tooltip="$format.money(expectedAmount.net_expected)">{{ $format.money(expectedAmount.net_expected) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { ModelAttributes } from '~types/lib/Model'
import CustomerSupportCreateCaseModal from '~/components/Page/CustomerSupport/CustomerSupportCreateCaseModal.vue'
import { CompanyContactCategory } from '~/api/models/CompanyContact/CompanyContact'
import { pluck, sortByKeyFn } from '~/lib/Helper/ArrayHelper'
import ChangeSaleTypeModal from '~/sections/Sale/modals/ChangeSaleTypeModal.vue'
import { SaleType } from '~/api/models/Sale/Sale'
import { $confirmDelete } from '@/plugins/ConfirmPlugin'
import ExpectedAmountEditModal from '~/sections/ExpectedAmount/modals/ExpectedAmountEditModal.vue'
import { routeToExpectedAmount } from '@/lib/core/helper/RouteNavigationHelper'
import ExpectedAmountResult from '~/components/Page/Issue/Actions/CustomerSupport/ExpectedAmountResult.vue'

defineOptions({
    name: 'IssueActionsCustomerSupport',
})

const props = defineProps<{
    issue: ModelAttributes<'Issue'>
}>()

const { useModel } = useContext()

const airlineCaseModel = useModel('AirlineCase').useResourceList({
    name: 'IssueAirlineCaseList',
    pk: usePk(props.issue),
}, {
    with: ['airline', 'contact', 'expectedAmount'],
})

const expectedAmountModel = useModel('ExpectedAmount').useResourceList({
    name: 'IssueExpectedAmountList',
    pk: usePk(props.issue),
})

airlineCaseModel.fetch()
expectedAmountModel.fetch()

const airlineCaseRecords = computed(() => {
    return airlineCaseModel.records.sort(sortByKeyFn('created_at', 'desc'))
})

const expectedAmountRecords = computed(() => {
    return expectedAmountModel.records.filter(value => {
        return !pluck(airlineCaseRecords.value, '_pk').includes(value.airline_case_pk)
    })
})

const createCsCaseModal = useModal(CustomerSupportCreateCaseModal)
const changeRequestModal = useModal(ChangeSaleTypeModal)
const editExpectedAmountModal = useModal(ExpectedAmountEditModal)

const openEditCaseModal = (airlineCasePk: PrimaryKey) => {
    createCsCaseModal.open({ airlineCasePk: airlineCasePk })
}

function action() {
    createCsCaseModal.open({
        issuePk: usePk(props.issue),
    })
}

const isModelSale = computed(() => props.issue.model_name === 'Sale')

function openChangeRequestModal() {
    changeRequestModal.open({
        salePk: props.issue.model_pk,
        type: SaleType.Exchange,
        mode: 'clone',
        issuePk: usePk(props.issue),
    })
}

async function deleteCase(airlineCasePk: PrimaryKey) {
    const confirmed = await $confirmDelete({ text: 'Are you sure you want to delete this case?' })

    if (confirmed) {
        await useModel('AirlineCase').actions.delete({
            pk: airlineCasePk,
        })
    }
}

async function createExpectedAmount() {
    await editExpectedAmountModal.open({
        airlineCasePk: airlineCaseRecords.value[0]?.pk,
        issuePk: usePk(props.issue),
    })
}
</script>

