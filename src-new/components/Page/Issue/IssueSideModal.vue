<template>
    <AppModalWrapper
        v-slot="{ close }"
        class="!max-w-[95vw]"
        close-button
        outer-button
        stick-to-side
    >
        <div class="issue">
            <div v-if="showInfoComponent" class="issue__addition">
                <Component :is="infoComponent" />

                <div
                    class="app-modal__close"
                    @click="close"
                >
                    <TabBackground class="app-modal__close__corner app-modal__close__corner--first" />
                    <CloseIcon />
                    <TabBackground class="app-modal__close__corner app-modal__close__corner--second" />
                </div>
            </div>

            <div
                v-if="infoComponent"
                class="app-modal__close !top-[70px]"
                @click="toggleModelInfo"
            >
                <TabBackground class="app-modal__close__corner app-modal__close__corner--first" />
                <InfoIcon v-if="!showInfoComponent" />
                <ArrowRightIcon v-else />
                <TabBackground class="app-modal__close__corner app-modal__close__corner--second" />
            </div>

            <div class="issue__content">
                <AppModalHeader
                    :class="{
                        '-mr-4': showInfoComponent
                    }"
                    :close-button="!showInfoComponent"
                    class="flex-none"
                >
                    <div class="issue__header">
                        <div class="truncate text-base font-semibold max-w-64">
                            {{ title }}
                        </div>
                        <div class="text-xs text-gray-600">
                            Request #{{ issue.id }}
                        </div>
                    </div>
                </AppModalHeader>

                <div class="issue__actions">
                    <div v-if="isDeleted" class="text-danger">
                        Request is deleted
                    </div>
                    <div v-else class="flex items-center justify-between">
                        <IssueStatusComponent :status="issue.status" />

                        <div v-if="shouldShowExpirationTime">
                            <div
                                class="text-sm border text-primary-500 font-medium rounded-full leading-none flex items-center"
                            >
                                <div class="py-1 px-3">
                                    {{ expires }}
                                </div>
                                <button
                                    v-if="isMember && canExtend"
                                    v-tooltip="{content: 'Extend'}"
                                    class="rounded-full border text-secondary-500 p-1 -ml-1 -m-px hover:border-primary-500 hover:text-primary-1"
                                    @click="extend"
                                >
                                    <PlusIcon class="w-4 h-4" />
                                </button>
                            </div>
                        </div>
                        <div v-if="canEditExpirationDate">
                            <div
                                class="text-sm border text-primary-500 font-medium rounded-full leading-none flex items-center"
                            >
                                <div class="py-1 px-3">
                                    {{ expires }}
                                </div>
                                <Dropdown
                                    ref="expirationTimeDropdown"
                                    placement="bottom-end"
                                    teleport
                                >
                                    <template #toggle>
                                        <button
                                            v-tooltip="{ content: 'Set new expiration date' }"
                                            class="rounded-full border text-secondary-500 p-1 -ml-1 -m-px hover:border-primary-500 hover:text-primary-1"
                                            @click="expirationTimeDropdown?.open()"
                                        >
                                            <Edit2Icon class="w-4 h-4" />
                                        </button>
                                    </template>
                                    <template #content>
                                        <div class="pt-4">
                                            <DatePicker
                                                :model-value="expireAt"
                                                mode="dateTime"
                                                :min-date="new Date()"
                                                is24hr
                                                @update:model-value="(date) => setExpirationTime(date)"
                                            />
                                        </div>
                                    </template>
                                </Dropdown>
                            </div>
                        </div>
                    </div>
                </div>
                <!--                grid grid-cols-12 gap-2-->
                <div v-if="canDecline || canApply || canSuggest || canJoin" class="issue__actions ">
                    <div v-if="canDecline || canApply" class="flex gap-x-4">
                        <AppButton
                            v-if="canDecline"
                            class="--danger --large --outline flex-none"
                            @click="decline"
                        >
                            <XIcon />
                            Decline
                        </AppButton>
                        <AppButton
                            v-if="canApply"
                            class="--primary --large flex-grow"
                            @click="apply"
                        >
                            <CheckIcon />
                            Approve
                        </AppButton>
                    </div>

                    <div class="grid grid-cols-12 gap-2">
                        <AppButton
                            v-if="canSuggest"
                            class="--outline --large"
                            :class="{
                                'col-span-6': showAssignButton,
                                'col-span-12': !showAssignButton,
                            }"
                            @click="suggest"
                        >
                            <ChatWithPlusIcon class="text-secondary-500" />
                            Suggest resolution
                        </AppButton>

                        <AppButton
                            v-if="showAssignButton"
                            class="--outline --large col-span-6"
                            @click="assignExecutor"
                        >
                            <UserRightIcon v-if="canAssignIssueTo" />
                            {{ canAssignIssueTo ? 'Assign executor' : 'Start work' }}
                        </AppButton>
                    </div>

                    <div
                        v-if="issue.executor_pk"
                        class="bg-white dark:bg-dark-4 rounded-md border px-2.5 py-2 flex gap-2 items-center"
                    >
                        <AppAvatar
                            v-if="issueExecutor?.avatar"
                            :image="issueExecutor.avatar"
                            :model="issueExecutor"
                            class="h-6 w-6"
                        />
                        <div class="flex-grow flex flex-col">
                            <span class="dark:text-white text-xs font-semibold">{{ getFullName(issueExecutor) }}</span>
                            <span class="text-gray-600 text-xs">Started on {{
                                $format.datetime(issue.executor_start_at)
                            }}</span>
                        </div>
                        <span class="badge --primary --outline --rounded --soft">Assigned</span>
                    </div>

                    <AppButton
                        v-if="canJoin"
                        class="--primary --large"
                        @click="join"
                    >
                        Join
                        <LogInIcon />
                    </AppButton>
                </div>

                <SuspenseManual :state="suspense">
                    <ChatComponent
                        v-if="chatInstance"
                        :chat="chatInstance"
                        :readonly="!isMember"
                        class="w-full h-full"
                    />

                    <template #fallback>
                        <div class="w-full h-full p-4 pt-0">
                            <PlaceholderBlock class="w-full h-full" />
                        </div>
                    </template>
                </SuspenseManual>
            </div>

            <div class="issue__sidebar fancy-scroll relative">
                <!-- Result -->
                <div v-if="isApplied || result" class="issue__sidebar__block">
                    <div class="issue__sidebar__block__title">
                        <CheckSquareIcon class="issue__sidebar__block__icon" />
                        <div>{{ isApplied ? 'Applied result' : 'Last approved result' }}</div>
                    </div>

                    <div class="issue__sidebar__block__body">
                        <IssueResult
                            :issue="issue"
                            :result="result"
                        />
                    </div>
                </div>

                <!-- Model Info -->
                <div class="issue__sidebar__block space-y-4">
                    <div
                        v-for="(block, index) in sidebarBlocks"
                        :key="index"
                    >
                        <div class="issue__sidebar__block__title">
                            <AlertCircleIcon class="issue__sidebar__block__icon" />
                            <div>{{ block.title }}</div>
                        </div>
                        <div class="issue__sidebar__block__body">
                            <Component :is="block.node" />
                        </div>
                    </div>
                </div>

                <!--Tasks-->
                <Component
                    :is="controller.tasksComponent"
                    v-show="controller.tasksComponent"
                    :issue="issue"
                    :can-delete-task="canDeleteTask"
                />

                <!-- Actions-->
                <div v-if="controller.actionsComponent && isMember" class="issue__sidebar__block">
                    <div
                        v-if="controller.actionsComponentTitle"
                        class="issue__sidebar__block__title"
                    >
                        <AlertCircleIcon class="issue__sidebar__block__icon" />
                        <div>{{ controller.actionsComponentTitle }}</div>
                    </div>
                    <div class="issue__sidebar__block__body">
                        <Component :is="controller.actionsComponent" :issue="issue" />
                    </div>
                </div>

                <!-- Members -->
                <div class="issue__sidebar__block">
                    <div class="issue__sidebar__block__title">
                        <UsersIcon class="issue__sidebar__block__icon" />
                        <div>Members</div>
                    </div>
                    <SuspenseManual :loading="false">
                        <div class="issue__sidebar__block__body">
                            <div
                                class="bg-white dark:bg-dark-3 rounded-md border px-2.5 py-4 max-w-full organizer-members space-y-5"
                            >
                                <OrganizerMember
                                    v-for="(member) in members"
                                    :key="member.pk"
                                    :member="member"
                                    :is-executor="issue?.executor_pk === member.pk"
                                />
                            </div>
                        </div>

                        <template #fallback>
                            <PlaceholderBlock class="issue__sidebar__block__body h-[118px]" />
                        </template>
                    </SuspenseManual>

                    <div v-if="hiddenMembers.length" class="issue__sidebar__block">
                        <div class="issue__sidebar__block__title mt-4">
                            <UsersIcon class="issue__sidebar__block__icon" />
                            <div>Hidden members</div>
                        </div>

                        <div class="issue__sidebar__block__body">
                            <div
                                class="bg-white dark:bg-dark-3 rounded-md border px-2.5 py-3 max-w-full organizer-members space-y-3"
                            >
                                <IssueMembersGroup
                                    v-for="(groupMembers, departmentPk) in hiddenMembersByDepartment"
                                    :key="departmentPk"
                                    :department-pk="departmentPk"
                                    :members="groupMembers"
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    v-if="manualNavigationIsAvailable"
                    class="fixed bottom-0 right-0 flex items-center border border-white rounded-t-lg gap-x-1 p-0.5 bg-secondary-50 dark:bg-dark-3 "
                >
                    <div
                        class="p-1.5 px-2 cursor-pointer hover:bg-secondary-100 dark:hover:bg-dark-4 rounded-t-lg"
                        @click="goToPrevious"
                    >
                        <ChevronLeftIcon />
                    </div>
                    <div class="w-[1px] py-2 bg-secondary-300 dark:bg-secondary" />
                    <div
                        class="p-1.5 px-2 cursor-pointer hover:bg-secondary-100 dark:hover:bg-dark-4 rounded-t-lg"
                        @click="goToNext"
                    >
                        <ChevronRightIcon />
                    </div>
                </div>
            </div>
        </div>
    </AppModalWrapper>
</template>

<script lang="ts" setup>
import { h } from 'vue'
import { useRouter } from 'vue-router'
import { match } from '~/lib/Helper/EnumHelper'
import type { z } from 'zod'
import type { ModelAttributes } from '~types/lib/Model'
import type { IssueData } from '~/api/models/Issue/Issue'
import { getIssueController, IssueCategory, IssueStatus } from '~/api/models/Issue/Issue'
import IssueStatusComponent from '~/components/Page/Issue/IssueStatus.vue'
import ChatWithPlusIcon from '@/assets/icons/ChatWithPlusIcon.svg?component'
import CloseIcon from '@/assets/icons/CloseIcon.svg?component'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import ModelDetailedInfo, { hasDetailedInfo } from '@/components/Model/ModelDetailedInfo.vue'
import OrganizerMember from '@/components/Organizer/Components/OrganizerMember.vue'
import TabBackground from '@/components/tabs/TabBackground.vue'
import { preventDuplication } from '~/lib/Helper/PromiseHelper'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import ChatComponent from '@/modules/chat/components/ChatComponent.vue'
import ChatSystemMessage from '@/modules/chat/lib/Model/ChatSystemMessage'
import type ChatMessage from '@/modules/chat/lib/Model/ChatMessage'
import SaleInfoTabbed from '@/views/sales/SaleInfoTabbed.vue'
import IssueResult from '~/components/Page/Issue/IssueResult.vue'
import IssueResultFormModal from '~/components/Page/Issue/IssueResultFormModal.vue'
import IssueDeclineFormModal from '~/components/Page/Issue/IssueDeclineFormModal.vue'
import SaleModelDetailedInfo from '@/components/Model/DetailedInfo/SaleModelDetailedInfo.vue'
import { computedAsync } from '@vueuse/core'
import IssueMembersGroup from '~/components/Page/Issue/IssueMembersGroup.vue'
import { groupBy } from '~/lib/Helper/ArrayHelper'
import { DatePicker } from 'v-calendar'
import { useProgress } from '@marcoschulte/vue3-progress'
import AssignAgentModal from '~/modals/AssignAgentModal.vue'
import { AssignLogColumn } from '~/api/models/AssignLog/AssignLog'
import UserRightIcon from '~/assets/icons/pack/UserRightIcon.svg?component'
import { getFullName } from '~/lib/Helper/PersonHelper'
import { useNavigationHotkeys } from '~/composables/useNavigationHotkeys'

defineOptions({
    name: 'IssueSideModal',

    modal: {
        position: 'right',
    },
})

const props = defineProps<{
    pk: PrimaryKey,
    issue?: ModelAttributes<'Issue'>
}>()

const emit = defineEmits<{
    close: [],
}>()

const context = props.issue ? useNewContext(props.issue) : await useNewContext('Issue', props.pk)

//

const {
    useDictionary,
    useModel,
    hasPermission,
    currentUserPk,
} = context

// @todo Remove this after modals support close on route change
watchOnce(useRouter().currentRoute, () => {
    emit('close')
})

// eslint-disable-next-line vue/no-dupe-keys
const issue = computed(() => context.record)

const modelController = useModel('Issue')
const controller = computed(() => getIssueController(issue.value))

const {
    record: chat,
    fetch: fetchChat,
} = useModel('Chat').useRecord().destructable()

const suspense = useSuspensableComponent(async () => {
    await fetchChat(issue.value.chat_pk)
}, { throwOnError: true })

const chatInstance = computed(() => {
    if (!chat.value) {
        return
    }

    return useService('chat').getOrCreateChat(chat.value)
})

//

const issueCategoryDictionary = useGeneralDictionary('IssueCategory')

const title = computed(() => {
    return issueCategoryDictionary.find(issue.value.category).title
})

const isMember = computed(() => {
    const userPk = context.currentUserPk

    return members.value.some((member) => member.pk === userPk)
})

const isDeleted = computed(() => {
    return issue.value.is_deleted
})

const isApplied = computed(() => {
    return issue.value.status === IssueStatus.Applied
})

const isDeclined = computed(() => {
    return issue.value.status === IssueStatus.Declined
})

const isClosed = computed(() => {
    return Boolean(issue.value.closed_at)
})

const canDoSomething = computed(() => {
    return isMember.value && !isApplied.value && !isDeleted.value
})

const permissions = computed(() => {
    const userPk = context.currentUserPk

    return members.value.find((member) => member.pk === userPk)?.permissions ?? {
        apply: hasPermission('apply', 'Issue', issue.value),
        vote: false,
        extend: false,
        deleteTask: false,
    }
})

const canDecline = computed(() => {
    if (!canDoSomething.value) {
        return false
    }

    return ![IssueStatus.Declined, IssueStatus.Canceled].includes(issue.value.status) && permissions.value.apply
})

const canApply = computed(() => {
    if (!canDoSomething.value) {
        return false
    }

    return !isApplied.value && permissions.value.apply && issue.value.status !== IssueStatus.Canceled
})

provide('canApply', canApply)

const canVote = computed(() => {
    if (!canDoSomething.value) {
        return false
    }

    return !isClosed.value && permissions.value.vote
})

provide('canVote', canVote)

const canSuggest = computed(() => {
    if (!canDoSomething.value) {
        return false
    }

    return !isClosed.value
})

const canJoin = computed(() => {
    return !isMember.value && hasPermission('join', 'Issue', issue.value)
})

const canExtend = computed(() => {
    if (!canDoSomething.value) {
        return false
    }

    return !isClosed.value &&
        permissions.value.extend &&
        issue.value.status === IssueStatus.Expired &&
        hasPermission('extend', 'Issue', issue.value)
})

const canDeleteTask = computed(() => {
    if (!canDoSomething.value) {
        return false
    }

    return !isClosed.value && permissions.value.deleteTask
})

const showAssignButton = computed(() => {
    if (issueController.isAssignable && canDoSomething.value && !isDeclined.value) {
        return canAssignIssueTo.value || issue.value.executor_pk !== currentUserPk
    }

    return false
})

const canAssignIssueTo = computed(() => {
    return hasPermission('assign', 'Issue', issue.value)
})

const canUnAssignIssueTo = computed(() => {
    return hasPermission('unAssign', 'Issue', issue.value)
})

const assignExecutorSendEvent = async (executor_pk?: PrimaryKey) => {
    return await useModel('Issue').actions.assignExecutor({
        pk: usePk(issue.value),
        executor_pk: executor_pk ? executor_pk : null,
    })
}

const assignAgentModal = useModal(AssignAgentModal)

async function assignExecutor() {
    if (canAssignIssueTo.value) {
        const members = issue.value.members.map(member => {
            const agent = agentsDictionary.find(member.pk)

            if (!agent) {
                useLogger('issue:members').fatal('Agent not found', member.pk)

                return
            }

            return agent
        }).filter(Boolean) as ModelAttributes<'Agent'>[]

        await assignAgentModal.open({
            model: {
                name: 'Issue',
                id: issue.value.id,
            },
            column: AssignLogColumn.Executor,
            defaultAgentPk: issue.value.executor_pk ? issue.value.executor_pk : undefined,
            canUnAssign: canUnAssignIssueTo.value,
            canAssign: canAssignIssueTo.value,
            agents: members,
            messageError: 'Can\'t assign executor',
            messageSuccess: 'Executor has been assigned',
            onAssign: (response) => assignExecutorSendEvent(response.agent_pk),
        })
    } else {
        await preventDuplication(async () => {
            await assignExecutorSendEvent(currentUserPk)
        })
    }
}

const issueController = getIssueController(issue.value)
const canEditExpirationDate = computed(() => {
    return isMember.value
        && hasPermission('setExpirationDate', 'Issue', issue.value)
        && issueController.isExpirationDateEditable
        && [IssueStatus.NeedApply].includes(issue.value.status)
})

const expirationTimeDropdown = ref<{
    open(): void,
}>()

const expireAt = computed(() => {
    const expiresAt = issue.value.expires_at

    return expiresAt ? Date.fromUnixTimestamp(expiresAt) : null
})

const isSettingsExpirationTime = ref(false)

async function setExpirationTime(expirationTime: Date | undefined) {
    if (!expirationTime) {
        return
    }

    await preventDuplication(async () => {
        await useProgress().attach(
            modelController.actions.updateExpirationDate({
                pk: props.pk,
                expires_at: Math.floor(expirationTime.getTime() / 1000),
            }),
        )

        toastSuccess('Expiration time was updated successfully')
    }, isSettingsExpirationTime)
}

const shouldShowExpirationTime = computed(() => {
    return issue.value.expires_at && [
        IssueStatus.Expired,
        IssueStatus.Processing,
        IssueStatus.New,
        IssueStatus.NeedApprove,
        IssueStatus.NeedMoreTime,
    ].includes(issue.value.status)
})

const expires = computed(() => {
    const time = Date.fromUnixTimestampOrNull(issue.value.expires_at)?.getTimeTill()

    if (time === 'already') {
        return '0 min'
    }

    return time
})

const sidebarBlocks = computedAsync(async () => {
    const modelsList = []

    if (issue.value.category === IssueCategory.Disclaimer && issue.value.data?.sale_id) {
        return [{
            title: 'Sale info',
            node: () => h(SaleModelDetailedInfo, {
                model: {
                    model_name: 'Sale',
                    model_pk: String(issue.value.data.sale_id),
                },
            }),
        }]
    }

    if (issue.value.category === IssueCategory.PnrSchedule && issue.value.data?.sale_pk) {
        return [{
            title: 'Sale info',
            node: () => h(SaleModelDetailedInfo, {
                model: {
                    model_name: 'Sale',
                    model_pk: issue.value.data.sale_pk,
                },
            }),
        }]
    }

    if (issue.value.category === IssueCategory.Voucher && issue.value.data?.voucher_id) {
        return [{
            title: issue.value.data.voucher_id,
            node: null,
        }]
    }

    if (!hasDetailedInfo(issue.value.model_name)) {
        return []
    }

    modelsList.push(
        {
            title: issue.value.model_name + ' info',
            node: () => h(ModelDetailedInfo, {
                model: {
                    model_name: issue.value.model_name,
                    model_pk: issue.value.model_pk,
                },
            }),
        },
    )

    if (issue.value.model_name === 'Lead') {
        const leadRecord = await useModel('LeadPreview').useRecord().fetch(issue.value.model_pk)

        modelsList.push(
            {
                title: 'Client info',
                node: () => h(ModelDetailedInfo, {
                    model: {
                        model_name: 'Client',
                        model_pk: leadRecord.value.client_pk,
                    },
                }),
            },
        )
    }

    return modelsList
}, [], { lazy: true })

const agentsDictionary = useDictionary('Agent')

const issueExecutor = computed(() => {
    if (!issue.value?.executor_pk) {
        return
    }

    const agent = agentsDictionary.find(issue.value.executor_pk)

    if (!agent) {
        useLogger('issue:members').fatal('Agent Executor not found', issue.value.executor_pk)

        return
    }

    return agent
})

const allMembers = computed(() => {
    return issue.value.members.map(member => {
        const agent = agentsDictionary.find(member.pk)

        if (!agent) {
            useLogger('issue:members').fatal('Agent not found', member.pk)

            return
        }

        return {
            ...member,
            ...agent,
        }
    }).filter(Boolean)
})

const members = computed(() => {
    return allMembers.value.filter((member) => !member.is_hidden)
})

const hiddenMembers = computed(() => {
    return allMembers.value.filter((member) => member.is_hidden)
})

//

export type IssueMember = {
    pk: PrimaryKey,
    role: string,
    is_hidden: boolean,
    permissions: {
        vote: boolean,
        apply: boolean,
        extend: boolean,
        deleteTask: boolean,
    }
} & ModelAttributes<'Agent'>

const hiddenMembersByDepartment = computed(() => {
    return groupBy(hiddenMembers.value, 'department_pk')
})

const lastApprovedResult = computed(() => {
    const chat = chatInstance.value

    if (!chat) {
        return
    }

    const messages = chat.messages.filter((message: ChatMessage) => {
        return (message instanceof ChatSystemMessage) && message.data?.category === 'approve-request' && message.data?.event === 'suggest'
    })

    const suggestionMessage = messages.findLast((message) => {
        return message.data.params.members?.all.length === message.data.params.members?.approved.length
    })

    return suggestionMessage?.data.params.result
})

const result = computed(() => {
    if (isApplied.value) {
        return issue.value.result
    }

    return lastApprovedResult.value
})

//

async function extend() {
    await modelController.actions.extend({ pk: usePk(issue.value) })
}

const declineModal = useModal(IssueDeclineFormModal, { issue: issue.value })

async function decline() {
    const remark = await declineModal.open()

    await modelController.actions.decline({
        pk: usePk(issue.value),
        remark,
    })

    toastSuccess('Request declined')
}

//

const resultModal = useModal(IssueResultFormModal)

async function apply() {
    const dataToApply = await resultModal.open({
        type: 'apply',
        issue: issue.value,
        result: result.value,
    })

    await modelController.actions.apply({
        pk: usePk(issue.value),
        result: dataToApply.result,
    })
}

//

async function suggest() {
    const dataToApply = await resultModal.open({
        type: 'suggest',
        issue: issue.value,
        result: result.value,
        withRemark: true,
    })

    await modelController.actions.suggest({
        pk: usePk(issue.value),
        result: dataToApply.result,
        remark: dataToApply.remark,
    })
}

//

async function join() {
    await preventDuplication(async () => {
        await modelController.actions.join({ pk: usePk(issue.value) })
    })
}

//

const infoComponent = computed(() => {
    const airlineReimbursementCallBack = () => {
        if (issue.value.model_name === 'Sale') {
            return () => h(SaleInfoTabbed, {
                pk: issue.value.model_pk,
            })
        }
    }

    return match(IssueCategory, issue.value.category, {
        [IssueCategory.CustomerSupportGeneral]: () => {
            if (issue.value.model_name === 'Sale') {
                return () => h(SaleInfoTabbed, {
                    pk: issue.value.model_pk,
                })
            }
        },
        [IssueCategory.AirlineReimbursement]: airlineReimbursementCallBack,
        [IssueCategory.AirlineReimbursementRefund]: airlineReimbursementCallBack,
        [IssueCategory.AirlineReimbursementCredit]: airlineReimbursementCallBack,
        [IssueCategory.AirlineReimbursementCancellation]: airlineReimbursementCallBack,
        [IssueCategory.SplitSale]: () => {
            const data = issue.value.data as z.infer<typeof IssueData[IssueCategory.SplitSale]>

            const canViewLeads = canApply.value || hasPermission('manage', 'all')

            return () => h(SaleInfoTabbed, {
                pk: issue.value.model_pk,
                sale_lead_pk: canViewLeads ? data.sale_lead_pk : undefined,
                lead_pk: canViewLeads ? data.lead_pk : undefined,
            })
        },
        [IssueCategory.ClientStatus]: () => undefined,
        [IssueCategory.TaskDiscussion]: () => undefined,
        [IssueCategory.KeepClient]: () => undefined,
        [IssueCategory.Disclaimer]: () => undefined,
        [IssueCategory.ClosingReason]: () => undefined,
        [IssueCategory.PriceDrop]: () => undefined,
        [IssueCategory.AltExtra]: () => undefined,
        [IssueCategory.VerificationTA]: () => undefined,
        [IssueCategory.VerificationOther]: () => undefined,
        [IssueCategory.Voucher]: () => undefined,
        [IssueCategory.PnrSchedule]: () => {
            if (!!issue.value.data?.sale_pk) {
                return () => h(SaleInfoTabbed, {
                    pk: issue.value.data?.sale_pk,
                })
            }
        },
    })
})

const showInfoComponent = ref(false)

async function toggleModelInfo() {
    showInfoComponent.value = !showInfoComponent.value
}

// requests navigation
const { goToNext, goToPrevious, manualNavigationIsAvailable } = useNavigationHotkeys({
    activePk: props.pk,
    eventName: 'navigateRequest',
})
</script>

