<template>
    <div class="flex flex-col gap-4">
        <FormField
            :form="form"
            :field="'closing_reason'"
            label="Closing reason"
        >
            <InputSelect
                v-model="form.data.closing_reason"
                :options="reasonOptions"
                filter
                with-empty
                placeholder="Please select a closing reason"
            />
        </FormField>

        <FormField
            v-if="showSelectLead && !result?.duplicate_lead_pk"
            :form="form"
            :field="'duplicate_lead_pk'"
            label="Select duplicate lead"
        >
            <InputSelect
                v-model="form.data.duplicate_lead_pk"
                :options="leadOptions"
                with-empty
                search
                @search="handleLeadSearch"
            />
        </FormField>
        <ModelDetailedInfo
            v-if="showSelectLead && form.data.duplicate_lead_pk"
            :key="form.data.duplicate_lead_pk || 'none'"
            :model="{
                model_pk: form.data.duplicate_lead_pk,
                model_name: 'Lead'
            }"
            class="mb-2"
        />

        <FormField
            v-if="showTextarea"
            :form="form"
            :field="'closing_reason_remark'"
        >
            <InputTextarea
                v-model="form.data.closing_reason_remark"
                placeholder="Please enter a closing reason"
                maxlength="256"
            />
        </FormField>
    </div>
</template>

<script setup lang="ts">
import type zod from 'zod'
import type { IssueCategory, IssueData, IssueResult } from '~/api/models/Issue/Issue'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import FormField from '~/components/Form/FormField.vue'
import { ClosingReasons } from '~/api/models/Lead/Lead'
import ModelDetailedInfo from '@/components/Model/ModelDetailedInfo.vue'
import { getFullName } from '~/lib/Helper/PersonHelper'

defineOptions({
    name: 'IssueResultClosingReasonForm',
})
const props = defineProps<{
    result?: Result | Empty,
    data?: Data | Empty,
}>()

const { useModel, useCurrentUser } = useContext()

const currentUser = useCurrentUser()

const leadModel = useModel('Lead')
const leadList = leadModel.useList({ with: ['clientPreview']})

const reasonOptions = computed(() => useGeneralDictionary('ClosingReason').mapRecordsForManualAssignment.forSelect())

type Data = zod.infer<typeof IssueData[IssueCategory.ClosingReason]>
type Result = zod.infer<typeof IssueResult[IssueCategory.ClosingReason]>

const showTextarea = computed((): boolean => {
    if (!form.data.closing_reason) {
        return false
    }

    return form.data.closing_reason === ClosingReasons.Other
})

const showSelectLead = computed(() => {
    return form.data.closing_reason === ClosingReasons.NewRequest
})

const validateReasonRemarkLength = (value: string) => {
    if (value && value.length < 10) {
        return 'Please provide a more detailed reason'
    }
}

const validateDuplicateLead = (value: PrimaryKey | Empty) => {
    if (props.data?.lead_pk) {
        return value === props.data.lead_pk ? 'Cannot link to the same lead ID. Please select a different lead.' : null
    }
}

//

const form = useForm({
    closing_reason: props.result?.closing_reason ?? null,
    closing_reason_remark: props.result?.closing_reason_remark ?? null,
    duplicate_lead_pk: props.result?.duplicate_lead_pk ?? null,
} satisfies Result, {
    closing_reason: ValidationRules.Required(),
    closing_reason_remark: [ValidationRules.RequiredWhen(showTextarea), validateReasonRemarkLength],
    duplicate_lead_pk: [ValidationRules.RequiredWhen(showSelectLead), validateDuplicateLead],
})

const leadOptions = computed(() => {
    return leadList.records.map((lead) => ({
        title: `#${lead.pk} | ${getFullName(lead.clientPreview)}`,
        subtitle: `${lead.from_iata_code} -> ${lead.to_iata_code}`,
        value: lead.pk,
    }))
})

const handleLeadSearch = (query: string) => {
    query = query.trim()

    if (query.length < 1) {
        return
    }

    leadList.fetch({
        where: (and) => {
            if (props.data?.lead_pk) {
                and.ne('id', props.data.lead_pk)
            }
            and.eq('can_merge', true)
            and.search(query)
        },
    })
}

//

defineExpose({
    form,
})
</script>

