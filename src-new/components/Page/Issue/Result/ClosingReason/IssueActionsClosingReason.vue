<template>
    <div v-if="issue.result?.duplicate_lead_pk" class="issue__sidebar__block">
        <ModelDetailedInfo
            :model="{
                model_name: 'Lead',
                model_pk: issue.result.duplicate_lead_pk
            }"
        />
    </div>
</template>

<script setup lang="ts">
import type { ModelAttributes } from '~types/lib/Model'
import ModelDetailedInfo from '@/components/Model/ModelDetailedInfo.vue'

defineOptions({
    name: 'IssueActionsClosingReason',
})

const props = defineProps<{
    issue: ModelAttributes<'Issue'>
}>()
</script>

