<template>
    <AppModalWrapper
        class="!max-w-[400px]"
        close-button
        :header="title"
        :loading="loading"
    >
        <template #default>
            <div class="p-10 border-t border-gray-200 dark:border-dark-5">
                <form
                    novalidate
                    class="space-y-4"
                    @submit.prevent="submit"
                >
                    <div v-if="'name' in form.data">
                        <FormField
                            :form="form"
                            field="name"
                            label="Team name"
                        >
                            <InputText v-model="form.data.name" type="text" />
                        </FormField>
                    </div>
                    <div v-if="'department_pk' in form.data">
                        <FormField
                            :form="form"
                            field="department_pk"
                            label="Department"
                        >
                            <InputSelect
                                v-model="form.data.department_pk"
                                :options="departmentDictionaryForSelect"
                                :with-empty="true"
                            />
                        </FormField>
                    </div>
                    <div>
                        <FormField
                            :form="form"
                            field="pnr_queue"
                            label="PNR queue number"
                        >
                            <InputNumber
                                v-model="form.data.pnr_queue"
                            />
                        </FormField>
                    </div>
                </form>
            </div>
        </template>

        <template #footer>
            <div class="flex justify-end">
                <button
                    class="btn btn-sm btn-outline-secondary box w-24"
                    data-dismiss="modal"
                    type="button"
                    @click="close"
                >
                    Cancel
                </button>

                <button
                    class="btn btn-sm btn-primary ml-2 min-w-24"
                    type="button"
                    @click="submit"
                >
                    Save
                </button>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script lang="ts">
import FormField from '~/components/Form/FormField.vue'
import WorksInContextMixin from '~/mixins/WorksInContextMixin'
import type { PropType } from 'vue'
import type { ModelAttributes } from '~types/lib/Model'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import InputText from '~/components/Input/InputText.vue'

export default defineComponent({
    name: 'TeamFormModal',

    components: {
        InputText,
        FormField,
    },

    mixins: [
        WorksInContextMixin,
    ],

    props: {
        mode: {
            type: String,
            required: true,
        },

        team: {
            type: Object as PropType<ModelAttributes<'Team'>>,
            default: null,
        },
    },

    emits: ['close'],

    data() {
        const isUpdateMode = this.mode === 'update'
        const initialFormData = {
            name: isUpdateMode ? this.team.name : '',
            department_pk: isUpdateMode ? this.team.department_pk : null as PrimaryKey | null,
            pnr_queue: isUpdateMode ? this.team.pnr_queue : '',
        }

        const form = useForm(initialFormData, {
            name: ValidationRules.Required(),
            department_pk: ValidationRules.Required('Department field is required'),
        })

        return {
            form,
        }
    },

    computed: {
        title() {
            return this.mode === 'update' ? 'Edit team' : 'Create team'
        },

        loading() {
            return this.form.loading.value
        },

        departmentDictionaryForSelect() {
            return this.useDictionary('Department').mapRecords.forSelect()
        },

        submit() {
            return this.form.useSubmit({
                resolver: async () => {
                    if (this.mode === 'update') {
                        await this.update()
                    } else if (this.mode === 'create') {
                        await this.create()
                    }

                    this.$emit('close')
                },
                silent: true,
            })
        },
    },

    methods: {
        async create() {
            await this.useModel('Team').actions.create({
                data: this.form.data,
            }, true)

            this.toastSuccess('Team created')
        },

        async update() {
            if (!this.team) {
                return
            }

            await this.useModel('Team').actions.update({
                pk: usePk(this.team),
                ...this.form.data,
            }, true)

            this.toastSuccess('Team updated')
        },

        close() {
            this.$emit('close')
        },
    },
})
</script>
