<template>
    <div
        class="input-field"
        :class="{
            [`--${size}`]: size,
            'input-field--placeholder-icon': placeholderIcon,
            'input-field--with-icon': iconComponent,
            'input-field--with-clear-button': clearButton,
        }"
    >
        <div v-if="iconComponent" class="input-field__icon">
            <Component :is="iconComponent" class="icon" />
        </div>

        <slot name="before" />

        <input
            ref="input"
            class="input-field__input"
            :value="modelValue"
            :placeholder="placeholder ?? (placeholderIcon ? ' ' : undefined)"
            :disabled="disabled"
            :readonly="readonly"
            :type="type"
            :maxlength="maxlength ?? undefined"
            autocomplete="Off"
            v-bind="inputAttrs"
            @input="$emit('update:modelValue', $event.target.value)"
            v-on="$inheritEvents(inheritableEvents)"
        >

        <AppButton
            v-if="clearButton && typeof modelValue === 'string' && modelValue.length"
            type="button"
            class="--ghost --danger --square absolute top-0 right-0"
            :class="{
                [`--${size}`]: size,
            }"
            @click="$emit('update:modelValue', '')"
        >
            <XIcon />
        </AppButton>

        <slot name="after" />
    </div>
</template>

<script setup lang="ts">
import type { Props, Emits } from '~/lib/Input/useInputField'
import { inheritableEvents } from '~/lib/Input/useInputField'
import { useInputField } from '~/lib/Input/useInputField'

const props = withDefaults(defineProps<Props<string | number | undefined | null> & {
    clearButton?: boolean,
}>(), {
    // @default-props Can't export them from useInputField because of TS bug
    placeholder: undefined,
    size: 'normal',
    disabled: false,
    readonly: false,
    autofocus: false,
    icon: undefined,
    placeholderIcon: undefined,
    inputAttrs: undefined,
    clearButton: false,

    //

    type: 'text',
    maxlength: undefined,
})

defineEmits<Emits<string | number>>()

// Refs declaration
// ================

const input = ref<HTMLInputElement>()

//

const { iconComponent } = useInputField<string | number | undefined | null>(props, input)

//

defineExpose({
    input,
    focus: () => input.value?.focus(),
})
</script>
