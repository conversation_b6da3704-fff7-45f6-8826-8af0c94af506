<template>
    <SnowflakeIcon
        v-if="!$slots.default && $festiveEvents.winter?.loader"
        class="animate-spin"
        :class="[iconClass, $attrs.class]"
        style="animation-duration: 1.5s"
    />
    <LoaderIcon
        v-else-if="!$slots.default"
        :class="[iconClass, $attrs.class]"
        class="animate-spin"
    />

    <div
        v-if="$slots.default"
        class="flex items-center"
        :class="containerClass"
    >
        <SnowflakeIcon
            v-if="$festiveEvents.winter?.loader"
            class="animate-spin text-blue-600"
            :class="[iconClass, $attrs.class]"
            style="animation-duration: 1.5s"
        />
        <LoaderIcon
            v-else
            :class="[iconClass, $attrs.class]"
            class="animate-spin"
        />

        <div :class="{'ml-2': $slots.default}">
            <slot />
        </div>
    </div>
</template>

<script setup lang="ts">
import { LoaderIcon } from '@zhuowenli/vue-feather-icons'
import SnowflakeIcon from '@/assets/icons/SnowflakeIcon.svg?component'
import type { AnyObject } from '@/types'

withDefaults(defineProps<{
    iconClass?: string | AnyObject
    containerClass?: string | AnyObject,
}>(), {
    iconClass: undefined,
    containerClass: undefined,
})
</script>
