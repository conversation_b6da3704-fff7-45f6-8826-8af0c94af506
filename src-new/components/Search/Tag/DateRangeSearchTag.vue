<template>
    <InputDateRange
        :model-value="value"
        size="small"
        :timezone="tag.timezone"
        class="--calendar"
        :placeholder="tag.placeholder"
        :date-presets="tag.datePresets"
        @update:model-value="handleUpdate"
        @update:preset="handlePresetUpdate"
    />
</template>

<script lang="ts" setup>
import type { SearchTagEmits, SearchTagProps } from '~/lib/Search/Tag/SearchTag'
import type DateRangeSearchTag from '~/lib/Search/Tag/DateRangeSearchTag'
import type { DateRange, DateRangeName } from '@/lib/core/helper/DateHelper'
import { moveDateRangeFromTimeZone } from '@/lib/core/helper/DateHelper'
import { getPredefinedRange } from '@/lib/core/helper/DateHelper'

defineOptions({
    name: 'DateRangeSearchTag',
})

const props = defineProps<SearchTagProps<DateRangeSearchTag>>()
const emit = defineEmits<SearchTagEmits<DateRangeSearchTag>>()

const value = computed<DateRange | undefined>(() => {
    const v = props.modelValue

    if (Array.isArray(v)) {
        useLogger('date-range-search-tag').warn('Multiple values are not supported')

        return
    }

    if (typeof v === 'string') {
        // Tag always works in app timezone, so we use it to get predefined range
        return moveDateRangeFromTimeZone(getPredefinedRange(v, props.tag.appTimezone), props.tag.appTimezone)
    }

    return v
})

const update = useDebounceFn((dateRange: DateRange | DateRangeName | undefined) => {
    emit('update:modelValue', dateRange)
}, 10)

const handleUpdate = (dateRange: DateRange | undefined) => {
    update(dateRange)
}

const handlePresetUpdate = (presetName: DateRangeName) => {
    update(presetName)
}
</script>
