<template>
    <div
        :id="modalId"
        ref="modalRef"
        class="app-modal"
        :class="classes"
        :data-place="place"
        v-bind="modal.options.bind"
        @mousedown.self="() => closeOnOverlay && close()"
    >
        <WorkspaceWrapper :workspace="modal.options.workspace">
            <slot
                :close="close"
                :resolve="resolve"
                :reject="reject"
            >
                <Suspense @resolve="onResolve" @fallback="onFallback">
                    <Transition :duration="0" name="app-modal-inner">
                        <Component
                            :is="modal.component"
                            v-if="modal.component"
                            v-show="loading"
                            ref="componentRef"
                            v-bind="modal.payload"
                            @close="close"
                            @resolve="resolve"
                            @reject="reject"
                        />
                    </Transition>

                    <template #fallback>
                        <div class="app-modal__loading">
                            <Loader />
                        </div>
                    </template>
                </Suspense>
            </slot>
        </WorkspaceWrapper>
    </div>
</template>

<script setup lang="ts">
import type { Modal } from '~/composables/useModal'
import { closeModal, prettyKey } from '~/composables/useModal'
import WorkspaceWrapper from '~/components/WorkspaceWrapper.vue'

defineOptions({
    name: 'AppModal',
    inheritAttrs: false,
})

const props = defineProps<{
    modal: Modal
}>()

const modalRef = ref()
const componentRef = ref()

const loading = ref(false)

const onFallback = () => loading.value = false
const onResolve = () => loading.value = true

const place = computed(() => props.modal.options.place)
const modalId = computed(() => 'app-modal--' + prettyKey(props.modal.key))
const withBlur = computed(() => props.modal.options.blur ?? false)
const position = computed(() => props.modal.options.position ?? 'top')
const withOverlay = computed(() => props.modal.options.overlay ?? true)
const shouldCloseOnEsc = computed(() => props.modal.options.closeOnEsc ?? true)
const closeOnOverlay = computed(() => props.modal.options.closeOnOverlay ?? true)

const classes = computed(() => {
    const classes = []

    classes.push('app-modal--' + position.value)

    if (Boolean(place.value)) {
        classes.push('app-modal--absolute')
    }

    if (!withOverlay.value) {
        classes.push('app-modal--no-overlay')
    }

    if (withBlur.value) {
        classes.push('app-modal--blur')
    }

    return classes
})

const closeInsideModals = () => {
    if (!modalRef.value) {
        return
    }

    const places = [...modalRef.value.querySelectorAll('[data-place]')]
        .map(node => node.dataset.place)
        .filter((v, i, a) => a.indexOf(v) === i) // Get unique

    places.forEach(place => {
        closeModal({ place })
    })
}

const close = async (...attrs: any[]): Promise<boolean> => {
    let data = attrs

    const onClose = props.modal.options.onClose || componentRef.value?.onClose

    if (onClose === false) {
        return false
    } else if (typeof onClose === 'function') {
        const rejectPayload = await onClose.bind(componentRef.value)(...data)

        if (rejectPayload === false) {
            return false
        } else if (rejectPayload !== true) {
            data = [rejectPayload]
        }
    }

    closeInsideModals()

    closeModal(props.modal.key)

    if (props.modal.internal.controllablePromise) {
        props.modal.internal.controllablePromise.reject(...data)
    }

    return true
}

const resolve = (...attrs: any[]) => {
    props.modal.internal.controllablePromise?.resolve(...attrs)

    closeModal(props.modal.key)
}

const reject = async (...attrs: any[]) => {
    if (await close.bind(componentRef.value)(...attrs)) {
        props.modal.internal.controllablePromise?.reject(...attrs)
    }
}

const closeOnEsc = async (event: KeyboardEvent) => {
    event.preventDefault()
    event.stopPropagation()
    event.stopImmediatePropagation()

    if (event.key === 'Escape') {
        await close()
    }
}

onMounted(() => {
    if (shouldCloseOnEsc.value) {
        window.addEventListener('keyup', closeOnEsc, { once: true })
    }

    props.modal.options.onMounted?.()
})

onUnmounted(() => {
    if (shouldCloseOnEsc.value) {
        window.removeEventListener('keyup', closeOnEsc)
    }
    props.modal.options.onUnmounted?.()
})

provide('close', close)
provide('isModalContext', true)
</script>
