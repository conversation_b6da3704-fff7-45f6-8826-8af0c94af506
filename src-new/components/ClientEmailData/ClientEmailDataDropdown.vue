<template>
    <div class="btn-group text-xs relative">
        <a
            v-if="selectedEmail"
            class="flex btn btn-outline-secondary !flex-1 min-w-0"
            v-on="isCopyAllowed ? {click : () => {
                copyToClipboard(props.defaultPhone)
                trackActivity('copy')
            }} : {}"
        >
            <div v-tooltip="withTitle ? undefined : (isCopyAllowed ? 'Copy email' : undefined)" class="text-primary">
                <MailIcon />
            </div>
            <template v-if="!selectedEmailPk">
                <HiddenDataComponent
                    v-if="withTitle"
                    :hidden-value="selectedEmail"
                    class="cursor-pointer ml-2 max-w-[230px] truncate"
                    :class="{ '!text-danger': getEmailFraudData(selectedEmail)?.is_fraud }"
                    @show="show"
                    @hide="hide"
                    @click-hidden="handleClickHidden"
                >
                    <template #default>
                        <span
                            v-tooltip="{ content: `${ getFraudTooltip(getEmailFraudData(selectedEmail), selectedEmail) ?? selectedEmail }`}"
                            @click="() => {
                                copyToClipboard(selectedEmail)
                                trackActivity('copy')
                            }"
                        >
                            {{ selectedEmail }}
                        </span>
                    </template>
                    <template #hidden="{ formatted }">
                        {{ formatted }}
                    </template>
                </HiddenDataComponent>
            </template>
            <template v-else>
                <div class="cursor-pointer ml-2 max-w-[230px] truncate">
                    <span
                        v-tooltip="{ content: selectedEmail }"
                        @click.stop="() => {
                            copyToClipboard(selectedEmail)
                            trackActivity('copy')
                        }"
                    >
                        {{ selectedEmail }}
                    </span>
                </div>
            </template>
        </a>
        <span v-else class="btn btn-outline-secondary !flex-1 min-w-0">
            <MailIcon />
            <span v-if="withTitle" class="ml-2 text-danger">Default email not set</span>
        </span>

        <Dropdown
            v-if="clientEmailDataList.records.length"
            ref="dropdownRef"
            teleport
            placement="bottom-end"
            :ignore-click-on-elements="[contextMenu, confirmModal]"
            class="flex-none"
        >
            <template #toggle="{toggle}">
                <div class="btn btn-outline-secondary box border-l-0 !rounded-l-none p-2 select-none" @click="toggle">
                    <span class="absolute text-3xs top-1">{{ clientEmailDataList.records.length }}</span>
                    <ChevronDownIcon class="top-1 relative text-theme-38 " />
                </div>
            </template>
            <template #content="{ close }">
                <div class="dropdown-menu__content mt-0 w-60">
                    <div class="max-h-[500px] overflow-y-auto fancy-scroll">
                        <HiddenDataComponent
                            v-for="(clientEmail, index) in clientEmailDataList.records"
                            :key="index"
                            :hidden-value="clientEmail.email.value"
                            class="dropdown-menu__item flex flex-col items-start w-full"
                            :class="{
                                'text-primary-600 font-bold': defaultEmail && clientEmail.email.value === defaultEmail,
                                '!text-danger': clientEmail.email.fraudInfo.is_fraud,
                            }"
                            @click-hidden="handleClickHidden"
                            @click-visible="handleClick(clientEmail, $event)"
                            @click-visible-right="openMenu($event, clientEmail, close)"
                        >
                            <template #default>
                                <div v-tooltip="{ content: getFraudTooltip(clientEmail.email.fraudInfo) }">
                                    <div class="truncate">
                                        {{ clientEmail.email.value }}
                                    </div>
                                    <div v-if="clientEmail.remark && withRemark" class="text-secondary-400 text-2xs">
                                        {{ clientEmail.remark }}
                                    </div>
                                </div>
                            </template>
                            <template #hidden="{ formatted }">
                                <div>
                                    {{ formatted }}
                                </div>
                                <div v-if="clientEmail.remark && withRemark" class="text-secondary-400 text-2xs">
                                    {{ clientEmail.remark }}
                                </div>
                            </template>
                        </HiddenDataComponent>
                    </div>

                    <template v-if="withEdit">
                        <hr class="my-3">

                        <div v-if="actionMode === 'email'">
                            <form class="relative" @submit.prevent="emailSubmit">
                                <FormField
                                    :form="emailForm"
                                    field="email"
                                    class="relative"
                                >
                                    <div class="absolute right-0 top-0 z-1 m-0.5">
                                        <AppButton class="--success --only --ghost --small dark:text-success dark:hover:text-success">
                                            <CheckIcon />
                                        </AppButton>
                                        <AppButton
                                            type="button"
                                            class="--danger --only --ghost --small dark:text-danger dark:hover:text-danger"
                                            @click="resetForms"
                                        >
                                            <XIcon />
                                        </AppButton>
                                    </div>

                                    <InputText
                                        ref="inputRef"
                                        v-model="emailForm.data.email"
                                        :input-attrs="{
                                            class: 'pr-14'
                                        }"
                                    />
                                </FormField>
                            </form>
                        </div>

                        <div v-else-if="actionMode === 'remark'">
                            <form class="relative" @submit.prevent="remarkSubmit">
                                <FormField
                                    :form="remarkForm"
                                    field="remark"
                                    class="relative"
                                >
                                    <div class="absolute right-0 top-0 z-1 m-0.5">
                                        <AppButton class="--success --only --ghost --small dark:text-success dark:hover:text-success">
                                            <CheckIcon />
                                        </AppButton>
                                        <AppButton
                                            type="button"
                                            class="--danger --only --ghost --small dark:text-danger dark:hover:text-danger"
                                            @click="resetForms"
                                        >
                                            <XIcon />
                                        </AppButton>
                                    </div>
                                    <InputText
                                        ref="inputRef"
                                        v-model="remarkForm.data.remark"
                                        placeholder="Remark here"
                                        class="!text-xs"
                                        :maxlength="11"
                                    />
                                </FormField>
                            </form>
                        </div>

                        <div
                            v-else
                            class="dropdown-menu__item font-semibold text-primary"
                            @click="actionMode = 'email'"
                        >
                            <PlusIcon class="mr-1.5" />
                            Add email
                        </div>
                    </template>
                </div>
            </template>
        </Dropdown>
    </div>
</template>

<script setup lang="ts">
import HiddenDataComponent from '~/sections/Lead/List/components/HiddenDataComponent.vue'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import NotificationTextIcon from '~assets/icons/NotificationTextIcon.svg?component'
import FormField from '~/components/Form/FormField.vue'
import { openContextMenu } from '@/plugins/ContextMenuPlugin'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import type { ModelRef } from '~types/lib/Model'
import { copyToClipboard } from '@/lib/core/helper/ClipboardHelper'
import type { MaybeElementRef } from '@vueuse/core'
import { $confirm } from '@/plugins/ConfirmPlugin'
import { DepartmentName } from '~/api/models/Department/Department'
import { PositionName } from '~/api/models/Position/Position'
import RatIcon from '~assets/icons/RatIcon.svg?component'
import PromptModal from '~/modals/PromptModal.vue'

const props = defineProps<{
    clientPk: PrimaryKey
    withEdit?: boolean
    withRemark?: boolean
    withTitle?: boolean
    defaultEmail?: string
    selectedEmailPk?: PrimaryKey
    // eslint-disable-next-line func-call-spacing
    setDefaultEmail?: (email_pk) => Promise<void>,
    onSelect?: (email: ModelRef<'ClientEmailData', 'email'>) => void
}>()

const emit = defineEmits<{
    show: [],
    copy: [],
}>()

const { useModel, hasPermission, useCurrentUser } = useContext()

const actionMode = ref<'remark' | 'email'>()

watch(actionMode, () => {
    nextTick(() => {
        focusInput()
    })
})

const confirmModal: MaybeElementRef = () => {
    return document.querySelector('#app-modal--confirm-fraud-email-modal')
}

const inputRef = ref<HTMLInputElement>()

function focusInput() {
    inputRef.value?.focus()
}

//

const selectedEmail = computed(() => {
    if (props.selectedEmailPk) {
        return clientEmailDataList.records.find(clientEmailData => clientEmailData.email_pk === props.selectedEmailPk)?.email.value
    }

    return props.defaultEmail
})

//

const isHidden = ref(true)

const isCopyAllowed = computed(() => {
    return hasPermission('manage', 'all') || !isHidden.value
})

function hide() {
    isHidden.value = true
}

function show() {
    isHidden.value = false
}

//

const clientEmailDataModel = useModel('ClientEmailData')
const emailModel = useModel('Email')

const clientEmailDataList = clientEmailDataModel.useResourceList({
    name: 'ClientClientEmailDataList',
    pk: props.clientPk,
}, {
    with: ['email', 'email.fraudInfo'],
})

const dropdownRef = ref<{
    isActive: boolean
    open(): void
    close(): void
    toggle(): void
}>()

//

defineExpose({
    close: () => {
        dropdownRef.value?.close()
    },
})

//

await clientEmailDataList.fetch()

const currentUser = useCurrentUser()

const canMarkAsFraud = computed(() => {
    return hasPermission('markAsFraud', 'all')
})

const canEdit = computed(() => {
    // Exactly how it is on backend
    return !(isUserInDepartment(currentUser, DepartmentName.Sales) && (isUserHasPosition(currentUser, PositionName.Agent) || isUserHasPosition(currentUser, PositionName.Supervisor)))
})

const emailForm = useForm<{email: string, clientEmailDataPk: PrimaryKey}>({
    email: '',
    clientEmailDataPk: undefined as PrimaryKey,
}, {
    email: [ValidationRules.Required(), ValidationRules.Email()],
    clientEmailDataPk: [ValidationRules.RequiredWhen(() => !emailForm.data.email)],
})

const remarkForm = useForm<{remark: string, clientEmailDataPk: PrimaryKey}>({
    remark: '',
    clientEmailDataPk: undefined as PrimaryKey,
}, {
    remark: [ValidationRules.Regex(/^[a-zA-Z\s]*$/)],
    clientEmailDataPk: [ValidationRules.Required()],
})

const emailSubmit = emailForm.useSubmit(async (data) => {
    const isFraud = await emailModel.actions.checkIfFraud({ value: [data.email]})

    if (isFraud.length) {
        await $confirm(`This email has been marked as fraudulent. Do you want to continue?`, {
            key: 'confirm-fraud-email-modal',
        })
    }

    if (data.clientEmailDataPk) {
        await clientEmailDataModel.actions.update({
            pk: data.clientEmailDataPk,
            email: data.email,
        })

        toastSuccess('Email updated')
    } else {
        await clientEmailDataModel.actions.create({
            email: data.email,
            client_pk: props.clientPk,
        })

        toastSuccess('Email created')
    }

    resetForms()
})

const remarkSubmit = remarkForm.useSubmit(async (data) => {
    await clientEmailDataModel.actions.saveRemark({
        remark: data.remark,
        pk: data.clientEmailDataPk,
    })

    toastSuccess('Remark updated')
    resetForms()
})

const resetForms = () => {
    actionMode.value = undefined

    actionMode.value = undefined

    emailForm.updateInitialData({
        email: '',
        clientEmailDataPk: undefined as PrimaryKey,
    })
    emailForm.reset()

    remarkForm.updateInitialData({
        remark: '',
        clientEmailDataPk: undefined as PrimaryKey,
    })
    remarkForm.reset()
}

const contextMenu: MaybeElementRef = () => {
    return document.querySelector('.app-modal--context-menu')
}

const openMenu = (event, clientEmailData: ModelRef<'ClientEmailData', 'email' | 'email.fraudInfo'>, closeDropdown) => {
    const options = [
        {
            enabled: true,
            text: 'Copy',
            icon: CopyIcon,
            onClick: () => {
                copyToClipboard(clientEmailData.email.value)
                closeDropdown()
            },
        },
        {
            enabled: clientEmailDataList.records.length > 1,
            text: 'Copy all',
            icon: LayersIcon,
            onClick: () => {
                copyToClipboard(clientEmailDataList.records.map(clientEmailData => clientEmailData.email.value).join('\n'))
                closeDropdown()
            },
        },
        {
            enabled: props.withRemark,
            text: clientEmailData.remark ? 'Edit Remark' : 'Add Remark',
            icon: NotificationTextIcon,
            onClick: () => {
                resetForms()
                remarkForm.updateInitialData({
                    remark: clientEmailData.remark || '',
                    clientEmailDataPk: usePk(clientEmailData),
                })

                actionMode.value = 'remark'
            },
        },
        {
            enabled: props.withEdit && props.defaultEmail !== clientEmailData.email.value && canEdit.value,
            text: 'Edit',
            icon: Edit2Icon,
            onClick: () => {
                resetForms()
                emailForm.updateInitialData({
                    clientEmailDataPk: usePk(clientEmailData),
                    email: clientEmailData.email.value,
                })
                actionMode.value = 'email'
            },
        },
        {
            enabled: Boolean(props.setDefaultEmail),
            text: 'Set default',
            icon: DiscIcon,
            class: 'text-primary',
            onClick: async () => {
                await props.setDefaultEmail(clientEmailData.email_pk)
                toastSuccess(`Email was set as default`)
                closeDropdown()
            },
        },
        {
            enabled: props.withEdit && props.defaultEmail !== clientEmailData.email.value && canEdit.value,
            text: 'Delete',
            icon: TrashIcon,
            class: 'text-danger',
            onClick: async () => {
                resetForms()
                await $confirm({ text: 'Do you want to delete this record?' })
                await clientEmailDataModel.actions.remove({
                    pk: usePk(clientEmailData),
                })

                toastSuccess('Email removed')
            },
        },
        {
            enabled: canMarkAsFraud.value,
            text: clientEmailData.email.fraudInfo.is_fraud ? 'Unmark as Fraud' : 'Mark as Fraud',
            icon: RatIcon,
            class: 'text-danger',
            onClick: async () => {
                const remark = clientEmailData.email.fraudInfo.is_fraud ? null : await useModal(PromptModal).open({
                    title: 'Please set reason',
                })

                await emailModel.actions.setFraud({
                    pk: clientEmailData.email_pk,
                    value: !clientEmailData.email.fraudInfo.is_fraud,
                    remark: remark,
                })

                toastSuccess(`Email ${clientEmailData.email.fraudInfo.is_fraud ? 'unmarked as Fraud' : 'marked as Fraud'}`)
            },
        },
    ].filter((option) => option.enabled)

    openContextMenu(event, options)
}

watch(() => dropdownRef.value?.isActive, (newValue) => {
    if (!newValue) {
        resetForms()
    }
})

//

function handleClick(email: ModelRef<'ClientEmailData', 'email'>) {
    if (props.onSelect) {
        return props.onSelect(email)
    }

    trackActivity('copy')

    copyToClipboard(email.email.value)
}

function handleClickHidden() {
    trackActivity('show')
}

function trackActivity(action: 'copy' | 'show') {
    if (action === 'copy') {
        emit('copy')
    } else {
        emit('show')
    }
}

const getEmailFraudData = (email: string): ModelRef<'FraudInfo'> | undefined => {
    return clientEmailDataList.records.find(clientEmailData => clientEmailData.email.value === email)?.email.fraudInfo
}

const getFraudTooltip = (fraudInfo: ModelRef<'FraudInfo'> | undefined, email?: string): string | undefined => {
    if (!fraudInfo || !fraudInfo.is_fraud) {
        return undefined
    }

    let tooltip = 'Fraud email'

    if (email) {
        tooltip += `: ${email}`
    } else if (fraudInfo.remark) {
        tooltip += `: ${fraudInfo.remark}`
    }

    return tooltip
}
</script>
