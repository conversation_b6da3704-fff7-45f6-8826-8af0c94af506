<template>
    <div>
        <div class="flex justify-between items-center">
            <div class="font-semibold">
                Emails
            </div>
            <AppButton
                v-if="withEdit"
                class="--only --outline --xs dark:bg-dark-3 dark:text-secondary-50 dark:border-secondary"
                @click="editModeActive = true"
            >
                <PlusIcon />
            </AppButton>
        </div>
        <ul class="mt-1">
            <li
                v-for="(clientEmailData) in clientEmailDataList"
                :key="clientEmailData.pk"
                class="dropdown-menu__item p-0 m-0"
            >
                <input
                    :disabled="!withEdit"
                    :checked="clientEmailData.email_pk === client.client_email_pk"
                    autocomplete="Off"
                    class="flex-none form-check-input form-check-input-sm form-check-input-darken mr-2 ml-3"
                    name="emails"
                    type="radio"
                    @click="setDefaultEmail(clientEmailData.email_pk)"
                >
                <HiddenDataComponent
                    :hidden-value="clientEmailData.email.value"
                    class="w-full !py-1.5"
                    :class="{ '!text-danger': clientEmailData.email.fraudInfo?.is_fraud }"
                    @click-visible="openMenu($event, clientEmailData, ()=>{})"
                    @click-visible-right="openMenu($event, clientEmailData, ()=>{})"
                >
                    <template #default>
                        <div
                            @click.prevent="openMenu($event, clientEmailData, ()=>{})"
                        >
                            <div
                                v-tooltip="{ content: getFraudTooltip(clientEmailData.email.fraudInfo)}"
                                class="truncate"
                            >
                                {{ clientEmailData.email.value }}
                            </div>
                        </div>
                    </template>
                    <template #hidden="{ formatted }">
                        <div>
                            {{ formatted }}
                        </div>
                    </template>
                </HiddenDataComponent>
            </li>
            <li v-if="editModeActive">
                <form class="relative" @submit.prevent="emailSubmit">
                    <FormField
                        :form="emailForm"
                        field="email"
                        class="relative"
                    >
                        <div class="absolute right-0 top-0 z-1 m-0.5">
                            <AppButton class="--success --only --ghost --small dark:text-success dark:hover:text-success">
                                <CheckIcon />
                            </AppButton>
                            <AppButton
                                type="button"
                                class="--danger --only --ghost --small dark:text-danger dark:hover:text-danger"
                                @click="resetForms"
                            >
                                <XIcon />
                            </AppButton>
                        </div>

                        <InputText
                            v-model="emailForm.data.email"
                            :input-attrs="{
                                class: 'pr-14'
                            }"
                        />
                    </FormField>
                </form>
            </li>
        </ul>
    </div>
</template>

<script setup lang="ts">
import { copyToClipboard } from '@/lib/core/helper/ClipboardHelper'
import HiddenDataComponent from '~/sections/Lead/List/components/HiddenDataComponent.vue'
import type { ModelRef } from '~types/lib/Model'
import { toastError, toastSuccess } from '@/lib/core/helper/ToastHelper'
import { openContextMenu } from '@/plugins/ContextMenuPlugin'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import FormField from '~/components/Form/FormField.vue'
import { $confirm } from '@/plugins/ConfirmPlugin'
import { DepartmentName } from '~/api/models/Department/Department'
import { PositionName } from '~/api/models/Position/Position'
import type { Point } from '@/types'
import PromptModal from '~/modals/PromptModal.vue'
import RatIcon from '~assets/icons/RatIcon.svg?component'

const props = defineProps<{
    clientPk: PrimaryKey
    withEdit?: boolean
    selectedLeadPk?: PrimaryKey
}>()

const { useModel, useCurrentUser, hasPermission } = useContext()

const clientEmailDataModel = useModel('ClientEmailData')
const leadModel = useModel('Lead')
const clientModel = useModel('Client')
const emailModel = useModel('Email')

const client = await clientModel.useRecord().fetch(props.clientPk)
const clientEmailDataList = await clientEmailDataModel.useResourceList({
    name: 'ClientClientEmailDataList',
    pk: props.clientPk,
}, {
    with: ['email', 'email.fraudInfo'],
}).fetch()

const editModeActive = ref(false)

const setDefaultEmail = async (email_pk) => {
    await clientModel.actions.setDefaultEmail({ client_pk: props.clientPk, email_pk: email_pk })
    toastSuccess('Email was set as default')
}

const emailForm = useForm<{email: string, clientEmailDataPk: PrimaryKey}>({
    email: '',
    clientEmailDataPk: undefined as PrimaryKey,
}, {
    email: [ValidationRules.Required(), ValidationRules.Email()],
    clientEmailDataPk: [ValidationRules.RequiredWhen(() => !emailForm.data.email)],
})

const emailSubmit = emailForm.useSubmit(async (data) => {
    const isFraud = await emailModel.actions.checkIfFraud({ value: [data.email]})

    if (isFraud.length) {
        await $confirm(`This email has been marked as fraudulent. Do you want to continue?`)
    }

    if (data.clientEmailDataPk) {
        await clientEmailDataModel.actions.update({
            pk: data.clientEmailDataPk,
            email: data.email,
        })

        toastSuccess('Email updated')
    } else {
        await clientEmailDataModel.actions.create({
            email: data.email,
            client_pk: props.clientPk,
        })

        toastSuccess('Email created')
    }

    resetForms()
})

const resetForms = () => {
    editModeActive.value = false

    emailForm.updateInitialData({
        email: '',
        clientEmailDataPk: undefined as PrimaryKey,
    })
    emailForm.reset()
}

const currentUser = useCurrentUser()

const canEdit = computed(() => {
    // Exactly how it is on backend
    return !(isUserInDepartment(currentUser, DepartmentName.Sales) && (isUserHasPosition(currentUser, PositionName.Agent) || isUserHasPosition(currentUser, PositionName.Supervisor)))
})

const canMarkAsFraud = computed(() => {
    return hasPermission('markAsFraud', 'all')
})

const openMenu = (event, clientEmailData: ModelRef<'ClientEmailData', 'email' | 'email.fraudInfo'>, closeDropdown) => {
    const options = [
        {
            enabled: true,
            text: 'Copy',
            icon: CopyIcon,
            onClick: () => {
                copyToClipboard(clientEmailData.email.value)
                closeDropdown()
            },
        },
        {
            enabled: clientEmailDataList.length > 1,
            text: 'Copy all',
            icon: LayersIcon,
            onClick: () => {
                copyToClipboard(clientEmailDataList.map(clientEmailData => clientEmailData.email.value).join('\n'))
                closeDropdown()
            },
        },
        {
            enabled: props.withEdit && canEdit.value,
            text: 'Edit',
            icon: Edit2Icon,
            onClick: () => {
                resetForms()
                emailForm.updateInitialData({
                    clientEmailDataPk: usePk(clientEmailData),
                    email: clientEmailData.email.value,
                })
                editModeActive.value = true
            },
        },
        {
            enabled: props.selectedLeadPk,
            text: `Set #${props.selectedLeadPk} email`,
            icon: Edit3Icon,
            onClick: async () => {
                await leadModel.actions.setDefaultEmail({
                    lead_pk: props.selectedLeadPk,
                    email_pk: clientEmailData.email_pk,
                })

                toastSuccess('Default email for lead updated')
            },
        },
        {
            enabled: props.withEdit && canEdit.value,
            text: 'Delete',
            icon: TrashIcon,
            class: 'text-danger',
            onClick: async () => {
                resetForms()

                if (clientEmailData.email_pk === client.value.client_email_pk) {
                    toastError('You\'re unable to delete the default email')

                    return
                }
                await $confirm({ text: 'Do you want to delete this record?' })
                await clientEmailDataModel.actions.remove({
                    pk: usePk(clientEmailData),
                })

                toastSuccess('Email removed')
            },
        },
        {
            enabled: canMarkAsFraud.value,
            text: clientEmailData.email.fraudInfo.is_fraud ? 'Unmark as Fraud' : 'Mark as Fraud',
            icon: RatIcon,
            class: 'text-danger',
            onClick: async () => {
                const remark = clientEmailData.email.fraudInfo.is_fraud ? null : await useModal(PromptModal).open({
                    title: 'Please set reason',
                })

                await emailModel.actions.setFraud({
                    pk: clientEmailData.email_pk,
                    value: !clientEmailData.email.fraudInfo.is_fraud,
                    remark: remark,
                })

                toastSuccess(`Email ${clientEmailData.email.fraudInfo.is_fraud ? 'unmarked as Fraud' :
                    'mark as Fraud'}`)
            },
        },
    ].filter((option) => option.enabled)

    openContextMenu(getElementPosition(event.target), options, {
        disableNextTick: true,
    })
}

const getElementPosition = (el: HTMLElement): Point => {
    const { height, top, left } = el.getBoundingClientRect()

    return {
        x: left,
        y: top + height,
    }
}

const getFraudTooltip = (fraudInfo: ModelRef<'FraudInfo'> | undefined): string | undefined => {
    if (!fraudInfo || !fraudInfo.is_fraud) {
        return undefined
    }

    let tooltip = 'Fraud email'

    if (fraudInfo.remark) {
        tooltip += `: ${fraudInfo.remark}`
    }

    return tooltip
}
</script>
