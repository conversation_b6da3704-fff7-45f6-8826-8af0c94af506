<template>
    <div class="btn-group text-xs relative">
        <a
            v-if="props.defaultPhone"
            :href="isCopyAllowed ? `tel:${props?.defaultPhone}` : undefined"
            class="flex btn btn-outline-secondary text-primary-600 dark:text-primary-600 gap-1 items-center"
            v-on="isCopyAllowed ? {click : ()=> {
                copyToClipboard(props.defaultPhone)
                trackActivity('copy')
            }} : {}"
        >
            <PhoneIcon v-tooltip="withTitle ? undefined : (isCopyAllowed ? 'Copy phone' : undefined)" />
            <PhoneCountryFlag v-if="withTitle" :phone-number="props.defaultPhone" />
            <HiddenDataComponent
                v-if="withTitle"
                :hidden-value="props.defaultPhone"
                class="cursor-pointer ml-2 max-w-[230px] truncate"
                :class="{ '!text-danger': getPhoneFraudData(defaultPhone)?.is_fraud }"
                @show="show"
                @hide="hide"
                @click-hidden="handleClickHidden"
            >
                <template #default>
                    <div
                        v-tooltip="{ content: `${ getFraudTooltip(getPhoneFraudData(defaultPhone), defaultPhone) ?? defaultPhone}` }"
                        @click="() => {
                            copyToClipboard(props.defaultPhone)
                            trackActivity('copy')
                        }"
                    >
                        {{ String.formatPhone(props.defaultPhone) }}
                    </div>
                </template>
                <template #hidden="{ formatted }">
                    {{ formatted }}
                </template>
            </HiddenDataComponent>
            <AlertCircleIcon v-if="!defaultPhoneIsValid" class="text-warning w-[14px] h-[14px]" />
        </a>
        <span v-else class="btn btn-outline-secondary">
            <PhoneIcon />
            <span v-if="withTitle" class="ml-2 text-danger">Default phone not set</span>
        </span>

        <Dropdown
            ref="dropdownRef"
            teleport
            placement="bottom-end"
            :ignore-click-on-elements="[contextMenu, confirmModal]"
        >
            <template #toggle="{toggle}">
                <div class="btn btn-outline-secondary box border-l-0 !rounded-l-none p-2 select-none" @click="toggle">
                    <span v-if="clientPhoneDataList.records.length" class="absolute text-3xs top-1">{{ clientPhoneDataList.records.length }}</span>
                    <div
                        class="top-1 relative text-theme-38"
                        :class="{
                            'top-auto': !clientPhoneDataList.records.length
                        }"
                    >
                        <ChevronDownIcon />
                    </div>
                </div>
            </template>
            <template #content="{ close }">
                <div class="dropdown-menu__content mt-0 w-60">
                    <div class="max-h-[500px] overflow-y-auto fancy-scroll">
                        <template v-if="clientPhoneDataList.records.length">
                            <HiddenDataComponent
                                v-for="(clientPhone, index) in clientPhoneDataList.records"
                                :key="index"
                                ref="child"
                                class="dropdown-menu__item flex justify-between"
                                :hidden-value="clientPhone.phone.value"
                                :class="{
                                    'text-primary-600 font-bold': props.defaultPhone && clientPhone.phone.value === props.defaultPhone,
                                    '!text-danger': clientPhone.phone.fraudInfo.is_fraud,
                                }"
                                @click-hidden="handleClickHidden"
                                @click-visible="() => {
                                    copyToClipboard(clientPhone.phone.value)
                                    trackActivity('copy')
                                }"
                                @click-visible-right="openMenu($event, clientPhone, close)"
                            >
                                <template #default>
                                    <div
                                        v-tooltip="{ content: getFraudTooltip(clientPhone.phone.fraudInfo) }"
                                        class="flex items-center gap-2"
                                    >
                                        <PhoneCountryFlag :phone-number="clientPhone.phone.value" />
                                        <div>
                                            <div class="truncate">
                                                {{ String.formatPhone(clientPhone.phone.value) }}
                                            </div>
                                            <div v-if="clientPhone.remark && withRemark" class="text-secondary-400 text-2xs">
                                                {{ clientPhone.remark }}
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <template #hidden="{ formatted }">
                                    <div>
                                        {{ formatted }}
                                    </div>
                                    <div v-if="clientPhone.remark && withRemark" class="text-secondary-400 text-2xs">
                                        {{ clientPhone.remark }}
                                    </div>
                                </template>
                                <template #common-after>
                                    <AlertCircleIcon v-if="!clientPhone.phone.is_valid" class="text-warning block" />
                                </template>
                            </HiddenDataComponent>
                        </template>
                        <template v-else>
                            <div class="text-sm text-secondary mt-1">
                                No data available
                            </div>
                        </template>
                    </div>

                    <template v-if="withEdit">
                        <hr class="my-3">

                        <div v-if="actionMode === 'phone'">
                            <form class="relative" @submit.prevent="phoneSubmit">
                                <FormField
                                    :form="phoneForm"
                                    field="phone"
                                    class="relative"
                                >
                                    <div class="absolute right-0 top-0 z-1 m-0.5">
                                        <AppButton class="--success --only --ghost --small dark:text-success dark:hover:text-success">
                                            <CheckIcon />
                                        </AppButton>
                                        <AppButton
                                            type="button"
                                            class="--danger --only --ghost --small dark:text-danger dark:hover:text-danger"
                                            @click="resetForms"
                                        >
                                            <XIcon />
                                        </AppButton>
                                    </div>

                                    <InputPhone
                                        ref="inputRef"
                                        v-model="phoneForm.data.phone"
                                        :input-attrs="{
                                            class: 'pr-14'
                                        }"
                                    />
                                </FormField>
                            </form>
                        </div>

                        <div v-else-if="actionMode === 'remark'">
                            <form class="relative" @submit.prevent="remarkSubmit">
                                <FormField
                                    :form="remarkForm"
                                    field="remark"
                                    class="relative"
                                >
                                    <div class="absolute right-0 top-0 z-1 m-0.5">
                                        <AppButton class="--success --only --ghost --small dark:text-success dark:hover:text-success">
                                            <CheckIcon />
                                        </AppButton>
                                        <AppButton
                                            type="button"
                                            class="--danger --only --ghost --small dark:text-danger dark:hover:text-danger"
                                            @click="resetForms"
                                        >
                                            <XIcon />
                                        </AppButton>
                                    </div>
                                    <InputText
                                        ref="inputRef"
                                        v-model="remarkForm.data.remark"
                                        placeholder="Remark here"
                                        class="!text-xs"
                                        :maxlength="11"
                                    />
                                </FormField>
                            </form>
                        </div>

                        <div
                            v-else
                            class="dropdown-menu__item font-semibold text-primary"
                            @click="actionMode = 'phone'"
                        >
                            <PlusIcon class="mr-1.5" />
                            Add phone
                        </div>
                    </template>
                </div>
            </template>
        </Dropdown>
    </div>
</template>

<script setup lang="ts">
import HiddenDataComponent from '~/sections/Lead/List/components/HiddenDataComponent.vue'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import NotificationTextIcon from '~assets/icons/NotificationTextIcon.svg?component'
import FormField from '~/components/Form/FormField.vue'
import { openContextMenu } from '@/plugins/ContextMenuPlugin'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import type { ModelRef } from '~types/lib/Model'
import { copyToClipboard } from '@/lib/core/helper/ClipboardHelper'
import type { MaybeElementRef } from '@vueuse/core'
import { $confirm } from '@/plugins/ConfirmPlugin'
import { DepartmentName } from '~/api/models/Department/Department'
import { PositionName } from '~/api/models/Position/Position'
import PhoneCountryFlag from '~/components/PhoneCountryFlag.vue'
import RatIcon from '~assets/icons/RatIcon.svg?component'
import PromptModal from '~/modals/PromptModal.vue'

const props = defineProps<{
    clientPk: PrimaryKey
    withEdit?: boolean
    withRemark?: boolean
    withTitle?: boolean
    defaultPhone?: string
    // eslint-disable-next-line func-call-spacing
    setDefaultPhone: (phone_pk) => Promise<void>
}>()

const emit = defineEmits<{
    copy: [],
    show: [],
}>()

const { useModel, useCurrentUser, hasPermission } = useContext()

const actionMode = ref<'remark' | 'phone'>()

watch(actionMode, () => {
    nextTick(() => {
        focusInput()
    })
})

const confirmModal: MaybeElementRef = () => {
    return document.querySelector('#app-modal--confirm-fraud-phone-modal')
}

const inputRef = ref<HTMLInputElement>()

function focusInput() {
    inputRef.value?.focus()
}

const isHidden = ref(true)

const isCopyAllowed = computed(() => {
    return hasPermission('manage', 'all') || !isHidden.value
})

function hide() {
    isHidden.value = true
}

function show() {
    isHidden.value = false
}

const clientPhoneDataModel = useModel('ClientPhoneData')
const phoneModel = useModel('Phone')

const clientPhoneDataList = clientPhoneDataModel.useResourceList({
    name: 'ClientClientPhoneDataList',
    pk: props.clientPk,
}, {
    with: ['phone', 'phone.fraudInfo'],
})

await clientPhoneDataList.fetch()

const currentUser = useCurrentUser()

const canMarkAsFraud = computed(() => {
    return hasPermission('markAsFraud', 'all')
})

const canEdit = computed(() => {
    // Exactly how it is on backend
    return !(isUserInDepartment(currentUser, DepartmentName.Sales) && (isUserHasPosition(currentUser, PositionName.Agent) || isUserHasPosition(currentUser, PositionName.Supervisor)))
})

const phoneForm = useForm<{ phone: string, clientPhoneDataPk: PrimaryKey }>({
    phone: '',
    clientPhoneDataPk: undefined as PrimaryKey,
}, {
    phone: [ValidationRules.Required(), ValidationRules.Phone()],
    clientPhoneDataPk: [ValidationRules.RequiredWhen(() => !phoneForm.data.phone)],
})

const remarkForm = useForm<{ remark: string, clientPhoneDataPk: PrimaryKey }>({
    remark: '',
    clientPhoneDataPk: undefined as PrimaryKey,
}, {
    remark: [ValidationRules.Regex(/^[a-zA-Z\s]*$/)],
    clientPhoneDataPk: [ValidationRules.Required()],
})

const phoneSubmit = phoneForm.useSubmit(async (data) => {
    const isFraud = await phoneModel.actions.checkIfFraud({ value: [data.phone]})

    if (isFraud.length) {
        await $confirm(`This phone has been marked as fraudulent. Do you want to continue?`, {
            key: 'confirm-fraud-phone-modal',
        })
    }

    if (data.clientPhoneDataPk) {
        await clientPhoneDataModel.actions.update({
            pk: data.clientPhoneDataPk,
            phone: data.phone,
        })

        toastSuccess('Phone updated')
    } else {
        await clientPhoneDataModel.actions.create({
            phone: data.phone,
            client_pk: props.clientPk,
        })

        toastSuccess('Phone created')
    }

    resetForms()
})

const remarkSubmit = remarkForm.useSubmit(async (data) => {
    await clientPhoneDataModel.actions.saveRemark({
        remark: data.remark,
        pk: data.clientPhoneDataPk,
    })

    toastSuccess('Remark updated')
    resetForms()
})

const resetForms = () => {
    actionMode.value = undefined

    phoneForm.updateInitialData({
        phone: '',
        clientPhoneDataPk: undefined as PrimaryKey,
    })
    phoneForm.reset()

    remarkForm.updateInitialData({
        remark: '',
        clientPhoneDataPk: undefined as PrimaryKey,
    })
    remarkForm.reset()
}

const contextMenu: MaybeElementRef = () => {
    return document.querySelector('.app-modal--context-menu') as HTMLElement
}
const openMenu = (event, clientPhoneData: ModelRef<'ClientPhoneData', 'phone' | 'phone.fraudInfo'>, closeDropdown) => {
    const options = [
        {
            enabled: true,
            text: 'Copy',
            icon: CopyIcon,
            onClick: () => {
                copyToClipboard(clientPhoneData.phone.value)
                closeDropdown()
            },
        },
        {
            enabled: true,
            text: 'Call',
            icon: PhoneIcon,
            onClick: () => {
                window.open(`tel:${clientPhoneData.phone.value}`, '_self')
            },
        },
        {
            enabled: props.withRemark,
            text: clientPhoneData.remark ? 'Edit Remark' : 'Add Remark',
            icon: NotificationTextIcon,
            onClick: () => {
                resetForms()
                remarkForm.updateInitialData({
                    remark: clientPhoneData.remark || '',
                    clientPhoneDataPk: usePk(clientPhoneData),
                })

                actionMode.value = 'remark'
            },
        },
        {
            enabled: props.withEdit && props.defaultPhone !== clientPhoneData.phone.value && canEdit.value,
            text: 'Edit',
            icon: Edit2Icon,
            onClick: () => {
                resetForms()
                phoneForm.updateInitialData({
                    clientPhoneDataPk: usePk(clientPhoneData),
                    phone: clientPhoneData.phone.value,
                })
                actionMode.value = 'phone'
            },
        },
        {
            enabled: true,
            text: 'Set default',
            icon: DiscIcon,
            class: 'text-primary',
            onClick: async () => {
                await props.setDefaultPhone(clientPhoneData.phone_pk)

                toastSuccess(`Phone was set as default`)
                closeDropdown()
            },
        },
        {
            enabled: props.withEdit && props.defaultPhone !== clientPhoneData.phone.value && canEdit.value,
            text: 'Delete',
            icon: TrashIcon,
            class: 'text-danger',
            onClick: async () => {
                resetForms()
                await $confirm({ text: 'Do you want to delete this record?' })
                await clientPhoneDataModel.actions.remove({
                    pk: usePk(clientPhoneData),
                })

                toastSuccess('Phone removed')
            },
        },
        {
            enabled: canMarkAsFraud.value,
            text: clientPhoneData.phone.fraudInfo.is_fraud ? 'Unmark as Fraud' : 'Mark as Fraud',
            icon: RatIcon,
            class: 'text-danger',
            onClick: async () => {
                const remark = clientPhoneData.phone.fraudInfo.is_fraud ? null : await useModal(PromptModal).open({
                    title: 'Please set reason',
                })

                await phoneModel.actions.setFraud({
                    pk: clientPhoneData.phone_pk,
                    value: !clientPhoneData.phone.fraudInfo.is_fraud,
                    remark,
                })

                toastSuccess(`Phone ${clientPhoneData.phone.fraudInfo.is_fraud ? 'unmarked as Fraud' : 'marked as Fraud'}`)
            },
        },
    ].filter((option) => option.enabled)

    openContextMenu(event, options)
}

const dropdownRef = ref<{
    isActive: boolean
    open(): void
    close(): void
    toggle(): void
}>()

// default phone is valid

const defaultPhoneIsValid = computed(() => {
    if (!clientPhoneDataList.records) {
        return true
    }

    const defaultPhoneData = clientPhoneDataList.records.find(clientPhoneData => clientPhoneData.phone.value === props.defaultPhone)

    if (!defaultPhoneData) {
        return true
    }

    return defaultPhoneData.phone.is_valid
})

watch(() => dropdownRef.value?.isActive, (newValue) => {
    if (!newValue) {
        resetForms()
    }
})

//

function handleClickHidden() {
    trackActivity('show')
}

function trackActivity(action: 'copy' | 'show') {
    if (action === 'copy') {
        emit('copy')
    } else {
        emit('show')
    }
}

const getPhoneFraudData = (phone: string): ModelRef<'FraudInfo'> | undefined => {
    return clientPhoneDataList.records.find(clientPhoneData => clientPhoneData.phone.value === phone)?.phone.fraudInfo
}

const getFraudTooltip = (fraudInfo: ModelRef<'FraudInfo'> | undefined, phone?: string): string | undefined => {
    if (!fraudInfo || !fraudInfo.is_fraud) {
        return undefined
    }

    let tooltip = 'Fraud phone'

    if (phone) {
        tooltip += `: ${phone}`
    } else if (fraudInfo.remark) {
        tooltip += `: ${fraudInfo.remark}`
    }

    return tooltip
}
</script>
