<template>
    <div>
        <div class="flex justify-between items-center">
            <div class="font-semibold">
                Phones
            </div>
            <AppButton
                v-if="withEdit"
                class="--only --outline --xs dark:bg-dark-3 dark:text-secondary-50 dark:border-secondary"
                @click="editModeActive = true"
            >
                <PlusIcon />
            </AppButton>
        </div>
        <ul class="mt-1">
            <li
                v-for="(clientPhoneData) in clientPhoneDataList.records"
                :key="clientPhoneData.pk"
                class="dropdown-menu__item p-0 m-0"
            >
                <input
                    :disabled="!withEdit"
                    :checked="clientPhoneData.phone_pk === client.client_phone_pk"
                    autocomplete="Off"
                    class="flex-none form-check-input form-check-input-sm form-check-input-darken ml-3"
                    name="phones"
                    type="radio"
                    @click="setDefaultPhone(clientPhoneData.phone_pk)"
                >
                <HiddenDataComponent
                    :hidden-value="clientPhoneData.phone.value"
                    class="w-full py-1.5 px-2 justify-between flex"
                    :class="{
                        '!text-danger': clientPhoneData.phone.fraudInfo.is_fraud,
                    }"
                    @click-visible="openMenu($event, clientPhoneData, ()=>{})"
                    @click-visible-right="openMenu($event, clientPhoneData, ()=>{})"
                >
                    <template #default>
                        <div class="flex items-center gap-1">
                            <PhoneCountryFlag :phone-number="clientPhoneData.phone.value" />
                            <div>
                                <div
                                    v-tooltip="{content: getFraudTooltip(clientPhoneData.phone.fraudInfo)}"
                                    class="truncate"
                                >
                                    {{ String.formatPhone(clientPhoneData.phone.value) }}
                                </div>
                            </div>
                        </div>
                    </template>
                    <template #hidden="{ formatted }">
                        <div>
                            {{ formatted }}
                        </div>
                    </template>
                    <template #common-after>
                        <AlertCircleIcon
                            v-if="!clientPhoneData.phone.is_valid"
                            class="text-warning"
                            @click.stop
                        />
                    </template>
                </HiddenDataComponent>
            </li>
            <li v-if="editModeActive">
                <form class="relative" @submit.prevent="phoneSubmit">
                    <FormField
                        :form="phoneForm"
                        field="phone"
                        class="relative"
                    >
                        <div class="absolute right-0 top-0 z-1 m-0.5">
                            <AppButton class="--success --only --ghost --small dark:text-success dark:hover:text-success">
                                <CheckIcon />
                            </AppButton>
                            <AppButton
                                type="button"
                                class="--danger --only --ghost --small dark:text-danger dark:hover:text-danger"
                                @click="resetForms"
                            >
                                <XIcon />
                            </AppButton>
                        </div>

                        <InputPhone
                            v-model="phoneForm.data.phone"
                            :input-attrs="{
                                class: 'pr-14'
                            }"
                        />
                    </FormField>
                </form>
            </li>
        </ul>
    </div>
</template>

<script setup lang="ts">
import { copyToClipboard } from '@/lib/core/helper/ClipboardHelper'
import HiddenDataComponent from '~/sections/Lead/List/components/HiddenDataComponent.vue'
import type { ModelRef } from '~types/lib/Model'
import { toastError, toastSuccess } from '@/lib/core/helper/ToastHelper'
import { openContextMenu } from '@/plugins/ContextMenuPlugin'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import FormField from '~/components/Form/FormField.vue'
import { $confirm } from '@/plugins/ConfirmPlugin'
import { DepartmentName } from '~/api/models/Department/Department'
import { PositionName } from '~/api/models/Position/Position'
import PhoneCountryFlag from '~/components/PhoneCountryFlag.vue'
import type { Point } from '@/types'
import PromptModal from '~/modals/PromptModal.vue'
import RatIcon from '~assets/icons/RatIcon.svg?component'

const props = defineProps<{
    clientPk: PrimaryKey
    withEdit?: boolean
}>()

const { useModel, useCurrentUser, hasPermission } = useContext()

const clientPhoneDataModel = useModel('ClientPhoneData')
const clientModel = useModel('Client')
const phoneModel = useModel('Phone')

const client = await clientModel.useRecord().fetch(props.clientPk)
const clientPhoneDataList = clientPhoneDataModel.useResourceList({
    name: 'ClientClientPhoneDataList',
    pk: props.clientPk,
}, {
    with: ['phone', 'phone.fraudInfo'],
})

await clientPhoneDataList.fetch()

const currentUser = useCurrentUser()

const canEdit = computed(() => {
    // Exactly how it is on backend
    return !(isUserInDepartment(currentUser, DepartmentName.Sales) && (isUserHasPosition(currentUser, PositionName.Agent) || isUserHasPosition(currentUser, PositionName.Supervisor)))
})

const canMarkAsFraud = computed(() => {
    return hasPermission('markAsFraud', 'all')
})

const editModeActive = ref(false)

const setDefaultPhone = async (phone_pk) => {
    await clientModel.actions.setDefaultPhone({ client_pk: props.clientPk, phone_pk: phone_pk })
    toastSuccess(`Phone was set as default`)
}

const phoneForm = useForm<{phone: string, clientPhoneDataPk: PrimaryKey}>({
    phone: '',
    clientPhoneDataPk: undefined as PrimaryKey,
}, {
    phone: [ValidationRules.Required(), ValidationRules.Phone()],
    clientPhoneDataPk: [ValidationRules.RequiredWhen(() => !phoneForm.data.phone)],
})

const phoneSubmit = phoneForm.useSubmit(async (data) => {
    const isFraud = await phoneModel.actions.checkIfFraud({ value: [data.phone]})

    if (isFraud.length) {
        await $confirm(`This phone has been marked as fraudulent. Do you want to continue?`)
    }

    if (data.clientPhoneDataPk) {
        await clientPhoneDataModel.actions.update({
            pk: data.clientPhoneDataPk,
            phone: data.phone,
        })

        toastSuccess('Phone updated')
    } else {
        await clientPhoneDataModel.actions.create({
            phone: data.phone,
            client_pk: props.clientPk,
        })

        toastSuccess('Phone created')
    }

    resetForms()
})

const resetForms = () => {
    editModeActive.value = false

    phoneForm.updateInitialData({
        phone: '',
        clientPhoneDataPk: undefined as PrimaryKey,
    })
    phoneForm.reset()
}

const openMenu = (event, clientPhoneData: ModelRef<'ClientPhoneData', 'phone' | 'phone.fraudInfo'>, closeDropdown) => {
    const options = [
        {
            enabled: true,
            text: 'Call',
            icon: PhoneIcon,
            onClick: () => {
                window.open(`tel:${clientPhoneData.phone.value}`, '_self')
            },
        },
        {
            enabled: props.withEdit && canEdit.value,
            text: 'Edit',
            icon: Edit2Icon,
            onClick: () => {
                resetForms()
                phoneForm.updateInitialData({
                    clientPhoneDataPk: usePk(clientPhoneData),
                    phone: clientPhoneData.phone.value,
                })
                editModeActive.value = true
            },
        },
        {
            enabled: true,
            text: 'Copy',
            icon: CopyIcon,
            onClick: () => {
                copyToClipboard(clientPhoneData.phone.value)
                closeDropdown()
            },
        },
        {
            enabled: props.withEdit && canEdit.value,
            text: 'Delete',
            icon: TrashIcon,
            class: 'text-danger',
            onClick: async () => {
                resetForms()

                if (clientPhoneData.phone_pk === client.value.client_phone_pk) {
                    toastError('You\'re unable to delete the default phone number')

                    return
                }
                await $confirm({ text: 'Do you want to delete this record?' })
                await clientPhoneDataModel.actions.remove({
                    pk: usePk(clientPhoneData),
                })

                toastSuccess('Phone removed')
            },
        },
        {
            enabled: canMarkAsFraud.value,
            text: clientPhoneData.phone.fraudInfo.is_fraud ? 'Unmark as Fraud' : 'Mark as Fraud',
            icon: RatIcon,
            class: 'text-danger',
            onClick: async () => {
                const remark = clientPhoneData.phone.fraudInfo.is_fraud ? null : await useModal(PromptModal).open({
                    title: 'Please set reason',
                })

                await phoneModel.actions.setFraud({
                    pk: clientPhoneData.phone_pk,
                    value: !clientPhoneData.phone.fraudInfo.is_fraud,
                    remark,
                })

                toastSuccess(`Phone ${clientPhoneData.phone.fraudInfo.is_fraud ? 'unmarked as Fraud' :
                    'marked as Fraud'}`)
            },
        },
    ].filter((option) => option.enabled)

    openContextMenu(getElementPosition(event.target), options, {
        disableNextTick: true,
    })
}

const getElementPosition = (el: HTMLElement): Point => {
    const { height, top, left } = el.getBoundingClientRect()

    return {
        x: left,
        y: top + height,
    }
}

const getFraudTooltip = (fraudInfo: ModelRef<'FraudInfo'> | undefined): string | undefined => {
    if (!fraudInfo || !fraudInfo.is_fraud) {
        return undefined
    }

    let tooltip = 'Fraud phone'

    if (fraudInfo.remark) {
        tooltip += `: ${fraudInfo.remark}`
    }

    return tooltip
}
</script>
