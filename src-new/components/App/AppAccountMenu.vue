<template>
    <Dropdown>
        <template #toggle="{toggle}">
            <button
                class="cursor-pointer w-8 h-8 rounded-full overflow-hidden shadow-lg image-fit zoom-in"
                @click="toggle"
            >
                <AppAvatar
                    v-if="userInfo.avatar"
                    :image="userInfo.avatar"
                    :name="userInfo.fio"
                    :info="userInfo"
                    class="w-8 h-8"
                />
            </button>
        </template>
        <template #content="{close}">
            <div class="notification-content__box dropdown-menu__content box dark:bg-dark-6 w-[230px]">
                <div
                    v-if="workspaces.length > 1"
                    class="flex p-4 border-b border-black border-opacity-5 dark:border-dark-3"
                >
                    <div
                        v-for="option in workspaces"
                        :key="option.pk"
                        class="form-check flex items-center mr-4"
                    >
                        <label class="form-check-label text-xs font-medium flex align-middle">
                            <input
                                :value="option.pk"
                                :checked="option.pk === selectedWorkspace"
                                class="form-check-input"
                                name="workspace"
                                type="radio"
                                @input="changeWorkspace($event.target.value)"
                            >
                            <span class="ml-1">{{ option.title }}</span>
                        </label>
                    </div>
                </div>
                <div class="p-4 border-b border-black border-opacity-5 dark:border-dark-3">
                    <div class="font-medium">
                        {{ userInfo.fio }} #{{ userInfo.id }}
                    </div>
                    <div class="text-xs text-gray-600 mt-0.5 dark:text-gray-600">
                        {{ userInfo.positionFull }}
                    </div>
                </div>

                <div class="flex flex-col gap-y-2 p-2">
                    <button
                        class="profile-button"
                        @click.prevent="()=> {
                            close()
                            openPNRConverter()
                        }"
                    >
                        <SendIcon class="breadcrumb__icon" />
                        <span class="ml-2">PNR Converter</span>
                    </button>
                    <button
                        v-if="projectFeatures.email_footer_generator.enabled"
                        class="profile-button whitespace-nowrap"
                        @click.prevent="()=> {
                            close()
                            openEmailFooterGeneratorModal()
                        }"
                    >
                        <MailIcon class="breadcrumb__icon" />
                        <span class="ml-2">Email Footer Generator</span>
                    </button>
                    <button
                        v-if="canGenerateHotelPdf"
                        class="profile-button whitespace-nowrap"
                        @click.prevent="()=> {
                            close()
                            openPDFGenerator()
                        }"
                    >
                        <FileTextIcon class="breadcrumb__icon" />
                        <span class="ml-2">Hotel PDF</span>
                    </button>
                    <RouterLink
                        class="profile-button"
                        :to="routeToSettings()"
                        @click="close"
                    >
                        <SettingsIcon
                            class="breadcrumb__icon"
                        />
                        <span class="ml-2">Settings</span>
                    </RouterLink>
                    <RouterLink
                        v-for="event in currentFestiveEvents"
                        :key="event.name"
                        class="profile-button"
                        :to="routeToFestiveEventSettings()"
                        @click="() => {
                            close()
                        }"
                    >
                        <Component
                            :is="event.icon"
                            class="breadcrumb__icon flex-none"
                        />
                        <span class="ml-2">{{ event.title }} Event Settings</span>
                    </RouterLink>
                    <RouterLink
                        class="profile-button"
                        :to="routeToAgentInvoice()"
                        @click="() => {
                            close()
                        }"
                    >
                        <TrendingUpIcon class="breadcrumb__icon" />
                        <span class="ml-2">Payment invoices</span>
                    </RouterLink>
                    <button
                        class="profile-button"
                        @click.prevent="openMySkillChart"
                    >
                        <BarChart class="breadcrumb__icon" />
                        <span class="ml-2">My Skills</span>
                    </button>
                    <button
                        class="profile-button"
                        @click.prevent="openReleaseNotes()"
                    >
                        <HelpCircleIcon class="breadcrumb__icon" />
                        <span class="ml-2">Release notes</span>
                    </button>
                </div>
                <div class="p-2 border-t border-black border-opacity-5 dark:border-dark-3">
                    <button
                        class="profile-button"
                        @click="logout"
                    >
                        <ToggleRightIcon class="breadcrumb__icon" />
                        <span class="ml-2">Logout</span>
                    </button>
                </div>
            </div>
        </template>
    </Dropdown>
</template>

<script lang="ts">
import { useAuth } from '~/stores/useAuth'
import { currentFestiveEvents } from '@/modules/festive-events/lib/FestiveEventHelper'
import WebUser from '@/lib/service/WebUser'
import SkillsChartModal from '~/modals/SkillsChartModal.vue'
import BarChart from '~/assets/icons/BarChart.svg?component'
import PNRConverterModal from '~/modals/PNRConverterModal.vue'
import EmailFooterGeneratorModal from '~/modals/EmailFooterGeneratorModal.vue'
import PDFGeneratorModal from '~/modals/PDFGeneratorModal.vue'

export default defineComponent({
    name: 'AppAccountMenu',
    components: { BarChart },

    setup() {
        const { useCurrentUser, hasPermission } = useContext()
        const currentUser = useCurrentUser()

        const emailFooterGeneratorModal = useModal(EmailFooterGeneratorModal)

        function openEmailFooterGeneratorModal() {
            emailFooterGeneratorModal.open()
        }

        const projectFeatures = useDictionary('Project', {
            workspace: useService('workspace').selectedWorkspace.value,
        }).mainProject.features

        return {
            openEmailFooterGeneratorModal,
            currentUser,
            hasPermission,
            projectFeatures,
        }
    },

    computed: {
        selectedWorkspace() {
            return useService('workspace').selectedWorkspace.value
        },

        workspaces() {
            const workspaceService = useService('workspace')

            return workspaceService.availableWorkspaces.map((workspaceInfo) => {
                const defaultProject = useDictionary('Project', {
                    workspace: workspaceInfo.pk,
                }).records.find((record) => {
                    if (record.is_main && workspaceInfo.project_pks.includes(record.pk)) {
                        return record
                    }
                })

                return {
                    pk: workspaceInfo.pk,
                    title: defaultProject?.abbreviation,
                }
            }).filter(Boolean)
        },

        userInfo() {
            return {
                id: WebUser.id,
                fio: WebUser.fio,
                sex: WebUser.sex,
                positionFull: (() => {
                    if ([474].includes(Number(WebUser.id))) {
                        return ''
                    }

                    return WebUser.positionFull
                })(),

                avatar: WebUser.avatar,
                last_new_lead_at: null,
                last_bonus_lead_at: null,
            }
        },

        currentFestiveEvents() {
            return currentFestiveEvents
        },

        canGenerateHotelPdf() {
            return this.hasPermission('generateHotelPdf', 'all')
        },
    },

    methods: {
        logout() {
            useAuth().logout()
        },

        openReleaseNotes() {
            useGlobalModal('ReleasePostModal').open()
        },

        openMySkillChart() {
            this.$showModal(SkillsChartModal, {}, { agentPk: String(this.userInfo.id) })
        },

        openPNRConverter() {
            this.$showModal(PNRConverterModal)
        },

        openPDFGenerator() {
            this.$showModal(PDFGeneratorModal)
        },

        changeWorkspace: useDebounceFn((workspace: Workspace) => {
            useService('workspace').setSelectedWorkspace(workspace)
        }, 500),
    },
})
</script>

<style lang="postcss" scoped>
.profile-button {
    /* @todo @css Move to global styles */
    @apply cursor-pointer flex items-center p-2 transition duration-300 w-full ease-in-out hover:bg-gray-200 dark:hover:bg-dark-3 rounded-md
}
</style>
