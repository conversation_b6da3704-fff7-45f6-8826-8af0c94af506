<template>
    <div class="tip-icon" :class="`--${type}`">
        <div class="tip-icon__inner">
            <Component :is="icon[type]" class="tip-icon__inner__svg" />
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Color } from '~/lib/UI'
import type { VNode } from 'vue'
import { HelpCircleIcon } from '@zhuowenli/vue-feather-icons'

defineProps<{
    type: Color
}>()

const icon: {
    [key in Color]: VNode
} = {
    success: CheckCircleIcon,
    info: InfoIcon,
    primary: InfoIcon,
    warning: AlertTriangleIcon,
    pending: AlertTriangleIcon,
    danger: AlertCircleIcon,
    neutral: CircleIcon,
    secondary: CircleIcon,
    muted: CircleIcon,
}
</script>
