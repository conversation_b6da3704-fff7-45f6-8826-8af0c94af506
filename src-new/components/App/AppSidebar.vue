<template>
    <nav
        class="app-sidebar"
        :class="{
            'app-sidebar--opened': isOpened,
            'app-sidebar--mini': layout.sidebarIsOpenForSearch ? !layout.sidebarIsOpenForSearch : layout.sidebarMode == SidebarMode.Mini,
            'app-sidebar--winter-event': $festiveEvents.winter?.sidebarSnow,
        }"
        @mouseenter="setSidebarVisibility(true)"
        @mouseleave="setSidebarVisibility(false)"
    >
        <BloodyPrintBottom
            v-if="$festiveEvents.halloween?.bloodyBackgroundDecor"
            class="absolute bottom-0 right-0 max-w-48 h-auto rotate-45 opacity-40 -z-1 pointer-events-none"
        />

        <div v-if="layout.sidebarMode === SidebarMode.Hideable" class="app-sidebar__expand">
            <AppButton class="--only relative">
                <MenuIcon class="!w-4 !h-4" />

                <img
                    v-if="$festiveEvents.winter?.menuHat"
                    :src="SantaHatImage"
                    class="h-8 w-8 absolute bottom-full text-sky-50 dark:text-white left-full ml-[-17px] mb-[-20px]"
                    alt=""
                >
            </AppButton>
        </div>

        <RouterLink to="/" class="app-sidebar__logo">
            <AppLogo class="cursor-pointer" />
            <div
                v-show="layout.sidebarMode !== SidebarMode.Mini || layout.sidebarIsOpenForSearch"
                class="app-sidebar__logo__text"
                :class="layout.sidebarMode !== SidebarMode.Mini || layout.sidebarIsOpenForSearch ? 'opacity-100 h-auto' : 'opacity-0 h-0'"
            >
                Back Office
            </div>
        </RouterLink>

        <div
            v-if="searchIsEnabled"
            v-tooltip="layout.sidebarMode === SidebarMode.Mini && !layout.sidebarIsOpenForSearch ? {
                content: 'Search',
                placement: 'right',
                distance: 14,
            } : undefined"
            class="app-sidebar__search"
            @click="openSidebarForSearch"
        >
            <div class="app-sidebar__search__icon">
                <SearchIcon class="w-4 h-4" />
            </div>
            <input
                ref="sidebarSearch"
                v-model.trim="searchInput"
                placeholder="Start typing ..."
                type="text"
                @keyup.enter="goToFirstSearchResult"
            >
            <div
                v-if="searchInput.length"
                class="app-sidebar__search__clear button --only --small --ghost"
                @click="resetSearch"
            >
                <XIcon class="!w-4 !h-4" />
            </div>
        </div>

        <div class="app-sidebar__menu side-menu">
            <div v-for="(menuItemsGroup, $i) in groupedMenuItems" :key="$i">
                <div v-if="menuItemsGroup.menuItems.length" class="side-menu__group">
                    <div class="side-menu__group__title">
                        {{ menuItemsGroup.header }}
                    </div>
                </div>
                <RouterLink
                    v-for="menu in menuItemsGroup.menuItems"
                    :key="menu.link.name"
                    v-tooltip="layout.sidebarMode === SidebarMode.Mini && !layout.sidebarIsOpenForSearch ? {
                        content: menu.title,
                        placement: 'right',
                        distance: 7,
                    } : undefined"
                    :to="menu.link"
                    class="side-menu__link"
                    @click="resetSearch"
                >
                    <div class="side-menu__item">
                        <div class="side-menu__icon">
                            <Component :is="menu.icon" class="w-4 h-4" />
                        </div>

                        <div class="side-menu__title" v-html="highlightSearchQuery(menu.title)" />
                    </div>
                </RouterLink>
            </div>
        </div>

        <button class="app-sidebar__state" @click="toggleSidebarMode">
            <ChevronLeftIcon v-if="layout.sidebarMode === SidebarMode.Hideable" />
            <ChevronRightIcon v-if="layout.sidebarMode === SidebarMode.Mini" />
        </button>

        <SnowForSidebar v-if="$festiveEvents.winter?.sidebarSnow" />
    </nav>
</template>

<script setup lang="ts">
import SnowForSidebar from '@/modules/festive-events/components/SnowForSidebar.vue'
import SantaHatImage from '@/assets/images/events/santa-hat.png'
import BloodyPrintBottom from '~/assets/images/festive-events/bloody-print-2.svg?component'

import AppLogo from '~/components/App/AppLogo.vue'
import { canOpenRoute } from '~/router/middleware/PermissionsRouteMiddleware'
import { SidebarMode, useAppLayout } from '~/stores/useAppLayout'
import { LeadPageMode } from '~/service/AppSettingsService'
import { DepartmentName } from '~/api/models/Department/Department'
import { isUserInDepartment } from '~/composables/useCurrentUser'
import { useRouter } from 'vue-router'
import type { RouteDefinition } from '~/router'
import { RouteGroup } from '~types/enums/RouteGroup'
import { PositionName } from '~/api/models/Position/Position'

defineOptions({
    name: 'AppSidebar',
})

//

const { useCurrentUser, workspace } = useNewContext('domain')

const layout = useAppLayout()

//

const sidebarSearch: Ref<HTMLHtmlElement> = ref({} as HTMLHtmlElement)

const searchInput = ref('')

const searchIsEnabled = computed(() => {
    return availableMenuItems.value.length > 4
})

function openSidebarForSearch() {
    if (layout.sidebarMode === SidebarMode.Mini) {
        layout.sidebarIsOpenForSearch = true
        layout.setSidebarVisibility(true)
    }
}

function isSearchFocused() {
    return document.activeElement === sidebarSearch.value
}

const resetSearch = () => {
    searchInput.value = ''
}

//

const isOpened = computed(() => layout.sidebarIsOpen)

function toggleSidebarMode() {
    if (layout.sidebarMode === SidebarMode.Mini) {
        layout.setSidebarMode(SidebarMode.Hideable)
    } else if (layout.sidebarMode === SidebarMode.Hideable) {
        layout.setSidebarMode(SidebarMode.Mini)
    }
}

function setSidebarVisibility(value: boolean) {
    if (layout.sidebarMode !== SidebarMode.Hideable && !layout.sidebarIsOpenForSearch) {
        return
    }

    layout.setSidebarVisibility(value)
}

//

const router = useRouter()

const routes = router.getRoutes() as RouteDefinition[]

const availableMenuItems = computed(() => {
    return routes.filter(route => route.meta?.inSidebar && canOpenRoute(route) && filterAlaCrutch(route))
})

const menuItems = computed<MenuItems[]>(() => {
    return availableMenuItems.value.filter(route => filterByKeywords(route))
        .sort(function(a, b) {
            if (b.meta?.sortMenu && a.meta?.sortMenu) {
                return b.meta.sortMenu - a.meta.sortMenu
            }

            // @ts-ignore
            return a.meta._defaultMenuSort - b.meta._defaultMenuSort
        }).map(route => {
            let name = route.name

            if (leadPageMode.value === LeadPageMode.List) {
                if (route.name === 'leads') {
                    name = 'leads.list'
                } else if (route.name === 'experts') {
                    name = 'experts.list'
                } else if (route.name === 'lead.management') {
                    name = 'lead.management.list'
                }
            }

            return {
                title: route.meta?.title || 'No title',
                icon: route.meta?.icon || 'CircleIcon',
                link: { name },
                group: route.meta?.group || RouteGroup.General,
            }
        })
})

//

const groupedMenuItems = computed(() => {
    const grouped = menuItems.value.reduce<Record<RouteGroup, MenuItems[]>>((record, route) => {
        if (!record[route.group]) {
            record[route.group] = []
        }

        record[route.group].push(route)

        return record
    }, {} as Record<RouteGroup, MenuItems[]>)

    return [
        { header: 'Leads and Clients', menuItems: grouped[RouteGroup.LeadsClients] || []},
        { header: 'Sales and Finances', menuItems: grouped[RouteGroup.SalesFinances] || []},
        { header: 'Analytics and Reports', menuItems: grouped[RouteGroup.AnalyticsReports] || []},
        { header: 'Tasks and Management', menuItems: grouped[RouteGroup.TasksManagement] || []},
        { header: 'Tools and Settings', menuItems: grouped[RouteGroup.ToolsSettings] || []},
        { header: 'General', menuItems: grouped[RouteGroup.General] || []},
        { header: 'Development', menuItems: grouped[RouteGroup.Development] || []},
    ].filter(group => group.menuItems.length)
})

//

const workspaceTitle = useService('workspace').getWorkspaceTitle(workspace)

const filterAlaCrutch = (route: RouteDefinition): boolean => {
    const user = useCurrentUser()

    if (!user) {
        return false
    }

    // noinspection RedundantIfStatementJS
    if ((route.name === 'sales')) {
        const isExpert = isUserInDepartment(user, DepartmentName.Experts)
        const isManager = isUserHasPosition(user, PositionName.Manager)
        const isRevTicketing = isUserInDepartment(user, DepartmentName.TicketingRevenue)

        if (isManager && isExpert) {
            return true
        }

        if (isExpert || isRevTicketing) {
            return false
        }
    }

    if (route.name === 'product-metrics') {
        return workspaceTitle === 'ANY' || workspaceTitle === 'TBC'
    }

    return true
}

function filterByKeywords(route: RouteDefinition): boolean {
    return route.meta?.title ? route.meta?.title.toLowerCase().includes(searchInput.value.toLowerCase()) : true
}

const leadPageMode = computed(() => useAppLayout().leadPageMode)

const handleKeydown = (event: KeyboardEvent) => {
    if (event.key.length === 1 && event.key !== ' ' && !isSearchFocused()) {
        sidebarSearch.value.focus()
    }
}

watch(layout, (value) => {
    if (searchIsEnabled.value) {
        if (value.sidebarIsOpen) {
            if (layout.sidebarMode !== SidebarMode.Mini) {
                window.addEventListener('keydown', handleKeydown)
            }
        } else if (!value.sidebarIsOpen) {
            if (layout.sidebarMode !== SidebarMode.Mini) {
                window.removeEventListener('keydown', handleKeydown)
            }

            resetSearch()

            sidebarSearch.value.blur()

            layout.sidebarIsOpenForSearch = false
        }
    }
})

//

function highlightSearchQuery(title: string): string {
    if (!searchInput.value) {
        return title
    }

    const search = searchInput.value.toLowerCase()

    const index = title.toLowerCase().indexOf(search)

    if (index === -1) {
        return title
    }

    return (title.slice(0, index).replace(' ', '&nbsp;') +
        '<span class="text-primary-400 font-bold">' +
        title.slice(index, index + search.length).replace(' ', '&nbsp;') +
        '</span>' +
        title.slice(index + search.length).replace(' ', '&nbsp;'))
}

function goToFirstSearchResult() {
    if (!searchInput.value) {
        return
    }

    const firstItem = menuItems.value[0]

    if (firstItem) {
        router.push(firstItem.link)
    }
}

type MenuItems ={
    title: string
    icon: string | Component
    link: { name: string }
    group: RouteGroup
}
</script>
