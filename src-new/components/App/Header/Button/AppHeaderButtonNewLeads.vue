<template>
    <AppHeaderCounterButton
        :category="category"
        :show-counter="showCounter"
        @click="onClick"
        @click.right.prevent="openMenu"
    >
        New leads
    </AppHeaderCounterButton>
</template>

<script lang="ts" setup>
import { CounterCategory } from '~/api/models/Counter/Counter'
import AppHeaderCounterButton from '~/components/App/Header/Button/AppHeaderCounterButton.vue'
import { goTo, goToLead, routeToLeadList } from '@/lib/core/helper/RouteNavigationHelper'
import { toastError, toastSuccess, toastWarning } from '@/lib/core/helper/ToastHelper'
import ManualAgentNewLeadAssignModal from '@/views/modals/ManualAgentNewLeadAssignModal.vue'
import { openContextMenu } from '@/plugins/ContextMenuPlugin'
import NewQueueVisualizationModal from '~/modals/NewQueueVisualization/NewQueueVisualizationModal.vue'
import { NewQueueCategory } from '~/api/models/Agent/Agent'

defineOptions({
    name: 'AppHeaderButtonNewLeads',
})

//

const {
    hasPermission,
    useModel,
    useCurrentUser,
} = useContext()

//

const user = useCurrentUser()

const category = computed(() => {
    if (hasPermission('manage', 'all')) {
        return CounterCategory.NewLeadsTop
    }

    switch (user.new_queue_category) {
        case NewQueueCategory.Beginner:
            return CounterCategory.NewLeadsBeginner
        case NewQueueCategory.Top:
            return CounterCategory.NewLeadsTop
        default:
            return CounterCategory.NewLeadsExperienced
    }
})

//

const showCounter = hasPermission('viewNewLeadCounter', 'Lead') || hasPermission('takeNewForTeam', 'Lead')

//

const modal = useModal(ManualAgentNewLeadAssignModal)

async function onClick() {
    if (useCurrentUser()?.team_pk && hasPermission('takeNewForTeam', 'Lead')) {
        await modal.open()

        return
    }

    try {
        const { lead_pks } = await useModel('Lead').actions.takeNewLead()

        if (!lead_pks.length) {
            toastError('No new leads available')

            return
        }

        const leadPk = lead_pks[0]

        toastSuccess(`New lead #${leadPk} was assigned to you`)

        if (lead_pks.length === 1) {
            goToLead(leadPk)
        } else {
            // If there are more than one lead, we need to show a warning
            toastWarning(`Same client leads were detected and assigned to you: ${lead_pks.map(pk => '#' + pk).join(', ')}`, {
                duration: 10000,
            })

            goTo({
                ...routeToLeadList(),
                query: {
                    tags: `Lead+id:+${lead_pks.join(',')}`,
                },
            })
        }
    } catch (e: any) {
        const leadPk = tryUsePk('Lead', e.data?.lead_id || e.data?.lead_pk)

        let message = e.message?.replace(/\n/g, '<br />') || 'No new leads available'

        if (leadPk) {
            message += `<br><b>Click to go to lead #${leadPk}</b>`
        }

        toastError(message, {
            duration: 6000,
            onClick: () => {
                if (leadPk) {
                    goToLead(leadPk)
                }
            },
        })
    }
}

const openMenu = (event) => {
    if (!hasPermission('manage', 'all')) {
        return
    }
    openContextMenu(event, [
        {
            icon: LogOutIcon,
            text: 'Visualization',
            onClick: () => useModal(NewQueueVisualizationModal).open(),
        },
    ])
}
</script>

