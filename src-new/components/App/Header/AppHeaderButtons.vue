<template>
    <div class="header__buttons flex items-center mr-auto">
        <AppHeaderButtonCheckIns v-if="canOpenSalesPage" />

        <template v-if="hasPermission('openPageLeadManagement', 'all') && !hasPermission('manage', 'all')">
            <AppHeaderButtonTakeFromLeadManagementQueue />
        </template>
        <template v-if="canAccessLeadFeatures">
            <div class="button-group">
                <AppHeaderButtonNewLeads />
                <AppHeaderButtonBonusLeads />
                <AppHeaderButtonBonusPlusLeads />
                <AppHeaderButtonToBeLost />
                <AppHeaderButtonTakeOwnLead />
                <AppHeaderButtonDuplicates />
            </div>
            <AppHeaderButtonAdminLeadTakeRequests v-if="hasPermission('approveTakeRequest', 'Lead')" />
            <AppHeaderButtonAgentLeadTakeRequests v-else />
        </template>

        <template v-if="hasPermission('openPageElrFrtCheck', 'all')">
            <AppHeaderButtonElrFrtCheck />
        </template>

        <div class="button-group">
            <template v-if="canOpenSalesPage">
                <AppHeaderButtonCardRequests v-if="hasPermission('manage', 'all')" />
                <AppHeaderButtonCardRequestsNotification v-else />
            </template>
            <AppHeaderButtonIssues />
            <AppHeaderButtonSaleCommunication v-if="canOpenSalesPage" />
            <AppHeaderButtonCustomerSupport />
        </div>
        <AppHeaderButtonPnrInfo v-if="canOpenPnrInfoPage" />
        <AppHeaderButtonPriceDrop v-if="canOpenPriceDropsPage" />
        <AppHeaderButtonAssistant v-if="canAccessLeadFeatures && canOpenSalesPage" />
    </div>
</template>

<script lang="ts" setup>
import AppHeaderButtonPriceDrop from '~/components/App/Header/Button/AppHeaderButtonPriceDrop.vue'
import AppHeaderButtonPnrInfo from '~/components/App/Header/Button/AppHeaderButtonPnrInfo.vue'
import AppHeaderButtonAssistant from '~/components/App/Header/Button/AppHeaderButtonAssistant.vue'

const { hasPermission } = useContext()

const canOpenSalesPage = computed(() => hasPermission('openPage', 'Sale'))
const canOpenPriceDropsPage = computed(() => hasPermission('openPagePriceDropChecks', 'all'))

const canOpenPnrInfoPage = computed(() => hasPermission('canEditPnrInfo', 'all'))

const canAccessLeadFeatures = computed(() => hasPermission('openPageLead') &&
    (hasPermission('takeNew', 'Lead') || hasPermission('takeNewForTeam', 'Lead')))
</script>
