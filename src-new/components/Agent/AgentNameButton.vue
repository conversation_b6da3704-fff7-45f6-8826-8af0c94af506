<template>
    <div
        v-if="agent"
        class="flex items-center gap-2"
    >
        <AppAvatar
            :image="agent.avatar"
            class="w-7 h-7"
        />

        <div class="flex flex-col">
            <div class="flex items-center text-2xs text-theme-7">
                <span
                    :class="{
                        'text-theme-20 dark:text-theme-20': isSplit
                    }"
                >
                    {{ label }}
                </span>
                <span v-if="badge" :class="`bg-white rounded-xl text-theme-7 p-1 px-2 text-3xs ml-2 -mt-1 -mb-1 ${badgeCss}`">{{ badge }}</span>
            </div>
            <div v-if="agentName" class="text-xs font-medium">
                {{ agentName }}
            </div>
            <div v-else class="text-xs font-medium">
                {{ getFullName(agent) }}
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ClientStatusName } from '~/api/models/Client/ClientStatus'
import { getFullName } from '~/lib/Helper/PersonHelper'

const props = withDefaults(defineProps<{
    agentPk: PrimaryKey,
    agentName?: string,
    label?: string,
    members?: Array<{
        client_status_id: number | null,
    }>
    badge?: string,
    badgeCss?: string,
}>(), {
    agentPk: null,
    agentName: null,
    label: null,
    members: null,
    badge: null,
    badgeCss: null,
})

const { useDictionary } = useContext()
const agentDictionary = useDictionary('Agent')
const positionDictionary = useDictionary('Position')
const teamDictionary = useDictionary('Team')
const clientStatusDictionary = useGeneralDictionary('ClientStatus')

const label = computed(() => {
    if (props.label) {
        return props.label
    }

    if (isSplit.value) {
        return props.members.map((member) => {
            if (!member.client_status_id) {
                return clientStatusDictionary.findByName(ClientStatusName.New)?.name
            }

            return clientStatusDictionary.find(usePk('ClientStatus', member.client_status_id))?.name
        }).join(' / ')
    }

    return [position.value?.name, team.value?.name].filter(Boolean).join(', ')
})

const isSplit = computed(() => props.members?.length >= 2)

const agent = computed(() => {
    return agentDictionary.find(props.agentPk)
})

const position = computed(() => {
    return positionDictionary.find(agent.value.position_pk)
})

const team = computed(() => {
    return agent.value.team_pk ? teamDictionary.find(agent.value.team_pk) : undefined
})
</script>
