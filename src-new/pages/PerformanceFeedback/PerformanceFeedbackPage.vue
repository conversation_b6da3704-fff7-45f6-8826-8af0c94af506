<template>
    <SuspenseManual :state="suspense">
        <template #default>
            <div class="layout__content layout__content--list h-full mb-0">
                <div class="flex flex-col gap-y-2">
                    <div class="w-full flex gap-x-2 h-[350px]">
                        <PerformanceFeedbackStatisticSection
                            :performance-feedback-record-list="performanceFeedbackListRecords"
                            :selected-range="selectedRange"
                            :selected-custom-range-value="selectedCustomRangeValue"
                            :on-selected-range-update="updateSelectedRange"
                            :on-selected-custom-range-value-update="updateSelectedCustomRangeValue"
                        />
                        <PerformanceFeedbackResultsSection :performance-feedback-record-list="performanceFeedbackListRecords" />
                    </div>
                    <div class="bg-white w-full rounded-md border border-secondary-100 dark:bg-dark-3">
                        <div class="w-full flex justify-between items-end pt-2.5 pr-4 border-b border-secondary-100 dark:border-dark-1">
                            <Tabs
                                v-model="activeTab"
                                :tabs="tabs"
                                class="w-[270px]"
                            />
                            <div id="performance-feedback-lists-pagination" />
                        </div>

                        <PerformanceFeedbackFeedbackListSection v-if="activeTab === FeedbackTab.Feedbacks" ref="feedbackListRef" />
                        <PerformanceFeedbackEventsListSection v-else ref="eventsListRef" />
                    </div>
                </div>
            </div>
        </template>
        <template #fallback>
            <PlaceholderBlock class="h-[300px] w-full" />
        </template>
    </SuspenseManual>

    <div id="performance-feedback-lists-pagination-under-list" />
</template>

<script setup lang="ts">
import PerformanceFeedbackStatisticSection
    from '~/sections/PerformanceFeedback/components/PerformanceFeedbackStatisticSection.vue'
import PerformanceFeedbackResultsSection
    from '~/sections/PerformanceFeedback/components/PerformanceFeedbackResultsSection.vue'
import Tabs from '@/components/tabs/Tabs.vue'
import PerformanceFeedbackFeedbackListSection
    from '~/sections/PerformanceFeedback/components/PerformanceFeedbackFeedbackListSection.vue'
import PerformanceFeedbackEventsListSection
    from '~/sections/PerformanceFeedback/components/PerformanceFeedbackEventsListSection.vue'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import type { DateRange } from '@/lib/core/helper/DateHelper'
import { getRangeThisMonth, getRangeThisWeek, getRangeThisYear, getRangeToday } from '@/lib/core/helper/DateHelper'
import { usePerformanceSearchStore } from '~/sections/PerformanceFeedback/composable/usePerformanceSearchStore'

defineOptions({
    name: 'PerformanceFeedbackPage',
    key: (route) => route.name,
})

enum FeedbackTab {
    Feedbacks = 'feedbacks',
    Events = 'events',
}

interface TabItem {
    id: FeedbackTab
    title: string
}

const { useModel } = useNewContext('selected')

//

const tabs: TabItem[] = [
    { id: FeedbackTab.Feedbacks, title: 'Feedback' },
    { id: FeedbackTab.Events, title: 'Events' },
]

const activeTab = ref<FeedbackTab>(FeedbackTab.Feedbacks)

// feedback

const { selectedRange, selectedCustomRangeValue,  chartSearchController, feedbackSearchController, eventsSearchController, syncControllers } = usePerformanceSearchStore()

provide('feedbackSearchController', feedbackSearchController)
provide('eventsSearchController', eventsSearchController)

const suspense = useSuspensableComponent(async () => {
    await performanceFeedbackList.fetch()
})

const performanceFeedbackModel = useModel('PerformanceFeedback')

const performanceFeedbackList = performanceFeedbackModel.useList({
    where: (and) => {
        chartSearchController.applyCondition(and)
    },
    limit: false,
})

chartSearchController.useList(performanceFeedbackList)

const performanceFeedbackListRecords = computed(() => {
    return performanceFeedbackList.records
})

//

const todayRange = getRangeToday()
const thisWeekRange = getRangeThisWeek()
const thisMonthRange = getRangeThisMonth()
const thisYearRange = getRangeThisYear()

// @todo refactor searchController date range tag to accept values like: 'currentYear' | 'currentMonth'
async function updateSelectedRange(range: 'year' | 'month' | 'week' | 'day' | 'custom') {
    selectedRange.value = range

    if (range === 'custom' && selectedCustomRangeValue.value) {
        syncControllers(selectedCustomRangeValue.value)
    }

    if (range === 'year') {
        syncControllers(thisYearRange)
    }

    if (range === 'month') {
        syncControllers(thisMonthRange)
    }

    if (range === 'week') {
        syncControllers(thisWeekRange)
    }

    if (range === 'day') {
        syncControllers(todayRange)
    }

    if ((range !== 'custom' || (range === 'custom'  && selectedCustomRangeValue.value))) {
        await suspense.fetch()
    }
}

async function updateSelectedCustomRangeValue(value: DateRange | undefined) {
    if (!value) {
        selectedRange.value = 'week'
        syncControllers(thisWeekRange)

        await suspense.fetch()

        return
    }

    syncControllers(value)
    selectedCustomRangeValue.value = value

    await suspense.fetch()
}

const eventService = useService('event')

onMounted(() => {
    eventService.on('performanceFeedbackCreated', suspense.fetch)
})
onUnmounted(() => {
    eventService.off('performanceFeedbackCreated', suspense.fetch)
})
</script>
