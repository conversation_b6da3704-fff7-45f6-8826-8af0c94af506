<template>
    <div class="layout__content my-4">
        <div class="flex items-center justify-between mb-4">
            <SearchFilter
                class="w-full max-w-[800px]"
                :controller="searchController"
            />

            <AppButton class="--primary" @click="openCompanyContactSettings()">
                Create contact
                <PlusIcon />
            </AppButton>
        </div>

        <SuspenseManual :state="suspense">
            <div class="card dark:bg-dark-7">
                <div class="card__header flex-wrap pb-0">
                    <div class="flex w-full justify-between border-b-2 pb-2">
                        <div class="flex justify-start items-center gap-4">
                            <div class="card__title">
                                Contacts stock

                                <Loader v-if="suspense.silentLoading.value" />
                            </div>
                        </div>

                        <div class="flex justify-end items-center gap-4">
                            <label class="flex items-center cursor-pointer select-none dark:text-slate-400 mr-3">
                                <InputCheckbox
                                    v-model="showDeleted"
                                    class="mr-1.5"
                                    @update:model-value="suspense.fetch"
                                />
                                <div class="text-xs">
                                    Show deleted contacts
                                </div>
                            </label>

                            <AppPaginationInfo
                                v-if="list.pagination"
                                class="mt-6"
                                :pagination="list.pagination"
                            />
                            <AppPaginationCompact
                                v-if="list.pagination"
                                class="mt-6"
                                :pagination="list.pagination"
                            />
                            <AppPageSize
                                v-if="list.pagination"
                                :pagination="list.pagination"
                                :options="[10, 20, 50, 100, 1000]"
                            />
                        </div>
                    </div>

                    <div class="flex w-full justify-between">
                        <Tabs
                            v-if="activeDepartmentTab && departmentTabs.length > 0"
                            v-model="activeDepartmentTab"
                            :tabs="departmentTabs"
                            class="w-[480px]"
                            @update:model-value="suspense.fetch"
                        />

                        <Tabs
                            v-model="activeTab"
                            :tabs="tabs"
                            class="px-3"
                            @update:model-value="suspense.fetch"
                        />
                    </div>
                </div>

                <div class="pb-2 max-w-full overflow-x-auto fancy-scroll-x relative ">
                    <div class="card card--bordered rounded-3">
                        <Component
                            :is="listComponent"
                            v-if="listComponent"
                            :key="`${activeDepartmentTab}--${activeTabValues.activeSubCategoryTab}`"
                            :list="list.records"
                            :pagination="list.pagination"
                            :detected-airlines="detectedAirlines"
                            :airlines-usage="airlinesUsage"
                            :active-department-tab="activeDepartmentTab"
                            :department-tabs="departmentTabs.map(tab => tab.id)"
                            :detected-mile-price-programs="detectedMilePricePrograms"
                            :mile-price-programs-usage="milePriceProgramsUsage"
                            @update="suspense.fetch"
                            @sort-by-airline="sortByAirline"
                        />
                    </div>
                </div>
            </div>
            <template #fallback>
                <PlaceholderBlock class="h-[500px]" />
            </template>
        </SuspenseManual>
    </div>
</template>

<script setup lang="ts">
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import type { ModelAttributes } from '~types/lib/Model'
import { CompanyContactCategory } from '~/api/models/CompanyContact/CompanyContact'
import Tabs from '@/components/tabs/Tabs.vue'
import CreateCompanyContactModal from '~/sections/ContactsStock/modals/CreateCompanyContactModal.vue'
import { DepartmentName } from '~/api/models/Department/Department'
import { PositionName } from '~/api/models/Position/Position'
import SearchController from '~/lib/Search/SearchController'
import TextSearchTag from '~/lib/Search/Tag/TextSearchTag'
import SearchFilter from '~/components/Search/SearchFilter.vue'
import AirlineCompanyContactList from '~/sections/ContactsStock/list/AirlineCompanyContactList.vue'
import MilePriceProgramCompanyContactList from '~/sections/ContactsStock/list/MilePriceProgramCompanyContactList.vue'

defineOptions({
    name: 'CompanyContactListPage',
})

const { useModel, useCurrentUser, useDictionary, hasPermission } = useNewContext('selected')

const currentUser = useCurrentUser()

const showDeleted = ref(false)
const companyContactCategoryDictionary = useGeneralDictionary('CompanyContactCategory')
const companyContactSubCategoryDictionary = useGeneralDictionary('CompanyContactSubCategory')
const departmentDictionary = useGeneralDictionary('Department')
const milePriceProgramsDictionary = useDictionary('MilePriceProgram')

const detectedAirlines = computed(() => {
    return Object.keys(airlinesUsage)
})

const airlinesUsage = reactive<Record<PrimaryKey, number>>({})

const detectAirlinesUsage = async () => {
    const result = await useModel('CompanyContact').actions.getAirlineUsageCount({
        department_pk: activeDepartmentTab.value,
        sub_category: activeTabValues.value.activeSubCategoryTab || null,
        category: activeTabValues.value.activeCategoryTab,
        is_deleted: showDeleted.value,
    })

    for (const key of Object.keys(airlinesUsage)) {
        delete airlinesUsage[key]
    }

    for (const record of result) {
        airlinesUsage[record.pk] = record.count
    }
}

const detectedMilePricePrograms = computed(() => {
    return Object.keys(milePriceProgramsUsage)
})

const milePriceProgramsUsage = reactive<Record<PrimaryKey, number>>({})

const detectMilePriceProgramsUsage = async () => {
    const result = await useModel('CompanyContact').actions.getMilePriceProgramUsageCount({
        department_pk: activeDepartmentTab.value,
        category: activeTabValues.value.activeCategoryTab,
        is_deleted: showDeleted.value,
    })

    const milePriceProgramPks = milePriceProgramsDictionary.records.map((mile_price_program) => mile_price_program.pk)
    for (const key of Object.keys(milePriceProgramsUsage)) {
        delete milePriceProgramsUsage[key]
    }
    for (const mile_price_program_pk of milePriceProgramPks) {
        milePriceProgramsUsage[mile_price_program_pk] = 0
    }

    for (const programUsage of result) {
        milePriceProgramsUsage[programUsage.pk] = programUsage.count
    }
}

async function fetchCounters() {
    const department = departmentDictionary.find(activeDepartmentTab.value)

    if (department.system_name === DepartmentName.TicketingAward) {
        await detectMilePriceProgramsUsage()
    } else {
        await detectAirlinesUsage()
    }
}

const suspense = useSuspensableComponent(async () => {
    await Promise.all([
        list.fetch(),
        fetchCounters(),
    ])
})

const listComponent = computed(() => {
    const department = departmentDictionary.find(activeDepartmentTab.value)

    if (department.system_name === DepartmentName.CustomerSupport) {
        return AirlineCompanyContactList
    } else if (department.system_name === DepartmentName.TicketingRevenue) {
        return AirlineCompanyContactList
    } else if (department.system_name === DepartmentName.TicketingAward) {
        return MilePriceProgramCompanyContactList
    } else {
        return null
    }
})

const sortAirline = ref<{ airline: PrimaryKey; direction: string }>()

function sortByAirline(airlinePk: PrimaryKey) {
    if (!sortAirline.value || airlinePk !== sortAirline.value.airline) {
        sortAirline.value = { airline: airlinePk, direction: 'asc' }
    } else if (airlinePk === sortAirline.value.airline) {
        sortAirline.value.direction =
            sortAirline.value.direction === 'asc' ? 'desc' : 'asc'
    } else {
        sortAirline.value = undefined
    }
    suspense.fetch()
}

const list = useModel('CompanyContact').useList({
    with: ['assignments', 'assignments.case'],
    where: (q) => {
        searchController.applyCondition(q)

        if (activeTabValues.value.activeSubCategoryTab) {
            q.eq('sub_category', activeTabValues.value.activeSubCategoryTab)
        }

        q.eq('category', activeTabValues.value.activeCategoryTab)
        q.eq('department_pk', activeDepartmentTab.value)

        if (!showDeleted.value) {
            q.eq('is_deleted', false)
        }
    },
    page: 1,
    pageSize: 20,
    customParams: () => {
        if (sortAirline.value) {
            return { airlineSort: { airline: sortAirline.value.airline, direction: sortAirline.value.direction } }
        }
    },
})

type DepartmentRecord = ModelAttributes<'Department'>

const isAwardTicketingManager = computed(() => {
    return isUserInDepartment(currentUser, DepartmentName.TicketingAward) && isUserHasPosition(currentUser, PositionName.Manager)
})

const departmentTabs = computed(() => {
    let permittedDepartmentTabs: DepartmentName[] = []

    if (hasPermission('manageCompanyContacts', 'all')) {
        if (isAwardTicketingManager.value) {
            permittedDepartmentTabs = [DepartmentName.TicketingRevenue, DepartmentName.TicketingAward]
        } else {
            permittedDepartmentTabs = [DepartmentName.CustomerSupport, DepartmentName.TicketingRevenue, DepartmentName.TicketingAward]
        }
    } else {
        if (currentUser?.department_pk) {
            const userDepartment = departmentDictionary.findOrFail(currentUser.department_pk)

            if (isAwardTicketingManager.value) {
                permittedDepartmentTabs.push(userDepartment.system_name)
                permittedDepartmentTabs.push(DepartmentName.TicketingRevenue)
            } else if ([DepartmentName.CustomerSupport, DepartmentName.TicketingRevenue, DepartmentName.TicketingAward].includes(userDepartment.system_name)) {
                permittedDepartmentTabs.push(userDepartment.system_name)

                if ([DepartmentName.TicketingAward].includes(userDepartment.system_name)) {
                    permittedDepartmentTabs.push(DepartmentName.TicketingRevenue)
                }
            } else if ([DepartmentName.Verification].includes(userDepartment.system_name)) {
                permittedDepartmentTabs.push(DepartmentName.CustomerSupport)
            } else if (hasPermission('editCompanyContact', 'all')) {
                permittedDepartmentTabs.push(DepartmentName.TicketingRevenue)
            }
        }
    }
    const revTicketingDepartment = departmentDictionary.findByName(DepartmentName.TicketingRevenue)
    const awardTicketingDepartment = departmentDictionary.findByName(DepartmentName.TicketingAward)

    return departmentDictionary.records.filter((department: DepartmentRecord) => {
        return permittedDepartmentTabs.includes(department.system_name)
    }).map((department: DepartmentRecord) => {
        if (department.system_name === DepartmentName.TicketingRevenue) {
            return {
                id: usePk(revTicketingDepartment),
                title: 'Revenue Ticketing',
            }
        }

        if (department.system_name === DepartmentName.TicketingAward) {
            return {
                id: usePk(awardTicketingDepartment),
                title: 'Award Ticketing',
            }
        }

        return {
            id: usePk(department),
            title: department.name,
        }
    })
})

const activeDepartmentTab = ref<PrimaryKey>(departmentTabs.value[0].id)

const isCsTab = computed(() => {
    return departmentDictionary.findOrFail(activeDepartmentTab.value)?.system_name === DepartmentName.CustomerSupport
})

const tabs = computed(() => {
    const res = []

    for (const category of companyContactCategoryDictionary.records) {
        if (isCsTab.value) {
            const subCategories = companyContactSubCategoryDictionary.findByCategory(category.value)

            if (subCategories.length) {
                for (const subCategory of subCategories) {
                    res.push({
                        title: subCategory.title,
                        id: `${category.value}:${subCategory.value}`,
                    })
                }
                continue
            }
        }

        res.push({
            title: category.title,
            id: category.value,
        })
    }

    return res
})

const activeTab = ref(tabs.value[0].id)

const activeTabValues = computed(() => {
    const [activeCategoryTab, activeSubCategoryTab] = activeTab.value.split(':')

    return {
        activeCategoryTab,
        activeSubCategoryTab,
    }
})

watch(activeDepartmentTab, () => {
    activeTab.value = tabs.value[0].id
})

const createRecordModal = useModal(CreateCompanyContactModal)

async function openCompanyContactSettings() {
    await createRecordModal.open({
        companyContact: undefined,
        departmentsPks: departmentTabs.value.map(tab => tab.id),
        activeDepartmentPk: activeDepartmentTab.value,
    })

    await suspense.fetch()
}

const searchController = SearchController.forModel('CompanyContact', {
    keywords: new TextSearchTag('Email / Phone', {
        searchStrategy: 'like',
    }),
}).useList(list)
</script>

