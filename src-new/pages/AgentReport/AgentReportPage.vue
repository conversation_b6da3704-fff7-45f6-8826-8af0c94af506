<template>
    <div>
        <div class="card">
            <div class="flex justify-between">
                <div>
                    <Tabs
                        v-if="activeTab"
                        v-model="activeTab"
                        :tabs="invoiceTabs"
                        class="border-b-4 px-4 pt-2 dark:border-dark-6"
                    />
                </div>
                <div class="flex gap-2 items-center py-2 pr-2">
                    <AppPaginationInfo :pagination="pagination" />
                    <AppPaginationCompact :pagination="pagination" />

                    <AppButton
                        v-if="hasPermission('canManageAgentReports', 'all')"
                        class="flex items-center gap-2 --primary --outline"
                        @click="logsModal.open({ reportPk: pk })"
                    >
                        <ChevronsLeftIcon />
                        Show logs
                    </AppButton>
                </div>
            </div>
        </div>
        <div class="pt-2">
            <div class="flex w-full gap-2 items-center">
                <div class="flex-grow">
                    <AgentReportInfo
                        v-if="activeTab"
                        :report-pk="pk"
                        :department-pk="activeTab"
                        :can-complete="canCompleteReport"
                        :can-send-for-approval="canSendForApproval"
                    />
                </div>
            </div>

            <div class="agentSales overflow-y-auto fancy-scroll mt-4">
                <AppTable
                    :key="pagination.records.length > 0 ? 'normal' : 'alarm' "
                    class="mb-4"
                    zebra
                    :columns="columns"
                    :items="pagination.records"
                    :search-tags="searchController.tags"
                >
                    <template #body>
                        <template v-if="pagination.records.length > 0">
                            <Component
                                :is="getInvoiceComponent(invoice)"
                                v-for="(invoice, $i) in pagination.records"
                                :key="`${$i}--${invoice.id}`"
                                :invoice="invoice"
                                :can-edit="!agentReport.completed_at && ( [AgentReportInvoiceStatus.New, AgentReportInvoiceStatus.RejectedByAgent].includes(invoice.status) )"
                                :not-complete-for-executive-cs="notCompleteForExecutiveCs"
                                :not-complete-for-executive-ticketing="notCompleteForExecutiveTicketing"
                            />
                        </template>
                    </template>
                </AppTable>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import AgentReportInfo from '~/sections/AgentReport/parts/AgentReportInfo.vue'
import { AgentReportInvoiceStatus } from '~/api/models/AgentReport/AgentReportInvoice'
import SearchController from '~/lib/Search/SearchController'
import SortController from '~/lib/Search/SortController'
import SelectSearchTag from '~/lib/Search/Tag/SelectSearchTag'
import TextSearchTag from '~/lib/Search/Tag/TextSearchTag'
import NumberRangeSearchTag from '~/lib/Search/Tag/NumberRangeSearchTag'
import DepartmentSearchTag from '~/lib/Search/Tag/Preset/DepartmentSearchTag'
import PositionSearchTag from '~/lib/Search/Tag/Preset/PositionSearchTag'
import TeamSearchTag from '~/lib/Search/Tag/Preset/TeamSearchTag'
import { FrontendPagination } from '~/lib/Model/FrontendPagination'
import { computedWithControl } from '@vueuse/core'
import Tabs from '@/components/tabs/Tabs.vue'
import { DepartmentName } from '~/api/models/Department/Department'
import type { ModelAttributes, ModelRef } from '~types/lib/Model'
import SaleDepartmentInvoice from '~/sections/AgentReport/parts/invoices/process/SaleDepartmentInvoice.vue'
import CSDepartmentInvoice from '~/sections/AgentReport/parts/invoices/process/CSDepartmentInvoice.vue'
import DefaultInvoice from '~/sections/AgentReport/parts/invoices/process/DefaultInvoice.vue'
import RevTicketingDepartmentInvoice
    from '~/sections/AgentReport/parts/invoices/process/RevTicketingDepartmentInvoice.vue'
import AwardTicketingDepartmentInvoice
    from '~/sections/AgentReport/parts/invoices/process/AwardTicketingDepartmentInvoice.vue'
import VerificationDepartmentInvoice
    from '~/sections/AgentReport/parts/invoices/process/VerificationDepartmentInvoice.vue'
import SpecialServicesDepartmentInvoice from '~/sections/AgentReport/parts/invoices/process/SpecialServicesDepartmentInvoice.vue'
import ExecutiveDepartmentInvoice from '~/sections/AgentReport/parts/invoices/process/ExecutiveDepartmentInvoice.vue'
import { unique } from '~/lib/Helper/ArrayHelper'
import AgentReportLogsSideModal from '~/sections/AgentReport/modals/AgentReportLogsSideModal.vue'
import LeadManagementDepartmentInvoice
    from '~/sections/AgentReport/parts/invoices/process/LeadManagementDepartmentInvoice.vue'

defineOptions({
    name: 'AgentReportPage',
    key: (route) => route.path,
})

const props = defineProps<{
    pk: PrimaryKey
}>()

const {
    useModel,
    workspace,
    useDictionary,
    hasPermission,
} = await useNewContext('AgentReport', props.pk)

const {
    record: agentReport,
    fetch: agentReportFetch,
} = useModel('AgentReport').useRecord({
    with: [
        'invoices',
        'invoices.report',
        'invoices.department',
        'invoices.agent',
        'invoices.agent.department',
        'invoices.agent.position',
        'invoices.agent.team',
        // assignments
        'invoices.sales',
        'invoices.additional',
        'invoices.ticketing',
        'invoices.internal',
    ],
}).destructable()

// ============================================

const activeTab = ref<PrimaryKey>()

const departmentDictionary = useGeneralDictionary('Department')

const canViewDepartmentTab = (department: ModelRef<'Department'>) => {
    return hasPermission('editAgentReports', 'Department', department)
}
const invoiceTabs = computed(() => {
    if (!agentReport.value?.department_pks) {
        return []
    }

    const options = agentReport.value.department_pks.map((pk) => {
        const department = departmentDictionary.findOrFail(pk)

        const name = department.system_name as DepartmentName

        return canViewDepartmentTab(department) ? {
            id: pk,
            title: department?.name || '----',
            notification: haveCanSendForApproval(pk) ? '!' : '',
        } : undefined
    }).filter(Boolean)

    return options
})

// ============================================

watch(() => props.pk, async () => {
    await agentReportFetch(props.pk, true)
    activeTab.value = invoiceTabs.value[0].id
}, { immediate: true })

const columns = useTableColumns({
    agent_pk: {
        label: 'Agent',
        width: 15,
        sortable: (pk, item) => {
            return `${item.agent.first_name} ${item.agent.last_name}`
        },
    },
    position_pk: {
        label: 'Position',
        width: 15,
        sortable: (pk, item) => {
            return item.agent.position?.name
        },
    },
    department_pk: {
        label: 'Department',
        width: 15,
        sortable: (pk, item) => {
            return item.agent.department?.name
        },
    },
    team_pk: {
        label: 'Team',
        width: 15,
        sortable: (pk, item) => {
            return item.agent.team?.name
        },
    },

    total_gross: {
        label: 'Total gross',
        width: 15,
        // sortable: true,
    },
    total_net: {
        label: 'Total net',
        width: 15,
        // sortable: true,
    },
    status: {
        label: 'Status',
        width: 15,
        // sortable: true,
    },
    remark: {
        label: 'Agent\'s Remark',
        width: 15,
        // sortable: true,
    },
    actions: {
        label: '',
        width: 10,
    },
})

const sortController = SortController.fromColumns(columns, {
    default: { agent_pk: 'asc' },
})

const canCompleteReport = computed(() => {
    if (agentReport.value?.invoices.length) {
        return agentReport.value.invoices.every(invoice => {
            return [AgentReportInvoiceStatus.ApprovedByAgent, AgentReportInvoiceStatus.ApprovedByManager].includes(invoice.status)
        })
    }

    return false
})

const agentsForSelect = useDictionary('Agent').mapRecords.forSelect({
    withImage: true,
    withDepartment: true,
    withTeam: true,
    withPosition: true,
})

const invoiceStatuses = computed(() => {
    return useGeneralDictionary('AgentReportInvoiceStatus').mapRecords.forSelect()
})

const searchController = SearchController.forModel('AgentReportInvoice', {
    agent_pk: new SelectSearchTag('Agent', agentsForSelect).setShortcut('agent:'),
    remark: new TextSearchTag('Agent\'s Remark'),
    status: new SelectSearchTag('Status', invoiceStatuses),
    department_pk: new DepartmentSearchTag(),
    position_pk: new PositionSearchTag(),
    team_pk: new TeamSearchTag(workspace),
    total_gross: new NumberRangeSearchTag('GP'),
    base_salary: new NumberRangeSearchTag('Salary'),
    total_net: new NumberRangeSearchTag('NET'),
}, {
    syncWithQuery: false,
})

const invoices = computed(() => {
    const invoices = agentReport.value?.invoices || []
    const typeFilteredRecords = invoices.filter((invoice) => invoice.department_pk === activeTab.value)

    const records = searchController.filter(typeFilteredRecords, {
        agent_pk: (invoice) => invoice.agent_pk,
        department_pk: (invoice) => {
            return invoice.agent.department_pk
        },
        position_pk: (invoice) => {
            return invoice.agent.position_pk
        },
        team_pk: (invoice) => {
            return invoice.agent.team_pk
        },
        status: (invoice) => invoice.status,
    }, 'AgentReportInvoice')

    return records
})

const pagination = computedWithControl([() => invoices.value.length, () => activeTab.value, () => searchController.activeTags], () => {
    return new FrontendPagination(invoices, {
        page: 1,
        pageSize: 20,
    })
})

const haveCanSendForApproval = (department_pk: PrimaryKey) => {
    return agentReport.value.invoices.some(invoice => {
        return invoice.status === AgentReportInvoiceStatus.Processed && !invoice.can_approve && invoice.department_pk === department_pk
    })
}

const canSendForApproval = computed(() => {
    if (agentReport.value?.invoices.length) {
        return haveCanSendForApproval(activeTab.value)
    }

    return false
})

const notCompleteForExecutiveCs = computed(() => {
    const result = []

    const invoices = agentReport.value?.invoices || []

    for (const invoice of invoices) {
        if ([DepartmentName.Verification, DepartmentName.CustomerSupport, DepartmentName.SpecialServices].includes(invoice.department.system_name)) {
            if (![AgentReportInvoiceStatus.ApprovedByAgent, AgentReportInvoiceStatus.ApprovedByManager].includes(invoice.status)) {
                result.push(invoice.department.system_name)
            }
        }
    }

    return unique(result)
})

const notCompleteForExecutiveTicketing = computed(() => {
    const result = []

    const invoices = agentReport.value?.invoices || []

    for (const invoice of invoices) {
        if ([DepartmentName.TicketingRevenue].includes(invoice.department.system_name)) {
            if (![AgentReportInvoiceStatus.ApprovedByAgent, AgentReportInvoiceStatus.ApprovedByManager].includes(invoice.status)) {
                result.push(invoice.department.system_name)
            }
        }
    }

    return unique(result)
})

function getInvoiceComponent(invoice: ModelAttributes<'AgentReportInvoice'>) {
    const department = departmentDictionary.find(invoice.department_pk)

    if (!department) {
        return markRaw(DefaultInvoice)
    }

    if (department.system_name === DepartmentName.Sales) {
        return markRaw(SaleDepartmentInvoice)
    } else if (department.system_name === DepartmentName.CustomerSupport) {
        return markRaw(CSDepartmentInvoice)
    } else if (department.system_name === DepartmentName.LeadManagement) {
        return markRaw(LeadManagementDepartmentInvoice)
    } else if (department.system_name === DepartmentName.Verification) {
        return markRaw(VerificationDepartmentInvoice)
    } else if (department.system_name === DepartmentName.TicketingRevenue) {
        return markRaw(RevTicketingDepartmentInvoice)
    } else if (department.system_name === DepartmentName.TicketingAward) {
        return markRaw(AwardTicketingDepartmentInvoice)
    } else if (department.system_name === DepartmentName.Executive) {
        return markRaw(ExecutiveDepartmentInvoice)
    } else if (department.system_name === DepartmentName.SpecialServices) {
        return markRaw(SpecialServicesDepartmentInvoice)
    } else {
        return markRaw(DefaultInvoice)
    }
}

const logsModal = useModal(AgentReportLogsSideModal)
</script>

<style lang="postcss" scoped>
.agentSales {
    height: calc(100vh - 200px);
}
</style>
