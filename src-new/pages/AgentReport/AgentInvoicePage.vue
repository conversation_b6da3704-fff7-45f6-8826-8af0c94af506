<template>
    <div class="layout__content my-4">
        <div class="card">
            <div class="card__header">
                <div class="card__title mr-auto">
                    Payment Invoices
                </div>
                <AppPaginationInfo :pagination="list.pagination" />
                <AppPaginationCompact :pagination="list.pagination" />
                <AppPageSize :pagination="list.pagination" :options="[17, 50, 100]" />
            </div>

            <div class="card__body p-0">
                <SuspenseManual v-bind="suspense">
                    <AppTable
                        class="mb-4"
                        zebra
                        :columns="columns"
                        :items="list.records"
                        :sort-controller="sortController"
                    >
                        <template #body>
                            <template v-for="invoice in list.records" :key="invoice.id">
                                <tr>
                                    <td>
                                        <div class="flex gap-2">
                                            {{ invoice.id }}

                                            <div class="badge">
                                                {{ invoice.department.name }}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {{
                                            invoice.report?.completed_at ? $format.date(invoice.report.completed_at) : 'Invoice still in work'
                                        }}
                                    </td>
                                    <td>
                                        {{
                                            `${$format.date(invoice.report.from_date, 'UTC')} - ${$format.date(invoice.report.to_date, 'UTC')}`
                                        }}
                                    </td>

                                    <td>{{ $format.money(invoice.total_gross) }}</td>
                                    <td>{{ $format.money(invoice.total_net) }}</td>

                                    <td>
                                        <div class="flex gap-2 items-center">
                                            <div class="badge --small" :class="`--${getInvoiceStatus(invoice)?.style}`">
                                                {{ getInvoiceStatus(invoice)?.title || '----' }}
                                            </div>
                                            <div v-if="invoice.can_approve" class="flex gap-2">
                                                <AppButton
                                                    v-if="invoice.status === AgentReportInvoiceStatus.Processed"
                                                    class="--danger --xs"
                                                    @click="rejectInvoice(invoice)"
                                                >
                                                    Reject
                                                </AppButton>

                                                <AppButton
                                                    v-if="invoice.status === AgentReportInvoiceStatus.Processed"
                                                    class="--success --xs"
                                                    @click="approveInvoice(invoice)"
                                                >
                                                    Approve
                                                </AppButton>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div
                                            v-if="invoice.remark"
                                            v-tooltip="`${invoice.remark}`"
                                            class="max-h-[250px] truncate"
                                        >
                                            {{ invoice.remark }}
                                        </div>
                                    </td>

                                    <td>
                                        <div class="flex justify-end">
                                            <AppButton
                                                class="--ghost --small --only"
                                                @click="selectInvoice(invoice.id)"
                                            >
                                                <ChevronDownIcon v-if="selectedInvoice !== invoice.id" />
                                                <ChevronUpIcon v-else />
                                            </AppButton>
                                        </div>
                                    </td>
                                </tr>
                                <template v-if="selectedInvoice === invoice.id">
                                    <Component
                                        :is="getInvoiceComponent(invoice)"
                                        :invoice="invoice"
                                    />
                                </template>
                            </template>
                        </template>
                    </AppTable>

                    <template #fallback>
                        <PlaceholderBlock class="w-full h-[300px]" />
                    </template>
                </SuspenseManual>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import SortController from '~/lib/Search/SortController'
import type { ModelAttributes } from '~types/lib/Model'
import PromptModal from '~/modals/PromptModal.vue'
import { AgentReportInvoiceStatus } from '~/api/models/AgentReport/AgentReportInvoice'
import { $confirm } from '@/plugins/ConfirmPlugin'
import { DepartmentName } from '~/api/models/Department/Department'
import CSDepartmentInvoicePreview from '~/sections/AgentReport/parts/invoices/preview/CSDepartmentInvoicePreview.vue'
import SaleDepartmentInvoicePreview
    from '~/sections/AgentReport/parts/invoices/preview/SaleDepartmentInvoicePreview.vue'
import DefaultInvoicePreview from '~/sections/AgentReport/parts/invoices/preview/DefaultInvoicePreview.vue'
import RevTicketingDepartmentInvoicePreview
    from '~/sections/AgentReport/parts/invoices/preview/RevTicketingDepartmentInvoicePreview.vue'
import AwardTicketingDepartmentInvoicePreview
    from '~/sections/AgentReport/parts/invoices/preview/AwardTicketingDepartmentInvoicePreview.vue'
import SpecialServicesDepartmentInvoicePreview
    from '~/sections/AgentReport/parts/invoices/preview/SpecialServicesDepartmentInvoicePreview.vue'
import VerificationDepartmentInvoicePreview
    from '~/sections/AgentReport/parts/invoices/preview/VerificationDepartmentInvoicePreview.vue'
import ExecutiveDepartmentInvoicePreview
    from '~/sections/AgentReport/parts/invoices/preview/ExecutiveDepartmentInvoicePreview.vue'
import LeadManagementDepartmentInvoicePreview
    from '~/sections/AgentReport/parts/invoices/preview/LeadManagementDepartmentInvoicePreview.vue'

defineOptions({
    name: 'AgentInvoicePage',
})

const { useModel, hasPermission } = useNewContext('domain')
const selectedInvoice = ref()

function selectInvoice(id: number) {
    if (selectedInvoice.value === id) {
        selectedInvoice.value = undefined
    } else {
        selectedInvoice.value = id
    }
}

const columns = useTableColumns({
    id: {
        label: '#',
        width: 4,
    },
    date: {
        label: 'Date',
        width: 10,
    },
    period: {
        label: 'Period',
        width: 10,
    },
    total_gross: {
        label: 'Total GP',
        width: 10,
    },
    total_net: {
        label: 'Total Net',
        width: 10,
    },
    status: {
        label: 'Status',
        width: 10,
    },
    remark: {
        label: 'Agent\'s Remark',
    },
    action: {
        label: '',
        width: 10,
    },
})

const agentReportStatusDictionary = useGeneralDictionary('AgentReportInvoiceStatus')

function getInvoiceStatus(invoice: ModelAttributes<'AgentReportInvoice'>) {
    return agentReportStatusDictionary.find(invoice.status)
}

const sortController = SortController.fromColumns(columns, {
    default: {
        'id': 'desc',
    },
})

// !!!
// filter by agent implemented on backend
const list = useModel('AgentReportInvoice').useList({
    with: ['agent', 'report', 'sales', 'sales.agent', 'additional', 'department', 'ticketing', 'internal'],
    sort: sortController,
    page: 1,
    pageSize: 17,
}).destructable()

const suspense = useSuspensableComponent(async () => {
    await list.fetch()
})

const promptModal = useModal(PromptModal)

async function rejectInvoice(invoice: ModelAttributes<'AgentReportInvoice'>) {
    const remark = await promptModal.open({
        title: 'Reject invoice',
        message: 'Please describe reject reason',
        required: true,
    })

    await useModel('AgentReportInvoice', {
        http: {
            workspace: useService('workspace').selectedWorkspace.value,
        },
    }).actions.changeStatus({
        pk: usePk(invoice),
        data: {
            status: AgentReportInvoiceStatus.RejectedByAgent,
            remark,
        },
    })
}

async function approveInvoice(invoice: ModelAttributes<'AgentReportInvoice'>) {
    await $confirm(`Are you sure you want to approve this invoice?`)
    await useModel('AgentReportInvoice', {
        http: {
            workspace: useService('workspace').selectedWorkspace.value,
        },
    }).actions.changeStatus({
        pk: usePk(invoice),
        data: {
            remark: null,
            status: AgentReportInvoiceStatus.ApprovedByAgent,
        },
    })
}

const departmentDictionary = useGeneralDictionary('Department')

function getInvoiceComponent(invoice: ModelAttributes<'AgentReportInvoice'>) {
    const department = departmentDictionary.find(invoice.department_pk)

    if (!department) {
        return markRaw(DefaultInvoicePreview)
    }

    if (department.system_name === DepartmentName.Sales) {
        return markRaw(SaleDepartmentInvoicePreview)
    } else if (department.system_name === DepartmentName.CustomerSupport) {
        return markRaw(CSDepartmentInvoicePreview)
    } else if (department.system_name === DepartmentName.LeadManagement) {
        return markRaw(LeadManagementDepartmentInvoicePreview)
    } else if (department.system_name === DepartmentName.Verification) {
        return markRaw(VerificationDepartmentInvoicePreview)
    } else if (department.system_name === DepartmentName.TicketingRevenue) {
        return markRaw(RevTicketingDepartmentInvoicePreview)
    } else if (department.system_name === DepartmentName.TicketingAward) {
        return markRaw(AwardTicketingDepartmentInvoicePreview)
    } else if (department.system_name === DepartmentName.Executive) {
        return markRaw(ExecutiveDepartmentInvoicePreview)
    } else if (department.system_name === DepartmentName.SpecialServices) {
        return markRaw(SpecialServicesDepartmentInvoicePreview)
    } else {
        return markRaw(DefaultInvoicePreview)
    }
}
</script>
