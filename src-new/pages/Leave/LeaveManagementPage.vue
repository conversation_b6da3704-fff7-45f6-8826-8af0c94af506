<template>
    <div class="layout__content flex flex-col h-full py-2">
        <div class="flex items-center justify-between mb-4">
            <div ref="filter" />

            <div class="flex items-center gap-4">
                <InputAgent
                    v-if="activeTab === 'my_requests' && hasPermission('viewPersonalBalance', 'LeaveRequest')"
                    v-model="form.data.agent_pk"
                    :options="agentOptions"
                    placeholder="Select team member"
                    size="large"
                    class="w-[320px]"
                />

                <div v-if="tabs.length > 1" class="button-group">
                    <AppButton
                        v-for="(tab, index) in tabs"
                        :key="index"
                        :class="activeTab === tab.id ? '--primary' : ''"
                        @click="activeTab = tab.id"
                    >
                        {{ tab.title }}
                    </AppButton>
                </div>
            </div>
        </div>

        <div class="flex flex-col gap-2 h-full">
            <template v-if="activeTab === 'my_requests'">
                <LeaveManagementSection :filter-container="filter" :agent-pk="form.data.agent_pk" />
            </template>
            <template v-else-if="activeTab === 'list'">
                <LeaveManagementRequestCardList class="h-full" :filter-container="filter" />
            </template>
            <template v-else-if="activeTab === 'summary'">
                <LeaveRequestSummary class="h-full" :filter-container="filter" />
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useNewContext } from '~/composables/useNewContext'
import LeaveManagementRequestCardList from '~/sections/Leave/LeaveManagementRequestCardList.vue'
import LeaveRequestSummary from '~/sections/Leave/LeaveRequestSummary.vue'
import LeaveManagementSection from '~/sections/Leave/PersonalLeave/LeaveManagementSection.vue'

const { hasPermission, useDictionary, useModel, currentUserPk } = useNewContext('selected')
const agentDictionary = useDictionary('Agent')
const agentList = useModel('Agent').useList({
    with: [ 'team', 'department'],
    limit: false,
})
await agentList.fetch()

const agentOptions = agentDictionary.adapter.mapRecords(agentList.records).forSelect({
    withImage: true,
    withTeam: true,
    withDepartment: true,
})

const form = useForm<{
    agent_pk: PrimaryKey,
}>({
    agent_pk: currentUserPk,
})

const filter = ref()

const tabs = [
    {
        id: 'my_requests',
        title: 'My requests',
    },
]

if (hasPermission('openLeaveRequestApproveList', 'all')) {
    tabs.push({
        id: 'list',
        title: 'Leave requests',
    })

    tabs.push({
        id: 'summary',
        title: 'Summary',
    })
}

const activeTab = ref(tabs[0].id)
</script>
