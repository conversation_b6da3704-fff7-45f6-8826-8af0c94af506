<template>
    <SuspenseManual :state="suspense">
        <template #default>
            <div class="bg-white px-5 py-4 flex flex-col gap-y-4">
                <div class="button-group">
                    <div class="button-group">
                        <AppButton
                            :class="{
                                '--primary': selectedRange === 'year',
                            }"
                            @click="setSelectedRange('year')"
                        >
                            Year
                        </AppButton>
                        <AppButton
                            :class="{
                                '--primary': selectedRange === 'month',
                            }"
                            @click="setSelectedRange('month')"
                        >
                            Month
                        </AppButton>
                        <AppButton
                            :class="{
                                '--primary': selectedRange === 'week',
                            }"
                            @click="setSelectedRange('week')"
                        >
                            Week
                        </AppButton>
                        <AppButton
                            :class="{
                                '--primary': selectedRange === 'day',
                            }"
                            @click="setSelectedRange('day')"
                        >
                            Day
                        </AppButton>
                        <AppButton
                            :class="{
                                '--primary': selectedRange === 'custom',
                            }"
                            @click="setSelectedRange( 'custom' )"
                        >
                            Custom
                        </AppButton>
                    </div>
                </div>
                <div class="h-[250px] relative">
                    <ChartComponent
                        ref="chartRef"
                        :chart-data="chartData"
                        :options="chartOptions"
                        class="w-full h-full z-[9]"
                    />
                    <div class="absolute left-0 bottom-[-20px] w-full pl-4">
                        <div class="flex justify-between font-medium text-2xs text-secondary">
                            <span
                                v-for="(label, index) in chartData.labels"
                                :key="index"
                                class="cursor-pointer"
                                @click="openCreateEventModal(label, index)"
                            >
                                {{ label }}
                            </span>
                        </div>
                    </div>
                </div>
                <div>
                    {{ performanceFeedbackRecords.length }} - {{ performanceFeedbackEventRecords.length }}
                </div>
            </div>
        </template>
        <template #fallback>
            t
        </template>
    </SuspenseManual>
</template>

<script setup lang="ts">
import {
    CategoryScale,
    Chart as ChartJS,
    Filler,
    LinearScale,
    LineController,
    LineElement,
    PointElement,
    Tooltip,
} from 'chart.js'
import { defineChartComponent } from 'vue-chart-3'
import { getRangeThisMonth, getRangeThisWeek, getRangeThisYear, getRangeToday } from '@/lib/core/helper/DateHelper'
import SearchController from '~/lib/Search/SearchController'
import DateRangeSearchTag from '~/lib/Search/Tag/DateRangeSearchTag'
import { useChartAggregation } from '~/sections/PerformanceFeedback/composable/useChartAggregation'
import PerformanceFeedbackCreateEventModal
    from '~/sections/PerformanceFeedback/modals/PerformanceFeedbackCreateEventModal.vue'

const { useModel } = await useNewContext('selected')

// feedbacks

const suspense = useSuspensableComponent(async () => {
    await Promise.all([performanceFeedbackList.fetch(), performanceFeedbackEventList.fetch()])
})

const performanceFeedbackModel = useModel('PerformanceFeedback')

const searchController = SearchController.forModel('PerformanceFeedback', {
    created_at: new DateRangeSearchTag('Created at'),
})

const performanceFeedbackList = performanceFeedbackModel.useList({
    where: (and) => {
        searchController.applyCondition(and)
    },
    limit: false,
})

const performanceFeedbackRecords = computed(() => {
    if (performanceFeedbackList.records.length === 0) return undefined

    return performanceFeedbackList.records
})

// events

const performanceFeedbackEventModel = useModel('PerformanceFeedbackEvent')

const searchControllerEvents = SearchController.forModel('PerformanceFeedbackEvent', {
    event_date: new DateRangeSearchTag('Event date'),
})

const performanceFeedbackEventList = performanceFeedbackEventModel.useList({
    where: (and) => {
        searchControllerEvents.applyCondition(and)
    },
    limit: false,
})

const performanceFeedbackEventRecords = computed(() => {
    if (performanceFeedbackEventList.records.length === 0) return undefined

    return performanceFeedbackEventList.records
})

const eventByIndex = ref<Record<number, any>>({})

//

const { aggregate, generateLabels, getDateFromLabel } = useChartAggregation()

// Register chart.js components
ChartJS.register(
    LineElement,
    LineController,
    PointElement,
    LinearScale,
    CategoryScale,
    Tooltip,
    Filler,
)

const ChartComponent = defineChartComponent('LineChartSelf', 'line')

//

const selectedRange = ref<'year' | 'month' | 'week' | 'day' | 'custom'>('month')

function setSelectedRange(range: 'year' | 'month' | 'week' | 'day' | 'custom') {
    selectedRange.value = range

    if (range === 'year') {
        searchController.tags.created_at.setValue(getRangeThisYear())
    }

    if (range === 'month') {
        searchController.tags.created_at.setValue(getRangeThisMonth())
    }

    if (range === 'week') {
        searchController.tags.created_at.setValue(getRangeThisWeek())
    }

    if (range === 'day') {
        searchController.tags.created_at.setValue(getRangeToday())
    }
}

const labels = computed(() => generateLabels(selectedRange.value))

watch(selectedRange, () => {
    chartData.value.labels = labels.value
})

//

const THRESHOLD = 3

const chartData = ref({
    labels: labels.value,
    datasets: [
        {
            label: 'Threshold Line',
            borderColor: '#4285f4',
            data: [],
            borderWidth: 1.5,
            pointRadius: 0,
            backgroundColor: 'rgba(235, 237, 243, 0.3)',
            tension: 0,
            fill: true,
            segment: {
                borderColor: ctx => {
                    const { p0, p1, chart } = ctx
                    const y0 = p0.parsed.y
                    const y1 = p1.parsed.y

                    if ((y0 >= THRESHOLD && y1 >= THRESHOLD) || (y0 < THRESHOLD && y1 < THRESHOLD)) {
                        return y0 >= THRESHOLD ? '#3B76F6' : '#FDA29B'
                    }

                    const gradient = chart.ctx.createLinearGradient(p0.x, p0.y, p1.x, p1.y)
                    const ratio = Math.abs((THRESHOLD - y0) / (y1 - y0))

                    if (y0 > y1) {
                        gradient.addColorStop(0, '#3B76F6')
                        gradient.addColorStop(Math.max(0, ratio), '#3B76F6')
                        gradient.addColorStop(Math.min(1, ratio), '#FDA29B')
                        gradient.addColorStop(1, '#FDA29B')
                    } else {
                        gradient.addColorStop(0, '#FDA29B')
                        gradient.addColorStop(Math.max(0, ratio), '#FDA29B')
                        gradient.addColorStop(Math.min(1, ratio), '#3B76F6')
                        gradient.addColorStop(1, '#3B76F6')
                    }

                    return gradient
                },
                borderWidth: () => 1.5,
            },
            order: 1,
        },
    ],
})

const format = useService('formatter')

const chartOptions = ref({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: { display: false },
        tooltip: {
            displayColors: false,
            filter: (tooltipItem) => {
                // Only show tooltip for dataset at index 0 (main line)
                return tooltipItem.datasetIndex === 1
            },
            callbacks: {
                title: (tooltipItems) => {
                    const index = tooltipItems[0].dataIndex
                    const event = eventByIndex.value[index]

                    // Top line of tooltip
                    return `Average rating: ${tooltipItems[0].formattedValue}` + '        ' + `${format.datetime(event.event_date, 'UTC', { full: true })}`
                },
                label: (tooltipItem) => {
                    // Below the title
                    const index = tooltipItem.dataIndex
                    const event = eventByIndex.value[index]

                    return `${event.title}`
                },
                afterLabel: (tooltipItem) => {
                    // Additional line if needed
                    const index = tooltipItem.dataIndex
                    const event = eventByIndex.value[index]

                    return `${event.description}`
                },
            },
        },
    },
    animation: false,
    scales: {
        y: {
            min: 0,
            max: 5.02,
            ticks: {
                stepSize: 1,
                precision: 0,
                callback: (value: number) => {
                    return value === 5.02 ? '5' : value.toString()
                },
            },
            grid: {
                drawBorder: false,
                color: '#E5E7EB',
            },
        },
        x: {
            ticks: {
                display: false,
            },
            grid: {
                display: false,
                drawBorder: false,
                drawTicks: false,
            },
        },
    },
})

watch([() => performanceFeedbackRecords.value, () => selectedRange.value], () => {
    if (!performanceFeedbackRecords.value?.length) return

    const range = selectedRange.value
    const bucketCount =
        range === 'year' ? 12 :
        range === 'month' ? labels.value.length :
        range === 'week' ? 7 :
        range === 'day' ? 24 : 0

    chartData.value.datasets[0].data = aggregate(performanceFeedbackRecords.value, range, bucketCount)
})

// event dataset

const eventDataset = {
    label: 'Events',
    data: [], // будет заполнено
    pointRadius: 4,
    pointBackgroundColor: '#293145',
    pointStyle: 'circle', // можно задать кастомный стиль
    borderWidth: 0,
    hitRadius: 6,
    hoverRadius: 6,
    fill: false,
    type: 'line',
    order: 0, // чем меньше, тем выше в z-index
    showLine: false, // скрыть соединительные линии
}

watch([() => performanceFeedbackEventRecords.value, () => selectedRange.value], () => {
    if (!performanceFeedbackEventRecords.value?.length) return

    const range = selectedRange.value
    const buckets = labels.value.length
    const result = Array.from({ length: buckets }, () => null)

    const eventMap: Record<number, any> = {}

    for (const event of performanceFeedbackEventRecords.value) {
        const date = new Date(event.event_date * 1000)
        let index = null

        if (range === 'year') index = date.getMonth()

        if (range === 'month') index = date.getDate() - 1

        if (range === 'week') index = (date.getDay() + 6) % 7

        if (range === 'day') index = date.getHours()

        if (index !== null && index >= 0 && index < result.length) {
            result[index] = chartData.value.datasets[0].data[index] ?? null
            eventMap[index] = event
        }
    }

    eventByIndex.value = eventMap

    chartData.value.datasets = [
        chartData.value.datasets[0],
        {
            ...eventDataset,
            data: result,
        },
    ]
})

//

const createEventModal = useModal(PerformanceFeedbackCreateEventModal)

function openCreateEventModal(label: string | number, index: number) {
    const date = getDateFromLabel(selectedRange.value, label, index)
    createEventModal.open({
        eventDate: date ? date : new Date(),
    })
}
</script>
