<template>
    <div class="bg-theme-10 p-12 m-24">
        <div class="mb-4">
            <label>Sell price</label>
            <InputNumber v-model="form.data.sell_price" @update:model-value="recalc" />
        </div>

        <div class="mb-4">
            <label>net_price</label>
            <InputNumber v-model="form.data.net_price" @update:model-value="recalc" />
        </div>

        <div class="mb-4">
            <label>CK</label>
            <InputNumber v-model="form.data.ck" />
        </div>
    </div>
</template>

<script setup async lang="ts">
import ProductService from '~/lib/Product/ProductService'

const form = useForm({
    sell_price: 0,
    net_price: 0,
    ck: 0,
    ck_enabled: true,
})

function recalc() {
    const product = ProductService.makeAdditionalExpense({ sell_price: form.data.sell_price, net_price: form.data.net_price })

    form.data.ck = product.getCalculateCk()
    console.log('Recalculated:', product)
}
</script>
