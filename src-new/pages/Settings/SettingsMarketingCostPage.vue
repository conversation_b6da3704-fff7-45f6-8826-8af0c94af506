<template>
    <div class="settings-content">
        <NavComponent />

        <div class="settings-main">
            <div class="settings-main">
                <div class="flex flex-col pt-4">
                    <div class="flex justify-between p-2 border-b">
                        <h2 class="text-lg px-3 block">
                            Marketing budget
                        </h2>
                        <div class="max-w-[150px]">
                            <InputSelect
                                v-model="year"
                                :options="yearOptions"
                                @update:model-value="suspense.fetch()"
                            />
                        </div>
                    </div>
                </div>

                <div class="settings-main-body">
                    <SuspenseManual :loading="suspense.silentLoading">
                        <div class="settings-list">
                            <template v-for="(record, $i) in form.data.marketing_costs" :key="$i">
                                <div class="settings-item items-center py-2 px-4">
                                    <div class="settings-label">
                                        <div class="settings-label-title">
                                            {{ getCalendarMonth(record.month) }} {{ year }}
                                        </div>
                                    </div>
                                    <div class="settings-value">
                                        <div class="flex items-center flex-end gap-2">
                                            =
                                            <InputMoney
                                                v-model="record.value"
                                                :min="0"
                                                size="xs"
                                                :readonly="!enabledItems.includes($i)"
                                            />
                                            <AppButton
                                                v-if="!enabledItems.includes($i)"
                                                class="--only ml-2"
                                                @click="enabledItem($i)"
                                            >
                                                <Edit3Icon
                                                    class="w-3 h-3 !stroke-2"
                                                />
                                            </AppButton>
                                            <AppButton
                                                v-if="enabledItems.includes($i)"
                                                class="--only text-green-500 ml-2"
                                                @click="saveItem($i)"
                                            >
                                                <CheckIcon
                                                    class="w-3 h-3 !stroke-2"
                                                />
                                            </AppButton>
                                            <AppButton
                                                v-if="enabledItems.includes($i)"
                                                class="--only text-red-500 ml-2"
                                                @click="disableItem($i)"
                                            >
                                                <XIcon
                                                    class="w-3 h-3 !stroke-2"
                                                />
                                            </AppButton>
                                        </div>
                                    </div>
                                </div>
                            </template>

                            <div class="settings-item">
                                <div class="settings-label">
                                    <div class="settings-label-title">
                                        Total
                                    </div>
                                </div>
                                <div class="settings-value">
                                    <div class="flex items-center flex-end font-semibold">
                                        {{ $format.money(total, {withCurrency: "USD"}) }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <template #fallback>
                            <div class="p-8 flex justify-center">
                                <Loader />
                            </div>
                        </template>
                    </SuspenseManual>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import NavComponent from '@/views/settings/nav/NavComponent.vue'
import type SelectOption from '~types/structures/SelectOption'
import type { CalendarMonth } from '~/api/models/Marketing/MarketingCost'
import { CheckIcon, Edit3Icon, XIcon } from '@zhuowenli/vue-feather-icons'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'

defineOptions({
    name: 'SettingsMarketingCostPage',
})

const { useModel } = useNewContext('selected')

function setupYearData() {
    const currentYear = new Date().getFullYear()
    const twoYearsAgo = currentYear - 2
    const oneYearAgo = currentYear - 1
    const nextYear = currentYear + 1

    return {
        currentYear,
        options: [
            { value: twoYearsAgo, title: twoYearsAgo.toString() },
            { value: oneYearAgo, title: oneYearAgo.toString() },
            { value: currentYear, title: currentYear.toString() },
            { value: nextYear, title: nextYear.toString() },
        ],
    }
}

const yearData = setupYearData()
const year = ref<number>(yearData.currentYear)
const yearOptions = ref<SelectOption[]>(yearData.options)

const marketingCostModel = useModel('MarketingCost').useList({
    where: (q) => {
        q.eq('year', year.value)
    },
})

const enabledItems = ref<number[]>([])

function enabledItem(index: number) {
    enabledItems.value.push(index)
}

function disableItem(index: number) {
    enabledItems.value = enabledItems.value.filter(item => item !== index)
}

async function saveItem(index: number) {
    const marketingCost = form.data.marketing_costs[index]

    marketingCost.value = Number(marketingCost.value) || 0

    if (marketingCost.pk) {
        await useModel('MarketingCost').actions.update({
            pk: marketingCost.pk,
            value: marketingCost.value,
        })
    } else {
        await useModel('MarketingCost').actions.create({
            year: marketingCost.year,
            month: marketingCost.month,
            value: marketingCost.value,
        })
    }

    disableItem(index)
    toastSuccess('Marketing cost has been updated successfully.')
}

type MarketingCostFormItem = {
    pk: PrimaryKey | null,
    year: number,
    month: CalendarMonth,
    value: number,
}

function getCalendarMonth(month: CalendarMonth): string {
    const CalendarMonthDict = {
        '1': 'January',
        '2': 'February',
        '3': 'March',
        '4': 'April',
        '5': 'May',
        '6': 'June',
        '7': 'July',
        '8': 'August',
        '9': 'September',
        '10': 'October',
        '11': 'November',
        '12': 'December',
    }

    return CalendarMonthDict[month]
}

type FormData = {
    marketing_costs: MarketingCostFormItem[],
}

const form = useForm<FormData>({
    marketing_costs: [],
})

const total = computed(() => {
    return form.data.marketing_costs.reduce((acc, item) => acc + (Number(item.value) || 0), 0)
})

const suspense = useSuspensableComponent(async () => {
    await marketingCostModel.fetch()
    await seedForm()
    enabledItems.value = []
})

const seedForm = () => {
    const months: Array<string> = Array.from({ length: 12 }, (_, i) => String(i + 1))

    const data: MarketingCostFormItem[] = []

    for (const month of months) {
        const marketingCostRecord = marketingCostModel.records.find(marketingCost => marketingCost.month === month)

        if (marketingCostRecord) {
            data.push({
                pk: usePk(marketingCostRecord),
                year: marketingCostRecord.year,
                month: marketingCostRecord.month,
                value: marketingCostRecord.value,
            })
        } else {
            data.push({
                pk: null,
                year: year.value,
                month: month as CalendarMonth,
                value: 0,
            })
        }
    }

    form.updateInitialData({
        marketing_costs: data,
    })
}
</script>

