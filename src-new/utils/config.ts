import type { TerminalEnvironment } from '~modules/gds-terminal/src/lib/TerminalTool'

const origin = normalizeUri(import.meta.env.VITE_API_ORIGIN)

export const config = {
    'cdn': {
        'base': normalizeUri(import.meta.env.VITE_BASE_URL) ?? '',
    },
    'api': {
        'origin': normalizeUri(import.meta.env.VITE_API_ORIGIN),
        'base': prefixWithOrigin(
            normalizeUri(import.meta.env.VITE_API_BASE_PATH) ?? '/api/database/v2',
            'BASE_PATH',
        ),
        'authBase': prefixWithOrigin(
            normalizeUri(import.meta.env.VITE_AUTH_BASE_PATH || '/jwt/auth'),
            'AUTH_BASE_PATH',
        ),
        'testBase': normalizeUri(import.meta.env.VITE_TEST_API_BASE_PATH),
    },
    'app': {
        'defaultProject': Number(import.meta.env.VITE_APP_DEFAULT_PROJECT) || undefined,
        'showRingcentralLoginRemindModal': import.meta.env.VITE_RC_LOGIN_REMIND_MODAL_ENABLED !== 'false',
        'testFestiveEvents': import.meta.env.VITE_FESTIVE_EVENTS_TEST === 'true',
    },
    'resource': {
        'cacheExclude': import.meta.env.VITE_RESOURCE_CACHE_EXCLUDE?.split(',') || [],
        'maxCacheBlockCount': Number(import.meta.env.VITE_RESOURCE_MAX_CACHE_BLOCK_COUNT || 60 / 5), // If considered that 1 block = 5 minutes. 1 hour = 12 blocks
    },
    'google': {
        'clientId': import.meta.env.GOOGLE_CLIENT_ID,
    },
    'debug': import.meta.env.VITE_APP_DEBUG === 'true',
    'console': {
        'excludeTags': import.meta.env.VITE_CONSOLE_EXCLUDE_TAGS?.split(',') || [],
    },
    'ws': {
        'host': (import.meta.env.VITE_WS_HOST as string) || undefined,
        'port': Number(import.meta.env.VITE_WS_PORT || 6001),
        'key': import.meta.env.VITE_WS_KEY || '41ae70d7be43ccf9731181bbaadc9f63',
        'cluster': import.meta.env.VITE_WS_CLUSTER || undefined,
    },
    'wsBalancer': {
        'enabled': import.meta.env.VITE_APP_WS_BALANCER_ENABLED === 'true',
        'maxDisconnects': Number(import.meta.env.VITE_APP_WS_BALANCER_MAX_DISCONNECTS) || undefined,
        'maxHoldMs': Number(import.meta.env.VITE_APP_WS_BALANCER_MAX_HOLD_MS) || undefined,
    },
    'mock': {
        'host': 'localhost',
        'port': Number(import.meta.env.VITE_MOCK_PORT || 8090),
        'ws': {
            'host': import.meta.env.VITE_MOCK_WS_HOST || 'localhost',
            'port': Number(import.meta.env.VITE_MOCK_WS_PORT || 6002),
            'channel': 'mock-server-channel',
            'publishPath': `${normalizeUri(import.meta.env.VITE_API_BASE_PATH)}/ws/publish`,
        },
    },
    'serviceWorker': {
        'registerFilePath': normalizeUri(import.meta.env.VITE_SW_REGISTER_FILE_PATH || '/sw/0/sw.js') ?? '',
        'configPath': {
            'telemetry': normalizeUri(import.meta.env.VITE_SW_TELEMETRY_CONFIG_FILE_PATH || '/sw/0/telemetry/config'),
            'ringCentral': normalizeUri(import.meta.env.VITE_SW_RING_CENTRAL_CONFIG_FILE_PATH || '/sw/0/ringCentral/config'),
            'test': normalizeUri(import.meta.env.VITE_SW_TEST_CONFIG_FILE_PATH || '/sw/0/test/config'),
            'orchestrator': normalizeUri(import.meta.env.VITE_SW_ORCHESTRATOR_CONFIG_FILE_PATH || '/sw/0/orchestrator/config'),
            'auth': normalizeUri(import.meta.env.VITE_SW_AUTH_CONFIG_FILE_PATH || '/sw/0/auth/config'),
        },
    },
    'telemetry': {
        'enabled': import.meta.env.VITE_TELEMETRY_ENABLED ? import.meta.env.VITE_TELEMETRY_ENABLED === 'true' : true,
        'tags': import.meta.env.VITE_TELEMETRY_TAGS?.split(',') || [],
    },
    'phoneNumber': {
        'unknown': ['****** 000 0000', '**************'],
        'example': '****** 855-1221',
    },
    'productConsultantTool': {
        'enabled': import.meta.env.VITE_APP_ENABLE_PRODUCT_CONSULTANT_TOOL === 'true',
    },
    'gds': {
        'httpBase': normalizeUri(import.meta.env.VITE_GDS_HTTP_BASE) ?? '',
    },
    'terminal': {
        'highlightProductionEnvironment': import.meta.env.VITE_TERMINAL_HIGHLIGHT_PRODUCTION_ENVIRONMENT === 'true',
        'environment': (import.meta.env.VITE_TERMINAL_ENVIRONMENT as TerminalEnvironment | undefined) ?? 'prod' as TerminalEnvironment,
        'helpUrl': normalizeUri(import.meta.env.VITE_TERMINAL_HELP_URL) ?? '',
    },
    'centrifuge': {
        'ws': {
            'url': import.meta.env.VITE_CENTRIFUGE_WS_URL ?? `ws://${getHost(origin)}:8900/connection/websocket`,
        },
    },
    'aeroData': {
        'url': import.meta.env.VITE_AERO_DATA_BASE_URL,
    },
} as const

function normalizeUri(uri: string | undefined) {
    if (uri === undefined || uri === '') {
        return undefined
    }

    if (uri === '/') {
        return undefined
    }

    return (!uri.startsWith('http') ? '/' : '') + uri.replace(/^\/{2,}$/, '/').replace(/^\/|\/$/, '')
}

function prefixWithOrigin(url: string | undefined = '', variable: string) {
    if (url && url.startsWith('http') && origin) {
        console.warn(`API_ORIGIN can't be applied when ${variable} is an absolute URL`)

        return url
    }

    return normalizeUri((origin ?? '') + url) as string
}

function getHost(url: string | undefined) {
    if (!url) {
        return 'localhost'
    }

    const parsed = new URL(url)

    return parsed.host
}

