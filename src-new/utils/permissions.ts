export const permissions = {
    'all': [
        // Backend
        'editUTM',
        'manage',
        'openPageExpert',
        'openPageLead', // @todo Should we use (Lead, openPage)?
        'openPageLeadManagement',
        'pageSize5',
        'readUTM',
        'viewSummary',
        'viewTeamLostLead',

        // Frontend
        'openAuthorizedPage', // All authorized users have this permission @todo Remove this permission
        'openPageBookkeeping',
        'openPageTransactions',
        'openPageTransactionsV2', // todo: remove
        'openPageInvoices',
        'openPageTeams',
        'openPageLeadTakeRules',
        'openPageSettingsConsolidator',
        'openPageSettingsIata',
        'openPageSettingsProjectCards',
        'openPageSettingsAward',
        'openPageSettingsExpertInvoices',
        'openCustomerSupportIssuesPage',
        'openApproveRequestsPage',
        'openPageCallManagement',

        'openPageStatistics', // @todo Add to backend
        'openPageAirlineReport', // @todo Add to backend
        'openTasksManager',

        'viewCompanyContact',
        'editCompanyContact',
        'manageCompanyContacts',

        'openPageLeadList',
        'openPageDashboard',
        'openPageDashboardSale',
        'openPageDashboardExpert',
        'openPageDashboardLeadManagement',
        'openPageExpectedAmounts',
        'updateSkillList',
        'sharePresets',

        'refreshDashboardData',
        'openPageReleasePosts',
        'openPagePriceDropChecks',
        'openPagePnrSchedule',
        'viewRefundablePriceDropChecks',
        'generateHotelPdf',

        // Agent report
        'openPageAgentReport',
        'openPageAgentReportTicketingRev',
        'canManageAgentReports',

        'useAltVpn',
        'openPageVoucher',

        'openPageElrFrtCheck',
        'openPageBets',

        'openPageMarkup',
        'openPageConsolidatorManagementTool',
        'editConsolidatorManagementTool',

        'openPageFlightHack',

        'manageRevenueAgentInvoices',

        // GDS Terminal
        'openPageGdsTerminal',

        // Pnr info
        'canEditPnrInfo',

        //Leave request

        'openLeaveRequestApproveList',

        'openPageVccManagement',
        'openQuickVpnLog',
        'createQuickVpn',

        'openPageAwardOffers',
        'markAsFraud',

        'openPageProductMetrics',

        'viewNewLeadCounter',
    ],

    'Lead': [
        // Backend
        'openPage',
        'approveTakeRequest',
        'requestExpertOnExpertsPage',
        'edit',
        'editTasks',
        'manage',
        'setAppointment',
        'unAssignAppointment',
        'takeNew',
        'takeNewForTeam',
        'view',
        'viewWorkingOnExpertLeads',
        'viewAppointmentLogs',
        'toggleAllClientMessages', // for toggle on lead chat Email tab
        'filterByCountryOrRegion',
        'viewLeadDuplicatesSection',
        'filterByExpertId',
        'viewExpertProcessingQueue',
    ],

    'PriceQuote': [
        'canEditMinimalMarkUp',
        'clone',
    ],

    'Client': [
        // Backend
        'openPage',
        'edit',
        'manage',
        'setAppointment',
        'view',
        'block',
    ],

    'Sale': [
        // Backend
        'openPage',
        'addCompanyExtraGP',
        'chatUploadFile',
        'delete',
        'edit',
        'editTasks',
        'editCards',
        'editPayments',
        'manage',
        'setAppointment',
        'view',
        'viewCards',
        'viewLogs',
        'viewSummary',
        'clone',
        'useAwardDetails',
        'manageAwardDetails',
        'viewSplits',
        'viewPendingSales',
        'viewRevTicketingRequest',
        'createRevTicketingRequest',
        'viewSaleMembers',
        'viewSalesSummaryMembers',
        'viewSaleCardTestAmount',
        'changeSaleDate',
        'editPnrFareType',
        'editPnrExchange', //@todo delete if not needed
        'viewVccUsage',
        'manualCardVerification',
        'createRequestHelp',
    ],

    'Agent': [
        // Backend
        'openPage',
        'manage',
        'edit', // With model only
        'create',
        'view',
        'employ', // Hire and Fire
        'editTarget',
        'editExperienced',
        'gdsInfo',
        'viewAgentDetails', // Customer Support Agent access restriction
        'transferClients',
        'updateSkillCharts',
        'updateAgentSkillLevel',
        'viewSkillChart',
        'viewTimeTracking',
        'unlinkFromRingcentral',
        'editHiringSource', // has two additional fields on Agent form and column in list
    ],

    'DuplicateLead': [
        // Backend
        'openPage',
        'edit',
        'manage',
    ],

    'Issue': [
        'apply',
        'approve',
        'join',
        'manage',
        'view',
        'extend',
        'setExpirationDate',
        'setExpirationDateOnRequestList',
        'filterByCase',
        'assign',
        'unAssign',
    ],

    'Task': [
        'edit',
        'delete',
        'extend',
        'delay',
        'assign',
        'viewCompleteReason',
    ],

    'AgentReport': [
        'openPage',  // todo: remove
    ],
    'AgentInvoice': [
        'openPage',
    ],
    'LeadTakeRule': [
        'openPage',
        'view',
        'edit',
    ],
    'ReleasePost': [
        'openPage',
        'view',
        'edit',
        'create',
        'approve',
        'publish',
        'reject',
    ],
    'Department': [
        'editAgentReports',
    ],
    'Voucher': [
        'create',
        'edit',
        'editSentVoucher',
        'manageVoucher',
        'delete',
    ],
    'GamblingLot': [
        'edit',
    ],
    'AwardAccount': [
        'manage',
        'viewRcpm',
    ],
    'LeaveRequest': [
        'openPage',
        'approve',
        'viewPersonalBalance',
    ],
    'SignedDocument': [
        'create',
    ],
} as const
