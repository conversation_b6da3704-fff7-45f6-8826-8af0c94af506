import AppService from '~/service/AppService'
import { onAppReady } from '~/composables/onAppReady'
import { useGlobalModal } from '~/composables/useGlobalModal'
import type { ModelAttributes } from '~types/lib/Model'
import { ReleasePostStatus } from '~/api/models/ReleasePost/ReleasePost'

export default class ReleaseCheckService extends AppService {
    public authRequired = true

    declare private releaseInfo

    public lastNotedReleasePost = useAppSettingWithControl('lastNotedReleasePost')
    protected lastViewedReleaseNumber = useLocalStorage(`state:last-viewed-release:${getDefaultUserPk()}`, 0)

    public async register() {
        const workspace = useService('workspace').getUserDefaultWorkspace().pk

        this.releaseInfo = useModel('ReleasesInfo', { http: { workspace } }).useRecord().destructable()
        await this.releaseInfo.fetch('all')

        const releasePostModel = useModel('ReleasePost', { http: { workspace } })

        if (this.lastNotedReleasePost.value.value !== this.releaseInfo.record.value.last_published_release_number && this.releaseInfo.record.value.last_published_release_number !== this.lastViewedReleaseNumber.value) {
            const availableReleases = await releasePostModel.useList({
                where: (and) => {
                    and.gt('id', this.lastNotedReleasePost.value.value ?? 0)
                    and.eq('status', ReleasePostStatus.Published)
                },
                customParams: {
                    scenario: 'modal',
                },
            }).fetch()

            if (availableReleases.length) {
                onAppReady(() => {
                    useGlobalModal('ReleasePostModal').open()
                }, { whenAuthorized: true })
            } else {
                await this.lastNotedReleasePost.save(this.releaseInfo.record.value.last_published_release_number)
            }
        }
    }

    public unregister() {
        //
    }

    public async updateLastNotedReleasePost(releasePost: ModelAttributes<'ReleasePost'>) {
        await this.lastNotedReleasePost.save(releasePost.id)
    }

    public updateLastViewedRelease(releasePost: ModelAttributes<'ReleasePost'>) {
        this.lastViewedReleaseNumber.value = releasePost.id
    }

    public isLastReleasePost(releasePost: ModelAttributes<'ReleasePost'>) {
        return releasePost.id == this.releaseInfo.record.value.last_published_release_number
    }

    public isNotedReleasePost(releasePost: ModelAttributes<'ReleasePost'>) {
        return releasePost.id <= (this.lastNotedReleasePost.value.value ?? 0)
    }
}

