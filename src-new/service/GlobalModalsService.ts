// noinspection JSIgnoredPromiseFromCall

import AppService from '~/service/AppService'
import type { App, ComponentPublicInstance } from 'vue'
import type { RouteLocationNormalizedLoaded, Router } from 'vue-router'
import AgentInfoSideModal from '~/sections/Agent/modals/AgentInfoSideModal.vue'
import { withCurrentQuery } from '@/lib/core/helper/RouteNavigationHelper'
import ReleasePostModal from '~/modals/ReleasePost/ReleasePostModal.vue'
import ReleasePostItemModal from '~/modals/ReleasePost/ReleasePostItemModal.vue'
import ClientInfoSideModal from '~/sections/Client/modals/ClientInfoSideModal.vue'

/**
 * @important Modals without workspace specified will be opened in SELECTED workspace.
 * @important Make sure you open modals that create context by themselves using passed props.
 */
export const globalModals = {
    'AgentInfoSideModal': {
        template: 'agent/edit/:pk',
        resolver(pk: string) {
            useModal(AgentInfoSideModal).open({ pk }, {
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                onClose: () => this.closeModal(),
            })
        },
        aliasRoute: 'agents.edit',
        aliasCloseRoute: 'agents',
    },
    'AgentInfoSideModalHR': {
        template: 'agent-hr/edit/:pk',
        resolver(pk: string) {
            useModal(AgentInfoSideModal).open({ pk }, {
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                onClose: () => this.closeModal(),
            })
        },
        aliasRoute: 'agents.hr.edit',
        aliasCloseRoute: 'agents.hr',
    },
    'IssueSideModal': {
        template: 'requests/:pk',
        async resolver(pk: string) {
            const issueModal = useModal((await import('~/components/Page/Issue/IssueSideModal.vue')).default)

            await issueModal.open({ pk }, {
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                onClose: () => this.closeModal(),
            })
        },
    },
    'ReleasePostModal': {
        template: 'releasePost',
        async resolver() {
            const releasePostModal = useModal(ReleasePostModal)

            await releasePostModal.open({}, {
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                onClose: () => this.closeModal(),
            })
        },
    },
    'ReleasePostItemModal': {
        template: 'releasePostItem/:postPk/:replyPk',
        async resolver(postPk: PrimaryKey, replyPk: PrimaryKey) {
            const releasePostItemModal = useModal(ReleasePostItemModal)

            await releasePostItemModal.open({
                releasePostItemPk: postPk,
                replyPk: replyPk,
            }, {
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                onClose: () => this.closeModal(),
            })
        },
    },

    'MarkupRuleModal': {
        template: 'markup/:category/:id',
        async resolver(category: string, id: string) {
            let modal

            if (category === 'offer') {
                modal = useModal((await import('@tmg/markup-tool-frontend/modals/MarkupOfferRuleModal.vue')).default)
            } else if (category === 'baggage') {
                modal = useModal((await import('@tmg/markup-tool-frontend/modals/MarkupBaggageRuleModal.vue')).default)
            } else if (category === 'flightHack') {
                modal = useModal((await import('#/packages/@tmg/markup-tool-frontend/src/modals/FlightHackModal.vue')).default)
            }

            if (!modal) {
                return
            }

            await modal.open({ id }, {
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                onClose: () => this.closeModal(),
            })
        },
        aliasRoute: 'markup.edit',
        aliasCloseRoute: 'markup.list',
    },
    'MarkupRuleCreateModal': {
        template: 'markup/:category/create',
        async resolver(category: string) {
            let modal

            if (category === 'offer') {
                modal = useModal((await import('@tmg/markup-tool-frontend/modals/MarkupOfferRuleModal.vue')).default)
            } else if (category === 'baggage') {
                modal = useModal((await import('@tmg/markup-tool-frontend/modals/MarkupBaggageRuleModal.vue')).default)
            } else if (category === 'flightHack') {
                modal = useModal((await import('#/packages/@tmg/markup-tool-frontend/src/modals/FlightHackModal.vue')).default)
            }

            if (!modal) {
                return
            }

            await modal.open({}, {
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                onClose: () => this.closeModal(),
            })
        },
        aliasRoute: 'markup.create',
        aliasCloseRoute: 'markup.list',
    },
    'FlightHackModal': {
        template: 'flight-hack/:id',
        async resolver(id: string) {
            await (useModal((await import('#/packages/@tmg/markup-tool-frontend/src/modals/FlightHackModal.vue')).default)).open({ id }, {
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                onClose: () => this.closeModal(),
            })
        },
        aliasRoute: 'flight-hack.edit',
        aliasCloseRoute: 'flight-hack.list',

    },
    'FlightHackCreateModal': {
        template: 'flight-hack/create',
        async resolver() {
            await (useModal((await import('#/packages/@tmg/markup-tool-frontend/src/modals/FlightHackModal.vue')).default)).open({}, {
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                onClose: () => this.closeModal(),
            })
        },
        aliasRoute: 'flight-hack.create',
        aliasCloseRoute: 'flight-hack.list',
    },
    'ClientInfoSideModal': {
        template: 'client/edit/:pk',
        resolver(pk: string) {
            useModal(ClientInfoSideModal).open({ pk }, {
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                onClose: () => this.closeModal(),
            })
        },
        aliasRoute: 'clients.edit',
        aliasCloseRoute: 'clients',
    },
} satisfies Record<string, Mapper>

export type GlobalModalName = keyof typeof globalModals

export default class GlobalModalsService extends AppService {
    private declare unwatch: () => void

    declare private router: Router

    public register(app: App) {
        this.router = (app as unknown as ComponentPublicInstance).$router

        this.initAliasMappings()

        this.unwatch = watch(() => this.router.currentRoute.value, this.onRouteChange.bind(this), { immediate: true })
    }

    public unregister() {
        this.unwatch()
    }

    //

    protected aliasMappings = new Map<string, {
        name: GlobalModalName
        closeRoute: string
    }>()

    protected initAliasMappings() {
        for (const [name, { aliasRoute, aliasCloseRoute }] of Object.entries(globalModals as Record<string, Mapper>)) {
            if (aliasRoute) {
                this.aliasMappings.set(aliasRoute, {
                    name: name as GlobalModalName,
                    closeRoute: aliasCloseRoute ?? aliasRoute,
                })
            }
        }
    }

    /**
     * We store data in "modal" query param in format:
     * /?modal={someQueryToParse}
     * For example: modal=agent/edit/1
     * Then we parse params and pass them to a mapper, that will decide what to do with them.
     * Every mapper should have a regex to check if params are valid.
     */
    private onRouteChange(route: RouteLocationNormalizedLoaded) {
        const alias = this.aliasMappings.get(route.name as string)

        if (alias) {
            useLogger('global-modals').log('Hit modal alias', 'Route:', route.name, 'Alias:', alias)

            const mapper = globalModals[alias.name] as Mapper

            mapper.resolver.bind({
                closeModal: () => {
                    this.router.push(withCurrentQuery({ name: alias.closeRoute }))
                },
            })(...(Object.values(route.params) as string[]))

            return
        }

        const query = route.query.modal

        // We do not work with many modals at the same time
        const rawValue = Array.isArray(query) ? query[0] : query

        if (!rawValue) {
            return
        }

        const mappers: [modalName: string, mapper: Mapper][] = Object.entries(globalModals)

        for (const [modalName, {
            template,
            resolver,
        }] of mappers) {
            const regex = new RegExp(`^${template.replaceAll(/:(\w+)/g, () => '(\\w+)')}$`)
            const match = rawValue.match(regex)

            if (match) {
                useLogger('global-modals').log('Hit modal mapper', 'Regex:', regex, 'Parsed value:', rawValue)

                // Get rid of full match
                resolver.bind({
                    closeModal: () => this.closeModal(modalName as GlobalModalName),
                })(...match.slice(1))

                break
            }
        }
    }

    protected runResolver<TModalName extends GlobalModalName>(name: TModalName, ...args: string[]) {
        const mapper = globalModals[name] as Mapper

        mapper.resolver.bind({
            closeModal: () => this.closeModal(name),
        })(...args)
    }

    //

    public openModal<TModalName extends GlobalModalName>(name: TModalName, ...params: Parameters<typeof globalModals[TModalName]['resolver']>) {
        const mapper = globalModals[name] as Mapper
        const paramNames = mapper.template.match(/:(\w+)/g)?.map(name => name.slice(1)) ?? []

        let url = mapper.template

        for (const [index, param] of params.entries()) {
            url = url.replace(`:${paramNames[index]}`, param)
        }

        return this.router.push({ query: { ...this.router.currentRoute.value.query, modal: url } })
    }

    // @todo Handle many modals at the same time
    public closeModal<TModalName extends GlobalModalName>(name: TModalName) {
        return this.router.push({ query: { ...this.router.currentRoute.value.query, modal: undefined } })
    }

    public closeAllModals() {
        this.router.push({ query: { modal: undefined } })
    }
}

type Mapper = {
    template: string
    resolver: (this: {
        closeModal(): void
    }, ...args: string[]) => void

    aliasRoute?: string
    aliasCloseRoute?: string
}
