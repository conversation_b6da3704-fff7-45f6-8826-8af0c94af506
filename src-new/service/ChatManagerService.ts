import AppService from '~/service/AppService'
import InternalChatServiceV2 from '@/modules/chat/service/InternalChatServiceV2'
import type ChatServiceV2 from '@/modules/chat/service/ChatServiceV2'
import { ChatGatewayDriver } from '~/api/models/Chat/ChatGateway'
import ChatStorageV2 from '@/modules/chat/storage/ChatStorageV2'
import type { ModelAttributes } from '~types/lib/Model'
import RingcentralChatServiceV2 from '@/modules/chat/service/RingcentralChatServiceV2'
import AssistantChatService from '@/modules/chat/service/AssistantChatService'

export type ChatServiceKey = PrimaryKey

export default class ChatManagerService extends AppService {
    private drivers = {
        [ChatGatewayDriver.Internal]: InternalChatServiceV2,
        [ChatGatewayDriver.InternalWithGroups]: InternalChatServiceV2,
        [ChatGatewayDriver.RingCentral]: RingcentralChatServiceV2,
        [ChatGatewayDriver.Assistant]: AssistantChatService,
    } satisfies {
        [Key in ChatGatewayDriver]: typeof ChatServiceV2<any>
    }

    public authRequired = true

    public async register() {
        this.initServices()

        await this.registerServices()

        this.watchForRefs()
    }

    public async unregister() {
        await this.unregisterServices()

        this.stopWatchForRefs()
        this.cleanupServices()
    }

    // ==========================

    private services = new Map<ChatServiceKey, ChatServiceV2>()

    public get availableServices() {
        return Array.from(this.services.values())
    }

    public storage = new ChatStorageV2()

    private initServices() {
        const workspaces = useService('workspace').availableWorkspaces

        for (const workspace of workspaces) {
            const gatweays = useDictionary('ChatGateway', {
                workspace: workspace.pk,
            }).records

            for (const gateway of gatweays) {
                const driver = this.drivers[gateway.driver as keyof typeof this.drivers]

                if (!driver) {
                    throw new Error(`Chat Driver ${gateway.driver} not found`)
                }

                const gatewayKey = usePk(gateway)

                const serviceInstance = new driver(gatewayKey, gateway)

                this.services.set(gatewayKey, serviceInstance)
            }
        }
    }

    private cleanupServices() {
        this.services.clear()
    }

    private async registerServices() {
        for (const gateway of this.services.values()) {
            await gateway.register()
        }
    }

    private async unregisterServices() {
        for (const gateway of this.services.values()) {
            await gateway.unregister()
        }
    }

    // ==========================

    public getService(serviceKey: ChatServiceKey) {
        return this.services.get(serviceKey)
    }

    public getServiceOrFail(serviceKey: ChatServiceKey) {
        const service = this.getService(serviceKey)

        if (!service) {
            throw new Error(`Chat Gateway ${serviceKey} not found`)
        }

        return service
    }

    public getChat(record: ModelAttributes<'Chat'>) {
        const pk = usePk(record)

        return pk ? this.storage.get(pk) : undefined
    }

    public getOrCreateChat(record: ModelAttributes<'Chat'>) {
        const serviceKey = record.gateway_pk

        const service = this.getServiceOrFail(serviceKey)

        const pk = usePk(record)

        let chat = this.storage.get(pk)

        if (chat) {
            return chat
        }

        chat = service.createChat(record)

        this.storage.set(pk, chat)

        return chat
    }

    // ===============

    private watchInterval: ReturnType<typeof setInterval> | undefined = undefined

    private watchForRefs() {
        // Check all chat services every 5 seconds to clean up them from memory
        setInterval(() => {
            this.storage.checkForActivity()
        }, Timespan.seconds(5).inMilliseconds)
    }

    private stopWatchForRefs() {
        if (this.watchInterval) {
            clearInterval(this.watchInterval)

            this.watchInterval = undefined
        }
    }
}

