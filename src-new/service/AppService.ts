import type { App } from 'vue'

export default abstract class AppService {
    public manualRegistration = false
    public authRequired = false

    //

    protected disposables: (() => void)[] = []

    protected callDisposables() {
        for (const disposable of this.disposables) {
            disposable()
        }

        this.disposables = []
    }

    protected registerDisposable(disposable: () => void) {
        this.disposables.push(disposable)
    }

    //

    // Called in App.vue on app initialization
    public abstract register(app: App): void | Promise<void>

    // Called in App.vue on app destruction
    public abstract unregister(app: App): void | Promise<void>
}
