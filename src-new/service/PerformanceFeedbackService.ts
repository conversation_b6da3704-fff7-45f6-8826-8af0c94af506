import AppService from '~/service/AppService'
import PerformanceFeedbackReportModal from '~/sections/PerformanceFeedback/modals/PerformanceFeedbackReportModal.vue'
import type { WritableComputedRef } from 'vue'
import { BroadcastMessageType } from '~/service/BroadcastService'
import type ActivityService from '~/service/ActivityService'
import { randomInt } from '~/lib/Helper/NumberHelper'

const ONE_HOUR = Timespan.hour().inSeconds
const MAX_COUNTER = 2
const MODAL_HOUR = 12
const MIN_ACTIVITY_FOR_MODAL = Timespan.minutes(15).inSeconds
const STORAGE_KEY = 'performanceFeedbackState'

interface FeedbackState {
    delayTill: number | null
    scheduleTill: number | null
    closeCount: number | null
}

function isValidTimestamp(value: unknown): value is number {
    return typeof value === 'number' && value > 0
}

export default class PerformanceFeedbackService extends AppService {
    public authRequired = true
    private isModalOpen = false
    private appPerformanceFeedbackSetting!: WritableComputedRef<FeedbackState>
    private broadcastService!: { post: (message: { type: BroadcastMessageType, payload?: unknown }) => void }
    private activityService: ActivityService
    private modalInstance: ReturnType<typeof useModal>
    private modalTimeoutId: NodeJS.Timeout | number | null = null

    private feedbackState: FeedbackState = {
        delayTill: null,
        scheduleTill: null,
        closeCount: null,
    }

    public register() {
        if (window.command) {
            window.command.openAgentReviewModal = this.triggerModalOpen.bind(this)
        }

        this.modalInstance = useModal(PerformanceFeedbackReportModal, {}, { keepOnRouteChange: true, closeOnEsc: false })
        this.broadcastService = useService('broadcast')
        this.activityService = useService('activity')
        this.appPerformanceFeedbackSetting = useAppSetting<'performanceFeedbackState'>(STORAGE_KEY)

        this.feedbackState = this.appPerformanceFeedbackSetting.value ?? { delayTill: null, scheduleTill: null, closeCount: null }
        this.appPerformanceFeedbackSetting.value ??= this.feedbackState

        this.isModalOpen = false
        this.checkAndScheduleModal()
    }

    public async unregister() {
        if (this.modalTimeoutId) {
            clearTimeout(this.modalTimeoutId)
            this.modalTimeoutId = null
        }

        this.closeModal()
    }

    public triggerModalOpen() {
        if (!document.hidden) {
            this.broadcastService.post({ type: BroadcastMessageType.OpenAgentReviewModal })
        }
    }

    public closeModal() {
        this.broadcastService.post({ type: BroadcastMessageType.CloseAgentReviewModal })
    }

    public internalOpenModal() {
        if (this.isModalOpen) return
        this.isModalOpen = true

        this.modalInstance.open({
            onSubmit: () => {
                this.handleModalClose('submit')
            },
            onManualClose: () => {
                this.handleModalClose('manual')
            },
            onDontShowAgain: () => {
                this.handleModalClose('dont-show-again')
            },
        })
    }

    public internalCloseModal() {
        this.isModalOpen = false
        this.modalInstance.close()
    }

    private handleModalClose(reason: 'submit' | 'manual' | 'dont-show-again') {
        switch (reason) {
            case 'submit':
                this.setDelayTill()
                break
            case 'manual': {
                const newCounter = (this.feedbackState.closeCount ?? 0) + 1
                this.setFeedbackState({ closeCount: newCounter })
                newCounter >= MAX_COUNTER ? this.setDelayTill() : this.scheduleModalWithRandomDelay()
                break
            }
            case 'dont-show-again':
                this.setDelayTill()
                break
        }
        this.closeModal()
    }

    private setDelayTill() {
        const date = new Date()
        const targetDate = new Date()

        if (date.getHours() >= MODAL_HOUR) targetDate.setDate(date.getDate() + 1)
        targetDate.setHours(MODAL_HOUR, 0, 0, 0)

        this.setFeedbackState({
            delayTill: Math.floor(targetDate.getTime() / 1000),
            scheduleTill: null,
            closeCount: 0,
        })
    }

    private getRandomDelaySeconds(): number {
        return randomInt(2 * ONE_HOUR, 3 * ONE_HOUR + 1)
    }

    private scheduleModalOpen(delaySeconds: number) {
        if (this.modalTimeoutId) {
            clearTimeout(this.modalTimeoutId)
            this.modalTimeoutId = null
        }

        this.modalTimeoutId = setTimeout(() => {
            this.triggerModalOpen()
            this.modalTimeoutId = null
        }, delaySeconds * 1000)
    }

    private scheduleModalWithRandomDelay() {
        const delaySeconds = this.getRandomDelaySeconds()
        this.setFeedbackState({ scheduleTill: Math.floor(Date.now() / 1000) + delaySeconds })
        this.scheduleModalOpen(delaySeconds)
    }

    private checkAndScheduleModal() {
        const now = Math.floor(Date.now() / 1000)

        if (this.checkDelayBlock(now)) return

        if (this.checkCounterLimit()) return

        if (this.openModalIfScheduled(now)) return

        if (this.feedbackState.scheduleTill) return

        this.scheduleModalWithRandomDelay()
    }

    private checkDelayBlock(now: number): boolean {
        if (isValidTimestamp(this.feedbackState.delayTill)) {
            if (now < this.feedbackState.delayTill) return true
            this.setFeedbackState({ delayTill: null })
        }

        return false
    }

    private checkCounterLimit(): boolean {
        if ((this.feedbackState.closeCount ?? 0) >= MAX_COUNTER) {
            this.setDelayTill()

            return true
        }

        return false
    }

    private openModalIfScheduled(now: number): boolean {
        if (isValidTimestamp(this.feedbackState.scheduleTill) && now >= this.feedbackState.scheduleTill) {
            const activityDuration = this.activityService.getActivityDuration()

            if (activityDuration < MIN_ACTIVITY_FOR_MODAL) {
                this.setFeedbackState({ scheduleTill: Math.floor(Date.now() / 1000) + MIN_ACTIVITY_FOR_MODAL - activityDuration })

                this.scheduleModalOpen(MIN_ACTIVITY_FOR_MODAL - activityDuration)

                return true
            }

            this.triggerModalOpen()

            return true
        }

        return false
    }

    private setFeedbackState(state: Partial<FeedbackState>) {
        this.feedbackState = { ...this.feedbackState, ...state }
        this.appPerformanceFeedbackSetting.value = { ...this.appPerformanceFeedbackSetting.value, ...state }
    }
}

declare global {
    interface GlobalCommands {
        openAgentReviewModal: () => void
    }
}
