import { useStorage } from '@vueuse/core'
import AppService from '~/service/AppService'

const secondsToBeInactive = Timespan.minute().inSeconds
const ONLINE_INTERVAL_SEC = Timespan.minutes(15).inSeconds

const trackedEvents = ['mousemove', 'keydown', 'touchstart']

export default class ActivityService extends AppService {
    public register() {
        this.track()
    }

    public unregister() {
        this.stopTracking()
    }

    // ======================

    // In seconds
    private lastActivityTime = useStorage('last-activity', 0)
    private firstActivityTime = useStorage('first-activity', 0)

    constructor() {
        super()

        this.recordActivity()
    }

    public getLastActivityTime(): number {
        return this.lastActivityTime.value
    }

    public getLastActivityTimeInMilliseconds(): number {
        return this.lastActivityTime.value * 1000
    }

    public getFirstActivityTime(): number {
        return this.firstActivityTime.value
    }

    public track(): void {
        for (const event of trackedEvents) {
            window.addEventListener(event, this.onActivity.bind(this))
        }
    }

    public stopTracking(): void {
        for (const event of trackedEvents) {
            window.removeEventListener(event, this.onActivity.bind(this))
        }
    }

    private onActivity(): void {
        const now = Date.currentUnixTimestamp()

        // Throttle for half of the time to be inactive to prevent too many updates
        if (this.lastActivityTime.value < now + secondsToBeInactive / 2) {
            this.recordActivity()
        }
    }

    public recordActivity(): void {
        const now = Date.currentUnixTimestamp()
        const oldActivity = this.lastActivityTime.value
        const newActivity = now + secondsToBeInactive

        this.lastActivityTime.value = newActivity

        if (!this.firstActivityTime.value) {
            this.firstActivityTime.value = now
        } else if (newActivity - oldActivity > ONLINE_INTERVAL_SEC) {
            this.firstActivityTime.value = now
        }
    }

    public getActivityDuration(): number {
        const now = Date.currentUnixTimestamp()

        return now - this.getFirstActivityTime()
    }
}
