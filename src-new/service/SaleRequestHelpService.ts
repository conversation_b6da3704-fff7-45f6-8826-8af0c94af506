import AppService from '~/service/AppService'
import TakeSaleRequestHelpModal from '~/modals/Sale/TakeSaleRequestHelpModal.vue'
import { DepartmentName } from '~/api/models/Department/Department'
import type { ModelRef } from '~types/lib/Model'

export default class SaleRequestHelpService extends AppService {
    public authRequired = true
    private isCurrentUserExpert: boolean = false

    public async register() {
        const workspaces = useService('workspace').availableWorkspaces.map((workspace) => workspace.pk)
        this.isCurrentUserExpert = this.isCurrentUserFromExpertDepartment()

        if (this.isCurrentUserExpert) {
            for (const workspace of workspaces) {
                await this.getSaleRequestHelpListRecords(workspace)
            }
        }
    }

    public unregister() {
        this.callDisposables()
    }

    //

    protected _agentPk: PrimaryKey | undefined

    protected get agentPk() {
        return this._agentPk ||= getDefaultUserPk()
    }

    //

    protected get workspace() {
        return useService('workspace').getUserDefaultWorkspace().pk as DefinedWorkspace
    }

    //

    public isCurrentUserFromExpertDepartment(): boolean {
        const agentDictionary = useDictionary('Agent', {
            workspace: this.workspace,
        })

        const currentUser = agentDictionary.findOrFail(this.agentPk)

        return isUserInDepartment(currentUser, DepartmentName.Experts)
    }

    //

    public async getSaleRequestHelpListRecords(workspacePk: DefinedWorkspace) {
        const saleRequestHelpRecords = useDictionary('SaleRequestHelp', {
            workspace: workspacePk,
        })

        const stop = watch(() => saleRequestHelpRecords.records.length, (length) => {
            if (this.isCurrentUserExpert && length > 0) {
                this.openModal(saleRequestHelpRecords.records, workspacePk)
            }
        }, { immediate: true })

        this.registerDisposable(stop)
    }

    //

    public openModal(records: ModelRef<'SaleRequestHelp'>[], workspacePk: DefinedWorkspace) {
        const modal = useModal(TakeSaleRequestHelpModal, {}, { keepOnRouteChange: true, position: 'center', blur: true, closeOnOverlay: false, overlay: true, closeOnEsc: false, workspace: workspacePk })
        modal.open({ records })
    }
}
