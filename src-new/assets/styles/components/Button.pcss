@layer components {
    .button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        font-weight: 500;
        line-height: 1rem;
        font-style: normal;

        cursor: pointer;
        border: 1px solid;
        box-sizing: border-box;
        border-radius: var(--border-radius);
        @apply transition duration-100;

        --tw-ring-color: var(--ring-color);

        /**
         * Default styles
         */
        &, &.\--default {
            --text-color-on-light: theme('colors.secondary.900');
            --text-color-on-dark: theme('colors.white');

            /* Default */
            background: theme('colors.white');
            color: var(--text-color);
            border-color: var(--color);

            --text-color: var(--text-color-on-light);
            --color: theme('colors.secondary.100');
            /*noinspection CssInvalidFunction*/
            --color-lightest: rgba(theme('colors.secondary.100/raw'), 0.1);
            --color-hover: theme('colors.secondary.50');
            --ring-color: var(--color-hover);
            --color-disabled: theme('colors.secondary.300');
        }

        /**
         * Sizes
         * ===========================
         */
        &.\--xs {
            --size: var(--size-xs);
            height: var(--size);
            padding: 0.25rem 0.5rem;
            font-size: 0.6875rem;
            gap: 0.25rem;
            --border-radius: var(--border-radius-xs)
        }

        &.\--small {
            --size: var(--size-small);
            height: var(--size);
            padding: 0.375rem 0.625rem;
            font-size: 0.75rem;
            gap: 0.25rem;
        }

        &, &.\--normal { /* Default */
            --size: var(--size-normal);
            height: var(--size);
            padding: 0.5rem 0.75rem;
            font-size: 0.75rem;
            gap: 0.375rem;
        }

        &.\--large {
            --size: var(--size-large);
            height: var(--size);
            padding: 0.625rem 0.875rem;
            font-size: 0.875rem;
            gap: 0.5rem;
        }

        &.\--xl {
            --size: var(--size-xl);
            height: var(--size);
            padding: 0.75rem 1rem;
            font-size: 1rem;
            gap: 0.625rem;
        }

        /**
         * States
         * ===========================
         */
        &:hover:not(:disabled,.\--no-hover) {
            color: var(--text-color);
            background: var(--color-hover);
        }

        &:focus {
            @apply ring-[3px] outline-none;

            .dark & {
            }
        }

        &:disabled {
            /* --color: var(--color-disabled); */
            opacity: 0.5;
            cursor: not-allowed;
        }

        &.\--no-hover {
            cursor: default;
        }

        /**
         * Variants
         * ===========================
         */

        /*
         * --only - DEPRECATED
         */
        &.\--only, &.\--square {
            padding: 0;
            width: var(--size);
        }

        &.\--rounded {
            border-radius: 180px;
        }

        /*
         * --ghost - DEPRECATED
         */
        &.\--ghost, &.\--soft {
            background: transparent;
            border-color: transparent;
            /*noinspection CssInvalidFunction*/
            --color-hover: rgba(theme('colors.secondary.300/raw'), 0.1);

            .dark & {
                --text-color: var(--text-color-on-dark);

                .button__count {
                    color: var(--text-color-on-light);
                }
            }

            .dark &.\--secondary {
                --text-color: var(--text-color-on-dark);
            }
        }

        &.\--bordered {
            border-color: var(--color-hover);
        }

        &.\--lifted {
            @apply shadow;
        }

        /**
         * Colors
         * ===========================
         */
        &:where(
            .\--primary,
            .\--secondary,
            .\--neutral,
            .\--muted,
            .\--success,
            .\--danger,
            .\--warning,
            .\--info,
            .\--pending
        ) {
            --text-color: var(--text-color-on-dark);
            color: var(--text-color);
            background: var(--color);
            border-color: var(--color);

            &.\--outline {
                border-width: 2px;
                background: transparent;
                color: var(--color);
                border-color: var(--color);

                &:hover:not(:disabled) {
                    color: var(--color);
                    background: var(--color-lightest);
                }
            }

            &.\--ghost, &.\--soft {
                --text-color: var(--color);
                --color-hover: var(--color-lightest);
            }
        }

        &.\--danger-lite {
            --text-color-on-light: theme('colors.danger.500');
        }

        /**
         * Inner parts
         * ===========================
         */
        &__count {
            font-size: 0.625rem;
            height: 1rem;
            display: flex;
            justify-content: center;
            align-items: center;
            line-height: 0.75rem;
            padding: 0 0.25rem;
            border-radius: 0.25rem;
            background: theme('colors.secondary.50');
            gap: 0.125rem;

            .button:where(
                .\--primary,
                .\--secondary,
                .\--neutral,
                .\--muted,
                .\--success,
                .\--danger,
                .\--warning,
                .\--info,
                .\--pending
            ) & {
                color: var(--color-hover);
            }

            --icon-size: 0.75rem;
        }

        /**
         * Icons
         * ===========================
         */
        svg:not(.icon) {
            width: var(--icon-size);
            height: var(--icon-size);
        }

        &.\--xs svg {
            --icon-size: var(--icon-size-2xs);
        }

        &.\--small svg {
            --icon-size: var(--icon-size-xs);
        }

        &, &.\--normal svg { /* Default */
            --icon-size: var(--icon-size-small);
        }

        &.\--large svg {
            --icon-size: var(--icon-size-normal);
        }

        &.\--xl svg {
            --icon-size: var(--icon-size-large);
        }
    }
}
