<template>
    <AppModalWrapper
        :header="header || title || 'Email preview'"
        close-button
    >
        <div class="border-t">
            <EmailTemplatePreview class="h-[720px]" :email-template="emailTemplate" />
        </div>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import EmailTemplatePreview from '~/components/Page/Email/EmailTemplatePreview.vue'
import { EmailTemplateName } from '~/api/models/Email/EmailTemplate'

defineOptions({
    name: 'EmailTemplatePreviewModal',
})

const props = defineProps<{
    emailTemplate: EmailTemplateName,
    header?: string,
}>()

const title = computed(() => {
    if (props.emailTemplate === EmailTemplateName.FollowUpIntro) {
        return 'Follow-up intro email preview'
    } else if (props.emailTemplate === EmailTemplateName.FollowUpTravelCash) {
        return 'TravelCash email preview'
    } else if (props.emailTemplate === EmailTemplateName.FollowUp1) {
        return '1<sup>st</sup> follow-up email preview'
    } else if (props.emailTemplate === EmailTemplateName.FollowUp2) {
        return '2<sup>nd</sup> follow-up email preview'
    } else if (props.emailTemplate === EmailTemplateName.FollowUp3) {
        return '3<sup>rd</sup> follow-up email preview'
    }
})
</script>
