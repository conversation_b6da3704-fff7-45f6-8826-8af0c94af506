<template>
    <AppModalWrapper
        close-button
        header="Create Hotel Booking PDF"
        class="!w-[1200px] !max-w-[1200px]"
    >
        <div class="h-full flex flex-row">
            <div class="p-1.5 bg-secondary-50 dark:bg-neutral-950 gap-1.5 flex flex-row h-full w-full">
                <div class="card w-[540px]">
                    <div class="card__header card__title">
                        Booking information
                    </div>
                    <div class="card__body text-xs">
                        <div class="grid grid-cols-2 gap-5">
                            <FormField
                                :form="form"
                                :field="'hotel'"
                                label="Hotel name"
                            >
                                <InputText v-model="form.data.hotel" />
                            </FormField>
                            <FormField
                                :form="form"
                                :field="'dates'"
                                label="Stay dates"
                            >
                                <InputDateRange
                                    v-model="form.data.dates"
                                    :date-format="'dd/MM/yyyy'"
                                    :min-date="Date.now()"
                                    timezone="UTC"
                                />
                            </FormField>

                            <FormField
                                :form="form"
                                :field="'booking_number'"
                                label="Booking number"
                            >
                                <InputText v-model="form.data.booking_number" />
                            </FormField>
                            <FormField
                                :form="form"
                                :field="'booking_date'"
                                label="Booking date"
                            >
                                <InputDate v-model="form.data.booking_date" timezone="UTC" />
                            </FormField>

                            <FormField
                                :form="form"
                                :field="'booked_by'"
                                label="Booked by"
                            >
                                <InputText v-model="form.data.booked_by" />
                            </FormField>
                            <FormField
                                :form="form"
                                :field="'hotel_price'"
                                label="Hotel price"
                            >
                                <InputMoney
                                    v-model="form.data.hotel_price"
                                    min="0"
                                    show-currency
                                />
                            </FormField>

                            <FormField
                                :form="form"
                                :field="'room_description'"
                                label="Room description"
                            >
                                <InputText v-model="form.data.room_description" />
                            </FormField>
                            <FormField
                                :form="form"
                                :field="'tour_name'"
                                label="Tour name"
                            >
                                <InputText v-model="form.data.tour_name" />
                            </FormField>

                            <FormField
                                :form="form"
                                :field="'tour_price'"
                                label="Tour price"
                            >
                                <InputMoney
                                    v-model="form.data.tour_price"
                                    min="0"
                                    show-currency
                                />
                            </FormField>
                            <FormField
                                :form="form"
                                :field="'payment_information'"
                                label="Payment info"
                            >
                                <InputText v-model="form.data.payment_information" />
                            </FormField>
                            <div class="flex gap-4">
                                <FormField
                                    :form="form"
                                    :field="'check_in_time'"
                                    label="Check-in time"
                                >
                                    <InputDate
                                        v-model="form.data.check_in_time"
                                        picker-mode="time"
                                        timezone="UTC"
                                    />
                                </FormField>
                                <FormField
                                    :form="form"
                                    :field="'check_out_time'"
                                    label="Check-out time"
                                >
                                    <InputDate
                                        v-model="form.data.check_out_time"
                                        picker-mode="time"
                                        timezone="UTC"
                                    />
                                </FormField>
                            </div>

                            <FormField
                                :form="form"
                                :field="'guests'"
                                label="Guests"
                            >
                                <InputPassengers v-model="form.data.guests" class="gap-2 text-xs" />
                            </FormField>

                            <FormField
                                :form="form"
                                :field="'cancellation_policy'"
                                label="Cancellation policy"
                                class="col-span-2"
                            >
                                <InputTextarea
                                    v-model="form.data.cancellation_policy"
                                    :rows="5"
                                />
                            </FormField>
                        </div>
                    </div>
                </div>
                <div class="card flex flex-col h-auto flex-grow">
                    <div class="card__header card__title">
                        Preview
                    </div>
                    <div class="card__body h-flex">
                        <PlaceholderBlock
                            v-if="form.loading.value"
                            class="w-full h-full"
                        />
                        <iframe
                            v-else-if="previewUrl"
                            :src="previewUrl"
                            class="w-full h-full"
                        />
                        <div v-else class="flex items-center justify-center h-[500px]">
                            Please fill out the form to generate a preview.
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <template #footer="{ close }">
            <div class="flex justify-between">
                <AppButton @click="close">
                    Cancel
                </AppButton>
                <div class="flex gap-4">
                    <AppButton @click="previewPdf">
                        Update Preview
                    </AppButton>
                    <AppButton class="--primary" @click="downloadPdf">
                        Download PDF
                    </AppButton>
                </div>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup  lang="ts">
import FormField from '~/components/Form/FormField.vue'
import type { DateRange } from '@/lib/core/helper/DateHelper'
import { dateRangeToTimestampRange } from '@/lib/core/helper/DateHelper'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import { base64ToBlob } from '~/lib/Helper/File/FileHelper'
import { downloadBlob } from '@/lib/core/helper/File/FileHelpers'

const emit = defineEmits<{
    close: [],
}>()

const { useModel } = useContext()
const saleModel = useModel('Sale')

const form = useForm<{
    hotel: string
    dates: DateRange | null
    booking_number: string
    booking_date: Date | null
    booked_by: string
    hotel_price: number
    room_description: string
    payment_information: string
    check_in_time: Date
    check_out_time: Date
    guests: {
        adult_count: number
        child_count: number
        infant_count: number
    },
    cancellation_policy: string

    tour_name?: string
    tour_price?: number
}>({
    hotel: '',
    dates: null,
    booking_number: '',
    booking_date: null,
    booked_by: '',
    hotel_price: 0,
    room_description: '',
    payment_information: '',
    guests: {
        adult_count: 1,
        child_count: 0,
        infant_count: 0,
    },
    check_in_time: new Date(0),
    check_out_time: new Date(0),
    cancellation_policy: '',
}, {
    hotel: ValidationRules.Required(),
    dates: ValidationRules.Required(),
    booking_number: ValidationRules.Required(),
    booking_date: ValidationRules.Required(),
    booked_by: ValidationRules.Required(),
    hotel_price: ValidationRules.Required(),
    room_description: ValidationRules.Required(),
    payment_information: ValidationRules.Required(),
    check_in_time: ValidationRules.Required(),
    check_out_time: ValidationRules.Required(),
    guests: (guests) => {
        if (!guests.adult_count && !guests.child_count && !guests.infant_count) {
            return 'Guests are required'
        }
    },
    cancellation_policy: ValidationRules.Required(),
})

const previewUrl = ref('')

const previewPdf = form.useSubmit(async (data) => {
    const { url } = await saleModel.actions.previewHotelBookingPdf({
        hotel: data.hotel,
        dates: dateRangeToTimestampRange(data.dates!),
        booking_number: data.booking_number,
        booking_date: data.booking_date!.unixTimestamp(),
        booked_by: data.booked_by,
        hotel_price: data.hotel_price,
        room_description: data.room_description,
        tour_name: data.tour_name ?? null,
        tour_price: data.tour_price ?? null,
        payment_information: data.payment_information,
        check_in_time: `${data.check_in_time.toFormatUTC?.('HH:mm') ?? '00:00'}`,
        check_out_time: `${data.check_out_time.toFormatUTC?.('HH:mm') ?? '00:00'}`,
        guests: data.guests,
        cancellation_policy: data.cancellation_policy,
    })

    previewUrl.value = url
}, {
    resetOnSuccess: false,
})

const downloadPdf = form.useSubmit(async (data) => {
    const { result } = await saleModel.actions.downloadHotelBookingPdf({
        hotel: data.hotel,
        dates: dateRangeToTimestampRange(data.dates!),
        booking_number: data.booking_number,
        booking_date: data.booking_date!.unixTimestamp(),
        booked_by: data.booked_by,
        hotel_price: data.hotel_price,
        room_description: data.room_description,
        tour_name: data.tour_name ?? null,
        tour_price: data.tour_price ?? null,
        payment_information: data.payment_information,
        check_in_time: `${data.check_in_time.toFormatUTC?.('HH:mm') ?? '00:00'}`,
        check_out_time: `${data.check_out_time.toFormatUTC?.('HH:mm') ?? '00:00'}`,
        guests: data.guests,
        cancellation_policy: data.cancellation_policy,
    })

    downloadBlob(base64ToBlob(result), `HotelBooking_${data.booking_number}_${data.hotel}.pdf`)

    emit('close')
}, {
    resetOnSuccess: false,
})
</script>
