<template>
    <AppModalWrapper
        class="!max-w-[420px]"
        close-button
        header=""
    >
        <div class="p-5 pt-0 w-full flex flex-col gap-y-4 justify-center items-center">
            <div class="tip --warning p-0 border-none bg-white dark:bg-dark-3">
                <div class="tip-icon --warning">
                    <div class="tip-icon__inner">
                        <HelpCircleIcon class="tip-icon__inner__svg" />
                    </div>
                </div>
            </div>
            <div class="flex flex-col items-center gap-y-1 text-center">
                <span class="text-base font-semibold dark:text-secondary-50">Request Expert help</span>
                <span class="text-center max-w-[370px] text-secondary dark:text-secondary-300">Are you sure you want to request expert help for this sale?</span>
            </div>
            <div class="border-t w-full pt-4">
                <FormField
                    :form="form"
                    field="reason"
                    label="Describe your problem"
                    class="w-full text-xs text-secondary-900 dark:text-secondary-300 font-medium"
                    required
                >
                    <InputTextarea v-model="form.data.reason" placeholder="Problem description" />
                </FormField>
            </div>
        </div>
        <template #footer>
            <div class="p-1 grid grid-cols-2 gap-x-3">
                <AppButton class="--large" @click="close">
                    Cancel
                </AppButton>
                <AppButton class="--primary --large" @click="submit">
                    Send Request
                </AppButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import FormField from '~/components/Form/FormField.vue'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'

defineOptions({
    name: 'CreateSaleRequestHelpModal',
})

const props = defineProps<{
    salePk: PrimaryKey
}>()

const emit = defineEmits<{
    'close': []
}>()

const { useModel } = await useNewContext('Sale', props.salePk)

function close() {
    emit('close')
}

const form = useForm({
    reason: '',
}, {
    reason: ValidationRules.Required(),
})

const saleRequestHelpModel = useModel('SaleRequestHelp')

const submit = form.useSubmit(async (data) => {
    await saleRequestHelpModel.actions.createRequest({
        sale_pk: props.salePk,
        reason: data.reason,
    })
    toastSuccess('Request sent successfully.')
    close()
})
</script>
