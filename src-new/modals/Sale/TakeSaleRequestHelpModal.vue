<template>
    <AppModalWrapper
        class="!max-w-[420px]"
        :close-button="false"
    >
        <SaleRequestHelpInfo
            v-if="recordToShow"
            :record="recordToShow"
        />
    </AppModalWrapper>
</template>

<script setup lang="ts">
import type { ModelAttributes } from '~types/lib/Model'
import SaleRequestHelpInfo from '~/sections/Sale/components/SaleRequestHelpInfo.vue'

defineOptions({
    name: 'TakeSaleRequestHelpModal',
})

const props = defineProps<{
    records: ModelAttributes<'SaleRequestHelp'>[]
}>()

const emit = defineEmits<{
    'close': []
}>()

const recordToShow = computed(() => {
    if (props.records.length == 0) {
        emit('close')

        return
    }

    return props.records[0]
})
</script>
