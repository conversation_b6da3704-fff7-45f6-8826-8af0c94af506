<template>
    <AppModalWrapper
        class="!max-w-[1600px]"
        header="Visualization"
    >
        <TabsComponent
            v-model="activeTab"
            class="mx-4"
            :tabs="tabs"
        />
        <div class="card card__body--partholder">
            <NewQueueVisualizationStatistics v-if="activeTab === 'statistics'" />
            <NewQueueSettings v-if="activeTab === 'settings'" />
        </div>
    </AppModalWrapper>
</template>

<script lang="ts" setup>
import TabsComponent from '@/components/tabs/Tabs.vue'
import type Tab from '@/types/structures/Tab'
import NewQueueVisualizationStatistics from '~/modals/NewQueueVisualization/NewQueueVisualizationStatistics.vue'
import NewQueueSettings from '~/modals/NewQueueVisualization/NewQueueSettings.vue'

const tabs: Tab[] = [
    {
        id: 'statistics',
        title: 'Statistics',
    },
    {
        id: 'settings',
        title: 'Settings',
    },
] satisfies Tab[]

const activeTab = ref(tabs[0].id)
</script>

