<template>
    <div class="flex gap-4">
        <SuspenseManual :state="suspense">
            <template #default>
                <div class="card h-[700px] w-[500px] flex flex-col">
                    <div class="card__header">
                        <div class="card__title gap-4">
                            <TrendingUpIcon /> Top agents
                        </div>
                        <label class="flex justify-center items-center gap-2 text-2xs cursor-pointer select-none">
                            Current top
                            <InputSwitch v-model="showMightBeTopList" />
                            Might be top
                        </label>
                    </div>
                    <div class="card__body flex flex-col gap-4 h-flex overflow-auto fancy-scroll">
                        <div
                            v-for="(agent,index) of agentList"
                            :key="index"
                            class="flex gap-4"
                        >
                            <AgentListItem
                                :agent="agent"
                                :state="agent.state"
                                class="text-xs w-[180px]"
                            />

                            <div class="text-xs">
                                Conversion: <span class="font-bold">{{ agent.conversion?.toFixed(2) }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card h-[700px] w-full">
                    <div class="card__header">
                        <div class="card__title gap-4">
                            <SettingsIcon /> Settings
                        </div>
                        <AppButton
                            class="--success"
                            :disabled="!form.hasChanges"
                            :loading="form.loading"
                            @click="submit"
                        >
                            Save
                        </AppButton>
                    </div>
                    <form class="card__body grid grid-cols-5 gap-4" @submit="submit">
                        <FormField
                            :form="form"
                            field="top_agent_max_count"
                            label="Top agent count"
                        >
                            <InputNumber v-model="form.data.top_agent_max_count" />
                        </FormField>

                        <FormField
                            :form="form"
                            field="prediction_threshold"
                            label="Prediction threshold"
                        >
                            <InputNumber v-model="form.data.prediction_threshold" />
                        </FormField>
                    </form>
                </div>
            </template>
            <template #fallback>
                <PlaceholderBlock class="h-[700px] w-[315px]" />
                <PlaceholderBlock class="h-[700px] w-full" />
            </template>
        </SuspenseManual>
    </div>
</template>

<script setup lang="ts">
import AgentListItem from '~/sections/ReleasePost/components/AgentListItem.vue'
import FormField from '~/components/Form/FormField.vue'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import { NewQueueCategory } from '~/api/models/Agent/Agent'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import type { ActionResponse } from '~types/lib/Model'

const { useModel } = useContext()

const leadModel = useModel('Lead')
const agentModel = useModel('Agent')

const showMightBeTopList = ref(false)
const topAgentMaxCountInitial = ref(0)

const topAgentsSortedList = computed(() => {
    return  topAgents.records
        .toSorted((agentA, agentB) => (agentB.conversion ?? 0) - (agentA.conversion ?? 0))
        .splice(0, topAgentMaxCountInitial.value)
})

const topAgents = agentModel.useList({
    limit: false,
    where: (and) => {
        and.eq('new_queue_category', NewQueueCategory.Top)
    },
    with: ['state'],
})

const mightBeTopAgentsSortedList = computed(() => {
    return mightBeTopAgents.records
        .toSorted((agentA, agentB) => (agentB.conversion ?? 0) - (agentA.conversion ?? 0))
})

const mightBeTopAgents = agentModel.useList({
    with: ['state'],
})

const agentList = computed(() => {
    if (showMightBeTopList.value) {
        return mightBeTopAgentsSortedList.value
    } else {
        return topAgentsSortedList.value
    }
})

const suspense = useSuspensableComponent(async () => {
    const mightBeTopAgentPks = await agentModel.actions.getAgentsMightBeTop({})

    const [settings]: [settings: ActionResponse<'Lead', 'getNewQueueSettings'>] = await Promise.all([
        leadModel.actions.getNewQueueSettings({}) as Promise<any>,
        mightBeTopAgents.fetch({ pks: mightBeTopAgentPks }),
        topAgents.fetch(),
    ])

    form.updateInitialData({
        top_agent_max_count: settings.top_agent_max_count,
        prediction_threshold: settings.prediction_threshold,
    })

    topAgentMaxCountInitial.value = settings.top_agent_max_count
})

const form = useForm<{
    top_agent_max_count: number
    prediction_threshold: number
}>({
    top_agent_max_count: 0,
    prediction_threshold: 0,
})

const submit = form.useSubmit(async (data) => {
    await leadModel.actions.updateNewQueueSettings({
        top_agent_max_count: data.top_agent_max_count,
        prediction_threshold: data.prediction_threshold,
    })

    form.updateInitialData()

    topAgentMaxCountInitial.value = data.top_agent_max_count

    toastSuccess('Settings was updated!')
}, {
    resetOnSuccess: false,
})
</script>
