<template>
    <Line
        :data="chartData"
        :options="chartOptions"
    />
</template>

<script setup lang="ts">
import { Line } from 'vue-chartjs'

import { computed } from 'vue'
import type { ChartOptions, TooltipItem } from 'chart.js'
import {
    Chart as ChartJS,
    Title,
    Tooltip,
    Legend,
    LineElement,
    PointElement,
    LinearScale,
    TimeScale,
    Filler,
    CategoryScale,
} from 'chart.js'
import 'chartjs-adapter-date-fns'
import type { ActionResponse } from '~types/lib/Model'
import { sortByKeyFn } from '~/lib/Helper/ArrayHelper'

const props = defineProps<{
    datasets: ActionResponse<'Lead', 'getNewQueueStatisticsPerDay'>
}>()

// const getOrCreateTooltip = (chart) => {
//     let tooltipEl = chart.canvas.parentNode.querySelector('div')
//
//     if (!tooltipEl) {
//         tooltipEl = document.createElement('div')
//         tooltipEl.style.background = 'rgba(0, 0, 0, 0.7)'
//         tooltipEl.style.borderRadius = '3px'
//         tooltipEl.style.color = 'white'
//         tooltipEl.style.opacity = 1
//         tooltipEl.style.pointerEvents = 'none'
//         tooltipEl.style.position = 'absolute'
//         tooltipEl.style.transform = 'translate(-50%, 0)'
//         tooltipEl.style.transition = 'all .1s ease'
//
//         const table = document.createElement('table')
//         table.style.margin = '0px'
//
//         tooltipEl.appendChild(table)
//         chart.canvas.parentNode.appendChild(tooltipEl)
//     }
//
//     return tooltipEl
// }
//
// const externalTooltipHandler = (context) => {
//     // Tooltip Element
//     const { chart, tooltip } = context
//     const tooltipEl = getOrCreateTooltip(chart)
//
//     // Hide if no tooltip
//     if (tooltip.opacity === 0) {
//         tooltipEl.style.opacity = 0
//
//         return
//     }
//
//     // Set Text
//     if (tooltip.body) {
//         const titleLines = tooltip.title || []
//         const bodyLines = tooltip.body.map(b => b.lines)
//
//         const tableHead = document.createElement('thead')
//
//         titleLines.forEach(title => {
//             const tr = document.createElement('tr')
//             tr.style.borderWidth = 0
//
//             const th = document.createElement('th')
//             th.style.borderWidth = 0
//             const text = document.createTextNode(title)
//
//             th.appendChild(text)
//             tr.appendChild(th)
//             tableHead.appendChild(tr)
//         })
//
//         const tableBody = document.createElement('tbody')
//         bodyLines.forEach((body, i) => {
//             const colors = tooltip.labelColors[i]
//
//             const span = document.createElement('span')
//             span.style.background = colors.backgroundColor
//             span.style.borderColor = colors.borderColor
//             span.style.borderWidth = '2px'
//             span.style.marginRight = '10px'
//             span.style.height = '10px'
//             span.style.width = '10px'
//             span.style.display = 'inline-block'
//
//             const tr = document.createElement('tr')
//             tr.style.backgroundColor = 'inherit'
//             tr.style.borderWidth = 0
//
//             const td = document.createElement('td')
//             td.style.borderWidth = 0
//
//             const text = document.createTextNode(body.join(' | '))
//
//             td.appendChild(span)
//             td.appendChild(text)
//             tr.appendChild(td)
//             tableBody.appendChild(tr)
//         })
//
//         const tableRoot = tooltipEl.querySelector('table')
//
//         // Remove old children
//         while (tableRoot.firstChild) {
//             tableRoot.firstChild.remove()
//         }
//
//         // Add new children
//         tableRoot.appendChild(tableHead)
//         tableRoot.appendChild(tableBody)
//     }
//
//     const { offsetLeft: positionX, offsetTop: positionY } = chart.canvas
//
//     // Display, position, and set styles for font
//     tooltipEl.style.opacity = 1
//     tooltipEl.style.left = positionX + tooltip.caretX + 'px'
//     tooltipEl.style.top = positionY + tooltip.caretY + 'px'
//     tooltipEl.style.font = tooltip.options.bodyFont.string
//     tooltipEl.style.padding = tooltip.options.padding + 'px ' + tooltip.options.padding + 'px'
//     tooltipEl.style.zIndex = '1'
// }

const { isDark } = useDarkMode()
const { useDictionary } = useContext()
const teamDictionary = useDictionary('Team')
const externalResourceDictionary = useDictionary('ExternalResource')

const xValues = computed(() => {
    const result = []
    props.datasets.forEach((item) => {
        item.dataset.forEach((element) => {
            result.push(element.timestamp)
        })
    })

    return result.sort(sortByKeyFn('timestamp', 'asc'))
})

const time = computed<{unit: 'day'} | {unit: 'hour', displayFormats: {hour: string}}>(() => {
    const diffMs = xValues.value[xValues.value.length - 1] - xValues.value[0]
    const oneDayMs = 60 * 60 * 24

    if (diffMs >= oneDayMs) {
        return {
            unit: 'day',
        }
    } else {
        return {
            unit: 'hour',
            displayFormats: {
                hour: 'HH:mm',
            },
        }
    }
})

const verticalLinePlugin = {
    id: 'verticalLine',
    beforeDraw(chart: any) {
        const tooltip = chart.tooltip

        if (tooltip && tooltip._active && tooltip._active.length) {
            const ctx = chart.ctx
            const activePoint = tooltip._active[0]
            const x = activePoint.element.x
            const y = activePoint.element.y

            const index = activePoint.index

            let minY = y
            chart.data.datasets.forEach((dataset: any, datasetIndex: number) => {
                const meta = chart.getDatasetMeta(datasetIndex)
                const element = meta.data[index]

                if (element && element.y < minY) {
                    minY = element.y
                }
            })

            ctx.save()
            ctx.beginPath()
            ctx.setLineDash([5, 5])
            ctx.moveTo(x, minY)
            ctx.lineTo(x, chart.scales.y.bottom)
            ctx.lineWidth = 1
            ctx.strokeStyle = '#94A3B8'
            ctx.stroke()
            ctx.restore()
        }
    },
}

ChartJS.register(
    Title,
    Tooltip,
    Legend,
    LineElement,
    PointElement,
    LinearScale,
    TimeScale,
    Filler,
    CategoryScale,
    verticalLinePlugin,
)

onUnmounted(() => {
    ChartJS.unregister(verticalLinePlugin)
})

const getDatasetColor = (index: number) => {
    const colors = [
        { r: 59, g: 118, b: 246 },   // синий
        { r: 255, g: 80, b: 80 },    // красный
        { r: 0, g: 200, b: 100 },    // зелёный
        { r: 170, g: 60, b: 255 },   // фиолетовый
        { r: 255, g: 140, b: 0 },    // оранжевый
        { r: 255, g: 215, b: 0 },    // жёлтый
        { r: 0, g: 255, b: 255 },    // бирюзовый
        { r: 255, g: 0, b: 255 },    // розово-фиолетовый
        { r: 128, g: 0, b: 0 },      // бордовый
        { r: 0, g: 128, b: 128 },    // тёмно-бирюзовый
        { r: 0, g: 0, b: 128 },      // тёмно-синий
        { r: 255, g: 105, b: 180 },  // розовый
        { r: 85, g: 107, b: 47 },     // оливковый
    ]

    if (index < colors.length) {
        return colors[index]
    }

    const r = (index * 67) % 256
    const g = (index * 137) % 256
    const b = (index * 199) % 256

    return { r, g, b }
}

const chartData = computed(() => ({
    labels: xValues.value.map((unixTimestamp) => Date.fromUnixTimestamp(unixTimestamp).setTimeZone('UTC')),
    datasets: props.datasets.map((dataset, i) => {
        const color = getDatasetColor(i)

        return {
            label: dataset.data ? (teamDictionary.find(dataset.data.team_pk)?.name ?? 'Unasigned') : 'Total NQ leads',
            data: dataset.dataset.map(item => item.value),
            fill: true,
            borderColor: `rgba(${color.r}, ${color.g}, ${color.b})`,
            tension: 0.3,
            color: `rgba(${color.r}, ${color.g}, ${color.b})`,
            pointBackgroundColor: 'rgba(0,0,0,0)',
            pointBorderColor: 'rgba(0,0,0,0)',
            pointHoverBackgroundColor: isDark.value ? 'rgb(255, 255, 255)' : 'rgba(41, 49, 69, 1)',
            borderWidth: 1.5,

            backgroundColor: (ctx: any) => {
                const { chart } = ctx
                const { ctx: canvasCtx, chartArea } = chart

                if (!chartArea) return

                const gradient = canvasCtx.createLinearGradient(0, chartArea.top, 0, chartArea.bottom)
                gradient.addColorStop(0, `rgba(${color.r}, ${color.g}, ${color.b}, 0.4)`)
                gradient.addColorStop(1, `rgba(${color.r}, ${color.g}, ${color.b}, 0)`)

                return gradient
            },
        }
    }),
}))

const chartOptions: ChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
        mode: 'index' as const,
        intersect: false,
    },
    normalized: true,
    scales: {
        x: {
            grid: {
                display: false,
            },
            ticks: {
                color: isDark.value ? '#FFFFFF' : '#616F96',
                source: 'data',
                autoSkipPadding: 5,
            },

            type: 'time' as const,
            time: time.value,

            title: {
                display: true,
            },
        },
        y: {
            ticks: {
                color: isDark.value ? '#FFFFFF' : '#616F96',
                maxTicksLimit: 6,
                autoSkip: false,
            },
            beginAtZero: true,
        },
    },
    plugins: {
        legend: {
            display: false,
            labels: {
                color: '#FFFFF',
            },
        },
        tooltip: {
            boxPadding: 4,
            displayColors: true,
            usePointStyle: true,
            callbacks: {
                labelPointStyle: () => ({
                    pointStyle: 'circle',
                    rotation: 0,
                }),
                label: (context) => {
                    const datasetConfig = props.datasets[context.datasetIndex]
                    const dataItem = datasetConfig.dataset[context.dataIndex].data

                    return [`${context.dataset.label}: ${context.raw}`, ...dataItem.map((external_resource) => `${externalResourceDictionary.find(external_resource.external_resource).title}: ${external_resource.count}`)].join(' | ')
                },
                title(tooltipItems): string | string[] | void {
                    return ''
                },
                labelColor: (context: TooltipItem<'line'>) => {
                    const dataset = context.chart.data.datasets[context.datasetIndex]

                    return {
                        borderColor: dataset.borderColor,
                        backgroundColor: dataset.borderColor,
                    }
                },
            },
        },
    },
}
</script>
