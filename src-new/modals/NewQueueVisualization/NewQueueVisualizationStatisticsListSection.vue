<template>
    <div class="card card--bordered card__body h-[400px]">
        <div class="card card--bordered card__body--partholder grid grid-cols-4 gap-4 overflow-scroll fancy-scroll h-full">
            <div
                v-for="(teamInfo, $i) in mappedList"
                :key="$i"
                class="card card--bordered"
            >
                <div class="card__header">
                    <label class="flex items-center gap-2 cursor-pointer font-bold">
                        <InputCheckbox
                            :disabled="isRequestsLoading"
                            :model-value="newQueueVisualizationStatisticsSearchController.tags.teamSearchTag.values?.includes(tryUsePk(teamInfo.team))"
                            @update:model-value="$event ? newQueueVisualizationStatisticsSearchController.tags.teamSearchTag.addValue(tryUsePk(teamInfo.team)) : newQueueVisualizationStatisticsSearchController.tags.teamSearchTag.removeValue(tryUsePk(teamInfo.team))"
                        />
                        <span class="w-30 text-ellipsis overflow-hidden whitespace-nowrap">{{ teamInfo.team?.name ?? 'Unassigned agents' }}</span>
                    </label>
                    <div class="text-xs">
                        <div class="text-2xs grid grid-cols-3 gap-x-1">
                            <div class="col-span-2">
                                Total taken:
                            </div>
                            <div class="font-bold">
                                {{ totalPerTeam[tryUsePk(teamInfo.team)] }}
                            </div>
                            <div class="col-span-2">
                                Total percent:
                            </div>
                            <div class="font-bold">
                                {{ getTeamPercent(tryUsePk(teamInfo.team)).toFixed(2) }}%
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card__body flex flex-col gap-4 h-[300px] overflow-auto fancy-scroll">
                    <div
                        v-for="(agent, index) in teamInfo.agents"
                        :key="index"
                        class="card card--bordered p-2 flex gap-4 justify-between"
                    >
                        <AgentListItem :agent="agent" class="text-xs w-[200px]" />

                        <div class="text-2xs grid grid-cols-3 ">
                            <div class="col-span-2">
                                Conversion:
                            </div>
                            <div class="font-bold">
                                {{ agent.conversion_value.toFixed(2) }}%
                            </div>
                            <div class="col-span-2">
                                Taken:
                            </div>
                            <div class="font-bold">
                                {{ agent.count }}
                            </div>
                            <div class="col-span-2">
                                Percent:
                            </div>
                            <div class="font-bold">
                                {{ getAgentPrecent(agent).toFixed(2) }}%
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import AgentListItem from '~/sections/ReleasePost/components/AgentListItem.vue'
import type {
    fetchStatisticsPerUser,
    NewQueueVisualizationStatisticsSearchController,
} from '~/modals/NewQueueVisualization/composable/useNewQueueVisualizationStatisticsController'
import type { ActionResponse, ModelAttributes } from '~types/lib/Model'
import { tryUsePk } from '~/composables/usePk'

const props = defineProps<{
    getStatisticsPerUser: fetchStatisticsPerUser
    isRequestsLoading: boolean,
    newQueueVisualizationStatisticsSearchController: NewQueueVisualizationStatisticsSearchController
}>()

const { useDictionary } = useContext()
const agentDictionary = useDictionary('Agent')
const teamDictionary = useDictionary('Team')

const list = ref<ActionResponse<'Lead', 'getNewQueueStatisticsPerUser'>>([])

const suspense = useSuspensableComponent(async () => {
    list.value = await props.getStatisticsPerUser()
})

const fallenTeamPk = teamDictionary.records.find(team => team.name === 'The Fallen')?.pk

const mappedList = computed(() => {
    const agentsByTeam: Map<
        PrimaryKey | undefined,
        {
            agents: Map<PrimaryKey, ModelAttributes<'Agent'> & { conversion_value: number; count: number }>;
            team: ModelAttributes<'Team'> | undefined;
        }
    > = new Map()

    agentDictionary.sellerAgents.forEach((agent: ModelAttributes<'Agent'>) => {
        const teamPk = agent.team_pk
        const team = teamPk ? teamDictionary.findOrFail(teamPk) : undefined

        if (!agentsByTeam.has(teamPk)) {
            agentsByTeam.set(teamPk, {
                agents: new Map(),
                team,
            })
        }

        agentsByTeam.get(teamPk)!.agents.set(agent._pk, {
            ...agent,
            conversion_value: agent.conversion ?? 0,
            count: 0,
        })
    })

    list.value.forEach((agentInfo) => {
        const agent = agentDictionary.findOrFail(agentInfo.agent_pk)
        const teamPk = agent.team_pk
        const team = teamPk ? teamDictionary.findOrFail(teamPk) : undefined

        if (!agentsByTeam.has(teamPk)) {
            agentsByTeam.set(teamPk, {
                agents: new Map(),
                team,
            })
        }

        const teamAgents = agentsByTeam.get(teamPk)!.agents

        teamAgents.set(agent.pk, {
            ...agent,
            conversion_value: agentInfo.conversion_value,
            count: agentInfo.value,
        })
    })

    return Array.from(agentsByTeam.values()).map(group => ({
        team: group.team,
        agents: Array.from(group.agents.values()).sort((a, b) => b.count - a.count),
    })).sort((a, b) => {
        if (a.team && usePk(a.team) == fallenTeamPk) return 1

        if (b.team && usePk(b.team) == fallenTeamPk) return -1

        return 0
    })
})

const stopWatch = props.newQueueVisualizationStatisticsSearchController.useChangeHandler(
    async () => {
        await suspense.fetch()
    },
)

onUnmounted(() => {
    stopWatch?.()
})

const totalCount = computed(() => Object.values(totalPerTeam.value).reduce((acc, teamTotal) => acc + teamTotal, 0))

const totalPerTeam = computed<Record<PrimaryKey | undefined, number>>(() => {
    const res: Record<PrimaryKey | undefined, number> = {}

    mappedList.value.forEach((teamInfo) => {
        res[teamInfo.team?._pk] = teamInfo.agents.reduce((acc, agent) => {
            return acc + agent.count
        }, 0)
    })

    return res
})

const getTeamPercent = (teamPk: PrimaryKey | undefined) => {
    return totalCount.value ? ((totalPerTeam.value[teamPk] / totalCount.value) * 100) : 0
}

const getAgentPrecent = (agent: ModelAttributes<'Agent'> & {count: number}) => {
    return totalPerTeam.value[agent.team_pk ?? undefined] ? ((agent.count / totalPerTeam.value[agent.team_pk ?? undefined]) * 100) : 0
}
</script>
