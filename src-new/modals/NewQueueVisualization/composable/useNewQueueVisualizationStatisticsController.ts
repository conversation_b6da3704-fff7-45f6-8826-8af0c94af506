import DateRangeSearchTag from '~/lib/Search/Tag/DateRangeSearchTag'
import { dateRangeToTimestampRange, getUTCOffset } from '@/lib/core/helper/DateHelper'
import TeamSearchTag from '~/lib/Search/Tag/Preset/TeamSearchTag'
import SelectSearchTag from '~/lib/Search/Tag/SelectSearchTag'
import { useAppTimezone } from '~/composables/useAppTimezone'
import { ExternalResource } from '~/api/dictionaries/Static/ExternalResourceDictionary'
import SearchController from '~/lib/Search/SearchController'

export function useNewQueueVisualizationStatisticsController() {
    const { useDictionary, useModel, workspace } = useContext()
    const externalResourceDictionary = useDictionary('ExternalResource')
    const leadModel = useModel('Lead')

    const externalResource = new SelectSearchTag('Resource', externalResourceDictionary.mapRecords.forSelect().filter((item) => item.value !== ExternalResource.Bonus))
    const dateRangeSearchTag = new DateRangeSearchTag('Date Range', { multiple: false })
    const teamSearchTag = new TeamSearchTag(workspace)

    const newQueueVisualizationStatisticsSearchController = new SearchController({
        externalResource,
        dateRangeSearchTag,
        teamSearchTag,
    }, {
        syncWithQuery: false,
    })

    // @ts-ignore
    externalResource.load()

    const isLoadingPerDay = ref(false)
    const isLoadingPerUser = ref(false)

    const getStatisticsPerDay = async () => {
        const dateRange = dateRangeSearchTag.values?.[0]

        if (!dateRange) {
            return []
        }

        const newDateRange = dateRangeSearchTag.getDateRangeValueWithoutMoveTimeZone(dateRange)

        isLoadingPerDay.value = true

        let result = []

        const offset = getUTCOffset(useAppTimezone().value)

        try {
            result = await leadModel.actions.getNewQueueStatisticsPerDay({
                date_range: dateRangeToTimestampRange(dateRangeSearchTag.getDateRangeValue(dateRange)),
                group_per_hours: (newDateRange.end.unixTimestamp() - newDateRange.start.unixTimestamp()) < Timespan.days(1).inSeconds,
                team_pks: teamSearchTag.values ?? [],
                external_resource: externalResource.values ?? [],
                offset,
            })
        } catch (error) {
            return []
        } finally {
            isLoadingPerDay.value = false
        }

        return result
    }

    const getStatisticsPerUser = async () => {
        const dateRange = dateRangeSearchTag.values?.[0]

        if (!dateRange) {
            return []
        }

        isLoadingPerUser.value = true

        let result = []

        try {
            result = await leadModel.actions.getNewQueueStatisticsPerUser({
                date_range: dateRangeToTimestampRange(dateRangeSearchTag.getDateRangeValue(dateRange)),
                external_resource: externalResource.values ?? [],
            })
        } catch (error) {
            return []
        } finally {
            isLoadingPerUser.value = false
        }

        return result
    }

    return {
        isRequestsLoading: computed(() => isLoadingPerDay.value || isLoadingPerUser.value),
        newQueueVisualizationStatisticsSearchController,
        getStatisticsPerUser,
        getStatisticsPerDay,
    }
}

export type NewQueueVisualizationStatisticsSearchController = ReturnType<typeof useNewQueueVisualizationStatisticsController>['newQueueVisualizationStatisticsSearchController']
export type fetchStatisticsPerDay = ReturnType<typeof useNewQueueVisualizationStatisticsController>['getStatisticsPerDay']
export type fetchStatisticsPerUser = ReturnType<typeof useNewQueueVisualizationStatisticsController>['getStatisticsPerUser']
