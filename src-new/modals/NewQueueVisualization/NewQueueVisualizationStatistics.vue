<template>
    <NewQueueVisualizationStatisticsChartSection
        :get-statistics-per-day="getStatisticsPerDay"
        :is-requests-loading="isRequestsLoading"
        :new-queue-visualization-statistics-search-controller="newQueueVisualizationStatisticsSearchController"
    />

    <NewQueueVisualizationStatisticsListSection
        :new-queue-visualization-statistics-search-controller="newQueueVisualizationStatisticsSearchController"
        :get-statistics-per-user="getStatisticsPerUser"
        :is-requests-loading="isRequestsLoading"
    />
</template>

<script setup lang="ts">
import NewQueueVisualizationStatisticsChartSection
    from '~/modals/NewQueueVisualization/NewQueueVisualizationStatisticsChartSection.vue'
import NewQueueVisualizationStatisticsListSection
    from '~/modals/NewQueueVisualization/NewQueueVisualizationStatisticsListSection.vue'
import {
    useNewQueueVisualizationStatisticsController,
} from '~/modals/NewQueueVisualization/composable/useNewQueueVisualizationStatisticsController'

const {
    getStatisticsPerUser,
    getStatisticsPerDay,
    isRequestsLoading,
    newQueueVisualizationStatisticsSearchController,
} = useNewQueueVisualizationStatisticsController()
</script>
