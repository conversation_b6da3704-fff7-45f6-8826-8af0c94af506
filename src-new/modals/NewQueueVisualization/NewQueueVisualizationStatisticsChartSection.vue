<template>
    <div class="card card--bordered rounded-lg">
        <div class="card__header">
            <div class="card__title w-full flex justify-between">
                <div class="flex flex-col">
                    <div class="text-xs">
                        <span class="text-secondary">Selected period: </span>
                        <span v-if="newQueueVisualizationStatisticsSearchController.tags.dateRangeSearchTag.values?.[0]">{{ formatRange(moveDateRangeToTimeZone(newQueueVisualizationStatisticsSearchController.tags.dateRangeSearchTag.getDateRangeValue(newQueueVisualizationStatisticsSearchController.tags.dateRangeSearchTag.values[0]), useAppTimezone().value), 'MMMM d, yyyy') }} <span class="text-primary font-bold">{{ timezoneCity }}</span></span>
                    </div>
                </div>

                <div class="flex gap-6">
                    <div class="button-group">
                        <AppButton
                            :class="{ '--primary': rangeState === 'thisYear' }"
                            :disabled="isRequestsLoading"
                            @click="updateRangeState('thisYear')"
                        >
                            Year
                        </AppButton>
                        <AppButton
                            :disabled="isRequestsLoading"
                            :class="{ '--primary': rangeState === 'thisMonth' }"
                            @click="updateRangeState('thisMonth')"
                        >
                            Month
                        </AppButton>
                        <AppButton
                            :disabled="isRequestsLoading"
                            :class="{ '--primary': rangeState === 'thisWeek' }"
                            @click="updateRangeState('thisWeek')"
                        >
                            Week
                        </AppButton>
                        <AppButton
                            :disabled="isRequestsLoading"
                            :class="{ '--primary': rangeState === 'today' }"
                            @click="updateRangeState('today')"
                        >
                            Day
                        </AppButton>
                        <AppButton
                            :disabled="isRequestsLoading"
                            :class="{ '--primary': !rangeState }"
                            @click="updateRangeState(undefined)"
                        >
                            Custom
                        </AppButton>
                    </div>

                    <Component
                        :is="newQueueVisualizationStatisticsSearchController.tags.dateRangeSearchTag.component"
                        :disabled="isRequestsLoading"
                        class="w-30"
                        teleport
                        :tag="newQueueVisualizationStatisticsSearchController.tags.dateRangeSearchTag"
                        :model-value="newQueueVisualizationStatisticsSearchController.tags.dateRangeSearchTag.isMultiple ? newQueueVisualizationStatisticsSearchController.tags.dateRangeSearchTag.values : newQueueVisualizationStatisticsSearchController.tags.dateRangeSearchTag.values?.[0]"
                        @update:model-value="applyUpdate(newQueueVisualizationStatisticsSearchController.tags.dateRangeSearchTag, $event)"
                    />

                    <Component
                        :is="newQueueVisualizationStatisticsSearchController.tags.externalResource.component"
                        :disabled="isRequestsLoading"
                        class="w-40"
                        :model-value="newQueueVisualizationStatisticsSearchController.tags.externalResource.isMultiple ? newQueueVisualizationStatisticsSearchController.tags.externalResource.values : newQueueVisualizationStatisticsSearchController.tags.externalResource.values?.[0]"
                        :tag="newQueueVisualizationStatisticsSearchController.tags.externalResource"
                        @update:model-value="applyUpdate(newQueueVisualizationStatisticsSearchController.tags.externalResource, $event)"
                    />
                </div>
            </div>
        </div>
        <div class="card__body">
            <SuspenseManual :loading="suspense.silentLoading">
                <template #default>
                    <NewQueueVisualizationChart
                        v-if="list.length"
                        class="h-[240px]"
                        :datasets="list"
                    />
                    <div v-else class="h-[240px] w-full text-center justify-center flex items-center ">
                        <div class="font-bold">
                            No data
                        </div>
                    </div>
                </template>
                <template #fallback>
                    <PlaceholderBlock class="!w-full h-[240px]" />
                </template>
            </SuspenseManual>
        </div>
    </div>
</template>

<script setup lang="ts">
import type AgentOrTeamSearchTag from '~/lib/Search/Tag/AgentOrTeamSearchTag'
import type SelectSearchTag from '~/lib/Search/Tag/SelectSearchTag'
import type DateRangeSearchTag from '~/lib/Search/Tag/DateRangeSearchTag'
import { type DateRangeName, formatRange, moveDateRangeToTimeZone } from '@/lib/core/helper/DateHelper'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import { ensureArray } from '~/lib/Helper/ArrayHelper'
import type {
    fetchStatisticsPerDay,
} from '~/modals/NewQueueVisualization/composable/useNewQueueVisualizationStatisticsController'
import type {
    NewQueueVisualizationStatisticsSearchController,
} from '~/modals/NewQueueVisualization/composable/useNewQueueVisualizationStatisticsController'
import type { ActionResponse } from '~types/lib/Model'
import NewQueueVisualizationChart from '~/modals/NewQueueVisualization/NewQueueVisualizationChart.vue'
import { CityTimezones, useAppTimezone } from '~/composables/useAppTimezone'

const props = defineProps<{
    getStatisticsPerDay: fetchStatisticsPerDay,
    isRequestsLoading: boolean,
    newQueueVisualizationStatisticsSearchController: NewQueueVisualizationStatisticsSearchController
}>()

const { value: timezoneCity } = useAppSetting('timezoneCity')

const list = ref<ActionResponse<'Lead', 'getNewQueueStatisticsPerDay'>>([])

const rangeState = ref<DateRangeName | undefined>()

updateRangeState('today')

const suspense = useSuspensableComponent(async () => {
    list.value = await props.getStatisticsPerDay()
})

const stopWatch = props.newQueueVisualizationStatisticsSearchController.useChangeHandler(
    async () => {
        await suspense.fetch()
    },
)

onUnmounted(() => {
    stopWatch?.()
})

function updateRangeState(value: DateRangeName | undefined) {
    rangeState.value = value

    if (value) {
        applyUpdate(props.newQueueVisualizationStatisticsSearchController.tags.dateRangeSearchTag, value, true)
    }
}

function applyUpdate(searchTag: AgentOrTeamSearchTag | SelectSearchTag | DateRangeSearchTag, value: any,  skipSideEffect?: boolean) {
    if (!searchTag) {
        return
    }

    if (searchTag.constructor.name === 'DateRangeSearchTag' && !skipSideEffect) {
        rangeState.value = undefined
    }

    if (!searchTag.isMultiple) {
        value = [value]
    }

    searchTag.setValues(ensureArray(value).filter((v) => v !== undefined))
}
</script>
