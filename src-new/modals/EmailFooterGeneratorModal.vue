<template>
    <AppModalWrapper
        close-button
        header="Email Footer Generator"
        class="!w-[1250px] !max-w-[1250px]"
    >
        <div class="p-2 bg-secondary-50 dark:bg-dark-3 flex gap-x-2 w-full">
            <div class="flex flex-col gap-y-2 w-full">
                <div class="px-5 py-4 rounded bg-white dark:bg-dark-2 flex items-center justify-between">
                    <span class="font-semibold">
                        Choose Company template
                    </span>
                    <div class="flex items-center gap-x-3">
                        <label
                            v-for="(project, $i) in projects"
                            :key="$i"
                            class="flex items-center gap-x-2"
                        >
                            <InputRadio
                                v-model="form.data.company"
                                name="company"
                                :value="project.abbreviation"
                                class="text-xs"
                            />
                            <span class="text-xs cursor-pointer select-none">{{ project.abbreviation }}</span>
                        </label>
                    </div>
                </div>
                <div class="px-5 py-4 rounded bg-white dark:bg-dark-2 flex flex-col gap-y-3">
                    <span class="text-sm font-semibold">
                        Personal information
                    </span>
                    <div class="w-full border-t border-secondary-100" />
                    <div class="grid grid-cols-2 gap-3">
                        <form @submit.prevent="submit">
                            <FormField
                                :form="form"
                                field="agent_pk"
                                label="Name"
                                class="text-xs"
                            >
                                <InputAgent
                                    v-model="form.data.agent_pk"
                                    class="dark:border dark:border-secondary-900"
                                    :options="agentOptions"
                                    @update:model-value="updateFormData"
                                />
                            </FormField>
                        </form>
                        <form @submit.prevent="submit">
                            <FormField
                                :form="form"
                                field="function"
                                label="Function"
                                class="text-xs"
                            >
                                <InputText v-model="form.data.function" />
                            </FormField>
                        </form>
                        <form @submit.prevent="submit">
                            <FormField
                                :form="form"
                                field="phone"
                                label="Phone"
                                class="text-xs"
                            >
                                <InputPhone v-model="form.data.phone" with-country-select />
                            </FormField>
                        </form>
                        <form @submit.prevent="submit">
                            <FormField
                                :form="form"
                                field="email"
                                label="Email"
                                class="text-xs"
                            >
                                <InputText v-model="form.data.email" />
                            </FormField>
                        </form>
                        <UploadArea
                            title="Upload Avatar"
                            class="col-span-2"
                            subtitle="Supported formats: jpg, png"
                            :max-size="10"
                            :accept="normalizedAccept"
                            compact
                            @load="handleLoad"
                        />
                        <UploadList
                            v-if="uploadFileList.length"
                            :files="uploadFileList"
                            @cancel="clearFile"
                        />
                    </div>
                </div>
                <div class="px-5 py-4 rounded bg-white dark:bg-dark-2 flex flex-col gap-y-3">
                    <span class="text-sm font-semibold">Company information</span>
                    <div class="w-full border-t border-secondary-100" />
                    <div class="grid grid-cols-3 gap-x-6 gap-y-3">
                        <FormField
                            :form="form"
                            field="with_company_phone"
                        >
                            <label class="text-xs flex items-center gap-x-2">
                                <InputCheckbox v-model="form.data.with_company_phone" />
                                <span class="cursor-pointer select-none">Company phone number</span>
                            </label>
                        </FormField>
                        <FormField
                            :form="form"
                            field="with_company_website"
                        >
                            <label class="text-xs flex items-center gap-x-2">
                                <InputCheckbox v-model="form.data.with_company_website" />
                                <span class="cursor-pointer select-none">Company website</span>
                            </label>
                        </FormField>
                        <FormField
                            :form="form"
                            field="with_company_logo"
                        >
                            <label class="text-xs flex items-center gap-x-2">
                                <InputCheckbox v-model="form.data.with_company_logo" />
                                <span class="cursor-pointer select-none">Company logo</span>
                            </label>
                        </FormField>
                        <FormField
                            v-if="selectedCompanyInfo?.trustPilotLink"
                            :form="form"
                            field="with_trustpilot_widget"
                        >
                            <label class="text-xs flex items-center gap-x-2">
                                <InputCheckbox v-model="form.data.with_trustpilot_widget" />
                                <span class="cursor-pointer select-none">Trustpilot widget</span>
                            </label>
                        </FormField>
                        <FormField
                            :form="form"
                            field="with_partners_logo"
                        >
                            <label class="text-xs flex items-center gap-x-2">
                                <InputCheckbox v-model="form.data.with_partners_logo" />
                                <span class="cursor-pointer select-none">Partner's logo</span>
                            </label>
                        </FormField>
                    </div>
                </div>
            </div>
            <div class="w-full rounded bg-white dark:bg-dark-2 px-5 py-4 flex flex-col gap-y-3">
                <div class="flex items-center justify-between">
                    <span class="font-semibold">Preview</span>
                </div>
                <div class="w-full border-t border-secondary-100" />
                <div
                    v-if="previewInfo"
                    ref="result"
                    class="bg-white p-2"
                >
                    <EmailFooterGeneratorToCopySection
                        :cdn-url="cdnUrl"
                        :preview-info="previewInfo"
                        :agent-preview="agentPreview"
                        :agent-avatar="agentAvatar"
                    />
                </div>
                <div v-else>
                    <span class="text-xs text-secondary-300">Fill information for Email footer preview</span>
                </div>
                <div v-if="showPreviewUpdatesTip" class="w-full mt-auto">
                    <AppTip type="warning" class="items-center">
                        Update preview with last changes
                    </AppTip>
                </div>
            </div>
        </div>

        <template #footer>
            <div class="flex justify-between">
                <AppButton
                    @click="close"
                >
                    Cancel
                </AppButton>
                <div class="flex items-center gap-x-4">
                    <AppButton @click="submit">
                        Update Preview
                    </AppButton>
                    <AppButton
                        v-tooltip="{content: previewInfo ? null : 'Preview Required'}"
                        :disabled="!previewInfo"
                        class="--primary"
                        @click="copyRenderedToClipboard"
                    >
                        Copy to Clipboard
                    </AppButton>
                </div>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup  lang="ts">
import FormField from '~/components/Form/FormField.vue'
import UploadList from '@/components/Upload/UploadList.vue'
import UploadArea from '@/components/Upload/UploadArea.vue'
import { normalizeFileAcceptValue } from '~/lib/Helper/File/FileExtensionHelper'
import { useFileUpload } from '~/composables/useFileUpload'
import type FileData from '@/lib/core/helper/File/FileData'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import { copyElementToClipboard } from '@/lib/core/helper/ClipboardHelper'
import EmailFooterGeneratorToCopySection from '~/sections/EmailFooterGenerator/EmailFooterGeneratorToCopySection.vue'
import type { ModelRef } from '~types/lib/Model'

defineOptions({
    name: 'EmailFooterGeneratorModal',
})

const emit = defineEmits<{
    close: []
}>()

type PreviewFormData = {
    company: string,
    agent_pk: PrimaryKey | undefined
    function: string | undefined,
    phone: string | undefined,
    email: string | undefined,
    with_company_phone: boolean,
    with_company_website: boolean,
    with_company_logo: boolean,
    with_trustpilot_widget: boolean,
    with_partners_logo: boolean,
}

export type CompanyInfo = {
    title: string,
    logo: string,
    site: string,
    siteLink: string,
    phone: string,
    trustPilotLink?: string,
    trustPilotId?: string,
    withTrustpilotStars?: boolean,
}

export type PreviewFullData = PreviewFormData & {
    companyInfo: CompanyInfo | undefined
}

const { useModel, currentUserPk, useDictionary } = useNewContext('selected')

// agent info

const cdnUrl = config.cdn.base + `/images/email-signature`

const companyInfo: Record<string, CompanyInfo> = {
    'TBC': {
        title: 'TravelBusinessClass',
        logo: `${cdnUrl}/TBCLogo.png`,
        site: 'www.travelbusinessclass.com',
        siteLink: 'https://travelbusinessclass.com/',
        phone: '+****************',
        trustPilotLink: 'https://trustpilot.com/review/travelbusinessclass.com?utm_medium=Trustbox&utm_source=EmailSignature2',
        trustPilotId: '5fb544c7c119fd0001c9e8a2',
        withTrustpilotStars: true,
    },
    'ABT': {
        title: 'AirBusinessTravel',
        logo: `${cdnUrl}/ABTLogo.png`,
        site: 'www.airbusinesstravel.com',
        siteLink: 'https://airbusinesstravel.com/',
        phone: '+****************',
        trustPilotLink: 'https://trustpilot.com/review/airbusinesstravel.com?utm_medium=Trustbox&utm_source=EmailSignature2',
        trustPilotId: '6127f008d6c6ac001d92a139',
    },
    'BCS': {
        title: 'BCFlights',
        logo: `${cdnUrl}/BCSLogo.png`,
        site: 'www.bcflight.com',
        siteLink: 'https://bcflights.com/',
        phone: '+****************',
    },
    'LFS': {
        title: 'Lux-Flights',
        logo: `${cdnUrl}/LFSLogo.png`,
        site: 'www.lux-flights.com',
        siteLink: 'https://lux-flights.com/',
        phone: '+****************',
    },
}

const allProjectsDictionary = useAggregatedDictionary('Project', (dictionary) => dictionary.records)

const projects = computed((): ModelRef<'Project'>[] => {
    return allProjectsDictionary.records.value.filter((project) => {
        return project.features.email_footer_generator.enabled && companyInfo[project.abbreviation]
    })
})

// form

const defaultCompany = projects.value.at(0)!.abbreviation

const form = useForm<PreviewFormData>({
    company: defaultCompany,
    agent_pk: currentUserPk,
    function: undefined,
    phone: undefined,
    email: undefined,
    with_company_phone: true,
    with_company_website: true,
    with_company_logo: true,
    with_trustpilot_widget: Boolean(companyInfo[defaultCompany].trustPilotId && companyInfo[defaultCompany].withTrustpilotStars),
    with_partners_logo: true,
}, {
    agent_pk: ValidationRules.Required(),
    phone: ValidationRules.Required(),
    email: ValidationRules.Required(),
})

// agent options

const agentList = useModel('Agent').useList()
const agentProjectSettingsList = useModel('AgentProjectSettings').useList({
    where: (and) => {
        and.eq('agent_pk', form.data.agent_pk)
    },
})

onMounted(() => {
    agentList.fetch()
})

const agentDictionary = useDictionary('Agent')

const agentOptions = computed(() => {
    if (agentList.records.length > 0) {
        return agentDictionary.adapter.mapRecords(agentList.records.filter(record => record.is_enabled)).forSelect({
            withTeam: true,
            withDepartment: true,
        })
    }

    return agentDictionary.mapFilteredRecords(agent => usePk(agent) === form.data.agent_pk).forSelect()
})

//

const departmentDictionary = useDictionary('Department')
const positionDictionary = useDictionary('Position')

watch([() => form.data.company, () => form.data.agent_pk], async ([company, agentPk]) => {
    form.data.with_trustpilot_widget = Boolean(companyInfo[company].trustPilotId && companyInfo[company].withTrustpilotStars)
    await agentProjectSettingsList.fetch(agentPk)
    updateFormDataContact(agentPk)
})

function updateFormDataContact(agentPk: PrimaryKey) {
    const agent = agentDictionary.find(agentPk)
    const projectPk = tryUsePk(projects.value.find(project => project.abbreviation === form.data.company))
    const agentProjectSettingInfo = agentProjectSettingsList.records.find(setting => setting.project_pk === projectPk)

    form.data.phone = agentProjectSettingInfo?.phone || agent?.phone
    form.data.email = agentProjectSettingInfo?.email || agent?.email
}

function updateFormData() {
    const agentPk = form.data.agent_pk

    if (!agentPk) {
        return
    }

    const formAgent = agentDictionary.findOrFail(agentPk)

    const department = departmentDictionary.find(formAgent.department_pk)?.name
    const position = positionDictionary.find(formAgent.position_pk)?.name

    form.data.function = `${department} ${position}`

    updateFormDataContact(form.data.agent_pk)
    form.updateInitialData()
}

updateFormData()

// file upload

const normalizedAccept = computed(() => normalizeFileAcceptValue('.jpg,.jpeg,.png'))

const agentAvatar = ref()

async function getAvatarBaseFormat(filePk: PrimaryKey | null) {
    const response = await useModel('Agent').actions.agentAvatarCompress({
        agent_pk: form.data.agent_pk,
        file_pk: filePk,
    })
    agentAvatar.value = response.avatar
}

const uploadFileList = ref<FileData[]>([])

function clearFile() {
    uploadFileList.value = []
    uploadedAvatarIsNotUpdatedInPreview.value = true
}

const uploadedAvatarIsNotUpdatedInPreview = ref(false)

const { handleLoad  } = useFileUpload((file) => {
    uploadFileList.value = [file]
    uploadedAvatarIsNotUpdatedInPreview.value = true
}, {
    onError: (file) => {
        console.error('Error occurred', file.errors)
    },
})

// update preview info

const previewInfo = ref<PreviewFullData>()

const agentPreview = computed(() => {
    if (previewInfo.value && previewInfo.value.agent_pk) {
        return agentDictionary.find(previewInfo.value.agent_pk)
    }

    return agentDictionary.find(form.data.agent_pk)
})

const submit = form.useSubmit(async (data) => {
    if (uploadFileList.value.length) {
        await getAvatarBaseFormat(uploadFileList.value[0].pk)
        uploadedAvatarIsNotUpdatedInPreview.value = false
    } else {
        await getAvatarBaseFormat(null)

        if (uploadedAvatarIsNotUpdatedInPreview.value) {
            uploadedAvatarIsNotUpdatedInPreview.value = false
        }
    }

    form.updateInitialData()

    previewInfo.value = {
        company: data.company,
        agent_pk: data.agent_pk,
        function: data.function,
        phone: data.phone,
        email: data.email,
        with_company_phone: data.with_company_phone,
        with_company_website: data.with_company_website,
        with_company_logo: data.with_company_logo,
        with_trustpilot_widget: data.with_trustpilot_widget,
        with_partners_logo: data.with_partners_logo,
        companyInfo: companyInfo[data.company] || undefined,
    }
}, { resetOnSuccess: false })

const selectedCompanyInfo = computed(() => companyInfo[form.data.company])

// form has changes between preview updates

const showPreviewUpdatesTip = computed(() => {
    if (!previewInfo.value) return false

    if (uploadedAvatarIsNotUpdatedInPreview.value) return true

    return form.hasChanges
})

// result html

const result = ref()

// footer actions

function close() {
    emit('close')
}

function copyRenderedToClipboard() {
    const el = result.value as HTMLElement

    if (!el) {
        console.warn('No element to copy')

        return
    }

    copyElementToClipboard(el)
}
//
</script>
