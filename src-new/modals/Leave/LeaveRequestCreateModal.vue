<template>
    <AppModalWrapper
        :header="header"
        class="w-[768px]"
        close-button
    >
        <div class="bg-secondary-50 dark:bg-neutral-950 p-1.5">
            <form class="grid grid-cols-2 gap-2" @submit.prevent="submit">
                <div class="overflow-y-auto fancy-scroll">
                    <div class="card px-4">
                        <div class="card__header px-0 font-semibold">
                            Leave details
                        </div>
                        <div class="card__body px-0 text-xs">
                            <div class="flex flex-col gap-4">
                                <FormField
                                    v-if="hasPermission('openLeaveRequestApproveList', 'all')"
                                    :form="form"
                                    field="beneficiary_pk"
                                    label="Employee"
                                >
                                    <InputAgent
                                        v-model="form.data.beneficiary_pk"
                                        :options="agentOptions"
                                        with-empty
                                        placeholder="Select employee"
                                    />
                                </FormField>
                                <FormField
                                    :form="form"
                                    field="dateRange"
                                    label="Dates"
                                    required
                                >
                                    <InputDateRange
                                        v-model="leaveDates"
                                        timezone="UTC"
                                        placeholder="Select date period"
                                        :min-date="!hasPermission('openLeaveRequestApproveList', 'all') ? new Date() : undefined"
                                    />
                                    <div class="mt-3 text-xs font-medium flex gap-1.5">
                                        <template v-if="daysCount === undefined">
                                            <InfoIcon class="text-secondary" />
                                            <span class="text-secondary-500">Choose leave period</span>
                                        </template>
                                        <template v-else>
                                            <InfoIcon class="text-primary" />
                                            <span class="text-secondary-500">Leave duration -</span> {{ daysCount }} calendar days
                                        </template>
                                    </div>
                                </FormField>

                                <FormField
                                    :form="form"
                                    field="request_type"
                                    label="Leave type"
                                >
                                    <InputSelect
                                        v-model="form.data.request_type"
                                        :options="requestTypeOptions"
                                    />
                                </FormField>
                                <FormField
                                    :form="form"
                                    field="comment"
                                    label="Comment"
                                >
                                    <InputTextarea
                                        v-model="form.data.comment"
                                        placeholder="Enter request comment"
                                        rows="15"
                                    />
                                </FormField>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card px-4">
                    <div class="card__header px-0 font-semibold">
                        Uploads
                    </div>
                    <div class="card__body px-0">
                        <div class="pb-4">
                            <FormField
                                :form="form"
                                field="file_pk"
                                label=""
                                class="mt-4"
                            >
                                <UploadArea
                                    compact
                                    :accept="normalizedAccept"
                                    @load="handleLoad"
                                />
                                <div class="text-2xs text-secondary-400 text-center mt-1.5">
                                    Please attach files in JPG, PNG, or PDF format (max 5 files)
                                </div>
                            </FormField>
                        </div>

                        <UploadList
                            :files="files"
                            can-preview
                            can-download
                            @cancel="deleteFile($event)"
                        />
                    </div>
                </div>
            </form>
        </div>

        <template #footer="{ close }">
            <div class="flex justify-between gap-4">
                <AppButton @click="close">
                    Cancel
                </AppButton>

                <FormField
                    v-if="!isEditMode && hasPermission('openLeaveRequestApproveList', 'all')"
                    :form="form"
                    field="auto_approve"
                    inline
                    class="ml-auto"
                >
                    <label class="flex items-center cursor-pointer gap-2 select-none">
                        <InputCheckbox v-model="form.data.auto_approve" /> Auto approve
                    </label>
                </FormField>
                <AppButton
                    class="--primary !min-w-[140px]"
                    :disabled="!form.hasChanges || isLoading"
                    @click="submit"
                >
                    <template v-if="isEditMode">
                        Update request
                    </template>
                    <template v-else>
                        {{ form.data.auto_approve ? 'Approve request' : 'Send request' }}
                    </template>
                </AppButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script lang="ts" setup>
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import FormField from '~/components/Form/FormField.vue'
import UploadArea from '@/components/Upload/UploadArea.vue'
import UploadList from '@/components/Upload/UploadList.vue'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import type { DateRange } from '@/lib/core/helper/DateHelper'
import { getDaysInRange } from '@/lib/core/helper/DateHelper'
import { calculateHolidaysInDateRange } from '~/lib/Helper/LeaveHelper'
import FileData from '@/lib/core/helper/File/FileData'
import { LeaveRequestType } from '~/api/models/LeaveManagement/LeaveRequest'
import { normalizeFileAcceptValue } from '~/lib/Helper/File/FileExtensionHelper'

type LeaveForm = {
    beneficiary_pk: PrimaryKey,
    dateRange: DateRange | undefined,
    request_type: LeaveRequestType,
    comment: string | null,
    file_pks: PrimaryKey[],
    auto_approve: boolean,
}

defineOptions({
    name: 'LeaveRequestCreateModal',
    modal: {
        promise: true,
    },
})

const props = defineProps<{
    pk?: PrimaryKey,
    formData?: {
        beneficiary_pk?: PrimaryKey,
        dateRange?: DateRange,
    }
}>()

const emit = defineEmits<{
    close: [],
    resolve: [],
}>()

const { useCurrentUser, useModel, useDictionary, hasPermission } = useContext()

const leaveRequestModel = useModel('LeaveRequest')

const currentUser = useCurrentUser()

const agentDictionary = useDictionary('Agent')

const requestTypeDictionary = useDictionary('LeaveRequestType')

const agentList = useModel('Agent').useList()

const record = leaveRequestModel.useRecord({
    with: ['files'],
})

const agentOptions = computed(() => agentDictionary.adapter.mapRecords(agentList.records).forSelect({
    withTeam: true,
    withDepartment: true,
}))

const requestTypeOptions = computed(() => requestTypeDictionary.mapRecords.forSelect())

const isEditMode = computed(() => !!props.pk)

const header = computed(() => {
    return isEditMode.value ? 'Edit leave request' : 'Create leave request'
})

const daysCount = ref<undefined | number>(undefined)
const holidays = (await leaveRequestModel.actions.getHolidays()).data

useSuspensableComponent(async () => {
    if (isEditMode.value) {
        await record.fetch(props.pk)

        await initForm()
    }
    await agentList.fetch()
})

const form = useForm<LeaveForm>({
    beneficiary_pk: usePk(currentUser),
    dateRange: undefined,
    request_type: LeaveRequestType.Vacation,
    comment: null,
    file_pks: [],
    auto_approve: hasPermission('openLeaveRequestApproveList', 'all') && !isEditMode.value,
}, {
    beneficiary_pk: ValidationRules.Required('Employee is required'),
    dateRange: ValidationRules.Required('You have to select leave dates'),
})

watch(() => form.data.dateRange, (newDateRange) => {
    if (newDateRange) {
        const daysInRange = getDaysInRange(newDateRange) + 1
        const holidaysCount = calculateHolidaysInDateRange(holidays, newDateRange)

        daysCount.value = daysInRange - holidaysCount
    } else {
        daysCount.value = undefined
    }
})

if (props.formData) {
    if (props.formData.beneficiary_pk) {
        form.data.beneficiary_pk = props.formData.beneficiary_pk
    }

    if (props.formData.dateRange) {
        form.data.dateRange = toRaw(props.formData.dateRange)
    }
}

const { files, handleLoad, isLoading, deleteFile } = useFilesUpload(() => {
    form.data.file_pks = files.value.map(f => f.pk).filter(Boolean)
})

const normalizedAccept = computed(() => normalizeFileAcceptValue('.jpg,.jpeg,.png,.pdf'))

const initForm = async () => {
    const leaveRequest = record.record.value

    if (leaveRequest.file_pks.length) {
        files.value = leaveRequest.files.map((fileModel) => FileData.fromFileV2Model(fileModel))
    }

    form.updateInitialData({
        beneficiary_pk: leaveRequest.beneficiary_pk,
        dateRange: {
            start: Date.fromUnixTimestamp(leaveRequest.dates.leave_start),
            end: Date.fromUnixTimestamp(leaveRequest.dates.leave_end),
        },
        request_type: leaveRequest.request_type,
        comment: leaveRequest.comment,
        file_pks: toRaw(leaveRequest.file_pks),
    })
}

const handleCreate = async (data: LeaveForm) => {
    if (data.dateRange) {
        await leaveRequestModel.actions.create({
            beneficiary_pk: data.beneficiary_pk,
            dates: {
                leave_start: data.dateRange.start.unixTimestamp(),
                leave_end: data.dateRange.end.unixTimestamp(),
            },
            request_type: data.request_type,
            days_count: daysCount.value,
            comment: data.comment,
            file_pks: data.file_pks,
            auto_approve: data.auto_approve,
        })

        toastSuccess('Leave request was created successfully')
    }
}

const handleUpdate = async (data: LeaveForm) => {
    if (data.dateRange) {
        await leaveRequestModel.actions.update({
            pk: props.pk,
            beneficiary_pk: data.beneficiary_pk,
            dates: {
                leave_start: data.dateRange.start.unixTimestamp(),
                leave_end: data.dateRange.end.unixTimestamp(),
            },
            request_type: data.request_type,
            days_count: daysCount.value,
            comment: form.data.comment,
            file_pks: data.file_pks,
        })

        toastSuccess('Leave request was updated successfully')
    }
}

const submit = form.useSubmit(async (data) => {
    if (isEditMode.value) {
        await handleUpdate(data)
    } else {
        await handleCreate(data)
    }

    emit('resolve')
})

const leaveDates = computed({
    get: () => {
        return form.data.dateRange
    },
    set: (dateRange: DateRange) => {
        form.data.dateRange = dateRange
    },
})
</script>
