<template>
    <AppModalWrapper
        :header="header"
        class="!w-[516px]"
        close-button
    >
        <template #default>
            <div v-if="leaveRequest" class="bg-secondary-50 dark:bg-neutral-950 p-2">
                <div class="flex flex-col gap-2">
                    <div class="card px-4">
                        <div class="card__header px-0">
                            <div class="text-sm font-semibold">
                                Status
                            </div>
                            <span
                                class="badge --soft  --normal --rounded"
                                :class="[{'--success': leaveRequest?.status === LeaveRequestStatus.Approved },
                                         {'--danger': leaveRequest?.status === LeaveRequestStatus.Rejected}]"
                            >
                                <span class="rounded-full p-1" :class="dictionaryRecord.bgStyleClass" />
                                <span class="">
                                    {{ dictionaryRecord.title }} request
                                </span>
                            </span>
                        </div>
                        <div class="card__body px-0">
                            <div class="grid grid-cols-3 text-xs">
                                <div class="col-span-3" :class="{ 'hidden': !leaveRequest.remark?.trim() }">
                                    <div class="flex items-center bg-neutral-50 dark:bg-neutral-950 border rounded-lg gap-2 p-2 mb-5">
                                        <div class="w-4 h-4">
                                            <MessageCircleIcon />
                                        </div>
                                        <div>
                                            {{ leaveRequest?.remark }}
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <div class="text-secondary dark:text-secondary-400">
                                        Requested from:
                                    </div>
                                    <span class="font-medium">
                                        {{ getFullName(leaveRequest.agent) }}
                                    </span>
                                </div>
                                <div>
                                    <div class="text-secondary dark:text-secondary-400">
                                        Team:
                                    </div>
                                    <span class="font-medium">
                                        {{ leaveRequest.agent.team?.name }}
                                    </span>
                                </div>
                                <div>
                                    <div class="text-secondary dark:text-secondary-400">
                                        Department:
                                    </div>
                                    <span class="font-medium">
                                        {{ leaveRequest.agent.department.name }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card px-4">
                        <div class="card__header px-0">
                            <div class="text-sm font-semibold">
                                Leave details
                            </div>
                        </div>
                        <div class="card__body px-0">
                            <div class="flex flex-col gap-5">
                                <div class="grid grid-cols-2 gap-x-3">
                                    <div class="flex items-center text-xs gap-2">
                                        <div class="flex items-center gap-2 w-[100px]">
                                            <span class="--primary --normal --soft badge --only">
                                                <CalendarArrowUpIcon />
                                            </span>
                                            <span class="text-secondary dark:text-secondary-400 ">
                                                Start date:
                                            </span>
                                        </div>
                                        <FormField
                                            v-if="isEditMode"
                                            :form="form"
                                            field="start_date"
                                            class="w-24"
                                        >
                                            <InputDate
                                                v-model="form.data.start_date"
                                                timezone="UTC"
                                                placeholder="Start date"
                                                size="small"
                                                :placeholder-icon="false"
                                            />
                                        </FormField>
                                        <span v-else class="font-medium">
                                            {{ formatter.date(leaveRequest.dates.leave_start, 'UTC') }}
                                        </span>
                                    </div>
                                    <div class="flex items-center text-xs gap-2">
                                        <div class="flex items-center gap-2 w-[100px]">
                                            <span class="--primary --normal --soft badge --only">
                                                <CalendarArrowDownIcon />
                                            </span>
                                            <span class="text-secondary dark:text-secondary-400 ">
                                                End date:
                                            </span>
                                        </div>

                                        <FormField
                                            v-if="isEditMode"
                                            :form="form"
                                            field="end_date"
                                            class="w-24"
                                        >
                                            <InputDate
                                                v-model="form.data.end_date"
                                                timezone="UTC"
                                                placeholder="End date"
                                                size="small"
                                                :placeholder-icon="false"
                                            />
                                        </FormField>
                                        <span v-else class="font-medium">
                                            {{ formatter.date(leaveRequest.dates.leave_end, 'UTC') }}
                                        </span>
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-x-3">
                                    <div class="flex items-center text-xs gap-2">
                                        <div class="flex items-center gap-2 !w-[100px]">
                                            <span class="--primary --normal --soft badge --only">
                                                <ClockIcon />
                                            </span>
                                            <span class=" block text-secondary dark:text-secondary-400 ">
                                                Leave days:
                                            </span>
                                        </div>
                                        <FormField
                                            v-if="isEditMode"
                                            :form="form"
                                            field="days_count"
                                            class="w-24"
                                        >
                                            <InputNumber
                                                v-model="form.data.days_count"
                                                size="small"
                                            />
                                        </FormField>
                                        <span v-else class="font-medium">
                                            {{ leaveRequest.days_count }}
                                        </span>
                                    </div>
                                    <div class="flex items-center text-xs gap-2">
                                        <div class="flex items-center gap-2 w-[100px]">
                                            <span class="--primary --normal --soft badge --only">
                                                <AlertCircleIcon />
                                            </span>
                                            <span class=" block text-secondary dark:text-secondary-400 ">
                                                Leave type:
                                            </span>
                                        </div>
                                        <FormField
                                            v-if="isEditMode"
                                            :form="form"
                                            field="request_type"
                                            class="w-24"
                                        >
                                            <InputSelect
                                                v-model="form.data.request_type"
                                                :options="requestTypeOptions"
                                                size="small"
                                            />
                                        </FormField>
                                        <span v-else class="font-medium">
                                            {{ requestTypeDictionary.find(leaveRequest.request_type).title }}
                                        </span>
                                    </div>
                                </div>
                                <div class="flex items-center text-xs gap-2">
                                    <div class="flex items-center gap-2">
                                        <span class="--primary --normal --soft badge --only flex-none">
                                            <MessageCircleIcon />
                                        </span>
                                        <span class="text-secondary dark:text-secondary-400">
                                            Comment:
                                        </span>
                                    </div>
                                    <span class="font-medium">
                                        {{ leaveRequest.comment }}
                                    </span>
                                </div>
                                <div v-if="leaveRequest.file_pks.length" class="flex items-start text-xs space-x-2">
                                    <div class="flex items-center gap-2">
                                        <span class="--primary --normal --soft badge --only">
                                            <PaperclipIcon />
                                        </span>
                                        <span class="text-secondary dark:text-secondary-400">
                                            Uploads:
                                        </span>
                                    </div>
                                    <UploadList
                                        class="w-full"
                                        :files="files"
                                        can-preview
                                        can-download
                                        :can-delete="false"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-if="isEditMode" class="card card__body">
                        <div class="flex flex-col gap-4">
                            <FormField
                                :form="form"
                                field="status"
                                class="flex flex-col"
                            >
                                <div class="grid grid-cols-2 gap-4">
                                    <AppButton
                                        class="rounded-md w-full"
                                        :class="{'--danger': form.data.status === LeaveRequestStatus.Rejected}"
                                        @click="form.data.status = LeaveRequestStatus.Rejected"
                                    >
                                        Decline request
                                    </AppButton>

                                    <AppButton
                                        class="rounded-md w-full"
                                        :class="{'--success': form.data.status === LeaveRequestStatus.Approved}"
                                        @click="form.data.status = LeaveRequestStatus.Approved"
                                    >
                                        Approve request
                                    </AppButton>
                                </div>
                            </FormField>
                            <FormField
                                :form="form"
                                field="remark"
                            >
                                <InputTextarea
                                    v-model="form.data.remark"
                                    maxlength="2048"
                                    placeholder="Remark"
                                />
                            </FormField>
                        </div>
                    </div>
                </div>
            </div>
        </template>

        <template #footer="{close}">
            <div class="flex justify-between">
                <AppButton @click="close">
                    Close
                </AppButton>
                <div v-if="isEditMode" class="flex gap-4">
                    <AppButton :disabled="!form.hasChanges" @click="form.reset()">
                        Discard changes
                    </AppButton>
                    <AppButton class="--primary" @click="submit">
                        Save
                    </AppButton>
                </div>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import { getFullName } from '~/lib/Helper/PersonHelper'
import UploadList from '@/components/Upload/UploadList.vue'
import FormField from '~/components/Form/FormField.vue'
import { LeaveRequestStatus, LeaveRequestType } from '~/api/models/LeaveManagement/LeaveRequest'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import CalendarArrowDownIcon from '~assets/icons/CalendarArrowDownIcon.svg?component'
import CalendarArrowUpIcon from '~assets/icons/CalendarArrowUpIcon.svg?component'
import { getDaysInRange } from '@/lib/core/helper/DateHelper'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import { calculateHolidaysInDateRange } from '~/lib/Helper/LeaveHelper'
import FileData from '@/lib/core/helper/File/FileData'

defineOptions({
    name: 'LeaveRequestReviewModal',
    modal: {
        promise: true,
    },
})

const props = defineProps<{
    pk: PrimaryKey,
    edit?: boolean,
}>()

const emit = defineEmits<{
    close: [],
    resolve: [],
}>()

const form = useForm<{
    status: LeaveRequestStatus
    start_date: Date | undefined
    end_date: Date | undefined
    remark: string | null
    days_count: number
    request_type: LeaveRequestType
}>({
    status: LeaveRequestStatus.Approved,
    start_date: undefined,
    end_date: undefined,
    remark: null,
    days_count: 0,
    request_type: LeaveRequestType.Vacation,
}, {
    start_date: ValidationRules.Required(),
    end_date: ValidationRules.Required(),
    days_count: [ValidationRules.Required('Days count is required', true), ValidationRules.MinNumber(0)],
    remark: ValidationRules.RequiredWhen(() => form.data.status === LeaveRequestStatus.Rejected, 'Remark is required when declining a request'),
    request_type: ValidationRules.Required(),
    status: (value) => {
        if (value !== LeaveRequestStatus.Approved && value !== LeaveRequestStatus.Rejected) {
            return 'Please choose whether to approve or decline the request'
        }
    },
})

const { useModel, useDictionary, hasPermission } = useContext()

const leaveRequestModel = useModel('LeaveRequest')

const requestTypeDictionary = useDictionary('LeaveRequestType')

const requestTypeOptions = computed(() => requestTypeDictionary.mapRecords.forSelect())

const leaveRequestRecord = leaveRequestModel.useRecord({
    with: ['agent', 'agent.team', 'agent.department', 'files'],
})

const daysCount = ref<undefined | number>(undefined)
const holidays = (await leaveRequestModel.actions.getHolidays()).data

watch([() => form.data.start_date, () => form.data.end_date], ([start, end]) => {
    if (form.data.start_date && form.data.end_date) {
        const daysInRange = getDaysInRange({ start, end }) + 1
        const holidaysCount = calculateHolidaysInDateRange(holidays, { start, end })

        daysCount.value = daysInRange - holidaysCount
    } else {
        daysCount.value = undefined
    }
})

useSuspensableComponent(async () => {
    await leaveRequestRecord.fetch(props.pk)

    initData(leaveRequestRecord.record.value)
})

const formatter = useService('formatter')

const leaveRequestStatusDictionary = useDictionary('LeaveRequestStatus')

const files = ref([])

const leaveRequest = computed(() => {
    return leaveRequestRecord.record.value
})

const dictionaryRecord = computed(() => {
    return leaveRequestStatusDictionary.find(leaveRequest.value.status)
})

const initData = (leaveRequest) => {
    form.updateInitialData({
        status: leaveRequest.status,
        start_date: Date.fromUnixTimestamp(leaveRequest.dates.leave_start),
        end_date: Date.fromUnixTimestamp(leaveRequest.dates.leave_end),
        remark: leaveRequest.remark,
        days_count: leaveRequest.days_count,
        request_type: leaveRequest.request_type,
    })

    files.value = leaveRequest.files.map((fileModel) => FileData.fromFileV2Model(fileModel))
}

const isEditMode = computed(() => {
    return props.edit && hasPermission('openLeaveRequestApproveList', 'all')
})

const header = computed(() => {
    return isEditMode.value ? 'Review leave request' : 'Leave request'
})

const submit = form.useSubmit(async (data) => {
    await leaveRequestModel.actions.changeStatus({
        days_count: data.days_count,
        pk: props.pk,
        status: data.status,
        remark: data.remark,
        dates: {
            leave_start: data.start_date.unixTimestamp(),
            leave_end: data.end_date.unixTimestamp(),
        },
        request_type: data.request_type,
    })

    toastSuccess('Leave request was updated successfully')
    emit('resolve')
})
</script>
