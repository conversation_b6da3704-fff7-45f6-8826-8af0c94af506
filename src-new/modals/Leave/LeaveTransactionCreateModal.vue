<template>
    <AppModalWrapper
        header="Create Transaction"
        class="w-[768px]"
        close-button
    >
        <div class="bg-secondary-50 dark:bg-neutral-950 p-1.5">
            <form class="grid grid-cols-2 gap-2" @submit.prevent="submit">
                <div class="overflow-y-auto fancy-scroll">
                    <div class="card px-4">
                        <div class="card__header px-0 font-semibold">
                            Leave details
                        </div>
                        <div class="card__body px-0 text-xs" />
                    </div>
                </div>
            </form>
        </div>
    </AppModalWrapper>
</template>

<script setup lang="ts">
const form = useForm<{
    data: AnyObject | undefined
}>({
    data: undefined,
})

const submit = form.useSubmit((data) => {

})
</script>
