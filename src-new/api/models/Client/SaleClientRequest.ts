import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        SaleClientRequest: SaleClientRequest
    }
}

export enum SaleClientRequestStatus {
    Pending = 'pending',
    Paid = 'paid'
}

export enum SaleClientRequestCategory {
    TicketBaggageProtection = 'ticket_baggage_protection'
}

export default class SaleClientRequest implements Definition.Model {
    public readonly name = 'SaleClientRequest'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        category: z.nativeEnum(SaleClientRequestCategory),
        status: z.nativeEnum(SaleClientRequestStatus),
        sale_version_pk: z.pk(),

        offered_price: z.number(),

        offer_link: z.string().nullable(),
        created_by_pk: z.pk(),
        created_at: z.timestamp(),
    })

    public searchFields = z.object({
        sale_version_pk: z.pk(),
    })

    public relations = defineRelations({
        createdBy: belongsTo('Agent', 'created_by_pk'),
    })

    public actions = defineActions({
        getProtectionOfferPreview: {
            params: z.object({
                sale_version_pk: z.pk(),
                subject: z.string(),
                message: z.string().nullable(),
                plan1: z.number(),
                plan2: z.number(),
                plan3: z.number(),
                baggage_protection_price: z.number(),
                selected_by_default_plan: z.enum(['plan1', 'plan2', 'plan3']),
            }),
            response: z.object({
                url: z.string(),
            }),
        },
        sendProtectionOffer: {
            params: z.object({
                sale_version_pk: z.pk(),
                message: z.string(),
                subject: z.string(),
                email: z.string(),
                plan1: z.number(),
                plan2: z.number(),
                plan3: z.number(),
                baggage_protection_price: z.number(),
                card_pks: z.array(z.pk()),
                selected_by_default_plan: z.enum(['plan1', 'plan2', 'plan3']),
            }),
            response: z.null(),
        },
        getProtectionFormInfo: {
            params: z.object({
                sale_version_pk: z.pk(),
            }),
            response: z.object({
                plan1: z.number(),
                plan2: z.number(),
                plan3: z.number(),
                baggage_protection_price: z.number(),
            }),
        },
        remove: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },
    })
}
