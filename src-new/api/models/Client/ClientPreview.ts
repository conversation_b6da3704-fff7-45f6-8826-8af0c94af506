import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        ClientPreview: ClientPreview
    }
}

/**
 * Public client information
 *
 * @important Doesn't need permissions to view
 */
export default class ClientPreview implements Definition.Model {
    public readonly name = 'ClientPreview'
    public readonly pk = 'id'
    public readonly defaulatOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        first_name: z.string(),
        last_name: z.string().nullable(),
        is_fraud: z.boolean(),
    })

    public relations = defineRelations({
        //
    })

    public actions = defineActions({
        //
    })
}
