import type { Definition } from '~types/lib/Model'
import { ExternalResource } from '~/api/dictionaries/Static/ExternalResourceDictionary'

declare global {
    export interface Models {
        Client: Client
    }
}

/**
 * General client information
 *
 * @important Needs permissions to view
 */
export default class Client implements Definition.Model {
    public readonly name = 'Client'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        client_cabinet_id: z.int().nullable(),
        first_name: z.string(),
        last_name: z.string().nullable(),

        created_at: z.timestamp(),
        from_resource: z.nativeEnum(ExternalResource),
        is_fixed: z.boolean(),
        is_fixed_by_pk: z.pk().nullable(),
        blocked_at: z.timestamp().nullable(),
        blocked_reason: z.string().nullable(),
        blocked_by_pk: z.pk().nullable(),
        client_phone_pk: z.pk().nullable(),
        client_email_pk: z.pk().nullable(),
        status_pk: z.pk(),
        is_fraud: z.boolean(),

        // Relation fields
        email: z.string(),
        phone: z.string().nullable(),
        curator_pk: z.pk().nullable(),
        project_pk: z.pk(),

        referral_client_pk: z.pk().nullable(),

        has_enriched_info: z.boolean(),
    })

    public searchFields = z.object({
        keywords: z.searchKeywords(),
        email: z.string(),
        phone: z.string(),
        id: z.int(),
        fullName: z.string(),
        curator_pk: z.string(),
        from_resource: z.nativeEnum(ExternalResource),

        first_name: z.string(),
        last_name: z.string(),
        recent_activity: z.date(),

        leads_count: z.int(),
        sold_count: z.int(),
        last_lead_pk: z.pk(),
        last_activity: z.timestamp(),
        client_cabinet_id: z.int(),
        has_online_cabinet: z.boolean(),
        has_enrichment_info: z.boolean(),

        utm_ga: z.string(),
        is_fixed: z.boolean(),
        status_pk: z.pk(),
        created_at: z.timestamp(),
    })

    public relations = defineRelations({
        curator: belongsTo('Agent', 'curator_pk'),
        fixedBy: belongsTo('Agent', 'is_fixed_by_pk'),

        emails: hasManyThrough('ClientEmailData', 'ClientClientEmailDataList', 'client_pk'),
        phones: hasManyThrough('ClientPhoneData', 'ClientClientPhoneDataList', 'client_pk'),

        passports: hasManyThrough('ClientPassport', 'ClientClientPassportList', 'client_pk'),
        travelers: hasManyThrough('Traveler', 'ClientTravelerList', 'client_pk'),

        info: belongsTo('ClientAdditionalInfo', 'id'),

        leads: hasManyThrough('Lead', 'ClientLeadList', 'client_pk'),

        enriched: belongsTo('ClientAdditionalEnrichmentInfo', 'id'),

    })

    public actions = defineActions({
        claimSale: {
            params: z.object({
                client_pk: z.pk(),
                sale_pk: z.pk(),
            }),
            response: z.null(),
        },

        setDefaultPhone: {
            params: z.object({
                client_pk: z.pk(),
                phone_pk: z.pk(),
            }),
            response: z.null(),
        },

        setDefaultEmail: {
            params: z.object({
                client_pk: z.pk(),
                email_pk: z.pk(),
            }),
            response: z.null(),
        },
        tryExactIdentifyClient: {
            params: z.object({
                phone: z.string(),
            }),
            response: IdentifiedClient.nullable(),
        },
        tryIdentifyClient: {
            params: z.object({
                phone: z.string(),
            }),
            response: z.array(IdentifiedClient),
        },
        registryClientCall: {
            params: z.object({
                client_pk: z.pk().nullable(),
                call_id: z.string(),
                to_ringcentral_id: z.string(),
                from_ringcentral_id: z.string().nullable(),
                from_phone_number: z.string(),
                payload: z.any(),
            }),
            response: z.null(),
        },
        findClientsBySearchString: {
            params: z.object({
                search_string: z.string(),
            }),
            response: z.array(IdentifiedClient),
        },

        block: {
            params: z.object({
                client_pk: z.pk(),
                reason: z.string(),
            }),
            response: z.null(),
        },

        getActiveCurator: {
            params: z.object({
                project_pk: z.pk(),
                emails: z.array(z.string()),
                phones: z.array(z.string()),
                is_unknown_phone: z.boolean(),
                client_pk: z.pk().nullable(),
            }),
            response: z.object({
                curator_pk: z.pk().nullable(),
            }),
        },

        getCabinetInfo: {
            params: z.object({
                pk: z.pk(),
            }),
            response: ClientInfoResponse,
        },

        getCabinetTransactions: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.object({
                transactions: z.array(ClientInfoTransaction),
            }),
        },

        getCabinetLogs: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.object({
                logs: z.array(ClientInfoLog),
            }),
        },

        createClientLink: {
            params: z.object({
                pk: z.pk(),
                email: z.string(),
            }),
            response: z.null(),
        },

        moveClientLink: {
            params: z.object({
                pk: z.pk(),
                client_cabinet_id: z.int(),
            }),
            response: z.null(),
        },

        createCabinetTransaction: {
            params: z.object({
                client_cabinet_id: z.int(),
                amount: z.number(),
                description: z.string(),
                type: z.nativeEnum(ClientCabinetTransactionType),
            }),
            response: z.null(),
        },

        restoreAccount: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },

        changeCuratorFixedStatus: {
            params: z.object({
                client_pk: z.pk(),
                is_fixed: z.boolean(),
            }),
            response: z.null(),
        },

        setCurator: {
            params: z.object({
                client_pk: z.pk(),
                agent_pk: z.pk().nullable(),
            }),
            response: z.null(),
        },

        updateClientName: {
            params: z.object({
                client_pk: z.pk(),
                first_name: z.string(),
                last_name: z.string().nullable(),
            }),
            response: z.null(),
        },

        agentHasSeenEnriched: {
            params: z.object({
                client_pk: z.pk(),
            }),
            response: z.null(),
        },

        loginAsClient: {
            params: z.object({
                client_pk: z.pk(),
            }),
            response: z.object({
                url: z.string(),
            }),
        },
    })
}

const IdentifiedClient = z.object({
    pk: z.pk(),
    first_name: z.string(),
    last_name: z.string().nullable(),
    email: z.string(),
    phone: z.string().nullable(),
    curator_pk: z.pk().nullable(),
    is_fixed: z.boolean(),
    is_preview: z.boolean(),
}).describe('IdentifiedClient')

export enum ClientCabinetTransactionType {
    RegistrationBonus = 'registration_bonus',
    FlightCashback = 'flight_cashback',
    ReferralRegistered = 'referral_registered',
    Manual = 'manual',
    TicketProtectionCashback = 'ticket_protection_cashback',
}

export enum ClientInfoLogAction {
    Create = 'create',
    Update = 'update',
    Delete = 'delete'
}

export const ClientInfoTransaction = z.object({
    id: z.int(),
    created_at: z.timestamp(),
    amount: z.number(),
    type: z.nativeEnum(ClientCabinetTransactionType),
    payload: z.object({
        sale_pk: z.pk().nullable(),
        pnrs: z.array(z.string()).nullable(),
        description: z.string().nullable(),
    }).nullable(),
}).describe('ClientInfoTransaction')

export const ClientInfoLog = z.object({
    id: z.int(),
    created_at: z.timestamp(),
    model_id: z.int(),
    model_name: z.string(),
    message: z.string(),
    agent_pk: z.pk().nullable(),
    action: z.nativeEnum(ClientInfoLogAction),
}).describe('ClientInfoLog')

export const ClientInfoResponse = z.object({
    person: z.object({
        first_name: z.string(),
        last_name: z.string(),
    }),
    contact: z.object({
        email: z.string(),
        phone: z.string().nullable(),
    }),
    referral: z.object({
        hold: z.number(),
        active: z.number(),
    }),
    deleted_at: z.timestamp().nullable(),
}).describe('ClientInfoResponse')
