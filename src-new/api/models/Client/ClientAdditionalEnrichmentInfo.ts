import type { Definition } from '~types/lib/Model'
import type { components } from '~types/tmgclick/enrichment'

export type ClientEnrichedData = components['schemas']['WebhookRequest']
declare global {
    export interface Models {
        ClientAdditionalEnrichmentInfo: ClientAdditionalEnrichmentInfo
    }
}

export default class ClientAdditionalEnrichmentInfo implements Definition.Model {
    public readonly name = 'ClientAdditionalEnrichmentInfo'
    public readonly pk = ['id', 'auth_pk']

    public fields = z.object({
        id: z.int(),
        has_seen: z.boolean(),
        data: z.custom<ClientEnrichedData>().nullable(),
    })

    public relations = defineRelations({
        //
    })

    public actions = defineActions({
        //
    })
}
