import type { Definition } from '~types/lib/Model'
import type { ClientEnrichedData } from '~/api/models/Client/ClientAdditionalEnrichmentInfo'

declare global {
    export interface Models {
        ClientAdditionalInfo: ClientAdditionalInfo
    }
}

export default class ClientAdditionalInfo implements Definition.Model {
    public readonly name = 'ClientAdditionalInfo'
    public readonly pk = 'id'

    public fields = z.object({
        id: z.int(),
        first_name: z.string(),
        last_name: z.string().nullable(),
        leads_count: z.int().nullable(),
        sold_count: z.int(),
        last_lead_pk: z.pk().nullable(),
        last_activity: z.timestamp(),
        utm_ga: z.string(),
        pending_status_pk: z.pk().nullable(),
        project_pk: z.pk(),
        has_enriched_info: z.boolean(),
    })

    public relations = defineRelations({
        lastLead: belongsTo('Lead', 'last_lead_pk'),
        enriched: belongsTo('ClientAdditionalEnrichmentInfo', 'id'),
    })

    public actions = defineActions({
        //
    })
}
