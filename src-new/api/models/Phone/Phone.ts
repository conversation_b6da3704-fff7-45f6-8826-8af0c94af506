import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        Phone: Phone
    }
}

export default class Phone implements Definition.Model {
    public readonly name = 'Phone'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        value: z.string(),
        is_valid: z.boolean().nullable(),
        fraud_info_pk: z.pk(),
    })

    public relations = defineRelations({
        fraudInfo: belongsTo('FraudInfo', 'fraud_info_pk'),
    })

    public actions = defineActions({
        setFraud: {
            params: z.object({
                pk: z.pk(),
                value: z.boolean(),
                remark: z.string().nullable(),
            }),
            response: z.null(),
        },
        checkIfFraud: {
            params: z.object({
                value: z.array(z.string()),
            }),
            response: z.array(z.string()),
        },
    })
}
