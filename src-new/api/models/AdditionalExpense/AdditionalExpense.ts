import type { Definition } from '~types/lib/Model'
import { ProductPayType, ProductSubType, ProductType } from '~/api/models/Product/Product'

declare global {
    export interface Models {
        AdditionalExpense: AdditionalExpense
    }
}

export default class AdditionalExpense implements Definition.Model {
    public readonly name = 'AdditionalExpense'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        sale_version_pk: z.pk(),
        product_pk: z.pk(),
        executor_pk: z.pk().nullable(),
    })

    public searchFields = z.object({
        id: z.int(),
        sale_version_pk: z.pk(),
    })

    public relations = defineRelations({
        product: belongsTo('Product', 'product_pk'),
    })

    public actions = defineActions({
        create: {
            params: z.object({
                sale_version_pk: z.pk(),
                product: z.object({
                    item_type: z.nativeEnum(ProductType),
                    sub_type: z.namedUnion(ProductSubType).nullable(),
                    sell_price: z.number(),
                    fare: z.number(),
                    pay_type: z.nativeEnum(ProductPayType).nullable(),
                    card_identity: z.pk().nullable(),
                    check_payment: z.number(),
                    check_payment_ps: z.number(),
                    //
                    consolidator_area_pk: z.pk().nullable(),
                    remark: z.string().nullable(),
                    executor_pk: z.pk().nullable(),
                }),
                points_trade: z.array(
                    z.object({
                        award_account_pk: z.pk(),
                        miles_count: z.number(),
                        price_per_mile: z.number(),
                        rcpm: z.number().nullable(),
                    }),
                ),
            }),
            response: z.object({
                pk: z.pk(),
            }),
        },
        update: {
            params: z.object({
                pk: z.pk(),
                product: z.object({
                    item_type: z.nativeEnum(ProductType),
                    sub_type: z.namedUnion(ProductSubType).nullable(),
                    sell_price: z.number(),
                    fare: z.number(),
                    pay_type: z.nativeEnum(ProductPayType).nullable(),
                    card_identity: z.pk().nullable(),
                    check_payment: z.number(),
                    check_payment_ps: z.number(),
                }),
            }),
            response: z.null(),
        },
        delete: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },
    })
}
