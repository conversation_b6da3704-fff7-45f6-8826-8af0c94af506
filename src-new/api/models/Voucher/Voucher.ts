import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        Voucher: Voucher
    }
}

export enum VoucherStatus {
    Active = 'active',
    Expired = 'expired',
    Used = 'used',
    PartiallyUsed = 'partially_used',
    Suspended = 'suspended',
}

export enum VoucherDuration {
    Temporary = 'temporary',
    SentToClient = 'sent_to_client',
}

export enum VoucherCreditWith {
    Airline = 'airline',
    InHouse = 'in_house',
    Company = 'company'
}

export default class Voucher implements Definition.Model {
    public readonly name = 'Voucher'
    public readonly pk = 'id'

    public readonly defaultOrderBy = { expires_at: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        voucher_id: z.string(),
        executor_pk: z.pk(),
        //
        pnr: z.array(z.string()),
        pcc: z.array(z.pk()),
        //
        //
        voucher_price: z.number(),
        net_price: z.number(),
        emd: z.number(),
        upgrade_amount: z.number(),
        points_trade: z.number(),
        fop: z.string(),
        //
        status: z.nativeEnum(VoucherStatus),
        credit_with: z.nativeEnum(VoucherCreditWith),

        issued_at: z.timestamp(),
        expires_at: z.timestamp(),

        model_name: z.string(), // Sale | Client
        model_pk: z.pk(),

        client_pk: z.pk(),

        sale_reflected_pk: z.pk().nullable(),
        is_sent: z.boolean(),
        description: z.string(),

        created_by_pk: z.pk(),
        expected_amount_pk: z.pk().nullable(),

        issue_pk: z.pk().nullable(),
        is_verified: z.boolean(),
    })

    public searchFields = z.object({
        voucher_id: z.string(),
        sale_pk: z.int(),
        client: z.string(),
        created_by_pk: z.pk(),
        executor_pk: z.pk(),
        pnr: z.string(),
        net_price: z.int(),
        upgrade_amount: z.int(),
        emd: z.int(),
        points_trade: z.int(),
        fop: z.string(),
        pcc: z.pk(),
        status: z.nativeEnum(VoucherStatus),
        credit_with: z.nativeEnum(VoucherCreditWith),
        voucher_price: z.int(),
        expires_at: z.timestamp(),
        sale_reflected_pk: z.pk(),
        is_sent: z.nativeEnum(VoucherDuration),
        created_at: z.timestamp(),
        issued_at: z.timestamp(),
        airline_pk: z.pk(),
        expected_amount_pk: z.pk(),
    })

    public relations = defineRelations({
        // sale: belongsTo('Sale', 'sale_pk'),
        client: belongsTo('Client', 'client_pk'),
        executor: belongsTo('Agent', 'executor_pk'),
        createdBy: belongsTo('Agent', 'created_by_pk'),
        issue: belongsTo('Issue', 'issue_pk'),
        additional: hasManyThrough('VoucherPnrAdditional', 'VoucherVoucherPnrAdditionalList', 'voucher_pk'),
    })

    public actions = defineActions({
        editVoucher: {
            params: z.object({
                pk: z.pk(),
                data: z.object({
                    status: z.nativeEnum(VoucherStatus),
                    expires_at: z.timestamp(),
                    issued_at: z.timestamp(),
                    executor_pk: z.pk(),
                    description: z.string(),
                }),
                pnrs: z.array(z.object({
                    id: z.int(),
                    voucher_price: z.number(),
                    net_amount: z.number(),
                    emd_amount: z.number(),
                    points_trade_amount: z.number(),
                    upgrade_amount: z.number(),
                    fop: z.string(),
                    credit_with: z.nativeEnum(VoucherCreditWith),
                })),
            }),
            response: z.null(),
        },
        editClientVoucher: {
            params: z.object({
                pk: z.pk(),
                data: z.object({
                    status: z.nativeEnum(VoucherStatus),
                    expires_at: z.timestamp(),
                    issued_at: z.timestamp(),
                    executor_pk: z.pk(),
                    description: z.string(),

                    voucher_price: z.number(),
                    net_price: z.number(),
                    emd: z.number(),
                    upgrade_amount: z.number(),
                    points_trade: z.number(),
                    fop: z.string(),
                    credit_with: z.nativeEnum(VoucherCreditWith),
                }),
            }),
            response: z.null(),
        },
        useVoucher: {
            params: z.object({
                voucher_pk: z.pk(),
                sale_reflected_pk: z.pk(),
                pnrs: z.array(z.object({
                    pk: z.pk(),
                    voucher_value: z.number(),
                    used_voucher_value: z.number(),
                })).nullable(),
                client_voucher: z.object({
                    voucher_value: z.number(),
                    used_voucher_value: z.number(),
                }).nullable(),
            }),
            response: z.null(),
        },
        createVoucher: {
            params: z.object({
                sale_pk: z.pk(),
                executor_pk: z.pk(),
                expires_at: z.timestamp(),
                issued_at: z.timestamp(),
                status: z.nativeEnum(VoucherStatus),
                passengers: z.array(z.object({
                    pk: z.pk(),
                    related_pnr: z.string(),
                    airline_pks: z.array(z.pk()).nullable(),
                })),
                pnrs: z.array(z.object({
                    pnr: z.string(),
                    voucher_price: z.number(),
                    net_amount: z.number(),
                    emd_amount: z.number(),
                    points_trade_amount: z.number(),
                    upgrade_amount: z.number(),
                    fop: z.string(),
                    pcc: z.array(z.pk()),
                    credit_with: z.nativeEnum(VoucherCreditWith),
                })),
                description: z.string(),
                send_to_client: z.boolean(),
                project_pk: z.pk(),
                template_context: z.object({
                    sale_card_pk: z.pk(),
                    first_address: z.string(),
                    second_address: z.string(),
                    voucher_id: z.string(),
                    expires_at: z.timestamp(),                    model_name: z.string(),
                    model_pk: z.pk(),
                    voucher_price: z.number(),
                    project_pk: z.pk(),
                    passengers: z.array(z.object({
                        pk: z.pk(),
                        related_pnr: z.string(),
                        first_name: z.string(),
                        last_name: z.string(),
                        middle_name: z.string(),
                        is_available: z.boolean(),
                        is_selected: z.boolean(),
                        airline_pks: z.array(z.pk()),
                    })),
                }),
            }),
            response: z.object({
                pk: z.pk(),
                voucher_id: z.string(),
            }),
        },
        getVoucherInfo: {
            params: z.object({
                sale_pk: z.pk(),
                project_pk: z.pk(),
            }),
            response: z.object({
                voucher_id: z.string(),
                passengers_info: z.array(z.object({
                    pk: z.pk(),
                    related_pnr: z.string(),
                    first_name: z.string(),
                    last_name: z.string(),
                    middle_name: z.string(),
                    is_available: z.boolean(),
                    airline_pk: z.pk().nullable(),
                })),
                available_airline_pks: z.array(z.pk()),
                sale_info: z.object({
                    net_amount: z.number(),
                    emd_amount: z.number(),
                    points_trade_amount: z.number(),
                    upgrade_amount: z.number(),
                    fop: z.string(),
                    pcc: z.array(z.pk()),
                    executor_pk: z.pk(),
                }),
            }),
        },
        getVoucherClientInfo: {
            params: z.object({
                client_pk: z.pk(),
                project_pk: z.pk(),
            }),
            response: z.object({
                voucher_id: z.string(),
                cards: z.array(z.object({
                    pk: z.pk(),
                    strip: z.string(),
                    card_holder_name: z.string(),
                    first_address: z.string(),
                    second_address: z.string(),
                })),
            }),
        },
        createClientVoucher: {
            params: z.object({
                client_pk: z.pk(),
                data: z.object({
                    executor_pk: z.pk(),
                    issued_at: z.timestamp(),
                    expires_at: z.timestamp(),
                    status: z.nativeEnum(VoucherStatus),
                    description: z.string(),
                    send_to_client: z.boolean(),

                    sale_card_pk: z.pk(),
                    card_holder_name: z.string(),
                    first_address: z.string(),
                    second_address: z.string(),

                    passenger_pk: z.pk().nullable(),
                    passenger_name: z.string().nullable(),
                    voucher_price: z.number(),
                    net_amount: z.number(),
                    emd_amount: z.number(),
                    points_trade_amount: z.number(),
                    upgrade_amount: z.number(),
                    fop: z.string(),
                    pcc: z.array(z.pk()),
                    credit_with: z.nativeEnum(VoucherCreditWith),
                    project_pk: z.pk(),
                }),
            }),
            response: z.null(),
        },

        getVoucherSummaryInfo: {
            params: z.object({
                status: z.array(z.nativeEnum(VoucherStatus)).nullable(),
                created_at: z.array(z.object({
                    start: z.timestamp(),
                    end: z.timestamp(),
                })).nullable(),
            }),
            response: z.object({
                inhouse_net: z.number(),
                airline_net: z.number(),
                project_debts: z.object({
                    project_pk: z.pk(),
                    debt: z.number(),
                }),
                total: z.number(),
            }),
        },
        sendToClient: {
            params: z.object({
                pk: z.pk(),
                email: z.string(),
            }),
            response: z.null(),
        },
        deleteVoucher: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },

        findClientVoucher: {
            params: z.object({
                voucher_id: z.string(),
            }),
            response: z.object({
                voucher_pk: z.pk().nullable(),
            }),
        },

        linkWithExpectedAmount: {
            params: z.object({
                pk: z.pk(),
                expected_amount_pk: z.pk(),
            }),
            response: z.null(),
        },

        sendToMe: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },

        download: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.object({
                result: z.string(),
            }),
        },
    })
}
