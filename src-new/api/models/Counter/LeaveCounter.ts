import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        LeaveCounter: LeaveCounter
    }
}

export default class LeaveCounter implements Definition.Model {
    public readonly name = 'LeaveCounter'
    public readonly pk = 'agent_pk'

    public fields = z.object({
        agent_pk: z.pk(),
        available_days: z.number(),
        pending_requests: z.number(),
        approved_future_days: z.number(),
        sick_days: z.number(),
        used_days_in_current_year: z.number(),
    })

    public relations = defineRelations({
        agent: belongsTo('Agent', 'agent_pk'),
    })

    public actions = defineActions({
        //
    })
}
