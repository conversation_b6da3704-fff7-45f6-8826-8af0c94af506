import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        Counter: Counter
    }
}

export enum CounterCategory {
    NewLeadsBeginner = 'new_leads_beginner',
    NewLeadsExperienced = 'new_leads_experienced',
    NewLeadsTop = 'new_leads_top',
    LeadTakeRequests = 'lead_take_requests', // Personal, no subscription
    CardAccessRequests = 'card_access_requests', // Personal, no subscription
    CheckIns = 'check_ins', // Subscribed
    BonusLeads = 'bonus_leads', // Unsubscribed
    BonusPlusLeads = 'bonus_plus_leads', // Unsubscribed
    ToBeLostLeads = 'to_be_lost_leads', // Unsubscribed
    DuplicateLeads = 'duplicate_leads', // Unsubscribed
    ElrFrt ='elr_frt', // Unsubscribed
    NotApprovedPriceDropChecks = 'not_approved_price_drop_checks',
    PnrInfo = 'pnr_info',
}

export default class Counter implements Definition.Model {
    public readonly name = 'Counter'
    public readonly pk = ['workspace', 'category', 'auth_pk']
    public readonly defaultOrderBy = { category: 'asc' } as const

    public fields = z.object({
        category: z.nativeEnum(CounterCategory),
        value: z.int(),
    })

    public relations = defineRelations({
        //
    })

    public actions = defineActions({
        //
    })
}
