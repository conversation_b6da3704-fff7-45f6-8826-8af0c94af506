import type { Definition } from '~types/lib/Model'
import { CreditCardType } from '~/api/models/Sale/SaleVersionCard'

declare global {
    export interface Models {
        ProjectCard: ProjectCard
    }
}

export enum ProjectCardCategory {
    ProjectCard = 'comCC',
    ProjectVirtualCard = 'comVCC',

    ProjectVirtualCardOld = 'oldComVCC',
    ProjectNewVirtualCardOld = 'oldNewVCC'
}

export enum ProjectCardUsageArea {
    Local = 'local',
    Global = 'global',
}

export default class ProjectCard implements Definition.Model {
    public readonly name = 'ProjectCard'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'desc' } as const

    public fields = z.object({
        id: z.int(),
        external_id: z.string().nullable(),
        project_pk: z.pk(),
        sale_pk: z.pk().nullable(),
        strip: z.string(),
        expire_month: z.string(),
        expire_year: z.string(),
        card_name: z.string(),

        category: z.nativeEnum(ProjectCardCategory),

        created_at: z.timestamp().nullable(),
        last_used_at: z.timestamp().nullable(),

        is_disabled: z.boolean(),
        is_terminated: z.boolean(),
    })

    public relations = defineRelations({
        //
        vccInfo: belongsTo('ProjectCardAdditionalVcc', 'id'),
        sale: belongsTo('Sale', 'sale_pk'),
    })

    public actions = defineActions({
        //
        createVirtualCard: {
            params: z.object({
                sale_pk: z.pk(),
                amount_limit: z.number(),
                airline_pk: z.pk(),
                client_pk: z.pk(),
                first_name: z.string(),
                last_name: z.string(),
                usage_area: z.nativeEnum(ProjectCardUsageArea),
                billing: z.object({
                    country: z.string(),
                    city: z.string(),
                    state: z.string(),
                    street: z.string(),
                    zip: z.string(),
                    bank_phone: z.string(),
                }),
            }),
            response: z.null(),
        },

        getBillingAddressForNewCard: {
            params: z.object({
                sale_pk: z.pk(),
            }),
            response: z.object({
                country: z.string(),
                city: z.string(),
                state: z.string(),
                street: z.string(),
                zip: z.string(),
                bank_phone: z.string(),
            }),
        },

        getCardBillingAddress: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.object({
                country: z.string(),
                city: z.string(),
                state: z.string(),
                street: z.string(),
                zip: z.string(),
                bank_phone: z.string(),
            }),
        },

        updateVirtualCardAmount: {
            params: z.object({
                pk: z.pk(),
                amount_limit: z.number(),
            }),
            response: z.null(),
        },

        updateTransactions: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },

        updateAllTransactions: {
            params: z.object({}),
            response: z.null(),
        },

        getCardInfo: {
            params: z.object({
                pk: z.pk(),
            }),
            response: GetCardInfoResponse,
        },

        getCredentialsUrl: {
            params: z.object({
                product_pk: z.pk(),
            }),
            response: z.object({
                url: z.string(),
            }),
        },
    })

    public searchFields = z.object({
        id: z.int(),
        strip: z.string(),
        card_name: z.string(),
        category: z.nativeEnum(ProjectCardCategory),
        sale_pk: z.pk(),
        amount: z.number(),
        amount_to_pay: z.number(),
        amount_used: z.number(),
        is_used: z.boolean(),
        is_terminated: z.boolean(),
        airline_pk: z.pk(),
        external_id: z.string(),
        is_sale_closed: z.boolean(),
    })
}

export const GetCardInfoResponse = z.object({
    card_type: z.nativeEnum(CreditCardType),
    holder_name: z.string(),
    card_number: z.string(),
    expiration: z.string(),
    cvv: z.string(),
}).describe('GetCardInfoResponse')
