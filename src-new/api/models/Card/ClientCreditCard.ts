import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        ClientCreditCard: ClientCreditCard
    }
}

export default class ClientCreditCard implements Definition.Model {
    public readonly name = 'ClientCreditCard'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        fraud_info_pk: z.pk(),
    })

    public relations = defineRelations({
        fraudInfo: belongsTo('FraudInfo', 'fraud_info_pk'),
    })

    public actions = defineActions({
        setFraud: {
            params: z.object({
                pk: z.pk(),
                value: z.boolean(),
                remark: z.string().nullable(),
            }),
            response: z.null(),
        },
        checkIfFraud: {
            params: z.object({
                card_number: z.string(),
                expiration: z.string(),
            }),
            response: z.boolean(),
        },
    })
}
