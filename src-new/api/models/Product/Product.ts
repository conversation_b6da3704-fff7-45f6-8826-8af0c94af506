import type { Definition } from '~types/lib/Model'
import { SaleInsuranceType } from '~/api/dictionaries/Static/Sale/SaleInsuranceTypeDictionary'
import { defineUnion } from '~/lib/Helper/EnumHelper'
import type zod from 'zod'

declare global {
    export interface Models {
        Product: Product
    }
}

export enum ProductPayType {
    CC = 'CC', // SaleCardList

    ComCC = 'comCC', // SaleProjectCardList for category
    ComVCC = 'comVCC',  // SaleProjectCardList for category

    PaxWire = 'PW',
    Invoice = 'Invoice',
    HolderCC = 'HCC',

    Other = 'Other',

    OldComVCC = 'oldComVCC', // not to use
    OldNewCVV = 'oldNewVCC', // not to use
}

export enum ProductType {
    Baggage = 'Baggage',
    CheckIn = 'Check-in',
    TicketRefund = 'Ticket Refund',
    CommissionRefund = 'Commission Refund',

    Other = 'Other',
    Voucher = 'Voucher',
    Seats = 'Seats',
    //
    ExtraGP = 'Extra GP',
    Insurance = 'Insurance',
    FlexibleTicket = 'Flexible Ticket',
    //
    Miles = 'Miles',
    Ticket = 'Ticket',
    Tips = 'Tips',

    SpecialServicesFee = 'Special Services Fee',
    AirlineReimbursementFee = 'Airline Reimbursement Fee',
    Emd = 'Emd',
    PointsTrade = 'Points Trade',

    Upgrade = 'Upgrade',
    UpgradeTicket = 'Upgrade Ticket',
    CashUpgrade = 'Cash Upgrade',
    AwardUpgrade = 'Award Upgrade'
}

export default class Product implements Definition.Model {
    public readonly name = 'Product'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'desc' } as const

    public fields = z.object({
        id: z.int(),
        sale_pk: z.pk(),
        item_type: z.nativeEnum(ProductType),
        sub_type: z.nativeEnum(SaleInsuranceType).nullable(), // TODD: implement named union of enums
        // money
        sell_price: z.number(),
        net_price: z.number(),
        net_price_base: z.number(),
        net_price_currency_pk: z.pk(),
        fare: z.number(),
        tax: z.number(),
        commission: z.number(),
        commission_ps: z.number(),

        issuing_fee: z.number(),
        issued_at: z.timestamp().nullable(),

        pay_type: z.string(), //
        card_identity: z.pk().nullable(),

        check_payment: z.number(),
        check_payment_ps: z.number(),
        profit: z.number(),
        balance: z.number(),
        fee: z.number(),

        //

        sale_version_pk: z.pk().nullable(),
        consolidator_pk: z.pk().nullable(),
        consolidator_area_pk: z.pk().nullable(),
        is_award: z.boolean(),
        external_number: z.string().nullable(),
        order_number: z.string().nullable(),

        remark: z.string().nullable(),

        created_at: z.timestamp(),
        created_by_pk: z.pk().nullable(),

        client_approve_pk: z.pk().nullable(),
        chat_branch_name: z.string().nullable(),
    })

    public searchFields = z.object({
        keywords: z.string(),
        id: z.int(),
        sale_pk: z.pk(),
        remark: z.string(),
        external_number: z.string(),
        order_number: z.string(),
        item_type: z.nativeEnum(ProductType),
        is_award: z.boolean(),
        has_request: z.boolean(),
        is_active: z.boolean(),
        sale_version_pk: z.pk(),
    })

    public relations = defineRelations({
        consolidator: belongsTo('Consolidator', 'consolidator_pk'),
        consolidatorArea: belongsTo('ConsolidatorArea', 'consolidator_area_pk'),
        info: belongsTo('ProductAdditionalInfo', 'id'),
        sale: belongsTo('Sale', 'sale_pk'),
        clientApprove: belongsTo('ProductClientApprove', 'client_approve_pk'),
        createdBy: belongsTo('Agent', 'created_by_pk'),
    })

    public actions = defineActions({
        sellPricesRebalance: {
            params: z.object({
                data: z.array(z.object({
                    id: z.number(),
                    sell_price: z.number(),
                    check_payment: z.number(),
                })),
            }),
            response: z.null(),
        },

    })
}

export type ProductSubTypeValue = zod.infer<typeof ProductSubType[keyof typeof ProductSubType]>

export const ProductSubType = defineUnion(ProductType, {
    [ProductType.Insurance]: z.nativeEnum(SaleInsuranceType),
    [ProductType.Baggage]: z.null(),
    [ProductType.CheckIn]: z.null(),
    [ProductType.TicketRefund]: z.null(),
    [ProductType.CommissionRefund]: z.null(),

    [ProductType.Other]: z.null(),
    [ProductType.Voucher]: z.null(),
    [ProductType.Seats]: z.null(),
    //
    [ProductType.ExtraGP]: z.null(),
    [ProductType.FlexibleTicket]: z.null(),
    //
    [ProductType.Miles]: z.null(),
    [ProductType.Ticket]: z.null(),
    [ProductType.Tips]: z.null(),

    [ProductType.SpecialServicesFee]: z.null(),
    [ProductType.AirlineReimbursementFee]: z.null(),
    [ProductType.Emd]: z.null(),
    [ProductType.PointsTrade]: z.null(),

    [ProductType.Upgrade]: z.null(),
    [ProductType.UpgradeTicket]: z.null(),
    [ProductType.CashUpgrade]: z.null(),
    [ProductType.AwardUpgrade]: z.null(),
})
