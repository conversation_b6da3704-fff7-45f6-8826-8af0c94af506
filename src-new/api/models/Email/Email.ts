import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        Email: Email
    }
}

export default class Email implements Definition.Model {
    public readonly name = 'Email'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'desc' } as const

    public fields = z.object({
        id: z.int(),
        value: z.string(),
        fraud_info_pk: z.pk(),
    })

    public relations = defineRelations({
        fraudInfo: belongsTo('FraudInfo', 'fraud_info_pk'),
    })

    public actions = defineActions({
        setFraud: {
            params: z.object({
                pk: z.pk(),
                value: z.boolean(),
                remark: z.string().nullable(),
            }),
            response: z.null(),
        },
        checkIfFraud: {
            params: z.object({
                value: z.array(z.string()),
            }),
            response: z.array(z.string()),
        },
    })
}
