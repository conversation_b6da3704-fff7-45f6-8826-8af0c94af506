import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        EmailTemplate: EmailTemplate
    }
}

export enum EmailTemplateName {
    FollowUpIntro = 'followUpIntro',
    FollowUpTravelCash = 'followUpTravelCash',
    FollowUp1 = 'followUp1',
    FollowUp2 = 'followUp2',
    FollowUp3 = 'followUp3',
    Voucher = 'voucher',
}

export enum EmailType {
    Default = 'default',
    Simplified = 'simplified'
}

export enum EmailSubject {
    Quotes = 'quotes',
    Booking = 'booking',
    Call = 'call',
    Custom = 'custom',
}

export default class EmailTemplate implements Definition.Model {
    public readonly name = 'EmailTemplate'
    public readonly pk = 'type'

    public readonly defaultOrderBy = { type: 'asc' } as const

    public fields = z.object({
        type: z.nativeEnum(EmailTemplateName),
    })

    public relations = defineRelations({
        //
    })

    public actions = defineActions({
        preview: {
            params: z.object({
                name: z.nativeEnum(EmailTemplateName),
                context: z.any(),
                type: z.nativeEnum(EmailType).nullable(),
                subject: z.nativeEnum(EmailSubject).nullable(),
                selected_pqs: z.array(z.int()).nullable(), // @todo PKs should be there
                include_travel_cash: z.boolean().nullable(),
            }),
            response: z.object({
                html: z.string().nullable(),
                url: z.string().nullable(),
            }),
        },
    })
}
