import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        FraudInfo: FraudInfo
    }
}

export default class FraudInfo implements Definition.Model {
    public readonly name = 'FraudInfo'
    public readonly pk = 'pk'
    public readonly defaultOrderBy = false

    public fields = z.object({
        model_name: z.string(),
        model_pk: z.pk(),
        is_fraud: z.boolean(),
        remark: z.string().nullable(),
    })

    public relations = defineRelations({
        //
    })

    public actions = defineActions({
        //
    })
}
