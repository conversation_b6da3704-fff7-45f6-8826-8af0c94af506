import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        ChatGateway: ChatGateway
    }
}

export enum ChatGatewayDriver {
    RingCentral = 'rc',
    Internal = 'internal',
    InternalWithGroups = 'internal_with_groups',
    Assistant = 'assistant',
}

export default class ChatGateway implements Definition.Model {
    public readonly name = 'ChatGateway'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        driver: z.nativeEnum(ChatGatewayDriver),
        config: z.any().nullable(),
        project_pk: z.pk(),
    })

    public relations = defineRelations({
        //
    })

    public actions = defineActions({
        //
    })
}
