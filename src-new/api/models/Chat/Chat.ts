import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        Chat: Chat
    }
}

export enum ChatType {
    Sale = 'sale',
    Assistant = 'assistant',
}

export const TestChatPK = '9999999999'

export default class Chat implements Definition.Model {
    public readonly name = 'Chat'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'desc' } as const

    public fields = z.object({
        id: z.int(),
        name: z.string(),
        type: z.softEnum(ChatType),
        is_readonly: z.boolean(),
        is_archived: z.boolean(),

        // Relation
        model_name: z.string(),
        model_pk: z.pk(),
        chat_group_pks: z.array(z.pk()),
        gateway_pk: z.pk(),
    })

    public searchFields = z.object({
        model_name: z.string(),
        model_pk: z.pk(),
    })

    public relations = defineRelations({
        sale: belongsTo('Sale', morphKey),
        groups: hasMany('ChatGroup', 'chat_group_pks'),
        messages: hasManyThrough('ChatMessage', 'ChatChatMessageList', 'chat_pk'),
        gateway: belongsTo('ChatGateway', 'gateway_pk'),

        info: belongsTo('ChatAdditionalInfo', 'id'),
    })

    public actions = defineActions({
        setLastReadAt: {
            params: z.object({
                pk: z.pk(),
                last_read_at: z.timestamp().nullable(),
            }),
            response: z.null(),
        },

        restoreFromArchive: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.model('Chat'),
        },
    })
}
