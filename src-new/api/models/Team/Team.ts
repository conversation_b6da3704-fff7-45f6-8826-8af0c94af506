import type { Definition } from '~types/lib/Model'
import { belongsTo, hasManyThrough } from '~/utils/relations'

declare global {
    export interface Models {
        Team: Team
    }
}

export default class Team implements Definition.Model {
    public readonly name = 'Team'
    public readonly pk = 'id'

    public readonly defaultOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        name: z.string(),
        department_pk: z.pk(),
        pnr_queue: z.int().nullable(),
    })

    public relations = defineRelations({
        department: belongsTo('Department', 'department_pk'),
        agents: hasManyThrough('Agent', 'TeamAgentList', 'team_pk'),
    })

    public actions = defineActions({
        create: {
            params: z.object({
                data: z.partialModel('Team'),
            }),
            response: z.model('Team'),
        },
        update: {
            params: z.object({
                pk: z.pk(),
                name: z.string(),
                department_pk: z.pk(),
                pnr_queue: z.int().nullable(),
                // manager_pk: z.pk().nullable(),
            }),
            response: z.model('Team'),
        },
        delete: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },
    })
}
