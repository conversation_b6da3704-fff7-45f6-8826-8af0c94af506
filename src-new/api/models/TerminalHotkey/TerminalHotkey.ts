import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        TerminalHotkey: TerminalHotkey
    }
}

export default class TerminalHotkey implements Definition.Model {
    public readonly name = 'TerminalHotkey'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        title: z.string(),
        description: z.string().nullable(),
        command: z.string(),
        autorun: z.boolean(),
        keys: z.array(z.string()),

        consolidator_system_name: z.string().nullable(),
    })

    public relations = defineRelations({
        //
    })

    public actions = defineActions({
        createOrUpdate: {
            params: z.object({
                data: z.array(z.object({
                    pk: z.pk().nullable(),
                    title: z.string(),
                    description: z.string().nullable(),
                    command: z.string(),
                    autorun: z.boolean(),
                    keys: z.array(z.string()),
                    consolidator_system_name: z.string().nullable(),
                })),
            }),
            response: z.null(),
        },

        delete: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },
    })
}
