import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        SaleVersionCard: SaleVersionCard
    }
}

export enum SaleCardVerifyStatus {
    New = 'new',
    Waiting = 'waiting',
    NeedApprove=  'need_approve',
    Complete = 'complete',
    Error = 'error'
}

export enum CreditCardType {
    Electron = 'electron',
    Maestro = 'maestro',
    Dankort = 'dankort',
    InterPayment = 'inter_payment',
    UnionPay = 'unionPay',
    Visa = 'visa',
    Master = 'master',
    Amex = 'amex',
    Diners = 'diners',
    Discover = 'discover',
    JCB = 'jcb',
}

const SaleCardData = z.object({
    first_name: z.string(),
    last_name: z.string(),
    amount: z.number(),
    card: z.string(),
    expiration: z.string(),
    cvv: z.string(),
    street: z.string(),
    city: z.string(),
    state: z.string(),
    zip: z.string(),
    country: z.string(),
    bank_phone: z.string(),
    email: z.string(),
}).describe('SaleCardData')

export default class SaleVersionCard implements Definition.Model {
    public readonly name = 'SaleVersionCard'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'desc' } as const
    public readonly cacheTTLms = Timespan.minutes(5).inMilliseconds
    public fields = z.object({
        id: z.int(),
        sale_pk: z.pk(),
        client_card_pk: z.pk(),
        sale_version_pk: z.pk(),
        strip: z.string().nullable(),
        credit_card_type: z.nativeEnum(CreditCardType),
        is_verified: z.boolean().nullable(),
        is_wrong: z.boolean(),
        verify_status: z.nativeEnum(SaleCardVerifyStatus),
        expiration: z.string().nullable(),
        first_name: z.string(),
        last_name: z.string(),
        postal_code: z.string(),
        state: z.string(),
        city: z.string(),
        street: z.string(),
        country: z.string(),
        email: z.string(),
        bank_phone: z.string(),
        amount: z.number(),
        invoice_number: z.string(),
    })

    public relations = defineRelations({
        sale: belongsTo('Sale', 'sale_pk'),
        clientCard: belongsTo('ClientCreditCard', 'client_card_pk'),
    })

    public actions = defineActions({
        requestTestAmount: {
            params: z.object({
                pk: z.pk(),
                sale_pk: z.pk(),
            }),
            response: z.object({
                amount: z.number(),
            }),
        },
        create: {
            params: z.object({
                sale_version_pk: z.pk(),
                data: SaleCardData,
            }),
            response: z.object({
                card_pk: z.pk(),
            }),
        },
        update: {
            params: z.object({
                card_pk: z.pk(),
                data: SaleCardData,
            }),
            response: z.null(),
        },
        delete: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },
        updateOafSplit: {
            params: z.object({
               data: z.record(z.pk(), z.number()),
            }),
            response: z.null(),
        },
        changeIsWrong: {
            params: z.object({
                pk: z.pk(),
                is_wrong: z.boolean(),
            }),
            response: z.null(),
        },
        verifyCard: {
            params: z.object({
                pk: z.pk(),
                amount: z.number(),
                test_amount: z.boolean(),
                payment_gateway_pk: z.pk(),
            }),
            response: z.object({
                success: z.boolean(),
                try_count: z.number(),
            }),
        },
        getUsedBeforeSalePks: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.object({
                sale_pks: z.array(z.pk()),
            }),
        },
        requestCardSensitiveData: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.object({
                cvv: z.string(),
                card: z.string(),
            }),
        },
        changeIsVerified: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },
    })
}
