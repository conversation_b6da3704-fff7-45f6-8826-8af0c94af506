import type { Definition } from '~types/lib/Model'
import { SaleCategoryType } from '~/api/models/Sale/Sale'
import { ItineraryType } from '~/api/models/Lead/Lead'
import { ProductPayType, ProductType } from '~/api/models/Product/Product'

declare global {
    export interface Models {
        SaleVersion: SaleVersion
    }
}

export default class SaleVersion implements Definition.Model {
    public readonly name = 'SaleVersion'
    public readonly pk = 'id'

    public readonly defaultOrderBy = { id: 'desc' } as const

    public fields = z.object({
        id: z.int(),
        sale_pk: z.pk(),
        from_iata_code: z.string(),
        to_iata_code: z.string(),
        departure_at: z.timestamp(),
        return_at: z.timestamp().nullable(),
        itinerary_type: z.nativeEnum(ItineraryType),
        is_active: z.boolean(),
        client_phone_pk: z.pk().nullable(),
        client_email_pk: z.pk().nullable(),
        price_quote_pk: z.pk(),
        sale_category: z.nativeEnum(SaleCategoryType),
        invoice: z.string(),
        payment_description: z.string(),
    })

    public relations = defineRelations({
        passenger: hasManyThrough('SaleVersionPassenger', 'SaleVersionSaleVersionPassengerList', 'sale_version_pk'),
        pnrs: hasManyThrough('SaleVersionPnr', 'SaleVersionSaleVersionPnrList', 'sale_version_pk'),
        products: hasManyThrough('Product', 'SaleVersionProductList', 'sale_version_pk'),
        sale: belongsTo('Sale', 'sale_pk'),
        cards: hasManyThrough('SaleVersionCard', 'SaleVersionSaleVersionCardList', 'sale_version_pk'),
        priceQuote: belongsTo('PriceQuote', 'price_quote_pk'),
        clientEmail: belongsTo('Email', 'client_email_pk'),
        incentiveSales: hasManyThrough('IncentiveSale', 'SaleVersionIncentiveSaleList', 'sale_version_pk'),
        additionalExpenses: hasManyThrough('AdditionalExpense', 'SaleVersionAdditionalExpenseList', 'sale_version_pk'),
    })

    public actions = defineActions({
        getAvailableAirlines: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.array(z.pk()),
        },

        setDefaultEmail: {
            params: z.object({
                sale_version_pk: z.pk(),
                email_pk: z.pk(),
            }),
            response: z.null(),
        },

        setDefaultPhone: {
            params: z.object({
                sale_version_pk: z.pk(),
                phone_pk: z.pk(),
            }),
            response: z.null(),
        },

        getCashUpgradeInfo: {
            params: z.object({
                sale_version_pk: z.pk(),
            }),
            response: GetCashUpgradeInfoData,
        },

        processCashUpgradeInfo: {
            params: z.object({
                airline_pk: z.pk(),
                passengers_pks: z.array(z.pk()),
                file_pks: z.array(z.pk()),
            }),
            response: ProcessCashUpgradeInfoData,
        },

        createCashUpgrades: {
            params: z.object({
                sale_version_pk: z.pk(),
                card_pk: z.pk().nullable(),
                pay_type: z.nativeEnum(ProductPayType),
                upgrades: z.array(z.object({
                    passenger_pk: z.pk(),
                    pnr: z.string(),
                    flight_segment: z.string(),
                    emd_price: z.number(),
                    sell_price: z.number(),
                    emd_number: z.string(),
                })),
            }),
            response: z.null(),
        },

        populateTicket: {
            params: z.object({
                ticket_pk: z.pk(),
            }),
            response: z.null(),
        },
        checkPnr: {
            params: z.object({
                ticket_pk: z.pk(),
                pnr: z.string(),
                consolidator_area_pk: z.pk().nullable(),
            }),
            response: z.object({
                current: CheckPnrResponse,
                tickets: z.array(CheckPnrResponse).nullable(),
                error_message: z.string().nullable(),
                error_code: z.int().nullable(),
            }),
        },
        applyCheckPnr: {
            params: z.object({
                ticket_pk: z.pk(),
                data: z.object({
                    validating_carrier_pk: z.pk().nullable(), // airline
                    consolidator_area_pk: z.pk().nullable(), // PCC
                    net_price: z.number().nullable(),
                    tax: z.number().nullable(),
                    fare: z.number().nullable(),
                    check_payment: z.number().nullable(),
                    commission: z.number().nullable(),
                    issued_at: z.timestamp().nullable(), /// issue_date
                    issuing_fee: z.number().nullable(),
                    external_number: z.string().nullable(), // ticket_number ??

                    passenger_first_name: z.string().nullable(),
                    passenger_last_name: z.string().nullable(),
                    passenger_middle_name: z.string().nullable(),
                    passenger_birthday_at: z.timestamp().nullable(),
                }),
            }),
            response: z.null(),
        },

        recoveryCreditCards: {
            params: z.object({
                sale_version_pk: z.pk(),
                key: z.string(),
                password: z.string(),
                check_sum: z.string(),
                part2: z.string(),
            }),
            response: z.any(),
        },

        checkOnlinePnr: {
            params: z.object({
                ticket_pk: z.pk(),
                file_pks: z.array(z.pk()),
            }),
            response: CheckOnlinePnrResponse,
        },

        applyOnlinePnr: {
            params: z.object({
                ticket_pk: z.pk(),
                tickets: z.array(z.object({
                    ticket_pk: z.pk(),
                    pnr: z.string(),
                    airline_pk: z.pk(),
                    first_name: z.string(),
                    last_name: z.string(),
                    tkt_number: z.string(),
                    air_pnr: z.string(),
                    fare: z.number(),
                    tax: z.number(),
                    fop: z.string(),
                })),
            }),
            response: z.null(),
        },
    })
}

export const CheckPnrResponse = z.object({
    pnr: z.string(),
    validating_carrier_pk: z.pk().nullable(), // airline
    consolidator_area_pk: z.pk().nullable(), // PCC
    net_price: z.number(),
    tax: z.number(),
    fare: z.number(),
    check_payment: z.number(),
    commission: z.number(),
    issued_at: z.timestamp(), /// issue_date
    issuing_fee: z.number(),
    external_number: z.string().nullable(), // ticket_number ??
    passenger_first_name: z.string().nullable(),
    passenger_last_name: z.string().nullable(),
    passenger_middle_name: z.string().nullable(),
    passenger_birthday_at: z.timestamp().nullable(),
    passenger_type: z.string().nullable(), // z.nativeEnum(PassengerAge),
    //issued_with ??? card strip ??
}).describe('CheckPnrResponse')

export const GetCashUpgradeInfoData = z.object({
    airlines: z.array(z.object({
        pk: z.pk(),
        name: z.string(),
        pnrs: z.array(z.object({
            pnr: z.string(),
            passengers: z.array(z.object({
                pk: z.pk(),
                first_name: z.string(),
                last_name: z.string(),
                middle_name: z.string(),
            })),
        })),
    })),
}).describe('GetCashUpgradeInfoData')

export const ProcessCashUpgradeInfoData = z.object({
    passengers: z.array(z.object({
        passenger: z.object({
            pk: z.pk(),
            first_name: z.string(),
            last_name: z.string(),
            middle_name: z.string(),
        }),
        upgrades: z.array(z.object({
            flight_segment: z.string(),
            emd_price: z.number(),
            emd_number: z.string(),
            upgrade_type: z.nativeEnum(ProductType),
        })),
    })),
}).describe('ProcessCashUpgradeInfoData')

export const CheckOnlinePnrResponse = z.object({
    tickets: z.array(z.object({
        ticket_pk: z.pk().nullable(),
        pnr: z.string(),
        airline_pk: z.pk(),
        first_name: z.string(),
        last_name: z.string(),
        tkt_number: z.string(),
        air_pnr: z.string(),
        fare: z.number(),
        tax: z.number(),
        fop: z.string().nullable(),
    })),
}).describe('CheckOnlinePnrResponse')
