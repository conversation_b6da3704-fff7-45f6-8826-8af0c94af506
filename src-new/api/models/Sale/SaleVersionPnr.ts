import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        SaleVersionPnr: SaleVersionPnr,
    }
}

export enum PnrFareType {
    PubNoComm = 'pub_no_comm',
    PubWithComm = 'pub_with_comm',
    Private = 'private',
    TourFare = 'tour_fare',
    TourFareCk = 'tour_fare_ck',
    CorporateFare = 'corporate_fare'
}

export enum PnrInfoType {
    RefundBeforeDeparture = 'refund_before_departure',
    RefundAfterDeparture = 'refund_after_departure',
    ExchangeBeforeDeparture = 'exchange_before_departure',
    ExchangeAfterDeparture = 'exchange_after_departure',
    NoShowRefund = 'no_show_refund',
    NoShowExchange = 'no_show_exchange',
}

export default class SaleVersionPnr implements Definition.Model {
    public readonly name = 'SaleVersionPnr'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { pnr: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        pnr: z.string(),
        sale_version_pk: z.pk(),
        last_ticket_pk: z.pk(),
        fare_type: z.nativeEnum(PnrFareType).nullable(),
        is_processed: z.boolean(),
        is_started_by_pk: z.pk().nullable(),
        is_started_at: z.timestamp().nullable(),
        remark: z.string().nullable(),
    })

    public relations = defineRelations({
        saleVersion: belongsTo('SaleVersion', 'sale_version_pk'),
        info: hasManyThrough('SaleVersionPnrInfo', 'SaleVersionPnrSaleVersionPnrInfoList', 'pnr_pk'),
    })

    public searchFields = z.object({
        sale_id: z.pk(),
        is_processed: z.boolean(),
        pnr: z.string(),
        departure_at: z.timestamp(),
        processed_by_pk: z.pk(),
    })

    public actions = defineActions({
        //

        updatePnrFareType: {
            params: z.object({
                pnr_pk: z.pk(),
                fare_type: z.nativeEnum(PnrFareType).nullable(),
            }),
            response: z.null(),
        },

        markAsProcessed: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },

        startWork: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },

        endWork: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },

        leaveRemark: {
            params: z.object({
                pk: z.pk(),
                remark: z.string().nullable(),
            }),
            response: z.null(),
        },

        getPnrDetails: {
            params: z.object({
                pnr: z.string(),
                sale_version_pk: z.pk(),
                ticket_pk: z.pk(),
            }),
            response: z.object({
                text: z.string(),
            }),
        },
    })
}
