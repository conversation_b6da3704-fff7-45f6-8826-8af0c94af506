import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        SaleRequestHelp: SaleRequestHelp
    }
}

export default class SaleRequestHelp implements Definition.Model {
    public readonly name = 'SaleRequestHelp'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        reason: z.string(),
        sale_pk: z.pk(),
        created_by_pk: z.pk(),
        executor_pk: z.pk().nullable(),
    })

    public relations = defineRelations({
        //
    })

    public actions = defineActions({
        createRequest: {
            params: z.object({
                sale_pk: z.pk(),
                reason: z.string(),
            }),
            response: z.null(),
        },
        takeRequest: {
            params: z.object({
                request_pk: z.pk(),
            }),
            response: z.null(),
        },
    })
}
