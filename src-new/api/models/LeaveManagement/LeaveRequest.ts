import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        LeaveRequest: LeaveRequest
    }
}

export enum LeaveRequestStatus {
    Rejected = 'rejected',
    Pending = 'pending',
    Approved = 'approved',
}

export enum LeaveRequestType {
    SickDay = 'sickday',
    Vacation = 'vacation',
}

export default class LeaveRequest implements Definition.Model {
    public readonly name = 'LeaveRequest'
    public readonly defaultOrderBy = { created_at: 'desc' } as const
    public readonly pk = 'id'

    public fields = z.object({
        id: z.int(),
        beneficiary_pk: z.pk(),
        dates: z.object({
            leave_start: z.timestamp(),
            leave_end: z.timestamp(),
        }),
        comment: z.string().nullable(),
        status: z.enum(LeaveRequestStatus),
        request_type: z.enum(LeaveRequestType),
        file_pks: z.array(z.pk()),
        days_count: z.int(),
        remark: z.string().nullable(),
        created_at: z.timestamp(),
    })

    public relations = defineRelations({
        agent: belongsTo('Agent', 'beneficiary_pk'),
        files: hasMany('File', 'file_pks'),
    })

    public searchFields = z.object({
        beneficiary_pk: z.pk(),
        beneficiary_team_pk: z.pk(),
        beneficiary_department_pk: z.pk(),
        dates: z.timestamp(),
        comment: z.string(),
        status: z.enum(LeaveRequestStatus),
        request_type: z.enum(LeaveRequestType),
        days_count: z.int(),
    })

    public actions = defineActions({
        create: {
            params: z.object({
                beneficiary_pk: z.pk(),
                dates: z.object({
                    leave_start: z.timestamp(),
                    leave_end: z.timestamp(),
                }),
                days_count: z.int(),
                comment: z.string().nullable(),
                request_type: z.enum(LeaveRequestType),
                file_pks: z.array(z.pk()),
                auto_approve: z.boolean(),
            }),
            response: z.null(),
        },
        update: {
            params: z.object({
                pk: z.pk(),
                beneficiary_pk: z.pk(),
                dates: z.object({
                    leave_start: z.timestamp(),
                    leave_end: z.timestamp(),
                }),
                days_count: z.int(),
                comment: z.string().nullable(),
                request_type: z.enum(LeaveRequestType),
                file_pks: z.array(z.pk()),
            }),
            response: z.null(),
        },
        delete: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },
        changeStatus: {
            params: z.object({
                pk: z.pk(),
                status: z.enum(LeaveRequestStatus),
                remark: z.string().nullable(),
                dates: z.object({
                    leave_start: z.timestamp(),
                    leave_end: z.timestamp(),
                }),
                request_type: z.enum(LeaveRequestType),
                days_count: z.int(),
            }),
            response: z.null(),
        },
        getSummaryChartData: {
            params: z.object({
                dates: z.object({
                    from: z.timestamp(),
                    to: z.timestamp(),
                }).nullable(),
                search_params: z.any(),
            }),
            response: z.object({
                data: z.array(z.array(z.number())),
            }),
        },
        getHolidays: {
            params: z.object({}),
            response: z.object({
                data: z.array(z.timestamp()),
            }),
        },
    })
}
