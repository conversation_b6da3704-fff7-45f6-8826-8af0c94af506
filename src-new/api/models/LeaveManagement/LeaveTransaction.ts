import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        LeaveTransaction: LeaveTransaction
    }
}

export default class LeaveTransaction implements Definition.Model {
    public readonly name = 'LeaveTransaction'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'desc' } as const

    public fields = z.object({
        id: z.int(),

        amount: z.number(),

        beneficiary_pk: z.pk(),
        request_pk: z.pk().nullable(),

        created_at: z.timestamp(),
    })

    public searchFields = z.object({
        beneficiary_pk: z.pk(),
    })

    public relations = defineRelations({
        //
    })

    public actions = defineActions({
        //
    })
}
