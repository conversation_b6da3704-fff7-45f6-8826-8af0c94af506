import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        MarketingCost: MarketingCost
    }
}

export enum CalendarMonth {
    January = '1',
    February = '2',
    March = '3',
    April = '4',
    May = '5',
    June = '6',
    July = '7',
    August = '8',
    September = '9',
    October = '10',
    November = '11',
    December = '12',
}

export default class MarketingCost implements Definition.Model {
    public readonly name = 'MarketingCost'
    public readonly pk = 'id'

    public readonly defaultOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        year: z.int(),
        month: z.nativeEnum(CalendarMonth),
        value: z.number(),
    })

    public relations = defineRelations({
        //
    })

    public searchFields = z.object({
        year: z.int(),
    })

    public actions = defineActions({
        //
        create: {
                params: z.object({
                    year: z.int(),
                    month: z.nativeEnum(CalendarMonth),
                    value: z.number(),
                }),
                response: z.null(),
        },
        update: {
            params: z.object({
                pk: z.pk(),
                value: z.number(),
            }),
            response: z.null(),
        },
    })
}
