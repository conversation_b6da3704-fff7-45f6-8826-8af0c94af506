import type { Definition } from '~types/lib/Model'
import { TaskCategory } from '~/api/models/Task/TaskGroup'
import { EmailSubject, EmailType } from '~/api/models/Email/EmailTemplate'

declare global {
    export interface Models {
        Task: Task
    }
}

export enum CustomerReachedType {
    Sms = 'sms',
    VoiceMail = 'voice_mail',
    Call = 'call',
    Email = 'email',
    Ai = 'ai'
}

export default class Task implements Definition.Model {
    public readonly name = 'Task'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        task_group_pk: z.pk(),

        issue_pk: z.pk().nullable(),
        chat_pk: z.pk().nullable(),

        completed_by_pk: z.pk().nullable(),
        completed_reason: z.softEnum(CustomerReachedType).nullable(),
        executor_pk: z.pk().nullable(),
        department_pk: z.pk().nullable(),
        team_pk: z.pk().nullable(),
        assigned_by_pk: z.pk().nullable(),

        description: z.string(),
        is_system: z.boolean(),
        is_autocompleted: z.boolean(),
        is_high_priority: z.boolean(),
        can_extend_expiration_time: z.boolean(),
        can_set_expiration_time: z.boolean(),
        need_confirm: z.boolean(),

        created_at: z.timestamp(),
        start_at: z.timestamp().nullable(),
        expire_at: z.timestamp(),
        completed_at: z.timestamp().nullable(),
        disabled_at: z.timestamp().nullable(),

        handler: z.string(),
        referer_pk: z.pk(),
        recommended_for_department_pks: z.array(z.pk()),
    })

    public relations = defineRelations({
        completedBy: belongsTo('Agent', 'completed_by_pk'),
        taskGroup: belongsTo('TaskGroup', 'task_group_pk'),
        executor: belongsTo('Agent', 'executor_pk'),
        department: belongsTo('Department', 'department_pk'),
        team: belongsTo('Team', 'team_pk'),
        chat: belongsTo('Chat', 'chat_pk'),
    })

    public searchFields = z.object({
        id: z.int(),
        start_at: z.timestamp(),
        description: z.string(),
        model: z.string(),
        task_group: z.nativeEnum(TaskCategory),
        is_completed: z.string(),
        is_delayed: z.string(),

        executor_pk: z.pk(),
        department_pk: z.pk(),
        team_pk: z.pk(),
        assigned_by_pk: z.pk(),
        is_on_task_page: z.boolean(),
    })

    public actions = defineActions({
        create: {
            params: z.object({
                task_group_pk: z.pk(),
                description: z.string(),
            }),
            response: z.null(),
        },
        update: {
            params: z.object({
                pk: z.pk(),
                description: z.string(),
            }),
            response: z.null(),
        },
        delete: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },
        setExpirationTime: {
            params: z.object({
                pk: z.pk(),
                expire_at: z.timestamp(),
            }),
            response: z.null(),
        },
        extendExpirationTime: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },
        delayStartTime: {
            params: z.object({
                pk: z.pk(),
                start_at: z.timestamp().nullable(),
            }),
            response: z.null(),
        },
        changeCompleted: {
            params: z.object({
                pk: z.pk(),
                status: z.boolean(),
            }),
            response: z.null(),
        },
        changeExecutor: {
            params: z.object({
                pk: z.pk(),
                executor_pk: z.pk().nullable(),
            }),
            response: z.null(),
        },
        changeDepartment: {
            params: z.object({
                pk: z.pk(),
                department_pk: z.pk().nullable(),
            }),
            response: z.null(),
        },
        changeTeam: {
            params: z.object({
                pk: z.pk(),
                team_pk: z.pk().nullable(),
            }),
            response: z.null(),
        },
        setFollowUpTaskStatus: {
            params: z.object({
                task_pk: z.pk(),
                email_pk: z.pk().nullable(),
                send_to_me: z.boolean(),
                selected_pqs: z.array(z.int()).nullable(),
                type: z.nativeEnum(EmailType).nullable(),
                subject: z.nativeEnum(EmailSubject),
                custom_subject: z.string().nullable(),
                include_travel_cash: z.boolean().nullable(),
            }),
            response: z.null(),
        },
    })
}
