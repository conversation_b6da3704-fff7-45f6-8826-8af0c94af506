import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        Project: Project
    }
}

export default class Project implements Definition.Model {
    public readonly name = 'Project'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        name: z.string(),
        abbreviation: z.string(),
        host: z.string(),
        pay_host: z.string(),
        is_main: z.boolean(),
        general_line_phone_number: z.array(z.string()),
        features: z.object({
            client_cabinet: z.object({
                enabled: z.boolean(),
            }),
            email_footer_generator: z.object({
                enabled: z.boolean(),
            }),
            // trustpilot: z.object({
            //     enabled: z.boolean(),
            // }),
        }),
    })

    public relations = defineRelations({
        //
    })

    public actions = defineActions({
        //
    })
}
