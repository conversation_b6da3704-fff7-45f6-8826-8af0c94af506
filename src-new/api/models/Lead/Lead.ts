import type { Definition } from '~types/lib/Model'
import { IssueActionData, IssueCategory, IssueResult } from '~/api/models/Issue/Issue'
import { LeadStatusName } from '~/api/models/Lead/LeadStatus'
import { ExternalResource } from '~/api/dictionaries/Static/ExternalResourceDictionary'
import { ItineraryClass } from '~/api/dictionaries/Static/ItineraryClassDictionary'
import { StarColor } from '~/api/dictionaries/Static/Lead/LeadStarTypeDictionary'
import { ExpertLeadStatus } from '~/api/models/Lead/LeadAdditionalExpertInformation'

declare global {
    export interface Models {
        Lead: Lead
    }
}

export enum ItineraryType {
    OneWay = 'oneWay',
    RoundTrip = 'roundTrip',
    MultiCity = 'multiCity',
}

export enum DuplicateStatus {
    Claimed = 'Claimed',
    Rejected = 'Rejected',
}

export enum ClosingReasons {
    NotInterested = 'not_interested',
    NoReply = 'no_reply',
    NewRequest = 'new_request',
    Other = 'other',
    FrequentEntryIntoBQ = 'frequent_entry_into_bq',
    ExpiredDepartureDate = 'expired_departure_date',
    BookedEconomy = 'booked_economy',
    FakeLead = 'fake_lead',
}

export enum LeadQueueOptions {
    NewQueue = 'new_queue',
    BonusQueue = 'bonus'
}

export const leadJivoLinkValidation = '^(https?:\\/\\/)?.(www\\.)?.*/offer/.+'

export const followUpMaxPoints = 41
export const agentLeadGiveRuleMaxCount = 20
export const agentLeadGiveRuleDefaultCount = 10

export default class Lead implements Definition.Model {
    public readonly name = 'Lead'
    public readonly pk = 'id'
    public readonly chunkBy = 100
    public readonly defaultOrderBy = { id: 'desc' } as const

    public readonly hasArchivableRelations = true

    public fields = z.object({
        id: z.int(),
        is_deleted: z.boolean(),
        created_at: z.timestamp(),

        status_pk: z.pk(),
        is_bonus: z.boolean(),
        external_resource: z.softEnum(ExternalResource),
        itinerary_class: z.softEnum(ItineraryClass),
        itinerary_type: z.nativeEnum(ItineraryType),
        itinerary_plan: z.array(z.string()).nullable(),
        duplicate_status: z.nativeEnum(DuplicateStatus).nullable(),
        from_iata_code: z.string(),
        to_iata_code: z.string(),
        taken_date: z.timestamp().nullable(),
        departure_date: z.timestamp(),
        return_date: z.timestamp().nullable(),
        is_cs_lead: z.boolean(),
        remark: z.string().nullable(),
        adult_count: z.int(),
        child_count: z.int(),
        infant_count: z.int(),
        is_award: z.boolean(),
        is_possible_to_add_pq: z.boolean(),
        expert_pk: z.pk().nullable(),

        closing_reason: z.nativeEnum(ClosingReasons).nullable(),
        closing_reason_remark: z.string().nullable(),

        from_iata_timezone: z.string().nullable(),
        to_iata_timezone: z.string().nullable(),

        //bonus queue
        active_keep_client_request_pk: z.pk().nullable(),

        // Relation fields
        client_session_activity_pk: z.pk().nullable(),
        client_phone_pk: z.pk().nullable(),
        client_email_pk: z.pk(),
        executor_pk: z.pk().nullable(),
        client_pk: z.pk(),
        project_pk: z.pk(),
        created_by_pk: z.pk().nullable(),
        first_expert_agent_pk: z.pk().nullable(),
        chat_pk: z.pk(),
        voucher_pks: z.array(z.pk()).nullable(),
        is_test: z.boolean(),
    })

    public searchFields = z.object({
        id: z.pk(),
        keywords: z.string(),
        from_iata_code: z.string(),
        to_iata_code: z.string(),
        remark: z.string(),
        pq_count: z.int(),
        spq_count: z.int(),
        follow_up_progress: z.int(),
        departure_date: z.timestamp(),
        session_expire_at: z.timestamp(),
        is_to_be_lost: z.boolean(),
        return_date: z.timestamp(),
        created_at: z.timestamp(),
        taken_date: z.timestamp(),
        external_resource: z.softEnum(ExternalResource),
        itinerary_class: z.softEnum(ItineraryClass),
        client_fullname: z.string(),
        client_email: z.string(),
        client_phone: z.string(),
        executor_pk: z.pk(),
        main_project_pk: z.pk(),
        utm_source: z.string(),
        utm_campaign: z.string(),
        utm_ga: z.string(),
        utm_medium: z.string(),
        utm_term: z.string(),
        status: z.softEnum(LeadStatusName),
        is_bonus: z.boolean(),
        star_type: z.softEnum(StarColor),
        client_status_pk: z.pk(),
        has_duplicates: z.boolean(),
        is_in_queue: z.boolean(),
        is_client_reached: z.boolean(),
        is_keep_client: z.boolean(),
        departure_region: z.pk(),
        destination_region: z.pk(),
        departure_country: z.pk(),
        destination_country: z.pk(),
        use_archive: z.boolean(),
        client_pk: z.pk(),
        airline_pk: z.pk(),
        prediction_score: z.number(),

        // Expert fields
        has_price_quotes: z.boolean(),
        price_quote_pk: z.pk(),
        itinerary_type: z.softEnum(ItineraryType),
        external_price: z.string(),
        is_expert_request_not_completed: z.boolean(),
        expert_pk: z.pk(),
        executor_team_pk: z.pk(),
        expert_status: z.softEnum(ExpertLeadStatus),
        is_expert_lead: z.boolean(),
        expert_request_start_at: z.timestamp().nullable(),
        is_award: z.boolean(),
        requested_by_pk: z.pk(),
        can_add_pq: z.boolean(),
        has_track_pq: z.boolean(),
        is_published: z.boolean(),
        can_merge: z.boolean(),
    })

    public relations = defineRelations({
        client: belongsTo('Client', 'client_pk'),
        clientPreview: belongsTo('ClientPreview', 'client_pk'),
        status: belongsTo('LeadStatus', 'status_pk'),
        executor: belongsTo('Agent', 'executor_pk'),
        expert: belongsTo('Agent', 'expert_pk'),
        createdBy: belongsTo('Agent', 'created_by_pk'),
        email: belongsTo('Email', 'client_email_pk'),
        phone: belongsTo('Phone', 'client_phone_pk'),

        pqs: hasManyThrough('PriceQuote', 'LeadPriceQuoteList', 'lead_pk'),
        pqList: belongsToResource('LeadPriceQuoteList', 'id'),

        followUp: belongsTo('LeadAdditionalFollowUp', 'id'),
        utm: belongsTo('LeadAdditionalUTM', 'id'),
        listInformation: belongsTo('LeadAdditionalListInformation', 'id'),
        prediction: belongsTo('LeadAdditionalPrediction', 'id'),
        profits: belongsTo('LeadAdditionalProfits', 'id'),
        agentLeadInfo: belongsTo('LeadAdditionalAgentInfo', 'id'),
        expertLeadInfo: belongsTo('LeadAdditionalExpertInformation', 'id'),
        expertLeadQueueInfo: belongsTo('LeadAdditionalExpertQueueInformation', 'id'),
        leadManagementStatus: belongsTo('LeadAdditionalManagementStatus', 'id'),
        leadAdditionalManagementProcessedRemark: belongsTo('LeadAdditionalManagementProcessedRemark', 'id'),
        taskGroups: hasManyThrough('TaskGroup', 'LeadTaskGroupList', morphKey),

        leadDuplicates: hasManyThrough('LeadDuplicate', 'LeadLeadDuplicateList', 'id'),
        leadOffers: hasManyThrough('LeadOffer', 'LeadLeadOfferList', 'lead_pk'),

        mailCounter: belongsTo('LeadAdditionalMailCounterInfo', 'id'),
        callCounter: belongsTo('LeadAdditionalCallCounterInfo', 'id'),

        sales: hasManyThrough('Sale', 'LeadSaleList', 'lead_pk'),
        salesPreview: hasManyThrough('SalePreview', 'LeadSaleList', 'lead_pk'),
        chat: belongsTo('Chat', 'chat_pk'),
        vouchers: hasMany('Voucher', 'voucher_pks'),
    })

    public actions = defineActions({
        createClientStatusChangeIssue: {
            params: z.object({
                pk: z.pk(),
                client_status_pk: z.pk(),
                referral_client_pk: z.pk().nullable(),
                return_from_sale_pk: z.pk().nullable(),
                issue_data: IssueActionData,
            }),
            response: z.object({
                issue_pk: z.pk().nullable(),
            }),
        },

        claimSale: {
            params: z.object({
                lead_pk: z.pk(),
                sale_pk: z.pk(),
            }),
            response: z.null(),
        },

        findLead: {
            params: z.object({
                query: z.string(),
            }),
            response: z.array(z.object({
                pk: z.pk(),
                from_iata_code: z.string(),
                to_iata_code: z.string().nullable(),
                created_at: z.timestamp(),
                client_fullname: z.string(),
                client_phone: z.string().nullable(),
                client_email: z.string().nullable(),
                executor_fullname: z.string().nullable(),
            })),
        },

        takeOwnLead: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },

        requestLead: {
            params: z.object({}),
            response: z.null(),
        },

        takeNewLead: {
            params: z.object({}),
            response: z.object({
                lead_pks: z.array(z.pk()),
            }),
        },

        sendFollowUp: {
            params: z.object({
                data: z.array(z.object({
                    lead_pk: z.pk(),
                    follow_up_1: z.boolean(),
                    follow_up_2: z.boolean(),
                    follow_up_3: z.boolean(),
                })),
            }),
            response: z.null(),
        },

        changeStatusBulk: {
            params: z.object({
                pks: z.array(z.pk()),
                status_pk: z.pk(),
            }),
            response: z.null(),
        },
        toggleUnsoldPriceQuotes: {
            params: z.object({
                lead_pk: z.pk(),
                visibility: z.boolean(),
            }),
            response: z.null(),
        },
        searchLeadsByAgentForShare: {
            params: z.object({
                agent_pk: z.pk(),
                sale_pk: z.pk(),
            }),
            response: z.array(z.object({
                pk: z.pk(),
                from_iata_code: z.string(),
                to_iata_code: z.string(),
                departure_date: z.timestamp(),
                return_date: z.timestamp().nullable(),
                itinerary_type: z.nativeEnum(ItineraryType),
            })),
        },
        changeLeadStarTypeForAgent: {
            params: z.object({
                lead_pk: z.pk(),
                star_type: z.string(),
            }),
            response: z.model('LeadAdditionalAgentInfo'),
        },
        setPinned: {
            params: z.object({
                lead_pk: z.pk(),
                state: z.boolean(),
            }),
            response: z.null(),
        },
        setDefaultPhone: {
            params: z.object({
                lead_pk: z.pk(),
                phone_pk: z.pk(),
            }),
            response: z.null(),
        },
        setDefaultEmail: {
            params: z.object({
                lead_pk: z.pk(),
                email_pk: z.pk(),
            }),
            response: z.null(),
        },
        assignExecutor: {
            params: z.object({
                lead_pk: z.pk(),
                executor_pk: z.pk().nullable(),
                lead_queue: z.nativeEnum(LeadQueueOptions).nullable(),
            }),
            response: z.null(),
        },
        assignExpert: {
            params: z.object({
                lead_pk: z.pk(),
                expert_pk: z.pk(),
            }),
            response: z.object({
                lead_pk: z.pk(),
            }),
        },
        getLeadClientSessionAgent: {
            params: z.object({
                lead_pk: z.pk(),
            }),
            response: z.object({
                agent_pk: z.pk().nullable(),
            }),
        },
        createKeepClientIssue: {
            params: z.object({
                lead_pk: z.pk(),
                expires_at: z.timestamp(),
                issue_data: IssueActionData,
            }),
            response: z.null(),
        },

        dropClient: {
            params: z.object({
                lead_pk: z.pk(),
            }),
            response: z.null(),
        },

        // @todo Rename to contain "leadManagement"
        addLeadToQueue: {
            params: z.object({
                lead_pk: z.pk(),
            }),
            response: z.null(),
        },

        closeLead: {
            params: z.object({
                lead_pk: z.pk(),
                closing_reason: z.nativeEnum(ClosingReasons),
                closing_reason_remark: z.string().nullable(),
                duplicate_lead_pk: z.pk().nullable(),
                issue_data: IssueActionData,
            }),
            response: z.null(),
        },

        getLeadStatistics: {
            params: z.object({
                agent_pk: z.pk(),
                type: z.string().nullable(),
                processed: z.boolean(),
            }),
            response: z.array(z.pk()),
        },

        checkLead: {
            params: z.object({
                jivo_link: z.string().nullable(),
                email: z.array(z.string()).nullable(),
                phone: z.array(z.string()).nullable(),
            }),
            response: z.object({
                lead_pk: z.pk().nullable(),
                leads_info: z.array(
                    z.object({
                        id: z.int(),
                        from_iata_code: z.string(),
                        to_iata_code: z.string(),
                        departure_date: z.timestamp(),
                        return_date: z.timestamp().nullable(),
                        created_at: z.timestamp(),
                        status_pk: z.pk(),
                        is_bonus: z.boolean(),
                        executor_pk: z.pk().nullable(),
                        created_by_pk: z.pk().nullable(),
                        closing_reason: z.nativeEnum(ClosingReasons).nullable(),
                        closing_reason_remark: z.string().nullable(),
                        // not from lead object
                        pq_count: z.int(),
                        itinerary_type: z.nativeEnum(ItineraryType),
                    }),
                ),
            }),
        },
        createFromCheckLead: {
            params: z.object({
                project_pk: z.pk(),
                itinerary_class: z.softEnum(ItineraryClass),
                adult_count: z.int().nullable(),
                child_count: z.int().nullable(),
                infant_count: z.int().nullable(),
                remark: z.string().nullable(),
                first_name: z.string(),
                last_name: z.string().nullable(),
                status_pk: z.pk(),
                referral_client_pk: z.pk().nullable(),
                request_comment: z.string().nullable(),
                from_iata_pk: z.pk(),
                to_iata_pk: z.pk(),
                departure_at: z.timestamp(),
                return_at: z.timestamp().nullable(),
                phone: z.array(z.string()).nullable(),
                email: z.array(z.string()),
                jivo_link: z.string(),
            }),
            response: z.pk(),
        },
        getLeadFollowUpProgress: {
            params: z.object({
                lead_pk: z.pk(),
            }),
            response: z.object({
                completed_points: z.int(),
                daily_progress: z.array(z.object({
                    first_phone_call: z.boolean().nullable(),
                    first_voice_message: z.boolean().nullable(),
                    second_phone_call: z.boolean().nullable(),
                    second_voice_message: z.boolean().nullable(),
                    text_massage: z.boolean().nullable(),
                    reply_to_email: z.boolean().nullable(),
                    manually_created_email: z.boolean().nullable(),
                    follow_up_template: z.boolean().nullable(),
                })),
            }),
        },

        getLeadOfferPreview: {
            params: z.object({
                pk: z.pk(),
                project_pk: z.pk(),
                pqs_pk: z.array(z.pk()),
            }),
            response: z.object({
                url: z.string(),
            }),
        },

        sendLeadOffer: {
            params: z.object({
                pk: z.pk(),
                project_pk: z.pk(),
                pqs_pk: z.array(z.pk()),
                email: z.string(),
                passengers_info: z.array(z.object({
                    name: z.string(),
                })).nullable(),
            }),
            response: z.null(),
        },
        sendLeadOfferToMe: {
            params: z.object({
                pk: z.pk(),
                project_pk: z.pk(),
                pqs_pk: z.array(z.pk()),
                passengers_info: z.array(z.object({
                    name: z.string(),
                })).nullable(),
            }),
            response: z.null(),
        },
        getClientReachedTaskPk: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.object({
                task_pk: z.pk(),
            }),
        },

        create: {
            params: z.object({
                lead: z.object({
                    external_resource: z.softEnum(ExternalResource),
                    itinerary_class: z.softEnum(ItineraryClass),
                    from_iata_pk: z.pk(),
                    to_iata_pk: z.pk(),
                    departure_date: z.timestamp(),
                    return_date: z.timestamp().nullable(),
                    adult_count: z.int(),
                    child_count: z.int(),
                    infant_count: z.int(),
                    remark: z.string().nullable(),
                    project_pk: z.pk(),
                }),
                client: z.namedUnion({
                    'pk': z.pk(),
                    'object': z.object({
                        data: z.object({
                            first_name: z.string(),
                            last_name: z.string().nullable(),
                            emails: z.array(z.string()),
                            phones: z.array(z.string()),
                            is_unknown_phone: z.boolean(),
                        }),
                        status: z.object({
                            result: IssueResult[IssueCategory.ClientStatus],
                            data: IssueActionData.nullable(),
                        }),
                    }),
                }),
                keep_client_curator: z.object({
                    remark: z.string(),
                    keep: z.boolean(),
                }).nullable(),
                send_to_queue: z.boolean(),
                jivo_link: z.string().nullable(),
            }),
            response: z.object({
                lead_pk: z.pk(),
            }),
        },

        update: {
            params: z.object({
                pk: z.pk(),
                lead: z.object({
                    itinerary_class: z.softEnum(ItineraryClass),
                    from_iata_pk: z.pk(),
                    to_iata_pk: z.pk(),
                    departure_date: z.timestamp(),
                    return_date: z.timestamp().nullable(),
                    adult_count: z.int(),
                    child_count: z.int(),
                    infant_count: z.int(),
                    remark: z.string().nullable(),
                }),
                client: z.object({
                    first_name: z.string(),
                    last_name: z.string().nullable(),
                    emails: z.array(z.string()),
                    phones: z.array(z.string()),
                    is_unknown_phone: z.boolean(),
                }),
            }),
            response: z.null(),
        },
        attachVoucher: {
            params: z.object({
                lead_pk: z.pk(),
                voucher_pk: z.pk(),
            }),
            response: z.null(),
        },
        updateRemark: {
            params: z.object({
                lead_pk: z.pk(),
                remark: z.string().nullable(),
            }),
            response: z.null(),
        },
        sendNewLead: {
            params: z.object({
                agent_pk: z.pk(),
            }),
            response: z.object({
                lead_pk: z.pk(),
            }),
        },
        searchAwardOffers: {
            params: z.object({
                url: z.string(),
                options: z.any(),
            }),
            response: z.custom<AnyObject>(),
        },

        getNewQueueStatisticsPerDay: {
            params: z.object({
                date_range: z.object({
                    start: z.timestamp(),
                    end: z.timestamp(),
                }),
                external_resource: z.array(z.string()),
                group_per_hours: z.boolean(),
                team_pks: z.array(z.pk().nullable()),
                offset: z.int(),
            }),
            response: z.array(z.object({
                dataset: z.array(z.object({
                    value: z.number(),
                    timestamp: z.timestamp(),
                    data: z.array(z.object({
                        external_resource: z.softEnum(ExternalResource),
                        count: z.number(),
                    })),
                })),

                data: z.object({
                    team_pk: z.pk().nullable(),
                }).nullable(),
            })),
        },

        getNewQueueStatisticsPerUser: {
            params: z.object({
                date_range: z.object({
                    start: z.timestamp(),
                    end: z.timestamp(),
                }),
                external_resource: z.array(z.string()),
            }),
            response: z.array(z.object({
                agent_pk: z.pk(),
                conversion_value: z.number(),
                value: z.number(),
            })),
        },

        getNewQueueSettings: {
            params: z.object({}),
            response: z.object({
                top_agent_max_count: z.number(),
                prediction_threshold: z.number(),
            }),
        },
        updateNewQueueSettings: {
            params: z.object({
                top_agent_max_count: z.number(),
                prediction_threshold: z.number(),
            }),
            response: z.null(),
        },
    })
}
