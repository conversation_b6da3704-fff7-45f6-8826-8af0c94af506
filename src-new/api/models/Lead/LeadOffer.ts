import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        LeadOffer: LeadOffer
    }
}

export enum PriceQuoteSortType {
    PriceBased = 'price_based',
    ClassPriority = 'class_priority',
}

export default class LeadOffer implements Definition.Model {
    public readonly name = 'LeadOffer'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'asc' } as const

    public readonly isArchivable = ['Lead']

    public fields = z.object({
        id: z.int(),
        is_award_offer: z.boolean(),

        is_published: z.boolean(),

        is_mail_sent_at: z.timestamp().nullable(),
        is_mail_viewed_at: z.timestamp().nullable(),
        is_mail_clicked_at: z.timestamp().nullable(),
        drop_off_point: z.int().nullable(),

        is_to_client: z.boolean(),
        links: z.array(z.string()), // First link is the main link, others are links to offer tabs
        created_at: z.timestamp(),
        lead_pk: z.pk(),
        pq_sort_type: z.nativeEnum(PriceQuoteSortType),

        // Relation fields
        created_by_pk: z.pk(),
    })

    public searchFields = z.object({
        //
    })

    public relations = defineRelations({
        createdBy: belongsTo('Agent', 'created_by_pk'),
    })

    public actions = defineActions({
        //
    })
}
