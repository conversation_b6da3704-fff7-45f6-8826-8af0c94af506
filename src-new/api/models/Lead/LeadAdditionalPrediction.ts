import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        LeadAdditionalPrediction: LeadAdditionalPrediction
    }
}

export default class LeadAdditionalPrediction implements Definition.Model {
    public readonly name = 'LeadAdditionalPrediction'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        lead_score: z.number().nullable(),
    })

    public relations = defineRelations({
        //
    })

    public actions = defineActions({
        //
    })
}
