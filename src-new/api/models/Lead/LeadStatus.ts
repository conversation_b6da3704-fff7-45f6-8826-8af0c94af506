import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        LeadStatus: LeadStatus
    }
}

export enum LeadStatusName {
    New = 'new',
    Potential = 'potential',
    FollowUp = 'follow_up',
    Closed = 'closed',
    Sold = 'sold',
    Lost = 'lost',
    SaleRejected = 'sale_rejected',
    SaleRejectedInactive = 'sale_rejected_inactive',
    Fraud = 'fraud'
}

export default class LeadStatus implements Definition.Model {
    public readonly name = 'LeadStatus'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        name: z.string(),
        system_name: z.softEnum(LeadStatusName),
        active: z.boolean(),
    })

    public relations = defineRelations({
        //
    })

    public actions = defineActions({
        //
    })
}
