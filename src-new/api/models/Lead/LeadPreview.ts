import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        LeadPreview: LeadPreview
    }
}

export default class LeadPreview implements Definition.Model {
    public readonly name = 'LeadPreview'
    public readonly pk = 'id'

    public fields = z.object({
        id: z.int(),
        from_iata_code: z.string(),
        to_iata_code: z.string(),
        departure_date: z.timestamp(),
        return_date: z.timestamp().nullable(),
        created_at: z.timestamp(),
        client_pk: z.pk(),
    })

    public searchFields = z.object({
        client_pk: z.pk(),
    })

    public relations = defineRelations({
        //
        client: belongsTo('ClientPreview', 'client_pk'),
    })

    public actions = defineActions({
        //
    })
}
