import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        PriceDropCheck: PriceDropCheck
    }
}

export default class PriceDropCheck implements Definition.Model {
    public readonly name = 'PriceDropCheck'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { voidable_till: 'asc', refundable_till: 'asc', id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        sale_pk: z.pk(),
        token: z.string(), // visible only on dev. for testing purposes
        voidable_till: z.timestamp().nullable(),
        is_voidable: z.boolean(),

        refundable_till: z.timestamp().nullable(),
        is_refundable: z.boolean(),

        pnr: z.string(),
        consolidator_area_pk: z.pk(),
        net_price: z.number(),
        commission: z.number(),
        issuing_fee: z.number(),
        price_drop_price: z.number().nullable(),

        ticket_info: z.array(z.string()),
    })

    public searchFields = z.object({
        id: z.int(),
        is_voidable: z.boolean(),
        is_refundable: z.boolean(),
        sale_pk: z.pk(),
        pnr: z.string(),
        ticket_info: z.string(),
        voidable_till: z.timestamp(),
        refundable_till: z.timestamp(),
        net_price: z.number(),
        commission: z.number(),
        issuing_fee: z.number(),
        price_drop_price: z.number(),
        consolidator_area_pk: z.pk(),
        has_offers: z.boolean(),
    })

    public relations = defineRelations({
        consolidatorArea: belongsTo('ConsolidatorArea', 'consolidator_area_pk'),
        offers: hasManyThrough('PriceDropOffer', 'PriceDropCheckPriceDropOfferList', 'check_pk'),
    })

    public actions = defineActions({
        getRawSegments: {
            params: z.object({
                url: z.string(),
                options: z.any(),
            }),
            response: z.object({
                segments: z.string(),
                validating_carrier: z.string().nullable(),
            }),
        },
    })
}
