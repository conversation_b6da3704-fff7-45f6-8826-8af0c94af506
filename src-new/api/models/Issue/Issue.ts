import type { Definition, ModelAttributes } from '~types/lib/Model'
import { defineUnion, match } from '~/lib/Helper/EnumHelper'
import type IssueController from '~/lib/Issue/IssueController'
import SplitSaleIssueController from '~/lib/Issue/Controllers/SplitSaleIssueController'
import ClientStatusIssueController from '~/lib/Issue/Controllers/ClientStatusIssueController'
import CustomerSupportIssueController from '~/lib/Issue/Controllers/CustomerSupportIssueController'
import TaskDiscussionController from '~/lib/Issue/Controllers/TaskDiscussionController'
import { ModelIdentifier } from '~/utils/zod'
import type SelectOption from '~types/structures/SelectOption'
import DisclaimerIssueController from '~/lib/Issue/Controllers/DisclaimerIssueController'
import KeepClientIssueController from '~/lib/Issue/Controllers/KeepClientIssueController'
import ClosingReasonIssueController from '~/lib/Issue/Controllers/ClosingReasonIssueController'
import { ClosingReasons } from '~/api/models/Lead/Lead'
import PriceDropIssueController from '~/lib/Issue/Controllers/PriceDropIssueController'
import AltExtraIssueController from '~/lib/Issue/Controllers/AltExtraIssueController'
import AirlineReimbursementIssueController from '~/lib/Issue/Controllers/AirlineReimbursementIssueController'
import VerificationIssueController from '~/lib/Issue/Controllers/VerificationIssueController'
import VoucherIssueController from '~/lib/Issue/Controllers/VoucherIssueController'
import { IssueStarColor } from '~/api/dictionaries/Static/Issue/IssueStarColorDictionary'
import PnrScheduleIssueController from '~/lib/Issue/Controllers/PnrScheduleIssueController'
import { IssueFilterTaskStatus } from '~/api/dictionaries/Static/Issue/IssueFilterTaskStatusDictionary'

declare global {
    export interface Models {
        Issue: Issue
    }
}

export default class Issue implements Definition.Model {
    public readonly name = 'Issue'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'desc' } as const

    public fields = z.object({
        id: z.int(),

        category: z.nativeEnum(IssueCategory),
        status: z.nativeEnum(IssueStatus),
        urgency: z.nativeEnum(IssueUrgency),

        is_deleted: z.boolean(),

        data: z.namedUnion(IssueData).nullable(),
        result: z.namedUnion(IssueResult).nullable(),

        members: z.array(z.object({
            pk: z.pk(),
            role: z.string().nullable(),
            is_hidden: z.boolean(),
            permissions: z.object({
                vote: z.boolean(),
                apply: z.boolean(),
                extend: z.boolean(),
                deleteTask: z.boolean(),
            }),
        })),

        closed_at: z.timestamp().nullable(),
        expires_at: z.timestamp().nullable(),
        created_at: z.timestamp(),

        // Relations
        model_name: z.string(),
        model_pk: z.pk(),
        created_by_pk: z.pk(),
        chat_pk: z.pk(),
        executor_pk: z.pk().nullable(),
        executor_start_at: z.timestamp().nullable(),

        // Other
        project_pk: z.pk(),
    })

    public searchFields = z.object({
        id: z.int(),
        category: z.nativeEnum(IssueCategory),
        urgency: z.nativeEnum(IssueUrgency),
        project_pk: z.pk(),
        created_by_pk: z.pk(),
        executor_pk: z.pk(),
        model_pk: z.pk(),
        result: z.string(),
        created_at: z.timestamp(),
        expires_at: z.timestamp(),
        closed_at: z.timestamp(),
        closed_by: z.pk(),
        status: z.nativeEnum(IssueStatus),
        case_number: z.string(),
        case_email: z.string(),
        has_unread_messages: z.boolean(),
        is_user_member: z.boolean(),
        is_user_mentioned: z.boolean(),
        favorites: z.nativeEnum(IssueStarColor),
        airline: z.string(),
        pnr: z.string(),
        task_assignment: z.nativeEnum(IssueFilterTaskAssignment),
        task_status: z.nativeEnum(IssueFilterTaskStatus),
    })

    public relations = defineRelations({
        createdBy: belongsTo('Agent', 'created_by_pk'),
        closedBy: belongsTo('Agent', 'closed_by'),
        chat: belongsTo('Chat', 'chat_pk'),
        airlineCase: hasManyThrough('AirlineCase', 'IssueAirlineCaseList', 'issue_pk'),
        expectedAmount: hasManyThrough('ExpectedAmount', 'IssueExpectedAmountList', 'issue_pk'),
        executor: belongsTo('Agent', 'executor_pk'),
        agentInfo: belongsTo('IssueAdditionalAgentInfo', 'id'),
        // Dynamic relations
        relatedSale: belongsTo('Sale', morphKey),
        taskGroups: hasManyThrough('TaskGroup', 'IssueTaskGroupList', morphKey),
    })

    public actions = defineActions({
        apply: {
            params: z.object({
                pk: z.pk(),
                result: z.namedUnion(IssueResult),
            }),
            response: z.null(),
        },
        suggest: {
            params: z.object({
                pk: z.pk(),
                remark: z.string().nullable(),
                result: z.namedUnion(IssueResult),
            }),
            response: z.null(),
        },
        decline: {
            params: z.object({
                pk: z.pk(),
                remark: z.string().nullable(),
            }),
            response: z.null(),
        },
        extend: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },
        join: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },
        updateUrgency: {
            params: z.object({
                pk: z.pk(),
                urgency: z.nativeEnum(IssueUrgency),
            }),
            response: z.null(),
        },
        updateExpirationDate: {
            params: z.object({
                pk: z.pk(),
                expires_at: z.timestamp(),
            }),
            response: z.null(),
        },
        //

        createCustomerSupportIssue: {
            params: z.object({
                related_model: ModelIdentifier,
                category: z.nativeEnum(IssueCategory),
                question: z.string(),
                issue_data: IssueActionData,
                pnrs: z.array(z.string()).nullable(),
                passengers_pks: z.array(z.pk()).nullable(),
                contact_pks: z.array(z.pk()).nullable(),
            }),
            response: z.object({
                issue_pk: z.pk(),
            }),
        },

        createVerificationIssue: {
            params: z.object({
                related_model: ModelIdentifier,
                category: z.nativeEnum(IssueCategory),
                question: z.string(),
                issue_data: IssueActionData,
                pnrs: z.array(z.string()).nullable(),
                passengers_pks: z.array(z.pk()).nullable(),
                contact_pks: z.array(z.pk()).nullable(),
            }),
            response: z.object({
                issue_pk: z.pk(),
            }),
        },

        createTaskDiscussionIssue: {
            params: z.object({
                related_model: ModelIdentifier,
                agent: z.array(z.string()),
                description: z.string(),
                urgency: z.nativeEnum(IssueUrgency),
                file_pks: z.array(z.pk()), // @todo Replace additional fields with IssueData
            }),
            response: z.object({
                issue_pk: z.pk(),
            }),
        },

        createDisclaimerIssue: {
            params: z.object({
                related_model: ModelIdentifier,
                question: z.string(),
                issue_data: IssueActionData,
            }),
            response: z.object({
                issue_pk: z.pk(),
            }),
        },

        createPriceDropIssue: {
            params: z.object({
                related_model: ModelIdentifier,
                data: IssueData[IssueCategory.PriceDrop],
            }),
            response: z.null(),
        },

        createAltExtraIssue: {
            params: z.object({
                related_model: ModelIdentifier,
                data: IssueData[IssueCategory.AltExtra],
            }),
            response: z.null(),
        },

        createPnrScheduleIssue: {
            params: z.object({
                related_model: ModelIdentifier,
            }),
            response: z.object({
                issue_pk: z.pk(),
            }),
        },

        assignExecutor: {
            params: z.object({
                pk: z.pk(),
                executor_pk: z.pk().nullable(),
            }),
            response: z.null(),
        },
        changeIssueStarColorForAgent: {
            params: z.object({
                issue_pk: z.pk(),
                star_color: z.nativeEnum(IssueStarColor).nullable(),
            }),
            response: z.null(),
        },
    })
}

export enum IssueCategory {
    SplitSale = 'split_sale',
    ClientStatus = 'client_status',
    KeepClient = 'keep_client',
    CustomerSupportGeneral = 'customer_support_general',
    TaskDiscussion = 'task_discussion',
    Disclaimer = 'disclaimer',
    ClosingReason = 'closing_reason',

    // Ticketing
    PriceDrop = 'price_drop',
    AltExtra = 'alternative_extra',

    // Verification,
    VerificationTA = 'verification_ta',
    VerificationOther = 'verification_other',

    // Airline reimbursement
    AirlineReimbursement = 'airline_reimbursement',
    AirlineReimbursementRefund = 'airline_reimbursement_refund',
    AirlineReimbursementCredit = 'airline_reimbursement_credit',
    AirlineReimbursementCancellation = 'airline_reimbursement_cancellation',
    PnrSchedule = 'pnr_schedule',

    // Voucher
    Voucher = 'voucher'
}

export enum IssueStatus {
    New = 'new',
    Processing = 'processing',
    NeedApprove = 'need_approve',
    NeedMoreTime = 'need_more_time',
    NeedApply = 'need_apply',
    Expired = 'expired',
    Applied = 'applied',
    Declined = 'declined',
    Canceled = 'canceled',
}

export enum IssueFilterTaskAssignment {
    ToMe = 'to_me',
    ToMyDepartment = 'to_my_department',
}

export enum IssueUrgency {
    Low = '10_low',
    Medium = '20_medium',
    High = '30_high',
    Critical = '40_critical',
}

export const issueUrgencyOptions: SelectOption[] = [
    { title: 'Low', value: IssueUrgency.Low, image: () => h(ChevronDownIcon, { class: 'text-primary icon --large' }) },
    { title: 'Medium', value: IssueUrgency.Medium, image: () => h(MinusIcon, { class: 'text-primary icon --large' }) },
    { title: 'High', value: IssueUrgency.High, image: () => h(ChevronUpIcon, { class: 'text-warning icon --large' }) },
    {
        title: 'Critical',
        value: IssueUrgency.Critical,
        image: () => h(ChevronsUpIcon, { class: 'text-danger icon --large' }),
    },
]

export const IssueResult = defineUnion(IssueCategory, {
    [IssueCategory.SplitSale]: z.object({
        ticket_profit: z.number(),
        ticket_protection: z.number(),
        tips: z.number(),
    }),
    [IssueCategory.ClientStatus]: z.object({
        client_status_pk: z.pk(),
        referral_client_pk: z.pk().nullable(),
        return_from_sale_pk: z.pk().nullable(),
    }),
    [IssueCategory.CustomerSupportGeneral]: z.object({
        response: z.string(),
    }),
    [IssueCategory.TaskDiscussion]: z.object({
        response: z.string(),
    }),
    [IssueCategory.KeepClient]: z.object({
        expires_at: z.timestamp(),
    }),
    [IssueCategory.Disclaimer]: z.object({
        response: z.string(),
    }),
    [IssueCategory.ClosingReason]: z.object({
        closing_reason: z.softEnum(ClosingReasons).nullable(),
        closing_reason_remark: z.string().nullable(),
        duplicate_lead_pk: z.pk().nullable(),
    }),
    [IssueCategory.PriceDrop]: z.object({
        // pnr: z.string(),
        old_net_price: z.number(),
        old_commission: z.number(),
        old_issuing_fee: z.number(),
        old_check_payment: z.number(),
        new_net_price: z.number(),
        new_commission: z.number(),
        new_issuing_fee: z.number(),
        new_check_payment: z.number(),
        amount: z.number(),
        // pax_count: z.number(),
    }),
    [IssueCategory.AltExtra]: z.object({
        old_pnr: z.string(),
        new_pnr: z.string(),
        old_net_price: z.number(),
        old_commission: z.number(),
        old_issuing_fee: z.number(),
        old_check_payment: z.number(),
        new_net_price: z.number(),
        new_commission: z.number(),
        new_issuing_fee: z.number(),
        new_check_payment: z.number(),
        amount: z.number(),
        // pax_count: z.number(),
    }),

    // Verification
    [IssueCategory.VerificationTA]: z.object({
        response: z.string(),
    }),
    [IssueCategory.VerificationOther]: z.object({
        response: z.string(),
    }),

    // Airline reimbursement
    [IssueCategory.AirlineReimbursement]: z.object({
        response: z.string(),
    }),
    [IssueCategory.AirlineReimbursementRefund]: z.object({
        response: z.string(),
    }),
    [IssueCategory.AirlineReimbursementCredit]: z.object({
        response: z.string(),
    }),
    [IssueCategory.AirlineReimbursementCancellation]: z.object({
        response: z.string(),
    }),
    [IssueCategory.PnrSchedule]: z.object({
        response: z.string(),
    }),
    [IssueCategory.Voucher]: z.object({
        response: z.string(),
    }),
})

export const IssueData = defineUnion(IssueCategory, {
    [IssueCategory.SplitSale]: z.object({
        agent_pk: z.pk(),
        lead_pk: z.pk(),
        sale_lead_pk: z.pk(),
    }),
    [IssueCategory.ClientStatus]: z.object({
        client_pk: z.pk(),
        executor_pk: z.pk(),
        old_client_status_pk: z.pk(),
    }),
    [IssueCategory.CustomerSupportGeneral]: z.object({
        pnrs: z.array(z.string()),
        passengers_pks: z.array(z.pk()),
    }),
    [IssueCategory.Disclaimer]: z.null(),
    [IssueCategory.TaskDiscussion]: z.null(),
    [IssueCategory.KeepClient]: z.object({
        lead_pk: z.pk(),
        client_pk: z.pk(),
        old_expires_at: z.timestamp().nullable(),
    }),
    [IssueCategory.ClosingReason]: z.object({
        lead_pk: z.pk(),
    }),
    [IssueCategory.PriceDrop]: z.object({
        pnr: z.string(),
        agent_pk: z.pk(),
    }),
    [IssueCategory.AltExtra]: z.object({
        agent_pk: z.pk(),
    }),

    // Verification
    [IssueCategory.VerificationTA]: z.object({
        pnrs: z.array(z.string()),
        passengers_pks: z.array(z.pk()),
    }),
    [IssueCategory.VerificationOther]: z.object({
        pnrs: z.array(z.string()),
        passengers_pks: z.array(z.pk()),
    }),

    // Airline reimbursement
    [IssueCategory.AirlineReimbursement]: z.object({
        pnrs: z.array(z.string()),
        passengers_pks: z.array(z.pk()),
    }),
    [IssueCategory.AirlineReimbursementRefund]: z.object({
        pnrs: z.array(z.string()),
        passengers_pks: z.array(z.pk()),
    }),
    [IssueCategory.AirlineReimbursementCredit]: z.object({
        pnrs: z.array(z.string()),
        passengers_pks: z.array(z.pk()),
    }),
    [IssueCategory.AirlineReimbursementCancellation]: z.object({
        pnrs: z.array(z.string()),
        passengers_pks: z.array(z.pk()),
    }),
    [IssueCategory.PnrSchedule]: z.object({
        sale_pk: z.pk().nullable(),
        pnr_schedule_pk: z.pk(),
    }),
    [IssueCategory.Voucher]: z.object({
        voucher_id: z.string(),
    }),
})

export const IssueActionData = z.object({
    remark: z.string().nullable(),
    urgency: z.nativeEnum(IssueUrgency),
    expires_at: z.timestamp().nullable(),
    file_pks: z.array(z.pk()).nullable(),
}).describe('IssueActionData')

export function getIssueController(issue: ModelAttributes<'Issue'>): IssueController<unknown> {
    return match(IssueCategory, issue.category, {
        [IssueCategory.SplitSale]: () => new SplitSaleIssueController(issue),
        [IssueCategory.ClientStatus]: () => new ClientStatusIssueController(issue),
        [IssueCategory.CustomerSupportGeneral]: () => new CustomerSupportIssueController(issue),
        [IssueCategory.Disclaimer]: () => new DisclaimerIssueController(issue),
        [IssueCategory.TaskDiscussion]: () => new TaskDiscussionController(issue),
        [IssueCategory.KeepClient]: () => new KeepClientIssueController(issue),
        [IssueCategory.ClosingReason]: () => new ClosingReasonIssueController(issue),
        [IssueCategory.PriceDrop]: () => new PriceDropIssueController(issue),
        [IssueCategory.AltExtra]: () => new AltExtraIssueController(issue),
        [IssueCategory.VerificationTA]: () => new VerificationIssueController(issue, IssueCategory.VerificationTA),
        [IssueCategory.VerificationOther]: () => new VerificationIssueController(issue, IssueCategory.VerificationOther),
        [IssueCategory.AirlineReimbursement]: () => new AirlineReimbursementIssueController(issue, IssueCategory.AirlineReimbursement),
        [IssueCategory.AirlineReimbursementRefund]: () => new AirlineReimbursementIssueController(issue, IssueCategory.AirlineReimbursementRefund),
        [IssueCategory.AirlineReimbursementCredit]: () => new AirlineReimbursementIssueController(issue, IssueCategory.AirlineReimbursementCredit),
        [IssueCategory.AirlineReimbursementCancellation]: () => new AirlineReimbursementIssueController(issue, IssueCategory.AirlineReimbursementCancellation),
        [IssueCategory.PnrSchedule]: () => new PnrScheduleIssueController(issue, IssueCategory.PnrSchedule),
        [IssueCategory.Voucher]: () => new VoucherIssueController(issue),
    })
}
