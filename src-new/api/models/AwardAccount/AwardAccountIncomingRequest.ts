import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        AwardAccountIncomingRequest: AwardAccountIncomingRequest
    }
}

export default class AwardAccountIncomingRequest implements Definition.Model {
    public readonly name = 'AwardAccountIncomingRequest'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        award_account_pk: z.pk(),
        consolidator_pk: z.pk(),
        miles_amount: z.number(),
        balance: z.number(),
        price: z.number(),
        remark: z.string(),
        rcpm: z.number(),
        created_at: z.timestamp(),
        is_deleted: z.boolean(),
        product_pk: z.pk().nullable(),
    })

    public relations = defineRelations({
        product: belongsTo('Product', 'product_pk'),
    })

    public actions = defineActions({
        //
        editRemark: {
            params: z.object({
                pk: z.pk(),
                remark: z.string().nullable(),
            }),
            response: z.null(),
        },
        void: {
            params: z.object({
                pk: z.pk(),
                remark: z.string().nullable(),
            }),
            response: z.null(),
        },
        writeOff: {
            params: z.object({
                pk: z.pk(),
                miles_amount: z.int(),
                remark: z.string(),
            }),
            response: z.null(),
        },
    })
}
