// This file is auto generatedby vite-plugin-import-dictionaries
// Do not edit this file manually

import AgentDictionary from '#/src-new/api/dictionaries/Resource/AgentDictionary'
import AgentReportInvoiceStatusDictionary from '#/src-new/api/dictionaries/Static/AgentReport/AgentReportInvoiceStatusDictionary'
import AirlineDictionary from '#/src-new/api/dictionaries/Resource/AirlineDictionary'
import AirlineReportStageDictionary from '#/src-new/api/dictionaries/Static/AirlineReport/AirlineReportStageDictionary'
import AwardAccountBookingTypeDictionary from '#/src-new/api/dictionaries/Static/AwardAccount/AwardAccountBookingTypeDictionary'
import AwardAccountRelationDictionary from '#/src-new/api/dictionaries/Static/AwardAccount/AwardAccountRelationDictionary'
import AwardAccountStatusDictionary from '#/src-new/api/dictionaries/Static/AwardAccount/AwardAccountStatusDictionary'
import AwardAccountTypeDictionary from '#/src-new/api/dictionaries/Static/AwardAccount/AwardAccountTypeDictionary'
import AwardAccountWarningFlagDictionary from '#/src-new/api/dictionaries/Static/AwardAccount/AwardAccountWarningFlagDictionary'
import BookkeepingInvoiceTypeDictionary from '#/src-new/api/dictionaries/Static/BookkeepingInvoice/BookkeepingInvoiceTypeDictionary'
import BookkeepingPaymentGatewayDictionary from '#/src-new/api/dictionaries/Resource/BookkeepingPaymentGatewayDictionary'
import BookkeepingTransactionDirectionDictionary from '#/src-new/api/dictionaries/Static/BookkeepingTransaction/BookkeepingTransactionDirectionDictionary'
import BookkeepingTransactionStatusDictionary from '#/src-new/api/dictionaries/Static/BookkeepingTransaction/BookkeepingTransactionStatusDictionary'
import ChatGatewayDictionary from '#/src-new/api/dictionaries/Resource/ChatGatewayDictionary'
import ClientCabinetTransactionTypeDictionary from '#/src-new/api/dictionaries/Static/ClientCabinet/ClientCabinetTransactionTypeDictionary'
import ClientStatusDictionary from '#/src-new/api/dictionaries/Resource/ClientStatusDictionary'
import ClosingReasonDictionary from '#/src-new/api/dictionaries/Static/Lead/ClosingReasonDictionary'
import CompanyContactCategoryDictionary from '#/src-new/api/dictionaries/Static/CompanyContact/CompanyContactCategoryDictionary'
import CompanyContactStatusDictionary from '#/src-new/api/dictionaries/Static/CompanyContact/CompanyContactStatusDictionary'
import CompanyContactSubCategoryDictionary from '#/src-new/api/dictionaries/Static/CompanyContact/CompanyContactSubCategoryDictionary'
import CompanyContactTypeDictionary from '#/src-new/api/dictionaries/Static/CompanyContact/CompanyContactTypeDictionary'
import ConsolidatorAreaDictionary from '#/src-new/api/dictionaries/Resource/ConsolidatorAreaDictionary'
import ConsolidatorDictionary from '#/src-new/api/dictionaries/Resource/ConsolidatorDictionary'
import ConsolidatorParserSchemaFieldTypeDictionary from '#/src-new/api/dictionaries/Static/Consolidator/ConsolidatorParserSchemaFieldTypeDictionary'
import ContactRuleTypeDictionary from '#/src-new/api/dictionaries/Static/ContactRule/ContactRuleTypeDictionary'
import ContactRuleValidationTypeDictionary from '#/src-new/api/dictionaries/Static/ContactRule/ContactRuleValidationTypeDictionary'
import CountryDictionary from '#/src-new/api/dictionaries/Resource/Country/CountryDictionary'
import CreditCardTypeDictionary from '#/src-new/api/dictionaries/Static/Sale/CreditCardTypeDictionary'
import CustomerReachedTypeDictionary from '#/src-new/api/dictionaries/Static/Task/CustomerReachedTypeDictionary'
import DepartmentDictionary from '#/src-new/api/dictionaries/Resource/DepartmentDictionary'
import ElrFrtCheckStatusDictionary from '#/src-new/api/dictionaries/Static/ElrFrtCheck/ElrFrtCheckStatusDictionary'
import ExpectedAmountResultDictionary from '#/src-new/api/dictionaries/Static/ExpectedAmount/ExpectedAmountResultDictionary'
import ExpectedAmountStatusDictionary from '#/src-new/api/dictionaries/Static/ExpectedAmount/ExpectedAmountStatusDictionary'
import ExpectedAmountTypeDictionary from '#/src-new/api/dictionaries/Static/ExpectedAmount/ExpectedAmountTypeDictionary'
import ExpertLeadStatusDictionary from '#/src-new/api/dictionaries/Static/ExpertLead/ExpertLeadStatusDictionary'
import ExternalResourceDictionary from '#/src-new/api/dictionaries/Static/ExternalResourceDictionary'
import GamblingLotStatusDictionary from '#/src-new/api/dictionaries/Static/GamblingLotStatus/GamblingLotStatusDictionary'
import HiringSourceDictionary from '#/src-new/api/dictionaries/Static/Agent/HiringSourceDictionary'
import IssueCategoryDictionary from '#/src-new/api/dictionaries/Static/Issue/IssueCategoryDictionary'
import IssueFilterTaskAssignmentDictionary from '#/src-new/api/dictionaries/Static/Issue/IssueFilterTaskAssignmentDictionary'
import IssueFilterTaskStatusDictionary from '#/src-new/api/dictionaries/Static/Issue/IssueFilterTaskStatusDictionary'
import IssueGroupDictionary from '#/src-new/api/dictionaries/Resource/IssueGroupDictionary'
import IssueStarColorDictionary from '#/src-new/api/dictionaries/Static/Issue/IssueStarColorDictionary'
import IssueStatusDictionary from '#/src-new/api/dictionaries/Static/Issue/IssueStatusDictionary'
import ItineraryClassDictionary from '#/src-new/api/dictionaries/Static/ItineraryClassDictionary'
import ItineraryTypeDictionary from '#/src-new/api/dictionaries/Static/Lead/ItineraryTypeDictionary'
import LeadOfferPqSortTypeDictionary from '#/src-new/api/dictionaries/Static/Lead/LeadOfferPqSortTypeDictionary'
import LeadStarTypeDictionary from '#/src-new/api/dictionaries/Static/Lead/LeadStarTypeDictionary'
import LeadStatusDictionary from '#/src-new/api/dictionaries/Resource/Lead/LeadStatusDictionary'
import LeaveRequestStatusDictionary from '#/src-new/api/dictionaries/Static/Leave/LeaveRequestStatusDictionary'
import LeaveRequestTypeDictionary from '#/src-new/api/dictionaries/Static/Leave/LeaveRequestTypeDictionary'
import MilePriceProgramDictionary from '#/src-new/api/dictionaries/Resource/MilePriceProgramDictionary'
import OfficeDictionary from '#/src-new/api/dictionaries/Resource/OfficeDictionary'
import PaymentGatewayDictionary from '#/src-new/api/dictionaries/Resource/PaymentGatewayDictionary'
import PnrFareTypeDictionary from '#/src-new/api/dictionaries/Static/Pnr/PnrFareTypeDictionary'
import PnrInfoTypeDictionary from '#/src-new/api/dictionaries/Static/Pnr/PnrInfoTypeDictionary'
import PnrScheduleTypeDictionary from '#/src-new/api/dictionaries/Static/Pnr/PnrScheduleTypeDictionary'
import PositionDictionary from '#/src-new/api/dictionaries/Resource/PositionDictionary'
import PriceDropOfferTypeDictionary from '#/src-new/api/dictionaries/Static/PriceDrop/PriceDropOfferTypeDictionary'
import ProductFieldTypeDictionary from '#/src-new/api/dictionaries/Static/Product/ProductFieldTypeDictionary'
import ProductTypeDictionary from '#/src-new/api/dictionaries/Static/Product/ProductTypeDictionary'
import ProjectCardUsageAreaDictionary from '#/src-new/api/dictionaries/Static/ProjectCard/ProjectCardUsageAreaDictionary'
import ProjectDictionary from '#/src-new/api/dictionaries/Resource/ProjectDictionary'
import RegionDictionary from '#/src-new/api/dictionaries/Resource/Region/RegionDictionary'
import ReleasePostStatusDictionary from '#/src-new/api/dictionaries/Static/ReleasePost/ReleasePostStatusDictionary'
import SaleCategoryTypeDictionary from '#/src-new/api/dictionaries/Static/Sale/SaleCategoryTypeDictionary'
import SaleClientRequestCategoryDictionary from '#/src-new/api/dictionaries/Static/SaleClientRequest/SaleClientRequestCategoryDictionary'
import SaleClientRequestStatusDictionary from '#/src-new/api/dictionaries/Static/SaleClientRequest/SaleClientRequestStatusDictionary'
import SaleInsuranceTypeDictionary from '#/src-new/api/dictionaries/Static/Sale/SaleInsuranceTypeDictionary'
import SaleRequestHelpDictionary from '#/src-new/api/dictionaries/Resource/SaleRequestHelpDictionary'
import SaleTransactionActionTypeDictionary from '#/src-new/api/dictionaries/Static/Sale/SaleTransactionActionTypeDictionary'
import SaleTransactionStatusDictionary from '#/src-new/api/dictionaries/Static/Sale/SaleTransactionStatusDictionary'
import SaleTypeDictionary from '#/src-new/api/dictionaries/Static/Sale/SaleTypeDictionary'
import SaleVersionPayTypeDictionary from '#/src-new/api/dictionaries/Static/Sale/SaleVersionPayTypeDictionary'
import SegmentTypeDictionary from '#/src-new/api/dictionaries/Static/Segment/SegmentTypeDictionary'
import ShiftTypeDictionary from '#/src-new/api/dictionaries/Static/Shift/ShiftTypeDictionary'
import SkillLevelDictionary from '#/src-new/api/dictionaries/Static/Skill/SkillLevelDictionary'
import TaskCategoryDictionary from '#/src-new/api/dictionaries/Static/Task/TaskCategoryDictionary'
import TeamDictionary from '#/src-new/api/dictionaries/Resource/TeamDictionary'
import TicketTypeDictionary from '#/src-new/api/dictionaries/Static/Ticket/TicketTypeDictionary'
import VoucherCreditWithDictionary from '#/src-new/api/dictionaries/Static/Voucher/VoucherCreditWithDictionary'
import VoucherStatusDictionary from '#/src-new/api/dictionaries/Static/Voucher/VoucherStatusDictionary'
import WorkspaceDictionary from '#/src-new/api/dictionaries/Static/WorkspaceDictionary'

export default {
    Agent: AgentDictionary,
    AgentReportInvoiceStatus: AgentReportInvoiceStatusDictionary,
    Airline: AirlineDictionary,
    AirlineReportStage: AirlineReportStageDictionary,
    AwardAccountBookingType: AwardAccountBookingTypeDictionary,
    AwardAccountRelation: AwardAccountRelationDictionary,
    AwardAccountStatus: AwardAccountStatusDictionary,
    AwardAccountType: AwardAccountTypeDictionary,
    AwardAccountWarningFlag: AwardAccountWarningFlagDictionary,
    BookkeepingInvoiceType: BookkeepingInvoiceTypeDictionary,
    BookkeepingPaymentGateway: BookkeepingPaymentGatewayDictionary,
    BookkeepingTransactionDirection: BookkeepingTransactionDirectionDictionary,
    BookkeepingTransactionStatus: BookkeepingTransactionStatusDictionary,
    ChatGateway: ChatGatewayDictionary,
    ClientCabinetTransactionType: ClientCabinetTransactionTypeDictionary,
    ClientStatus: ClientStatusDictionary,
    ClosingReason: ClosingReasonDictionary,
    CompanyContactCategory: CompanyContactCategoryDictionary,
    CompanyContactStatus: CompanyContactStatusDictionary,
    CompanyContactSubCategory: CompanyContactSubCategoryDictionary,
    CompanyContactType: CompanyContactTypeDictionary,
    ConsolidatorArea: ConsolidatorAreaDictionary,
    Consolidator: ConsolidatorDictionary,
    ConsolidatorParserSchemaFieldType: ConsolidatorParserSchemaFieldTypeDictionary,
    ContactRuleType: ContactRuleTypeDictionary,
    ContactRuleValidationType: ContactRuleValidationTypeDictionary,
    Country: CountryDictionary,
    CreditCardType: CreditCardTypeDictionary,
    CustomerReachedType: CustomerReachedTypeDictionary,
    Department: DepartmentDictionary,
    ElrFrtCheckStatus: ElrFrtCheckStatusDictionary,
    ExpectedAmountResult: ExpectedAmountResultDictionary,
    ExpectedAmountStatus: ExpectedAmountStatusDictionary,
    ExpectedAmountType: ExpectedAmountTypeDictionary,
    ExpertLeadStatus: ExpertLeadStatusDictionary,
    ExternalResource: ExternalResourceDictionary,
    GamblingLotStatus: GamblingLotStatusDictionary,
    HiringSource: HiringSourceDictionary,
    IssueCategory: IssueCategoryDictionary,
    IssueFilterTaskAssignment: IssueFilterTaskAssignmentDictionary,
    IssueFilterTaskStatus: IssueFilterTaskStatusDictionary,
    IssueGroup: IssueGroupDictionary,
    IssueStarColor: IssueStarColorDictionary,
    IssueStatus: IssueStatusDictionary,
    ItineraryClass: ItineraryClassDictionary,
    ItineraryType: ItineraryTypeDictionary,
    LeadOfferPqSortType: LeadOfferPqSortTypeDictionary,
    LeadStarType: LeadStarTypeDictionary,
    LeadStatus: LeadStatusDictionary,
    LeaveRequestStatus: LeaveRequestStatusDictionary,
    LeaveRequestType: LeaveRequestTypeDictionary,
    MilePriceProgram: MilePriceProgramDictionary,
    Office: OfficeDictionary,
    PaymentGateway: PaymentGatewayDictionary,
    PnrFareType: PnrFareTypeDictionary,
    PnrInfoType: PnrInfoTypeDictionary,
    PnrScheduleType: PnrScheduleTypeDictionary,
    Position: PositionDictionary,
    PriceDropOfferType: PriceDropOfferTypeDictionary,
    ProductFieldType: ProductFieldTypeDictionary,
    ProductType: ProductTypeDictionary,
    ProjectCardUsageArea: ProjectCardUsageAreaDictionary,
    Project: ProjectDictionary,
    Region: RegionDictionary,
    ReleasePostStatus: ReleasePostStatusDictionary,
    SaleCategoryType: SaleCategoryTypeDictionary,
    SaleClientRequestCategory: SaleClientRequestCategoryDictionary,
    SaleClientRequestStatus: SaleClientRequestStatusDictionary,
    SaleInsuranceType: SaleInsuranceTypeDictionary,
    SaleRequestHelp: SaleRequestHelpDictionary,
    SaleTransactionActionType: SaleTransactionActionTypeDictionary,
    SaleTransactionStatus: SaleTransactionStatusDictionary,
    SaleType: SaleTypeDictionary,
    SaleVersionPayType: SaleVersionPayTypeDictionary,
    SegmentType: SegmentTypeDictionary,
    ShiftType: ShiftTypeDictionary,
    SkillLevel: SkillLevelDictionary,
    TaskCategory: TaskCategoryDictionary,
    Team: TeamDictionary,
    TicketType: TicketTypeDictionary,
    VoucherCreditWith: VoucherCreditWithDictionary,
    VoucherStatus: VoucherStatusDictionary,
    Workspace: WorkspaceDictionary,
} as const