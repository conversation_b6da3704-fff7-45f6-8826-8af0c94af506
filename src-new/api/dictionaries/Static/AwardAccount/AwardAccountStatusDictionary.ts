import Dictionary from '~/api/dictionaries/Dictionary'
import { AwardAccountStatus } from '~/api/models/AwardAccount/AwardAccount'
import type { ForSelectMapperOptions } from '~/api/dictionaries/DictionaryAdapter'
import type SelectOption from '~types/structures/SelectOption'
import { UIColor } from '~/lib/UI'
import CircleSlashIcon from '~assets/icons/CircleSlashIcon.svg?component'
import CircleEllipsisIcon from '~assets/icons/CircleEllipsisIcon.svg?component'
import CircleCheckSoldIcon from '~assets/icons/CircleCheckSoldIcon.svg?component'

interface Item {
    title: string,
    value: AwardAccountStatus,
    icon: AnyComponent,
    style: string
}

type Adapters = {
    forSelect: (record: Item, options?: ForSelectMapperOptions) => SelectOption,
}

export default class AwardAccountStatusDictionary extends Dictionary<Item, Adapters> {
    public static indexBy = ['value']

    public records: Item[] = [
        {
            value: AwardAccountStatus.Active,
            title: 'Active',
            style: UIColor.Success,
            icon: CheckCircleIcon,
        },
        {
            value: AwardAccountStatus.InUse,
            title: 'In use',
            style: UIColor.Warning,
            icon: PlayCircleIcon,
        },
        {
            value: AwardAccountStatus.Pending,
            title: 'Pending',
            style: UIColor.Pending,
            icon: ClockIcon,
        },
        {
            value: AwardAccountStatus.Lost,
            title: 'Lost',
            style: UIColor.Danger,
            icon: HelpCircleIcon,
        },
        {
            value: AwardAccountStatus.Blocked,
            title: 'Blocked',
            style: UIColor.Danger,
            icon: CircleSlashIcon,
        },
        {
            value: AwardAccountStatus.BlockedAudit,
            title: 'Blocked - audit',
            style: UIColor.Danger,
            icon: CircleSlashIcon,
        },
        {
            value: AwardAccountStatus.Leftover,
            title: 'Leftovers',
            style: UIColor.Neutral,
            icon: CircleEllipsisIcon,
        },
        {
            value: AwardAccountStatus.Used,
            title: 'Used',
            style: UIColor.Neutral,
            icon: MinusCircleIcon,
        },
        {
            value: AwardAccountStatus.Sold,
            title: 'Sold',
            style: UIColor.Primary,
            icon: CircleCheckSoldIcon,
        },
    ]

    public adapters = {
        forSelect: (record: Item, options?: ForSelectMapperOptions): SelectOption => {
            return {
                title: record.title,
                value: record.value,
                image: options?.withImage ? record.icon : undefined,
                classes: record.style,
            }
        },
    }

    public find(value: AwardAccountStatus) {
        return super.findIndexed('value', value) as Item
    }
}
