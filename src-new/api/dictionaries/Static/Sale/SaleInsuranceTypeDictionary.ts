import Dictionary from '~/api/dictionaries/Dictionary'
import type SelectOption from '~types/structures/SelectOption'
import type { ForSelectMapperOptions } from '~/api/dictionaries/DictionaryAdapter'
import { toHumanPhrase } from '~/lib/Helper/StringHelper'

export enum SaleInsuranceType {
    TicketProtectionPlan1 = 'ticketProtectionPlan1',
    TicketProtectionPlan2 = 'ticketProtectionPlan2',
    TicketProtectionPlan3 = 'ticketProtectionPlan3',
    BaggageProtection = 'baggageProtection',
}

export enum SaleInsuranceTypeGroup {
    TicketProtection = 'ticketProtection',
    BaggageProtection = 'baggageProtection',
}

interface Item {
    title: string,
    titleShort?: string,
    id: SaleInsuranceType,
    group: SaleInsuranceTypeGroup,
    features: (keyof typeof features)[],
}

type InsuranceTypeForSelectMapperOptions = ForSelectMapperOptions & {
    grouped?: boolean,
}

type Adapters = {
    forSelect: (record: Item, options?: InsuranceTypeForSelectMapperOptions) => SelectOption<SaleInsuranceType>,
}

export default class SaleInsuranceTypeDictionary extends Dictionary<Item, Adapters> {
    public static indexBy = ['id']

    public records: Item[] = [
        {
            title: 'TP (Basic)',
            titleShort: 'Basic',
            id: SaleInsuranceType.TicketProtectionPlan1,
            group: SaleInsuranceTypeGroup.TicketProtection,
            features: ['healthRefund', 'flightDelay'],
        },
        {
            title: 'TP (Advantage)',
            titleShort: 'Advantage',
            id: SaleInsuranceType.TicketProtectionPlan2,
            group: SaleInsuranceTypeGroup.TicketProtection,
            features: ['healthRefund', 'flightDelay', 'flexibleChange', 'baggageProtection', 'voucher'],
        },
        {
            title: 'TP (All Included)',
            titleShort: 'All Included',
            id: SaleInsuranceType.TicketProtectionPlan3,
            group: SaleInsuranceTypeGroup.TicketProtection,
            features: ['healthRefund', 'flightDelay', 'flexibleChange', 'cancel', 'baggageProtection', 'voucher'],
        },
        {
            title: 'Baggage Protection',
            id: SaleInsuranceType.BaggageProtection,
            group: SaleInsuranceTypeGroup.BaggageProtection,
            features: ['baggageProtection'],
        },
    ]

    public features = features

    public adapters = {
        forSelect: (record: Item, options: InsuranceTypeForSelectMapperOptions): SelectOption<SaleInsuranceType> => {
            const group = options.grouped ? toHumanPhrase(record.group) : undefined

            return {
                title: record.title,
                value: record.id,
                group,
            }
        },
    }

    public find(value: SaleInsuranceType) {
        return super.findIndexed('id', value) as Item
    }
}

const features = {
    healthRefund: {
        title: 'Health-Based Refund Guarantee',
        description: [
            'TBC Ticket Protection allows a 100% refund of fully unused tickets for traveler(s) hospitalized at the time of scheduled departure.',
        ],
    },
    flightDelay: {
        title: 'Flight Delay & Connection Coverage',
        description: [
            'Missed your connection? We\'ve got you covered.',
        ],
    },
    flexibleChange: {
        title: 'Flexible Change (Any Reason)',
        description: [
            'Make one change to your trip – no questions asked.',
        ],
    },
    cancel: {
        title: 'Cancel for Any Reason',
        description: [
            'Cancel with confidence – get a 100% credit or 75% refund. No fine print, no hidden costs.',
        ],
    },
    baggageProtection: {
        title: 'Baggage Protection',
        description: [
            'Lost or delayed luggage? We\'ve got you covered – up to $1,000 per bag',
        ],
    },
    voucher: {
        title: 'Next Trip Voucher (up to $150)',
        description: [
            'Get up to $150 off your next trip – on us.',
        ],
    },
} satisfies {
    [key: string]: {
        title: string,
        description: string[],
    }
}
