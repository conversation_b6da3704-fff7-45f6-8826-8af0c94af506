import Dictionary from '~/api/dictionaries/Dictionary'
import type SelectOption from '~types/structures/SelectOption'
import type { ForSelectMapperOptions } from '~/api/dictionaries/DictionaryAdapter'
import { ProductType } from '~/api/models/Product/Product'

interface Item {
    title: string,
    value: ProductType,
    category?: ProductTypeCategory[],
    access?: string[],
}

type Adapters = {
    forSelect: (record: Item, options?: ForSelectMapperOptions) => SelectOption<ProductType>,
}

export enum ProductTypeCategory {
    AdditionalExpense = 'additional',
    IncentiveSale = 'incentive'
}

export default class ProductTypeDictionary extends Dictionary<Item, Adapters> {
    public static indexBy = ['value']

    public records: Item[] = [

        {
            title: 'Baggage',
            value: ProductType.Baggage,
            category: [ProductTypeCategory.AdditionalExpense],
        },
        {
            title: 'Commission/Refund',
            value: ProductType.CommissionRefund,
            category: [ProductTypeCategory.AdditionalExpense],
        },
        {
            title: 'Tips',
            value: ProductType.Tips,
            category: [ProductTypeCategory.IncentiveSale, ProductTypeCategory.AdditionalExpense],
        },
        {
            title: 'Flexible ticket',
            value: ProductType.FlexibleTicket,
            category: [ProductTypeCategory.IncentiveSale],
        },
        {
            title: 'Extra GP',
            value: ProductType.ExtraGP,
            access: ['manage'],
            category: [ProductTypeCategory.IncentiveSale],
        },
        {
            title: 'Miles',
            value: ProductType.Miles,
        },
        {
            title: 'Ticket',
            value: ProductType.Ticket,

        },
        {
            title: 'CheckIn',
            value: ProductType.CheckIn,
            category: [ProductTypeCategory.AdditionalExpense],
        },
        {
            title: 'Insurance',
            value: ProductType.Insurance,
        },
        {
            title: 'Ticket Refund',
            value: ProductType.TicketRefund,
            category: [ProductTypeCategory.AdditionalExpense],
        },
        {
            title: 'Upgrade',
            value: ProductType.Upgrade,
        },
        {
            title: 'Upgrade Ticket',
            value: ProductType.UpgradeTicket,
        },
        {
            title: 'Cash Upgrade',
            value: ProductType.CashUpgrade,
            category: [ProductTypeCategory.AdditionalExpense],
        },
        {
            title: 'Award Upgrade',
            value: ProductType.AwardUpgrade,
            category: [ProductTypeCategory.AdditionalExpense],

        },
        {
            title: 'Voucher',
            value: ProductType.Voucher,
            category: [ProductTypeCategory.AdditionalExpense],
        },
        {
            title: 'Seats',
            value: ProductType.Seats,
            category: [ProductTypeCategory.AdditionalExpense],
        },
        {
            title: 'Special Services Fee',
            value: ProductType.SpecialServicesFee,
            category: [ProductTypeCategory.AdditionalExpense],
        },
        {
            title: 'Airline Reimbursement Fee',
            value: ProductType.AirlineReimbursementFee,
            category: [ProductTypeCategory.AdditionalExpense],
        },
        {
            title: 'EMD',
            value: ProductType.Emd,
            category: [ProductTypeCategory.AdditionalExpense],
        },
        {
            title: 'Points trade',
            value: ProductType.PointsTrade,
            category: [ProductTypeCategory.AdditionalExpense],
        },
        {
            title: 'Other',
            value: ProductType.Other,
            category: [ProductTypeCategory.AdditionalExpense],
        },

    ]

    public adapters = {
        forSelect: (record: Item, options?: ForSelectMapperOptions): SelectOption<ProductType> => {
            return {
                title: record.title,
                value: record.value,
            }
        },
    }

    public get mapRecordsForFilter() {
        return this.adapter.mapRecords(this.records.filter((record) => {
            return ![ProductType.TicketRefund, ProductType.CommissionRefund].includes(record.value)
        }))
    }

    public getCategoryRecords(category: ProductTypeCategory) {
        return this.records.filter((record) => {
            return record.category?.includes(category)
        })
    }

    public find(value: ProductType) {
        return super.findIndexed('value', value)
    }

    public get mapRecordsForFilterHasUpgrade() {
        return this.adapter.mapRecords(this.records.filter(record => {
            return [ProductType.CashUpgrade, ProductType.AwardUpgrade].includes(record.value)
        }))
    }
}
