import Dictionary from '~/api/dictionaries/Dictionary'
import type { ForSelectMapperOptions } from '~/api/dictionaries/DictionaryAdapter'
import type SelectOption from '~types/structures/SelectOption'
import { ExpectedAmountResult } from '~/api/models/ExpectedAmount/ExpectedAmount'
import { UIColor } from '~/lib/UI'

interface Item {
    title: string,
    value: ExpectedAmountResult,
    style: UIColor
}

type Adapters = {
    forSelect: (record: Item, options?: ForSelectMapperOptions) => SelectOption,
}

export default class ExpectedAmountResultDictionary extends Dictionary<Item, Adapters> {
    public static indexBy = ['value']

    public records: Item[] = [
        {
            value: ExpectedAmountResult.Pending,
            title: 'Pending',
            style: UIColor.Pending,
        },
        {
            value: ExpectedAmountResult.Received,
            title: 'Received',
            style: UIColor.Success,
        },
        {
            value: ExpectedAmountResult.Denied,
            title: 'Denied',
            style: UIColor.Danger,
        },
    ]

    public adapters = {
        forSelect: (record: Item, options?: ForSelectMapperOptions): SelectOption => {
            return {
                title: record.title,
                value: record.value,
            }
        },
    }

    public find(value: ExpectedAmountResult) {
        return super.findIndexed('value', value) as Item
    }
}
