import Dictionary from '~/api/dictionaries/Dictionary'
import type SelectOption from '~types/structures/SelectOption'
import { LeaveRequestType } from '~/api/models/LeaveManagement/LeaveRequest'

interface Item {
    title: string,
    value: LeaveRequestType
}

type Adapters = {
    forSelect: (record: Item) => SelectOption,
}

export default class LeaveRequestStatusDictionary extends Dictionary<Item, Adapters> {
    public static indexBy = ['value']

    public records = [
        { title: 'Vacation', value: LeaveRequestType.Vacation },
        { title: 'Sick day', value: LeaveRequestType.SickDay },
    ]

    public adapters: Adapters = {
        forSelect: (record) => {
            return {
                title: record.title,
                value: record.value,
            }
        },
    }

    public find(value: LeaveRequestType) {
        return super.findIndexed('value', value) as Item
    }
}
