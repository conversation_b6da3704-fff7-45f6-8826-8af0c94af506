import Dictionary from '~/api/dictionaries/Dictionary'
import type SelectOption from '~types/structures/SelectOption'
import { LeaveRequestStatus } from '~/api/models/LeaveManagement/LeaveRequest'

interface Item {
    title: string,
    value: LeaveRequestStatus
    classModifier: string,
    bgStyleClass: string
}

type Adapters = {
    forSelect: (record: Item) => SelectOption,
}

export default class LeaveRequestStatusDictionary extends Dictionary<Item, Adapters> {
    public static indexBy = ['value']

    public records = [
        { title: 'Rejected', value: LeaveRequestStatus.Rejected, classModifier: '--danger', bgStyleClass: 'bg-danger' },
        { title: 'Pending', value: LeaveRequestStatus.Pending, classModifier: '--secondary', bgStyleClass: 'bg-secondary' },
        { title: 'Approved', value: LeaveRequestStatus.Approved, classModifier: '--success', bgStyleClass: 'bg-success' },
    ]

    public adapters: Adapters = {
        forSelect: (record) => {
            return {
                title: record.title,
                value: record.value,
            }
        },
    }

    public find(value: LeaveRequestStatus) {
        return super.findIndexed('value', value) as Item
    }
}
