import type { ModelAttributes } from '~types/lib/Model'
import type SelectOption from '~types/structures/SelectOption'
import { PositionName } from '~/api/models/Position/Position'
import ResourceDictionary from '~/api/dictionaries/ResourceDictionary'
import type { ForSelectMapperOptions } from '~/api/dictionaries/DictionaryAdapter'
import { DepartmentName } from '~/api/models/Department/Department'
import { DepartmentCategory } from '~/api/models/Agent/Agent'
import UserMinusRounded from '~/assets/icons/UserMinusRounded.svg?component'

export interface AgentForSelectMapperOptions extends ForSelectMapperOptions {
    withPosition?: boolean,
    withDepartment?: boolean,
    withEmail?: boolean,
    withTeam?: boolean,
    withDisabledAgentBadge?: boolean,
    groupByTeam?: boolean,
    groupByDepartment?: boolean,
}

type Adapters = {
    forSelect: (record: ModelAttributes<'Agent'>, options?: AgentForSelectMapperOptions) => SelectOption,
}

export default class AgentDictionary extends ResourceDictionary<'Agent', Adapters> {
    // public static indexBy: string[] = ['_pk']

    protected getList() {
        return useModel('Agent', this.modelOptions).useResourceList()
    }

    public adapters: Adapters = {
        forSelect: (record, options) => {
            let group = undefined

            let showTitleBadges = !record.is_enabled

            if (options?.withDisabledAgentBadge === false) {
                showTitleBadges = false
            }

            if (options?.groupByTeam && record.team_pk) {
                const teamDictionary = this.useDictionary('Team')

                group = teamDictionary.find(record.team_pk)?.name
            } else if (options?.groupByDepartment && record.department_pk) {
                const departmentDictionary = this.useDictionary('Department')

                group = departmentDictionary.find(record.department_pk)?.name
            }

            return {
                title: record.first_name + ' ' + record.last_name,
                value: usePk(record),
                image: options?.withImage ? record.avatar : undefined,
                subtitle: this.getInputSelectSubtitle(record, options),
                titleBadges: showTitleBadges ? [
                    () => h('div', { class: 'badge max-h-3.5 --muted --rounded --xs --soft !text-secondary-700 dark:!text-secondary-300' },
                        [h('div', { class: 'flex items-center' }, [h(UserMinusRounded, { class: '!text-secondary-600 dark:!text-secondary-300 mr-0.5' }), 'Inactive'])]),
                    ] : undefined,
                classes: !record.is_enabled && showTitleBadges ? 'text-secondary' : undefined,
                group,
            }
        },
    }

    private getInputSelectSubtitle(record: ModelAttributes<'Agent'>, options?: AgentForSelectMapperOptions) {
        let subtitle = []

        if (options?.withEmail) {
            subtitle.push(record.email)
        }

        if (options?.withDepartment && record.department_pk) {
            const department = this.useDictionary('Department').find(record.department_pk)

            if (department) {
                subtitle.push(department.name)
            }
        }

        if (options?.withPosition) {
            const position = this.useDictionary('Position').find(record.position_pk)

            if (position) {
                subtitle.push(position.name + (position.system_name === PositionName.Banned ? ' ❌' : ''))
            }
        }

        if (options?.withTeam && record.team_pk) {
            const team = this.useDictionary('Team').find(record.team_pk)

            if (team) {
                subtitle.push(team.name)
            }
        }

        if (options?.withTeam && record.ex_team_pk) {
            const team = this.useDictionary('Team').find(record.ex_team_pk)

            if (team) {
                subtitle.push(`Ex.${team.name}`)
            }
        }

        subtitle = subtitle.filter(Boolean)

        if (!subtitle.length) {
            return
        }

        return subtitle.join(', ')
    }

    // =================

    public get mapRecords() {
        return this.adapter.mapRecords(this.recordsForProcessCategory(DepartmentCategory.Business))
    }

    public get mapActiveRecords() {
        return this.adapter.mapRecords(this.recordsForProcessCategory(DepartmentCategory.Business, true))
    }

    public get mapAllRecords() {
        return this.adapter.mapRecords(toValue(this.records))
    }

    public get businessAgents() {
        return this.recordsForProcessCategory(DepartmentCategory.Business)
    }

    public mapRecordsForProcessCategory(category: DepartmentCategory) {
        this.adapter.mapRecords(this.recordsForProcessCategory(category))
    }

    public recordsForProcessCategory(category: DepartmentCategory, active?: boolean) {
        return toValue(this.records).filter(record => {
            if (active !== undefined && record.is_enabled !== active) {
                return false
            }

            return record.department_category.includes(category)
        })
    }

    public get supervisors() {
        const supervisorPositionPk = usePk(this.useDictionary('Position').findByName(PositionName.Supervisor))

        return this.records.filter(record => record.position_pk === supervisorPositionPk)
    }

    public get experts() {
        const expertDepartmentPk = usePk(this.useDictionary('Department').findByName(DepartmentName.Experts))

        return this.records.filter(record => record.department_pk === expertDepartmentPk)
    }

    public get sellers() {
        const expertDepartmentPk = usePk(this.useDictionary('Department').findByName(DepartmentName.Sales))

        return this.records.filter(record => record.department_pk === expertDepartmentPk)
    }

    public get sellerAgents() {
        const salesDepartmentPk = usePk(this.useDictionary('Department').findByName(DepartmentName.Sales))
        const agentPositionPk = usePk(this.useDictionary('Position').findByName(PositionName.Agent))

        return this.records.filter(record => record.department_pk === salesDepartmentPk && record.position_pk === agentPositionPk)
    }

    public get ticketingRevenue() {
        const ticketingDepartmentPk = usePk(this.useDictionary('Department').findByName(DepartmentName.TicketingRevenue))

        return this.records.filter(record => record.department_pk === ticketingDepartmentPk)
    }

    public get mapSupervisors() {
        return this.adapter.mapRecords(this.supervisors)
    }

    public get mapActiveExperts() {
        const activeExperts = this.experts.filter(expert => expert.is_enabled)

        return this.adapter.mapRecords(activeExperts)
    }

    public get mapSellers() {
        const activeSeller = this.sellers.filter(seller => seller.is_enabled)

        return this.adapter.mapRecords(activeSeller)
    }

    //

    public recordsWithPosition(position: PositionName) {
        const positionModel = this.useDictionary('Position').findByName(position)
        const positionPk = usePk(positionModel)

        return this.businessAgents.filter(record => record.position_pk === positionPk)
    }

    public getAgentsByTeam(teamPk: PrimaryKey) {
        return this.records.filter(record => record.team_pk === teamPk)
    }

    public getAgentsByDepartment(departmentPk: PrimaryKey) {
        return this.records.filter(record => record.department_pk === departmentPk)
    }

    public getAgentsByDepartmentName(departmentName: DepartmentName) {
        const departmentPk = usePk(this.useDictionary('Department').findByName(departmentName))

        return this.getAgentsByDepartment(departmentPk)
    }

    public mapAgentsByDepartment(departmentPk: PrimaryKey) {
        return this.adapter.mapRecords(this.getAgentsByDepartment(departmentPk))
    }

    public mapAgentsByDepartmentName(departmentName: DepartmentName) {
        return this.adapter.mapRecords(this.getAgentsByDepartmentName(departmentName))
    }
}
