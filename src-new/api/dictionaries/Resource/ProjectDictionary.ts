import type { ModelAttributes } from '~types/lib/Model'
import ResourceDictionary from '~/api/dictionaries/ResourceDictionary'
import type SelectOption from '~types/structures/SelectOption'

type ForSelectOptions = {
    full: true,
}

type Adapters = {
    forSelect: (record: ModelAttributes<'Project'>, options?: ForSelectOptions) => SelectOption,
}

export default class ProjectDictionary extends ResourceDictionary<'Project', Adapters> {
    protected getList() {
        return useModel('Project', this.modelOptions).useResourceList()
    }

    public adapters: Adapters = {
        forSelect: (record, options) => {
            return {
                title: options?.full ? record.name : record.abbreviation,
                value: usePk(record),
            }
        },
    }

    public get mainProject() {
        return this.records.find((record) => record.is_main)!
    }

    public get mapMainProjects() {
        return this.mapFilteredRecords((record) => record.is_main)
    }

    public isTBCProject(project_pk: PrimaryKey) {
        const project = this.findOrFail(project_pk)

        return project?.abbreviation ? 'TBC' === project.abbreviation : false
    }
}

