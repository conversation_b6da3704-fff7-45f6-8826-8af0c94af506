// noinspection JSDeprecatedSymbols

import type SelectOption from '~types/structures/SelectOption'
import type { ForSelectMapperOptions } from '~/api/dictionaries/DictionaryAdapter'
import LeadStatusBadge from '~/components/Page/Lead/LeadStatusBadge.vue'
import ResourceDictionary from '~/api/dictionaries/ResourceDictionary'
import { LeadStatusName } from '~/api/models/Lead/LeadStatus'
import type { ModelAttributes } from '~types/lib/Model'
import type { ContextMenuOption } from '@/plugins/ContextMenuPlugin'

interface ForSelectOptions extends ForSelectMapperOptions {
    hideInactive?: boolean,
}

interface ForContextMenuOptions {
    withImage?: boolean,
    onClick?: (record: ModelAttributes<'LeadStatus'>) => void,
}

type Adapters = {
    forSelect: (record: ModelAttributes<'LeadStatus'>, options?: ForSelectOptions) => SelectOption,
    forContextMenu: (record: ModelAttributes<'LeadStatus'>, options?: ForContextMenuOptions) => ContextMenuOption,
}

type MapItem = {
    image: AnyComponent,
    style: string,
    short_name: string
}

export default class LeadStatusDictionary extends ResourceDictionary<'LeadStatus', Adapters> {
    public static indexBy = ['_pk', 'system_name']
    public static readonly isGeneral = true

    protected getList() {
        return useModel('LeadStatus', this.modelOptions).useResourceList()
    }

    protected map: Record<LeadStatusName, MapItem> = {
        [LeadStatusName.New]: {
            image: () => h(LeadStatusBadge, { status: LeadStatusName.New, small: true }),
            style: 'bg-orange-500 text-white',
            short_name: 'N',
        },
        [LeadStatusName.Potential]: {
            image: () => h(LeadStatusBadge, { status: LeadStatusName.Potential, small: true }),
            style: 'bg-vista-blue-300 text-white',
            short_name: 'P',
        },
        [LeadStatusName.FollowUp]: {
            image: () => h(LeadStatusBadge, { status: LeadStatusName.FollowUp, small: true }),
            style: 'bg-feldspar-300 text-white',
            short_name: 'F',
        },
        [LeadStatusName.Closed]: {
            image: () => h(LeadStatusBadge, { status: LeadStatusName.Closed, small: true }),
            style: 'bg-red-500 text-white',
            short_name: 'C',
        },
        [LeadStatusName.Sold]: {
            image: () => h(LeadStatusBadge, { status: LeadStatusName.Sold, small: true }),
            style: 'bg-aquamarine-300 text-white',
            short_name: 'S',
        },
        [LeadStatusName.Lost]: {
            image: () => h(LeadStatusBadge, { status: LeadStatusName.Lost, small: true }),
            style: 'bg-slate-500 text-white',
            short_name: 'L',
        },
        [LeadStatusName.SaleRejected]: {
            image: () => h(LeadStatusBadge, { status: LeadStatusName.SaleRejected, small: true }),
            style: 'bg-rose-700 text-white',
            short_name: 'R',
        },
        [LeadStatusName.SaleRejectedInactive]: {
            image: () => h(LeadStatusBadge, { status: LeadStatusName.SaleRejectedInactive, small: true }),
            style: 'bg-indigo-900 text-white',
            short_name: 'R',
        },
        [LeadStatusName.Fraud]: {
            image: () => h(LeadStatusBadge, { status: LeadStatusName.Fraud, small: true }),
            style: 'bg-danger text-white !text-4xs',
            short_name: 'FD',
        },
    }

    public adapters: Adapters = {
        forSelect: (record, options) => {
            const pk = usePk(record)

            return {
                title: record.name,
                value: pk,
                image: options?.withImage ? () => h(LeadStatusBadge, { statusPk: pk, small: true }) : undefined,
                hidden: options?.hideInactive && !record.active,
            }
        },
        forContextMenu: (record, options) => {
            const pk = usePk(record)

            return {
                text: record.name,
                icon: options?.withImage ? () => h(LeadStatusBadge, { statusPk: pk, small: true }) : undefined,
                onClick: () => options?.onClick?.(record),
            }
        },
    }

    public get mapActiveRecords() {
        return this.mapFilteredRecords((record) => record.active)
    }

    public get activeRecords() {
        return this.records.filter((record) => record.active)
    }

    public get inactiveRecords() {
        return this.records.filter((record) => !record.active)
    }

    public getStyle(system_name: string): string | undefined {
        return this.map[system_name as LeadStatusName]?.style
    }

    public getShortName(system_name: string): string | undefined {
        return this.map[system_name as LeadStatusName]?.short_name
    }

    public getByName(system_name: string) {
        return this.records.find((record) => record.system_name === system_name)!
    }

    public isActive(pk: PrimaryKey) {
        return !!this.find(pk)?.active
    }
}
