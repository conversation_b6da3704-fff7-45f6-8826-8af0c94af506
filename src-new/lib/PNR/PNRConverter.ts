import type { ActionResponse, ModelRef } from '~types/lib/Model'
import { capitalize } from '~/lib/Helper/StringHelper'
import type { InstanceContext } from '~/lib/Context'
import type ModelController from '~/lib/Model/ModelController'
import type ResourceList from '~/lib/Model/ResourceList'
import { copyElementToClipboard } from '~/lib/Helper/CopyToClipboardHelper'

const formatter = useService('formatter')

export default class PNRConverter {
    public messages: Message[] = []
    public formattedMessages: FormattedMessage[][][] = []
    public airlines: ModelRef<'Airline'>[]
    public priceQuoteModel: ModelController<'PriceQuote'>
    public airports: ResourceList<'Iata'>

    public constructor(context: InstanceContext) {
        this.airlines = useDictionary('Airline', { workspace: context.workspace }).records
        this.priceQuoteModel = useModel('PriceQuote', { http: { workspace: context.workspace } })
        this.airports = useModel('Iata', { http: { workspace: context.workspace } }).useList()
    }

    public async convert(segmentText: string): Promise<FormattedMessage[][][]> {
        this.messages = []
        this.formattedMessages = []

        const response: ParsedSegments = await this.priceQuoteModel.actions.parseSegments({
            segment_text: segmentText,
            pk: null,
            lead_pk: null,
        })

        const segments: Segment[] = response.segments

        const airportCodes: string[] = this.getAirportsFromSegments(segments)

        await this.airports.fetch({
            where: (q) => {
                q.or(condition => {
                    airportCodes.forEach(value => {
                        condition.eq('code', value)
                    })
                })
            },
        })

        for (const segment of segments) {
            const result: Message = this.parseMessage(segment)
            this.messages.push(result)
        }

        this.combineMessages()

        return this.formattedMessages
    }

    private combineMessages() {
        let combinedMessage: FormattedMessage[] = []
        let combinedMessages: FormattedMessage[][] = []

        this.messages.forEach((message: Message, index) => {
            combinedMessage = []

            combinedMessage.push({
                msg_type: 'image',
                content: message.image,
                image_alt: message.airline_name,
            })

            combinedMessage.push({
                msg_type: 'title',
                content: `${formatter.date(message.departure_date, message.departure_timezone, { format: 'EEE, dd MMM' })} - ${message.airline_details}`,
            })

            combinedMessage.push({
                msg_type: 'text',
                content: `Departing: ${message.departure_airport} at ${this.formatDate(message.departure_date, message.departure_timezone)}`,
            })

            combinedMessage.push({
                msg_type: 'text',
                content: `Arriving: ${message.arriving_airport} at ${this.calcArriving(message.departure_date, message.arriving_date, message.departure_timezone, message.arriving_timezone)}`,
            })

            if (this.messages[index + 1] && this.isLinked(message, this.messages[index + 1])) {
                combinedMessage.push({
                    msg_type: 'delimiter',
                    content: this.calcTransitTime(message.arriving_date, this.messages[index + 1].departure_date),
                })

                combinedMessages.push(combinedMessage)
            } else {
                combinedMessages.push(combinedMessage)
                this.formattedMessages.push([...combinedMessages])
                combinedMessages = []
            }
        })
    }

    private parseMessage(segment: Segment): Message {
        const { flightData, additionalData } = this.parseAdditional(segment)
        const airline = this.airlines.find(value => value.code == flightData.carrier)

        const airportFrom = this.airports.records.find(value => value.code == flightData.from)

        const airportTo = this.airports.records.find(value => value.code == flightData.to)

        return {
            id: Number(segment.id),
            airline_name: airline?.name,
            airline_details: `${airline?.name} -${additionalData.operated_by ? ` Operated By ${additionalData.operated_by} - ` : ''} ${flightData.number} - ${capitalize(segment.flight_class)} - ${this.calcFlightTime(additionalData.flight_time)}`,
            arriving_airport: `${airportTo?.airport_name} (${flightData.to})`,
            departure_date: flightData.departure_at,
            departure_timezone: airportFrom?.city_timezone,
            departure_airport: `${airportFrom?.airport_name} (${flightData.from})`,
            arriving_date: flightData.arrive_at,
            arriving_timezone: airportTo?.city_timezone,
            image: airline?.url,
        }
    }

    public calcTransitTime(arriving: number, departure: number): string {
        let connection = ''

        const difference = Date.getTimeDifference(arriving, departure)

        if (difference.hours >= 4) {
            connection = 'Long Connection'
        } else if (difference.hours == 1 && difference.minutes <= 30) {
            connection = 'Short Connection'
        }

        return `<div style="color: #888;">-----<span style="color:#E63B1F">${connection}</span>-----Transit Time: ${difference.hours}h ${difference.minutes}m--------</div>`
    }

    public getAirportsFromSegments(segments: Segment[]): string[] {
        const airportCodes: Set<string> = new Set()

        segments.forEach(segment => {
            const { flightData } = this.parseAdditional(segment)

            airportCodes.add(flightData.from)
            airportCodes.add(flightData.to)
        })

        return Array.from(airportCodes)
    }

    public parseAdditional(segment: Segment): { flightData: FlightInfo, additionalData: AdditionalData } {
        const additionalData: AdditionalData = JSON.parse(segment.additional) as AdditionalData
        const flightData: FlightInfo = additionalData.flight

        return { flightData, additionalData }
    }

    public calcFlightTime(flightTime: number): string  {
        const hours = Math.floor(flightTime / 60)

        const minutes = flightTime % 60

        return `${hours}h ${minutes}m`
    }

    public formatDate(date: number, timezone: string): string {
        const hours = Date.fromUnixTimestamp(date).setTimeZone(timezone).getHours()

        let formatted = formatter.date(date, timezone, { format: 'hh:mm' })

        if (formatted && formatted.startsWith('00:')) {
            formatted = '12:' + formatted.slice(3)
        }

        const suffix = hours >= 12 ? 'pm' : 'am'

        return `${formatted} ${suffix}`
    }

    public isLongTransit(from: number, to: number): boolean {
        const diff = Date.getTimeDifference(from, to)

        return diff.days > 0
    }

    public calcArriving(departing: number, arriving: number, departingTimezone: string, arrivingTimeZone: string): string  {
        const depart = Date.fromUnixTimestamp(departing).setTimeZone(departingTimezone).toDateString()
        const arrive = Date.fromUnixTimestamp(arriving).setTimeZone(arrivingTimeZone).toDateString()

        if (depart !== arrive) {
            return this.formatDate(arriving, arrivingTimeZone) + ' ' + formatter.date(arriving, arrivingTimeZone, { format: '(x dd MMM)' })?.replace('x', 'on the')
        } else {
            return this.formatDate(arriving, arrivingTimeZone)
        }
    }

    public isLinked(msg: Message, msgNext: Message): boolean {
        return !(this.isLongTransit(msg.arriving_date, msgNext?.departure_date) || msg.arriving_airport !== msgNext?.departure_airport)
    }

    public copyAllSegments(messagesHtmlRef: Ref<HTMLElement>) {
        const tempDiv = document.createElement('div')

        const delimiter = document.createElement('div')
        delimiter.innerHTML = `<div style="margin-left: 90px; color: #888">------------------------------------------------------------------------------------------</div>`

        messagesHtmlRef.value.forEach(val => {
            const cloned = val.firstChild.cloneNode(true)

            tempDiv.append(cloned)
            tempDiv.append(delimiter.cloneNode(true))
        })

        this.addLineBreaks(tempDiv)

        copyElementToClipboard(tempDiv)
    }

    private addLineBreaks(element) {
        // when using copyElementToClipboard, it copies innerText as it is, without line breaks, this cycle adds them before copy
        element.childNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
                this.addLineBreaks(node)
            } else if (node.nodeType === Node.TEXT_NODE && node.textContent.trim() !== '' && node.textContent.trim() !== '-----' && !node.textContent.trim().includes('Connection')) {
                node.textContent += '\n'
            }
        })
    }
}

type ParsedSegments = ActionResponse<'PriceQuote', 'parseSegments'>
type Segment = ParsedSegments['segments'][number]

type Message = {
    id: number,
    airline_name: string,
    airline_details: string,
    departure_airport: string,
    departure_date: number,
    departure_timezone: string,
    arriving_airport: string,
    arriving_date: number,
    arriving_timezone: string,
    image?: string
}

type FlightInfo = {
    from: string,
    to: string,
    carrier: string,
    number: string,
    departure_at: number,
    arrive_at: number,
    plane: string,
}

type AdditionalData = {
    flight: FlightInfo,
    meal: boolean,
    meal_type: string | null,
    flight_time: number,
    flight_distance: string,
    gds: string,
    departure_terminal: string,
    arrival_terminal: string,
    is_technical: boolean,
    parent_segment_number: boolean,
    operated_by: string,
}

export type FormattedMessage = {
    msg_type: 'image' | 'title' | 'text' | 'delimiter',
    content: string,
    image_alt?: string
}
