import { toastDefault, toastError, toastWarning } from '@/lib/core/helper/ToastHelper'
import { removeElementWithAttribute, applyInlineStylesToClone } from '~/lib/Helper/DOMHelper'

export const copyToClipboard = (text: string, showToast = true) => {
    // noinspection JSIgnoredPromiseFromCall
    handlePromise(text, showToast)
}

export const copyElementToClipboard = (element: HTMLElement, showToast = true) => {
    if (!window.ClipboardItem) {
        copyToClipboard(element.innerText, showToast)

        toastWarning('Copying HTML is not supported in this browser')

        return
    }

    const clipboardItem = new ClipboardItem({
        'text/plain': new Blob([element.innerText], { type: 'text/plain' }),
        'text/html': new Blob([element.outerHTML], { type: 'text/html' }),
    })

    // noinspection JSIgnoredPromiseFromCall
    handlePromise([clipboardItem], showToast)
}

export async function copyElementWithStylesToClipboard(data: HTMLElement, showToast = true) {
    const clone = data.cloneNode(true) as HTMLElement

    applyInlineStylesToClone(data, clone)
    removeElementWithAttribute(clone, 'data-copy', 'false')

    if (!window.ClipboardItem) {
        copyToClipboard(clone.innerText, showToast)

        toastWarning('Copying HTML is not supported in this browser')

        return
    }

    const clipboardItem = new ClipboardItem({
        ['text/plain']: new Blob([clone.innerText], { type: 'text/plain' }),
        ['text/html']: new Blob([clone.outerHTML], { type: 'text/html' }),
    })

    // noinspection ES6MissingAwait
    handlePromise([clipboardItem], showToast)

    clone.remove()
}

const handlePromise = async (clipboardItemsOrText: ClipboardItem[] | string, showToast = true) => {
    try {
        if (Array.isArray(clipboardItemsOrText)) {
            await navigator.clipboard.write(clipboardItemsOrText)
        } else {
            await navigator.clipboard.writeText(String(clipboardItemsOrText)) // For untyped js files
        }

        if (showToast) {
            toastDefault('Copied!')
        }
    } catch (error) {
        if (showToast) {
            if (error instanceof Error && error.message.includes('focus')) {
                toastError(`Can't copy, please don't leave this window`, { duration: Timespan.seconds(5).inMilliseconds })
            } else {
                toastError('Failed to copy')
            }
        }
    }
}
