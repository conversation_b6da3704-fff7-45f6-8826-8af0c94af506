import type { DateRange } from '@/lib/core/helper/DateHelper'

export function calculateHolidaysInDateRange(holidays: number[], dateRange: DateRange) {
    let holidaysCount = 0

    for (const holidayTimestamp of holidays) {
        const holidayDate = Date.fromUnixTimestamp(holidayTimestamp)

        const firstStripped = new Date(dateRange.start.getUTCFullYear(), dateRange.start.getUTCMonth(), dateRange.start.getUTCDate())
        const secondStripped = new Date(dateRange.end.getUTCFullYear(), dateRange.end.getUTCMonth(), dateRange.end.getUTCDate())
        const thirdStripped = new Date(holidayDate.getUTCFullYear(), holidayDate.getUTCMonth(), holidayDate.getUTCDate())

        const isInRange = thirdStripped >= firstStripped && thirdStripped <= secondStripped

        if (isInRange) {
            holidaysCount++
        }
    }

    return holidaysCount
}
