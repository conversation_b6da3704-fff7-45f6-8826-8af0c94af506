import { ProductPayType } from '~/api/models/Product/Product'

type ProductCostFields = {
    check_payment: number,
    check_payment_ps: number,
    sell_price: number,
    tax: number,
    fare: number,
    pay_type: ProductPayType,
    is_award: boolean,
    commission: number,
    commission_ps: number,
    issuing_fee: number
}

export default class ProductService {
    private check_payment: number = 0
    public readonly check_payment_ps: number = 0
    private readonly sell_price: number = 0
    private readonly tax: number = 0
    private readonly fare: number =  0
    private readonly commission: number =  0
    private readonly issuing_fee: number = 0
    private readonly commission_ps: number =  0

    private readonly pay_type: ProductPayType =  ProductPayType.CC
    private readonly is_award: boolean = false

    private constructor(product: ProductCostFields) {
        this.pay_type = product.pay_type
        this.is_award = product.is_award
        this.check_payment = product.check_payment
        this.check_payment_ps = product.check_payment_ps
        this.sell_price = product.sell_price
        this.tax = product.tax
        this.fare = product.fare
        this.commission = product.commission
        this.commission_ps = product.commission_ps
        this.issuing_fee = product.issuing_fee
    }

    public static makeIncentiveSale(options: {
        sell_price: number,
        fare: number,
        check_payment: number,
        check_payment_enabled: boolean,
        pay_type?: ProductPayType
    }) {
        return new ProductService({
            fare: options.fare ?? 0,
            pay_type: options.pay_type ?? ProductPayType.CC,
            sell_price: options.sell_price ?? 0,
            is_award: false,
            check_payment: options.check_payment ?? 0,
            check_payment_ps: options.check_payment_enabled ? 3.5 : 0,
            commission: 0,
            commission_ps: 0,
            issuing_fee: 0,
            tax: 0,
        })
    }

    public static makeAdditionalExpense(options: {
        sell_price: number,
        fare: number,
        check_payment: number,
        check_payment_enabled: boolean,
        pay_type?: ProductPayType
    }) {
        return new ProductService({
            fare: options.fare ?? 0,
            pay_type: options.pay_type ?? ProductPayType.CC,
            sell_price: options.sell_price ?? 0,
            is_award: false,
            check_payment: options.check_payment ?? 0,
            check_payment_ps: options.check_payment_enabled ? 3.5 : 0,
            commission: 0,
            commission_ps: 0,
            issuing_fee: 0,
            tax: 0,
        })
    }

    private getAirlineCharge() {
        let airline_charge = 0

        if (this.pay_type === ProductPayType.CC) {
            airline_charge = this.is_award ? this.tax : this.tax + this.fare
        }

        return airline_charge
    }

    private getInhouseCharge() {
        return this.sell_price - this.getAirlineCharge()
    }

    private getCalculateCkFromPs = (amount: number, ps: number) => {
        return ps > 0 ? (amount * ps / 100).toMoney() : 0
    }

    private getCalculateCommission() {
        return this.commission_ps > 0 ? this.fare * this.commission_ps / 100 : this.commission
    }

    public getCalculateCk() {
        if (this.pay_type === ProductPayType.PaxWire) {
            return 0
        } else if (this.check_payment_ps === 0) {
            return 0
        } else {
            const inhouse_charge = this.getInhouseCharge()

            return this.getCalculateCkFromPs(inhouse_charge, this.check_payment_ps)
        }
    }

    public getCalculateProfit() {
        return this.sell_price + this.getCalculateCommission() - this.tax - this.fare - this.issuing_fee - this.getCalculateCk()
    }
}
