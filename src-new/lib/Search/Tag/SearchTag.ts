import type { Component, MaybeRefOrGetter } from 'vue'
import type ArrayConditionAndOr from '~/lib/Helper/ArrayCondition/Conditions/ArrayConditionAndOr'
import type { TableHeading } from '~/composables/useTableColumns'
import { unique } from '~/lib/Helper/ArrayHelper'

export type SearchTagOptions = {
    model?: ModelName
    visualGroup?: string
    placeholder?: string
    multiple?: boolean
    shortcut?: true | string | SearchTagShortcut
    applyCondition?: (tag: SearchTag, field: string, and: ArrayConditionAndOr<any>) => void
    disabled?: MaybeRefOrGetter<boolean>
    local?: true, // Will not be sent to the server
}

export type SearchTagShortcut = {
    label: string
    regex: RegExp
}

export const HiddenVisualGroup = 'hidden'

export default abstract class SearchTag<TValue = unknown> {
    declare public abstract component: Component

    private state: {
        values: TValue[] | undefined
        defaultValues: TValue[] | undefined
    } = reactive({
        values: undefined,
        defaultValues: undefined,
    })

    public constructor(protected label: string, protected options: SearchTagOptions = {}) {
        this.init()
    }

    protected init() {
        this.initShortcut()
    }

    // Model
    // =========================

    public get model() {
        return this.options.model
    }

    public setModel(model: ModelName) {
        this.options.model = model

        return this
    }

    // Visual Group
    // =========================

    public get visualGroup() {
        return this.options.visualGroup
    }

    public setVisualGroup(visualGroup: string | undefined) {
        this.options.visualGroup = visualGroup

        return this
    }

    // Shortcut
    // =========================

    private initShortcut() {
        const shortcut = this.options.shortcut

        if (!shortcut) {
            return
        }

        this.setShortcut(shortcut)
    }

    public setShortcut(shortcut: true | string | SearchTagShortcut) {
        if (typeof shortcut === 'string') {
            this.shortcut = {
                label: shortcut,
                regex: new RegExp(`^${shortcut}$`),
            }
        } else if (shortcut === true) {
            this.shortcut = makeShortcutFromLabel(this.label)
        } else {
            this.shortcut = shortcut
        }

        return this
    }

    public get isLocal() {
        return this.options.local ?? false
    }

    public setLocal(value?: boolean = true) {
        this.options.local = value

        return this
    }

    // Labels
    // =========================

    public get queryLabel() {
        return this.label
    }

    // Value
    // =========================

    public get values() {
        return this.state.values
    }

    public reset() {
        this.state.values = undefined
    }

    public removeValue(value: TValue) {
        if (this.state.values === undefined) {
            return
        }

        this.setValues(this.state.values.filter(v => v !== value))
    }

    public setValues(values: TValue[]) {
        if (JSON.stringify(toRaw(this.state.values)) === JSON.stringify(values)) {
            return
        }

        this.state.values = values.length ? values : undefined
    }

    public setValue(value: TValue) {
        this.setValues([value])
    }

    public addValue(value: TValue) {
        this.state.values = unique([...(this.state.values ?? []), value])
    }

    public hasValue(value: TValue) {
        return this.state.values?.includes(value) ?? false
    }

    public setDefaultValue(values: TValue[] | undefined) {
        this.state.defaultValues = values

        return this
    }

    public get isMultiple() {
        return this.options.multiple ?? false
    }

    public get isActive() {
        return this.values !== undefined
    }

    // Placeholder
    // =========================

    public get placeholder() {
        return this.options.placeholder
    }

    public setPlaceholder(placeholder: string) {
        this.options.placeholder = placeholder

        return this
    }

    // Shortcuts
    // =========================

    public shortcut: SearchTagShortcut | undefined

    // Visibility
    // =========================

    public hideInSearch(value = true) {
        if (value) {
            this.setVisualGroup(HiddenVisualGroup)
        } else {
            this.setVisualGroup(undefined)
        }

        return this
    }

    public disable(value: MaybeRefOrGetter<boolean> = true) {
        this.options.disabled = value

        return this
    }

    public get isDisabled() {
        return toValue(this.options.disabled) ?? false
    }

    // Hydration
    // =========================

    /**
     * Parse value from query string
     *
     * @implement this method in the child class
     */
    protected hydrateValue(value: string, label: string): TValue | undefined {
        if (value === undefined) {
            return
        }

        return value.trim() as TValue
    }

    /**
     * @implement this method in the child class
     */
    public toQueryValue(value: TValue): string {
        return String(value)
    }

    //

    protected get queryPrefix() {
        return `${this.queryLabel}: `
    }

    public toQuery(value: TValue) {
        if (value === undefined) {
            return
        }

        const queryValue = this.toQueryValue(value)

        return `${this.queryPrefix}${queryValue}`
    }

    public toSearchLabel(value: TValue) {
        return this.toQuery(value)
    }

    public initDefaultValues() {
        if (this.state.defaultValues) {
            this.state.values = [...this.state.defaultValues]
        }
    }

    public hydrate(query: string[]): string[] {
        this.reset()

        // To prevent infinite loops, we need to make sure that every tag is hydrated only once
        // So we need to remove the hydrated tags from the query
        return query.filter(value => {
            const parsed = this.hydrateSection(value)

            if (parsed === undefined) {
                return true // Keep the value in the query and skip it
            }

            this.addValue(parsed)

            return false // Remove the value from the query when it's hydrated
        })
    }

    public hydrateSection(value: string): TValue | undefined {
        const label = this.queryPrefix

        if (!value.startsWith(label)) {
            return
        }

        const parsed = this.hydrateValue(value.slice(label.length), label)

        if (parsed !== undefined) {
            return parsed
        }
    }

    // Conditions
    // =========================

    protected get customApplyCondition() {
        return this.options.applyCondition
    }

    public get hasCustomApplyCondition() {
        return Boolean(this.options.applyCondition)
    }

    /**
     * Applies a condition to a field.
     *
     * Used in 'filter' and 'applyCondition' method of SearchController.
     *
     * @implement this method in the child class
     * @optional
     */
    protected _applyCondition(field: string, and: ArrayConditionAndOr<any>) {
        and.or(or => {
            or.eq(field, this.values)
        })
    }

    /**
     * Applies a condition to a field.
     *
     * Used in 'filter' and 'applyCondition' method of SearchController.
     */
    public applyCondition(field: string, and: ArrayConditionAndOr<any>) {
        // Local tags should not be applied to the server
        if (this.isLocal) {
            return
        }

        if (this.customApplyCondition) {
            this.customApplyCondition(this, field, and)
        } else {
            this._applyCondition(field, and)
        }
    }
}

// @ts-ignore
export type SearchTagValue<TTag extends SearchTag<any>> = ReturnType<TTag['hydrateValue']>

export type SearchTagProps<TTag extends SearchTag<any>> = {
    modelValue: SearchTagValue<TTag> | SearchTagValue<TTag>[] | undefined,
    tag: TTag,
    heading?: TableHeading,
}

export type SearchTagEmits<TTag extends SearchTag<any>> = {
    'update:modelValue': [value: SearchTagValue<TTag> | undefined],
}

//

const makeShortcutFromLabel = (label: string): SearchTagShortcut => {
    const parts = label.split(' ')

    let shortcut = ''

    for (const part of parts) {
        if (part.length <= 2) {
            shortcut += part
        } else {
            shortcut += part[0]
        }
    }

    shortcut += ':'

    shortcut = shortcut.toLowerCase()

    return {
        label: shortcut,
        regex: new RegExp(`^${shortcut}$`),
    }
}
