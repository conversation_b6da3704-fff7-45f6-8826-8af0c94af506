import type SelectOption from '~types/structures/SelectOption'
import AgentOrTeamSearchComponent from '~/components/Search/Tag/AgentOrTeamSearchTag.vue'
import type { ModelAttributes } from '~types/lib/Model'
import { getFullName } from '~/lib/Helper/PersonHelper'
import type { SearchTagOptions } from '~/lib/Search/Tag/SearchTag'
import SearchTag from '~/lib/Search/Tag/SearchTag'
import type ArrayConditionAndOr from '~/lib/Helper/ArrayCondition/Conditions/ArrayConditionAndOr'
import { uniqueBy } from '~/lib/Helper/ArrayHelper'

interface TeamsAndAgentsData{
    title: string,
    value: string,
    team_pk?: PrimaryKey | null
}
export default class AgentOrTeamSearchTag extends SearchTag<string> {
    public component = AgentOrTeamSearchComponent

    protected localState = reactive({
        options: [] as SelectOption[],
    })

    protected teamsAndAgents = reactive({
        values: { team: []  as TeamsAndAgentsData[], agent: []  as TeamsAndAgentsData[] },
    })

    public constructor(label: string, protected options: SearchTagOptions = {}) {
        super(label,  {
            multiple: true,
            ...options,
        })
        this.localState.options = this.setOptions()
        this.teamsAndAgents.values = this.setTeamAndAgentsValues()
    }

    protected teamsAndAgentsFullData() {
        const workspaces = useService('workspace').availableWorkspaces
        const agentFullDataList =  uniqueBy(workspaces.flatMap(({ pk: workspace }) => {
            return useDictionary('Agent', { workspace }).businessAgents
        }) as ModelAttributes<'Agent'>[], 'id')
        const teamFullDataList = uniqueBy(workspaces.flatMap(({ pk: workspace }) => {
            return useDictionary('Team', { workspace }).records
        }) as ModelAttributes<'Team'>[], 'id')

        return { teams: teamFullDataList, agents: agentFullDataList }
    }

    protected setOptions() {
        const teamsAndAgentsLists = this.teamsAndAgentsFullData()

        const getSubtitle = (pk: PrimaryKey | null) => {
            if (!pk) return undefined

            const exTeamName = teamsAndAgentsLists.teams.find(team => team._pk === pk)?.name

            return exTeamName ? `Ex.${exTeamName}` : undefined
        }

        const activeAgentOptions = teamsAndAgentsLists.agents.filter(value => value.is_enabled).map((el: ModelAttributes<'Agent'>) => {
            return {
                group: 'Active agents',
                title: getFullName(el),
                value: `agent-${usePk(el)}`,
                subtitle: getSubtitle(el.ex_team_pk),
            }
        })

        const inactiveAgentOptions = teamsAndAgentsLists.agents.filter(value => !value.is_enabled).map((el: ModelAttributes<'Agent'>) => {
            return {
                group: 'Inactive agents',
                title: getFullName(el),
                value: `agent-${usePk(el)}`,
                subtitle: getSubtitle(el.ex_team_pk),
            }
        })

        const teamOptions = teamsAndAgentsLists.teams.map((el: ModelAttributes<'Team'>) => {
            return {
                group: 'Teams',
                title: el.name,
                value: `team-${usePk(el)}`,
            }
        })

        return [{ group: '', value: 'unassigned', title: 'Unassigned' }, ...activeAgentOptions, ...inactiveAgentOptions, ...teamOptions]
    }

    protected setTeamAndAgentsValues() {
        const teamsAndAgentsLists = this.teamsAndAgentsFullData()
        const agentData = teamsAndAgentsLists.agents.map((el: ModelAttributes<'Agent'>) => {
            return { title: getFullName(el), value: `agent-${usePk(el)}`, team_pk: el.team_pk }
        })
        const teamData = teamsAndAgentsLists.teams.map((el: ModelAttributes<'Team'>) => {
            return { title: el.name, value: `team-${usePk(el)}` }
        })

        return { team: teamData, agent: agentData }
    }

    public get selectOptions() {
        return this.localState.options
    }

    public toQuery(value: string) {
        if (value === undefined) {
            return
        }
        const stringValue = String(value)

        const queryValue = this.toQueryValue(value)

        if (stringValue.includes('agent') || stringValue === 'unassigned') {
            return `Agent: ${queryValue}`
        } else if (stringValue.includes('team')) {
            return `Team: ${queryValue}`
        }

        return `${this.queryPrefix}${queryValue}`
    }

    public hydrateSection(value: string): PrimaryKey | undefined {
        let parsed

        if (value.startsWith('Agent:')) {
            parsed = this.hydrateValue(value.slice('Agent:'.length).trim(), 'Agent:')
        } else if (value.startsWith('Team:')) {
            parsed = this.hydrateValue(value.slice('Team:'.length).trim(), 'Team:')
        }

        if (parsed !== undefined) {
            return parsed
        }
    }

    protected hydrateValue(value: string, label: string): PrimaryKey | undefined {
        const key = label?.split(':')[0]
        const stringValue = String(value)

        if (key === 'Agent' && stringValue !== 'Unassigned') {
            return this.teamsAndAgents.values.agent.find(option => option.title === stringValue)?.value
        } else if (key === 'Team') {
            return this.teamsAndAgents.values.team.find(option => option.title === stringValue)?.value
        }

        return 'unassigned'
    }

    public toQueryValue(value: string): string  {
        const stringValue = String(value)
        const key = stringValue.split('-')[0]

        if (key === 'agent') {
            const selectedAgent = this.teamsAndAgents.values.agent.find(option => option.value === stringValue)?.title

            return selectedAgent ? selectedAgent : stringValue
        }

        if (key === 'team') {
            const team = this.teamsAndAgents.values.team.find(option => option.value === stringValue)?.title

            return team ? team : stringValue
        }

        return 'Unassigned'
    }

    public getAgentPks() {
        const selectedValues: string[] = []

        for (const option of this.values ?? []) {
            const selector =  option.split('-')[0]

            if (option === 'unassigned') {
                selectedValues.push(option)
                continue
            }

            if (selector.includes('agent')) {
                const pickedAgent =  this.teamsAndAgents.values.agent.find((el: TeamsAndAgentsData) => el.value === option)?.value

                if (pickedAgent) {
                    selectedValues.push(pickedAgent.split('-')[1])
                }
            } else if (selector.includes('team')) {
                this.teamsAndAgents.values.agent.map((el: TeamsAndAgentsData) => {
                    if (el.team_pk === option.split('-')[1]) {
                        selectedValues.push(el.value.split('-')[1])
                    }
                })
            }
        }

        return selectedValues
    }

    public applyCondition(field: string, and: ArrayConditionAndOr<any>) {
        const selectedValues: string[] = this.getAgentPks()

        and.or(or => {
            for (const option of selectedValues ?? []) {
                or.and(q => {
                    q.eq(field, option)
                })
            }
        })
    }

    // Checkbox Enabled
    // =========================
    public get withCheckbox() {
        return this.options.withCheckbox
    }
}
