import type SearchTag from '~/lib/Search/Tag/SearchTag'
import type { RouteLocationNormalizedLoaded, Router } from 'vue-router'
import { arraysAreEqual, ensureArray } from '~/lib/Helper/ArrayHelper'
import type ArrayConditionAndOr from '~/lib/Helper/ArrayCondition/Conditions/ArrayConditionAndOr'
import type { Model, ModelSearchFields } from '~types/lib/Model'
import type ResourceList from '~/lib/Model/ResourceList'
import { until, watchDebounced } from '@vueuse/core'
import { ensureInEffectScope } from '~/composables/ensureInEffectScope'
import type { Awaitable, WatchDebouncedOptions } from '@vueuse/shared'
import AsyncSearchTag from '~/lib/Search/Tag/AsyncSearchTag'
import ArrayConditionHelper from '~/lib/Helper/ArrayCondition/ArrayConditionHelper'
import { useProgress } from '@marcoschulte/vue3-progress'
import {
    type Preset,
    type PresetsStorage,
    type PresetStorageOptions,
    usePresetsStorage,
} from '~/composables/usePresetsStorage'
import { toastWarning } from '@/lib/core/helper/ToastHelper'
import router from '~/router'

type Options = {
    /**
     * When true, hydrates tags from query on init
     */
    autoHydrate?: boolean

    /**
     * When true, syncs tags with query on change (bidirectional)
     */
    syncWithQuery?: boolean

    /**
     * Use presets for tags
     */

    presets?: PresetStorageOptions
}

const defaultOptions: Options = {
    autoHydrate: true,
    syncWithQuery: true,
}

export default class SearchController<TTags extends {
    [Key in string]: SearchTag<unknown>
} = {
    [Key in string]: SearchTag<unknown>
}> {
    private state = reactive({
        hydrated: false,
    })

    public constructor(
        public tags: TTags,
        private options: Options = {},
    ) {
        this.options = {
            ...defaultOptions,
            ...options,
        }

        //

        if (isDevelopment) {
            const tagLabels: Record<string, number> = {}

            for (const tag of Object.values(tags)) {
                const label = tag.queryLabel

                if (tagLabels[label]) {
                    tagLabels[label]++
                } else {
                    tagLabels[label] = 1
                }
            }

            for (const [label, count] of Object.entries(tagLabels)) {
                if (count > 1) {
                    useLogger('search-controller').error(`Duplicate query label "${label}" in tags`)
                }
            }
        }

        //

        const route = router.currentRoute

        if (this.options.autoHydrate) {
            // noinspection JSIgnoredPromiseFromCall
            this.hydrate(route.value)
        } else {
            this.initDefaultValues()
        }

        //

        if (this.options.syncWithQuery) {
            watch(() => this.activeTags, () => {
                if (!this.isHydrated) {
                    return
                }

                // @todo Reset syncErrorCounter on each successful sync
                if (this.syncErrorCounter > 100) {
                    throw new Error('Query mismatch. Sync stopped.')
                }

                this.syncQuery(router)
            })

            watch(() => route.value.query.tags, () => {
                if (!this.isHydrated) {
                    return
                }

                if (this.syncErrorCounter > 100) {
                    throw new Error('Query mismatch. Sync stopped.')
                }

                // noinspection JSIgnoredPromiseFromCall
                this.hydrate(route.value)
            })
        }

        if (this.options.presets) {
            this.presetsStorage = usePresetsStorage(this.options.presets)
        }
    }

    public get availableTags() {
        return Object.fromEntries(Object.entries(this.tags).filter(([_, tag]) => !tag.isDisabled))
    }

    private lastHydratedQuery: string | undefined

    private syncErrorCounter = 0

    public async hydrate(route: RouteLocationNormalizedLoaded) {
        if (!this.options.syncWithQuery) {
            await this.load()

            this.initDefaultValues()

            await nextTick(() => {
                this.state.hydrated = true
            })

            return
        }

        const queryHash = JSON.stringify(route.query.tags)

        if ((this.lastHydratedQuery || queryHash) && this.lastHydratedQuery === queryHash) {
            return
        }

        let query = ensureArray(route.query.tags).filter(Boolean) as string[]

        await this.load()

        for (const tag of Object.values(this.tags)) {
            if (tag.isDisabled) {
                continue
            }

            query = tag.hydrate(query)
        }

        //

        if (!this.isSameQuery(route.query)) {
            if (isDevelopment) {
                useLogger('search-controller').warn(
                    'Query mismatch.',
                    '\t',
                    'Hydrated query:',
                    this.toQuery().tags,
                    '\t',
                    'Query from route:',
                    ensureArray(route.query.tags ?? []),
                    '\t',
                    'Unhydrated query',
                    query,
                )
            }

            this.syncErrorCounter++
        }

        if (!this.isHydrated) {
            this.initDefaultValues()
        }

        this.lastHydratedQuery = queryHash

        await nextTick(() => {
            this.state.hydrated = true
        })
    }

    private isSameQuery(query: any[] | any) {
        const tags = ensureArray(query?.tags ?? [])

        if (!Array.isArray(tags)) {
            return false
        }

        return arraysAreEqual(tags, this.toQuery().tags)
    }

    public get isHydrated() {
        return this.state.hydrated
    }

    public syncQuery(router: Router) {
        if (this.isSameQuery(router.currentRoute.value.query)) {
            return
        }

        useLogger('search-controller').log('Syncing query', structuredClone(this.toQuery()), 'Current query', structuredClone(router.currentRoute.value.query))

        // noinspection JSIgnoredPromiseFromCall
        router.replace({
            query: {
                ...router.currentRoute.value.query,
                ...this.toQuery(),
            },
        })
    }

    public get activeTags() {
        return Object.values(this.tags).filter(tag => !tag.isDisabled && tag.values?.length)
    }

    public get hasActiveTags() {
        return !!this.activeTags.length
    }

    protected initDefaultValues() {
        if (this.activeTags.length) {
            return
        }

        for (const tag of Object.values(this.tags)) {
            tag.initDefaultValues()
        }
    }

    public toQuery() {
        const query: string[] = []

        for (const tag of Object.values(this.tags)) {
            if (!tag.values?.length) {
                continue
            }

            for (const value of tag.values) {
                const tagQuery = tag.toQuery(value)

                if (tagQuery === undefined) {
                    continue
                }

                query.push(tagQuery)
            }
        }

        return {
            tags: query,
        }
    }

    //

    public reset() {
        for (const tag of this.activeTags) {
            tag.reset()
        }
    }

    // Conditions
    // =========================

    private applyFilteredConditions(
        applyFunction: (tag: SearchTag, field: string) => void,
        model?: ModelName | null,
    ) {
        // When there are no model tags, and model passed = throw warning
        // When there are model tags and tags without model, but model is not passed = throw warning

        let lastModel: string | null | undefined = undefined

        for (const field of Object.keys(this.tags)) {
            const tag = this.tags[field]

            if (tag.isDisabled) {
                continue
            }

            if (lastModel !== null && lastModel !== undefined && lastModel !== tag.model && model === undefined) {
                throw new Error('There are tags with different models, but model is not passed to "applyCondition" or "filter" method')
            }

            lastModel = tag.model || null

            if (model !== null && model !== undefined && tag.model !== model) {
                continue
            }

            if (!tag.values?.length && !tag.hasCustomApplyCondition) {
                continue
            }

            applyFunction(tag, field)
        }
    }

    public applyCondition(and: ArrayConditionAndOr<any>, model?: ModelName | null, options?: {
        exclude?: (keyof TTags)[]
    }) {
        this.applyFilteredConditions((tag, field) => {
            if (options?.exclude?.includes(field)) {
                return
            }
            tag.applyCondition(field, and)
        }, model)
    }

//

    public filter<
        TItem extends AnyObject,
        TModel extends ModelName,
        TResolvers extends {
            // eslint-disable-next-line
            [Key in Extract<keyof TTags, Exclude<keyof ModelSearchFields<TModel>, Exclude<keyof TItem, Model.Relation.Field<TModel>>>>]: (item: TItem) => ModelSearchFields<TModel>[Key] | ModelSearchFields<TModel>[Key][] | undefined
        } & {
            [Key in Extract<keyof TTags, Extract<keyof ModelSearchFields<TModel>, keyof TItem>>]?: (item: TItem) => ModelSearchFields<TModel>[Key] | ModelSearchFields<TModel>[Key][] | undefined
        },
    >(
        items: TItem[],
        searchFieldResolvers?: TResolvers,
        model?: TModel | null,
    ) {
        const where = new ArrayConditionHelper()

        this.applyFilteredConditions((tag, field) => {
            tag.applyCondition(field, where)
        }, model)

        return items.filter((record) => {
            if (!searchFieldResolvers) {
                return where.check(record)
            }

            const additionalData: Record<string, any> = {}

            for (const field of Object.keys(searchFieldResolvers)) {
                additionalData[field] = (searchFieldResolvers as any)[field](record)
            }

            return where.check({
                ...record,
                ...additionalData,
            })
        })
    }

    // Helpers
    // =========================

    public static useModel<
        TModelName extends ModelName,
        TTags extends {
            [Key in keyof ModelSearchFields<TModelName>]?: SearchTag<unknown>
        }
    >(model: TModelName, tags: TTags) {
        for (const field of Object.keys(tags)) {
            // @remove-on-production-begin
            if (isDevelopment) {
                if (!useModelDefinition(model).searchFields?.shape[field]) {
                    useLogger('search-controller').warn(`Field "${field}" is not searchable in model ${model}`)
                }
            }
            // @remove-on-production-end

            const tag = tags[field as keyof TTags]

            if (!tag) {
                throw new Error(`Tag for field "${field}" is not defined`)
            }

            tag.setModel(model)
        }

        return tags as {
            [Key in keyof TTags]: Exclude<TTags[Key], undefined>
        }
    }

    public static useVisualGroup<TTags extends Record<string, SearchTag<unknown>>>(group: string, tags: TTags) {
        for (const tag of Object.values(tags)) {
            tag.setVisualGroup(group)
        }

        return tags as TTags
    }

    public static forModel<
        TModelName extends ModelName,
        TTags extends {
            [Key in keyof ModelSearchFields<TModelName>]?: SearchTag<unknown>
        },
    >(
        model: TModelName,
        tags: TTags,
        options?: Options,
    ) {
        return new SearchController(SearchController.useModel(model, tags), options)
    }
//

    public useChangeHandler(
        callback: () => Awaitable<void>,
        watchOptions: WatchDebouncedOptions<false> = {
            debounce: 300,
        },
    ) {
        return watchDebounced(() => this.activeTags, callback, watchOptions)
    }

    public useList(
        list: ResourceList,
        watchOptions: WatchDebouncedOptions<false> = {
            debounce: 300,
        },
    ) {
        ensureInEffectScope()

        list.depends(this.waitForDependanciesReady.bind(this))

        let stopWatch: (() => void) | undefined = undefined

        const createDebouncedWatcher = () => {
            if (!this.isHydrated) {
                return
            }

            stopWatch = this.useChangeHandler(async () => {
                // noinspection JSIgnoredPromiseFromCall
                useProgress().attach(list.fetch({ resetPage: true }))
            }, watchOptions)
        }

        createDebouncedWatcher()

        onUnmounted(() => {
            stopWatch?.()
        })

        watchOnce(() => this.isHydrated, () => {
            createDebouncedWatcher()
        })

        return this
    }

    public async load() {
        await Promise.all(Object.values(this.tags).map(tag => {
            if (!(tag instanceof AsyncSearchTag) || tag.isReady) {
                return
            }

            return tag.load()
        }).filter(Boolean))
    }

    public waitForDependanciesReady() {
        return Promise.all(Object.values(this.tags).map(tag => {
            if (!(tag instanceof AsyncSearchTag) || tag.isReady) {
                return
            }

            return until(() => tag.isReady).toBeTruthy()
        }).filter(Boolean))
    }

    // Presets
    // =========================

    public presetsStorage: PresetsStorage | undefined

    public get isPresetsEnabled() {
        return Boolean(this.options.presets)
    }

    protected presetQueryCache = new Map<PrimaryKey, string[]>()

    public isPresetActive(preset: Preset) {
        if (!this.isHydrated) {
            return false
        }

        let presetQuery: string[]

        const presetKey = `${usePk(preset)}-${preset.updated_at}`

        const cachedPresetQuery = this.presetQueryCache.get(presetKey)

        if (!cachedPresetQuery) {
            const decoded = decodePresetFilters(this.tags, preset.filters)

            presetQuery = Object.entries(decoded).flatMap(([key, values]) => {
                const tag = this.tags[key]

                if (!tag) {
                    return
                }

                return values.map(v => tag.toQuery(v))
            }).filter(Boolean)

            this.presetQueryCache.set(presetKey, presetQuery)
        } else {
            presetQuery = cachedPresetQuery
        }

        const tagsQuery = this.toQuery().tags

        return arraysAreEqual(presetQuery.sort(), tagsQuery.sort())
    }

    protected presetValidityCache = new Map<PrimaryKey, boolean | unknown>()

    public isPresetValid(preset: Preset) {
        if (!this.isHydrated) {
            return true
        }

        const presetKey = `${usePk(preset)}-${preset.updated_at}`

        if (this.presetValidityCache.has(presetKey)) {
            return this.presetValidityCache.get(presetKey) === true
        }

        try {
            decodePresetFilters(this.tags, preset.filters, true)

            this.presetValidityCache.set(presetKey, true)

            return true
        } catch (e: unknown) {
            this.presetValidityCache.set(presetKey, e)

            return false
        }
    }

    public getPresetValidityState(preset: Preset) {
        return this.presetValidityCache.get(`${usePk(preset)}-${preset.updated_at}`)
    }

    public applyPreset(preset: Preset) {
        if (!this.isHydrated) {
            useLogger('search-controller').warn('Trying to apply preset before hydration is completed')

            return
        }

        const filters = decodePresetFilters(this.tags, preset.filters)

        if (Object.values(filters).length === 0) {
            toastWarning('Looks like preset is empty')

            return
        }

        this.reset()

        for (const [key, values] of Object.entries(filters)) {
            const tag = this.tags[key]

            if (!tag || tag.isDisabled) {
                continue
            }

            tag.setValues(values)
        }
    }

    public get presets() {
        return this.presetsStorage?.presets.value || []
    }
}
