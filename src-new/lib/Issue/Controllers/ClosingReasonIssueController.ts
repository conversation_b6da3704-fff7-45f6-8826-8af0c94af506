import IssueController from '~/lib/Issue/IssueController'
import type z from 'zod'
import type { IssueCategory, IssueResult } from '~/api/models/Issue/Issue'
import IssueResultClosingReason from '~/components/Page/Issue/Result/ClosingReason/IssueResultClosingReason.vue'
import IssueResultClosingReasonForm from '~/components/Page/Issue/Result/ClosingReason/IssueResultClosingReasonForm.vue'
import IssueActionsClosingReason from '~/components/Page/Issue/Result/ClosingReason/IssueActionsClosingReason.vue'
import type { ModelAttributes } from '~types/lib/Model'

export default class ClosingReasonIssueController extends IssueController<
    z.infer<typeof IssueResult[IssueCategory.ClosingReason]>
> {
    public resultComponent = IssueResultClosingReason
    public resultCompactComponent = IssueResultClosingReason
    public resultFormComponent = IssueResultClosingReasonForm

    public actionsComponentTitle = ''
    public actionsComponent = IssueActionsClosingReason

    public categoryInfo = {
        title: 'Closing reason',
        icon: () => h(AlertTriangleIcon, { class: 'text-danger' }),
    }

    public declineReasons = [
        { value: 'no_PQ', title: 'No PQs sent' },
        { value: 'no_reply', title: 'Agent did not reply to inquiry' },
        { value: 'keep_client', title: 'Keep client instead of closing' },
        { value: 'other', title: 'Other' },
    ]

    public constructor(issue: ModelAttributes<'Issue'>) {
        super(issue)

        if (issue.result?.duplicate_lead_pk) {
            this.actionsComponentTitle = 'Duplicate lead'
        } else {
            this.actionsComponent = null
        }
    }
}
