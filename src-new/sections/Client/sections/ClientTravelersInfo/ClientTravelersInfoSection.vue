<template>
    <div
        class="card p-2 overflow-y-auto fancy-scroll"
        :class="{
            'p-0':selectTravelerMode
        }"
    >
        <SuspenseManual :state="suspense">
            <template #default>
                <AppTable
                    :items="travelerRecords"
                    :columns="columns"
                    zebra
                >
                    <template #body>
                        <tr
                            v-for="traveler in travelerRecords"
                            :key="traveler.id"
                            :class="{
                                '!table-tr--highlighted-primary text-primary-700': selectedTravelerPk === usePk(traveler)
                            }"
                            @click="()=>{
                                emit('update:selected-traveler', traveler, 'tr')
                            }"
                        >
                            <td>
                                {{ traveler.id }}
                            </td>
                            <td>
                                <div
                                    class="truncate"
                                    :class="{
                                        'max-w-[100px]': selectTravelerMode
                                    }"
                                >
                                    {{ traveler.first_name }}
                                </div>
                            </td>
                            <td>
                                <div
                                    class="truncate"
                                    :class="{
                                        'max-w-[100px]': selectTravelerMode
                                    }"
                                >
                                    {{ traveler.last_name }}
                                </div>
                            </td>
                            <td>
                                <div
                                    class="truncate"
                                    :class="{
                                        'max-w-[100px]': selectTravelerMode
                                    }"
                                >
                                    {{ traveler.middle_name }}
                                </div>
                            </td>
                            <td>
                                {{ $format.date(traveler.birth_date, undefined, { full:true }) }}
                            </td>
                            <td>
                                <div class="text-center">
                                    {{ traveler.gender }}
                                </div>
                            </td>
                            <td>
                                <div
                                    v-tooltip="traveler.known_traveler_number"
                                    class="truncate max-w-[100px]"
                                >
                                    {{ traveler.known_traveler_number }}
                                </div>
                            </td>
                            <td>
                                <div
                                    v-tooltip="traveler.global_entry_number"
                                    class="truncate max-w-[100px]"
                                >
                                    {{ traveler.global_entry_number }}
                                </div>
                            </td>
                            <td>
                                <div
                                    v-tooltip="traveler.tsa_precheck_number"
                                    class="truncate max-w-[100px]"
                                >
                                    {{ traveler.tsa_precheck_number }}
                                </div>
                            </td>
                            <td>
                                <div
                                    class="truncate"
                                    :class="{
                                        'max-w-[100px]': selectTravelerMode
                                    }"
                                >
                                    {{ ticketExtraPreferenceDescription(traveler.seat_preference_pk) }}
                                </div>
                            </td>
                            <td>
                                <div
                                    class="truncate max-w-[120px]"
                                >
                                    {{ ticketExtraPreferenceDescription(traveler.special_assistance) }}
                                </div>
                            </td>
                            <td>
                                <div
                                    class="truncate max-w-[120px]"
                                >
                                    {{ frequentFlyerInfoText(traveler.frequent_flyer) }}
                                </div>
                            </td>
                            <td>
                                <div
                                    class="truncate max-w-[150px]"
                                >
                                    {{ traveler.remark }}
                                </div>
                            </td>
                            <td>
                                <AppButton
                                    class="-my-2 --xs --primary --ghost"
                                    @click="copyTravelerInfo(traveler)"
                                >
                                    <CopyIcon />
                                </AppButton>
                            </td>
                            <td>
                                <AppButton
                                    class="-my-2 --xs --primary --ghost"
                                    @click.prevent="travelerEditModal.open({
                                        travelerPk: usePk(traveler),
                                        clientPk,
                                    })"
                                >
                                    <Edit3Icon />
                                </AppButton>
                            </td>
                            <td v-if="!selectTravelerMode">
                                <AppButton
                                    class="-my-2 --xs --danger --ghost"
                                    @click.prevent="deleteTraveler(usePk(traveler))"
                                >
                                    <TrashIcon />
                                </AppButton>
                            </td>
                            <td v-if="selectTravelerMode">
                                <AppButton
                                    class="-my-2 --xs"
                                    @click.prevent="()=>{
                                        emit('update:selected-traveler', traveler, 'button')
                                    }"
                                >
                                    Select
                                </AppButton>
                            </td>
                        </tr>
                    </template>
                </AppTable>
            </template>
            <template #fallback>
                <PlaceholderBlock class="w-full h-18" />
            </template>
        </SuspenseManual>
    </div>
</template>

<script setup lang="ts">
import AppTable from '~/components/Table/AppTable.vue'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import ClientTravelerInfoFormModal from '~/sections/Client/modals/ClientTravelerInfoFormModal.vue'
import type { ModelAttributes } from '~types/lib/Model'
import { $confirmDelete } from '@/plugins/ConfirmPlugin'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import { copyToClipboard } from '@/lib/core/helper/ClipboardHelper'
import { getSACode } from '~/lib/Helper/PersonCodesHelper'

defineOptions({
    name: 'ClientTravelersInfoSection',
})

const props = withDefaults(defineProps<{
    clientPk: PrimaryKey,
    selectTravelerMode?: boolean
    selectedTravelerPk?: PrimaryKey | undefined
}>(), {
    selectTravelerMode: false,
    selectedTravelerPk: undefined,
})

const emit = defineEmits<{
    'update:selected-traveler': [traveler: ModelAttributes<'Traveler'>, source: 'tr' | 'button']
}>()

type FrequentFlyerType = {
    airline_pk: PrimaryKey,
    frequent_flyer_number: string
}

const { useModel } = await useNewContext('Client', props.clientPk)

const travelerList = useModel('Traveler').useResourceList({
    name: 'ClientTravelerList',
    pk: props.clientPk,
})

//

const suspense = useSuspensableComponent(async () => {
    await travelerList.fetch()
})

const travelerRecords = computed(() => travelerList.records)

const columns = useTableColumns({
    id: {
        width: 9,
        label: 'ID',
    },
    first_name: {
        width: 10,
        label: 'First name',
    },
    last_name: {
        width: 10,
        label: 'Last name',
    },
    middle_name: {
        width: 10,
        label: 'Middle name',
    },
    birth_date: {
        width: 6,
        label: 'Birth date',
    },
    gender: {
        label: 'Sex',
        width: 2,
    },
    known_traveler: {
        label: 'Known traveler',
        width: 8,
    },
    global_entry_number: {
        label: 'Global Entry',
        width: 8,
    },
    tsa_precheck_number: {
        label: 'TSA Precheck',
        width: 8,
    },
    seat_preference_pk: {
        label: 'Seating',
        width: 8,
    },
    special_assistance: {
        label: 'Special assistance',
        width: 8,
    },
    frequent_flyer: {
        label: 'Frequent flyer',
        width: 27,
    },
    remark: {
        label: 'Remark',
    },
    copy: {
        width: 'min',
    },
    edit: {
        width: 'min',
    },
    delete: {
        width: 'min',
        enabled: () => !props.selectTravelerMode,
    },
    select_traveler: {
        width: 8,
        enabled: props.selectTravelerMode,
    },
})

const travelerEditModal = useModal(ClientTravelerInfoFormModal)

// frequent flyer table info

const airlineDictionary = useGeneralDictionary('Airline')

function frequentFlyerInfoText(frequentFlyer: FrequentFlyerType[]) {
    const frequentFlyersToShow = frequentFlyer.slice(0, 2).map(frequentFlyer => {
        const airline = airlineDictionary.find(frequentFlyer.airline_pk)?.name

        return `${airline} - ${frequentFlyer.frequent_flyer_number}`
    }).join(', ')

    if (frequentFlyer.length <= 2) {
        return frequentFlyersToShow
    }

    return `${frequentFlyersToShow}, ${frequentFlyer.length - 2} more...`
}

// special assistance

const optionsAllList = useModel('TicketExtraPreference').useResourceList()

await (async () => {
    await optionsAllList.fetch()
})()

function ticketExtraPreferenceDescription(assistancePk: PrimaryKey) {
    return (optionsAllList.records.find((record) => usePk(record) === assistancePk))?.name
}

// delete

async function deleteTraveler(travelerPk: PrimaryKey) {
    await $confirmDelete({ text: 'Are you sure you want to delete this traveler info?' })

    await useModel('Traveler').actions.deleteTraveler({
        traveler_pk: travelerPk,
    })

    toastSuccess('Traveler info deleted')
}
//

const copyTravelerInfo = (traveler: ModelAttributes<'Traveler'>) => {
    const travelerInfo = {
        sex: traveler.gender,
        birthday_at: traveler.birth_date,
        ...traveler,
    }

    return  copyToClipboard(getSACode(travelerInfo))
}
</script>
