<template>
    <div class="card p-2 overflow-y-auto fancy-scroll">
        <SuspenseManual :state="suspense" :loading="suspense.silentLoading">
            <template #default>
                <div class="overflow-x-auto fancy-scroll-x">
                    <AppTable
                        :columns="columns"
                        :items="leadList.records"
                        zebra
                    >
                        <template #body>
                            <tr
                                v-for="lead of leadList.records"
                                :key="lead.id"
                                @click="selectLead(usePk(lead))"
                            >
                                <TableCellLink
                                    :to="linkToLead(lead.pk)"
                                    target="_blank"
                                    class="border-l-2"
                                    :class="selectedLeadPk == usePk(lead) ? 'border-danger dark:border-danger' : 'border-transparent'"
                                >
                                    {{ lead.pk }}
                                </TableCellLink>
                                <td>{{ lead.from_iata_code }}</td>
                                <td>{{ lead.to_iata_code }}</td>
                                <td>{{ formatter.date(lead.departure_date, 'UTC', { full: true }) }}</td>
                                <td>
                                    {{ lead.return_date ? formatter.date(lead.return_date, 'UTC', { full: true }) :
                                        dateFormatItinerary('', lead.itinerary_type) }}
                                </td>
                                <td class="text-center">
                                    {{ lead.listInformation.count < 0 ? '-' : lead.listInformation.count }}
                                </td>
                                <td>{{ externalResourceDictionary.tryFind(lead.external_resource)?.title }}</td>
                                <td>{{ formatter.date(lead.created_at, 'UTC', { full: true }) }}</td>
                                <td @click="assignAgent(lead)">
                                    <span v-if="lead.executor_pk">
                                        {{ getFullNameByAgentPk(lead.executor_pk) }}
                                    </span>
                                    <span v-else class="text-danger">
                                        Not assigned
                                    </span>
                                </td>
                                <td>
                                    <HiddenDataComponent :hidden-value="lead.phone?.value">
                                        <template #default>
                                            <div class="flex items-center">
                                                {{ lead.phone?.value }}
                                            </div>
                                        </template>
                                        <template #hidden="{formatted}">
                                            <div class="flex items-center">
                                                {{ formatted }}
                                            </div>
                                        </template>
                                    </HiddenDataComponent>
                                </td>
                                <td>
                                    <HiddenDataComponent :hidden-value="lead.email?.value">
                                        <template #default>
                                            <div class="flex items-center">
                                                {{ lead.email?.value }}
                                            </div>
                                        </template>
                                        <template #hidden="{formatted}">
                                            <div class="flex items-center">
                                                {{ formatted }}
                                            </div>
                                        </template>
                                    </HiddenDataComponent>
                                </td>
                                <td
                                    :class="{
                                        'text-danger': lead.is_deleted,
                                        'text-green-500': !lead.is_deleted
                                    }"
                                >
                                    {{ lead.is_deleted ? 'Deleted' : 'Active' }}
                                </td>
                                <td v-if="canReadUTM">
                                    <InfoIcon
                                        v-tooltip="{content: lead.utm.utm_ga}"
                                        class="w-4 h-4 mx-auto"
                                    />
                                </td>
                                <td class="text-center">
                                    <div class="inline-block">
                                        <LeadStatusBadge :status-pk="lead.status_pk" small />
                                    </div>
                                </td>
                            </tr>
                        </template>
                    </AppTable>
                </div>
            </template>
            <template #fallback>
                <PlaceholderBlock class="w-full h-[300px]" />
            </template>
        </SuspenseManual>
        <div v-if="leadList.pagination.value?.page.count > 1" class="flex items-center gap-2 justify-end mt-3">
            <AppPaginationInfo :pagination="leadList.pagination" />
            <AppPaginationCompact :pagination="leadList.pagination" />
        </div>
    </div>
</template>

<script setup lang="ts">
import AppTable from '~/components/Table/AppTable.vue'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import { getFullName } from '~/lib/Helper/PersonHelper'
import LeadStatusBadge from '~/components/Page/Lead/LeadStatusBadge.vue'
import { toastError } from '@/lib/core/helper/ToastHelper'
import type { ModelAttributes } from '~types/lib/Model'
import type { AssignmentResult } from '~/modals/AssignAgentModal.vue'
import AssignAgentModal from '~/modals/AssignAgentModal.vue'
import { AssignLogColumn } from '~/api/models/AssignLog/AssignLog'
import HiddenDataComponent from '~/sections/Lead/List/components/HiddenDataComponent.vue'
import { dateFormatItinerary } from '@/lib/core/helper/LeadHelper'
import { useModel as useGlobalModel } from '~/composables/useModel'
import { composeResourcePk } from '~/composables/usePk'

const props = defineProps<{
    clientPk: PrimaryKey,
    selectedLeadPk?: PrimaryKey,
}>()

const emit = defineEmits<{
    'selectLead': [leadPk: PrimaryKey],
}>()

const { useModel, useDictionary, hasPermission, currentUserPk } = useContext()

const formatter = useService('formatter')

const externalResourceDictionary = useGeneralDictionary('ExternalResource')

const assignAgentModal = useModal(AssignAgentModal)

const agentDictionary = useDictionary('Agent')

const leadModel = useModel('Lead')

const leadList = leadModel.useResourceList({
    name: 'ClientLeadList',
    pk: composeResourcePk('ClientLeadList', {
        client_pk: props.clientPk,
        auth_pk: currentUserPk,
    }),
}, {
    with: ['listInformation', 'phone', 'email', 'utm', 'status'],
    pageSize: 19,
})

const suspense = useSuspensableComponent(async () => {
    await leadList.fetch()
})

const canReadUTM = computed(() => {
    return hasPermission('readUTM', 'all')
})

const columns = useTableColumns({
    pk: {
        label: '#',
        width: 7,
    },
    from_iata_code: {
        label: 'From',
        width: 5,
    },
    to_iata_code: {
        label: 'To',
        width: 5,
    },
    departure_date: {
        label: 'Departure',
        width: 5,
    },
    return_date: {
        label: 'Return',
        width: 5,
    },
    pq_count: {
        label: 'PQs',
        tooltip: 'Total PQ\'s count',
        center: true,
        width: 3,
    },
    external_resource: {
        label: 'Created by',
        width: 'min',
    },
    created_at: {
        label: 'Created at',
        width: 5,
    },
    agent: {
        label: 'Agent',
        width: 10,
    },
    phone: {
        label: 'Phone',
        width: 10,
    },
    email: {
        label: 'Email',
        width: 15,
    },
    state: {
        label: 'State',
        width: 'min',
    },
    utm: {
        label: 'UTM',
        width: 'min',
        center: true,
        enabled: canReadUTM,
    },
    status: {
        center: true,
        label: 'Status',
        width: 'min',
    },
})

watch(() => props.clientPk, () => {
    suspense.fetch()
})

const getFullNameByAgentPk = (pk: PrimaryKey) => {
    if (!pk) {
        return ''
    }

    return getFullName(agentDictionary.findOrFail(pk))
}

function canAssignAgentToLead(lead: ModelAttributes<'Lead'>) {
    return hasPermission('setAppointment', 'Lead', lead)
}

function canViewAppointmentLogs(lead: ModelAttributes<'Lead'>) {
    return hasPermission('viewAppointmentLogs', 'Lead', lead)
}

function canUnAssignAgentFromLead(lead: ModelAttributes<'Lead'>) {
    return hasPermission('unAssignAppointment', 'Lead', lead)
}

const assignAgent = (lead: ModelAttributes<'Lead'>) => {
    if (!canAssignAgentToLead(lead) && !canViewAppointmentLogs(lead)) {
        toastError('You can\'t change curator or see logs')

        return
    }

    assignAgentModal.open({
        model: {
            name: 'Lead',
            pk: usePk(lead),
        },
        column: AssignLogColumn.Executor,
        defaultAgentPk: lead.executor_pk ? lead.executor_pk : undefined,
        canUnAssign: canUnAssignAgentFromLead(lead),
        canAssign: canAssignAgentToLead(lead),
        onAssign: (params: AssignmentResult) => setExecutor(lead, params.agent_pk),
    }, {
        workspace: getWorkspaceFromObject(lead),
    })
}

const setExecutor = async (lead: ModelAttributes<'Lead'>, agentPk: PrimaryKey | null) => {
    return await useGlobalModel('Lead', {
        http: {
            workspace: getWorkspaceFromObject(lead),
        },
    }).actions.assignExecutor({
        lead_pk: usePk(lead),
        executor_pk: agentPk,
        lead_queue: null,
    })
}

const selectLead = (leadPk: PrimaryKey) => {
    emit('selectLead', leadPk !== props.selectedLeadPk ? leadPk : undefined)
}
</script>
