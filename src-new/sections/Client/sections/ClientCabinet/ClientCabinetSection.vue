<template>
    <div class="card p-2">
        <div v-if="!client.client_cabinet_id">
            <div>Client cabinet profile is not linked</div>

            <div v-if="hasPermission('manage', 'all')" class="flex gap-2 flex-auto">
                <InputText v-model="idCabinetProfileToLink" placeholder="Email" />
                <AppButton
                    class="--primary"
                    :disabled="!idCabinetProfileToLink"
                    @click="linkCabinetProfile"
                >
                    Link cabinet profile
                </AppButton>
            </div>
        </div>
        <div
            v-else
            :key="client.client_cabinet_id"
            class="flex gap-4"
        >
            <div class="w-[310px]">
                <ClientCabinetInfoSection
                    :client-pk="clientPk"
                    :client-cabinet-id="client.client_cabinet_id"
                    @balance="changeBalance"
                />
            </div>

            <div class="flex-grow">
                <Tabs
                    v-model="activeTab"
                    :tabs="clientCabinetTabs"
                    class="border-b-4 px-4 pt-2 dark:border-dark-6"
                />
                <ClientCabinetTransactionsSection
                    v-if="activeTab === 'transactions'"
                    :client-pk="clientPk"
                    :client-cabinet-id="client.client_cabinet_id"
                    :balance="balance"
                />
                <ClientCabinetLogsSection
                    v-if="activeTab === 'logs'"
                    :client-pk="clientPk"
                    :client-cabinet-id="client.client_cabinet_id"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import { $confirm } from '@/plugins/ConfirmPlugin'
import ClientCabinetTransactionsSection
    from '~/sections/Client/sections/ClientCabinet/ClientCabinetTransactionsSection.vue'
import ClientCabinetLogsSection from '~/sections/Client/sections/ClientCabinet/ClientCabinetLogsSection.vue'
import Tabs from '@/components/tabs/Tabs.vue'
import ClientCabinetInfoSection from '~/sections/Client/sections/ClientCabinet/ClientCabinetInfoSection.vue'

defineOptions({
    name: 'ClientCabinetSection',
})
const props = defineProps<{
    clientPk: PrimaryKey,
}>()

const { useModel, hasPermission, record: client } = await useNewContext('Client', props.clientPk)

const clientCabinetTabs = [
    {
        id: 'transactions',
        title: 'Transactions',
    },
    {
        id: 'logs',
        title: 'Logs',
    },
]
const activeTab = ref('transactions')

const idCabinetProfileToLink = ref<string>()

async function linkCabinetProfile() {
    if (!idCabinetProfileToLink.value) {
        return
    }
    await $confirm('Are you sure you want to link account?')

    await useModel('Client').actions.createClientLink({
        pk: props.clientPk,
        email: idCabinetProfileToLink.value,
    })

    toastSuccess(`Client account was linked`)
    idCabinetProfileToLink.value = undefined
}

const balance = ref<{active: number, hold: number}>({ active: 0, hold: 0 })
function changeBalance(evt) {
    balance.value = evt
}
</script>

