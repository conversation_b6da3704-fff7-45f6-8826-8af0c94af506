<template>
    <SuspenseManual :state="suspense">
        <div v-if="clientInfo" class="flex flex-col gap-4">
            <div class="card p-4 shadow-none rounded-[6px] border border-secondary-50 dark:border-gray-700">
                <div class="text-secondary text-lg mb-2 dark:text-secondary-300 flex items-center gap-4">
                    Client

                    <div v-if="clientInfo.deleted_at" class="badge --danger">
                        Deleted: {{ $format.datetime(clientInfo.deleted_at) }}
                    </div>
                </div>
                <div>First name: {{ clientInfo.person.first_name }}</div>
                <div>Last name: {{ clientInfo.person.last_name }}</div>
            </div>
            <div class="card p-4 shadow-none rounded-[6px] border border-secondary-50 dark:border-gray-700">
                <div class="text-secondary text-lg mb-2 dark:text-secondary-300 flex items-center gap-4">
                    Contacts
                </div>
                <div>Email: {{ clientInfo.contact.email }}</div>
                <div>Phone: {{ clientInfo.contact?.phone }}</div>
            </div>

            <div
                class="card p-4 shadow-none rounded-[6px] border border-secondary-50 dark:border-gray-700 flex flex-col gap-2"
            >
                <div class="flex gap-1 items-center justify-between">
                    <div class="text-secondary text-lg mb-2 dark:text-secondary-300 flex items-center gap-4">
                        Balance
                    </div>
                    <div>
                        <div>
                            Active: {{
                                $format.money(clientInfo.referral.active, {withCurrency: 'USD'})
                            }}
                        </div>
                        <div>
                            On hold: {{
                                $format.money(clientInfo.referral.hold, {withCurrency: 'USD'})
                            }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-2 gap-1">
                <AppButton
                    v-if="hasPermission('manage', 'all')"
                    @click="loginAsClient"
                >
                    Login as this client<LogInIcon />
                </AppButton>
            </div>

            <div class="grid grid-cols-2 gap-1">
                <AppButton
                    v-if="hasPermission('manage', 'all')"
                    @click="linkClient"
                >
                    Link to another client
                </AppButton>

                <AppButton
                    v-if="hasPermission('manage', 'all') && clientInfo.deleted_at"
                    class="--danger"
                    @click="restoreAccount"
                >
                    Restore account
                </AppButton>
            </div>
        </div>
        <template #fallback>
            <PlaceholderBlock class="w-full h-[400px]" />
        </template>
    </SuspenseManual>
</template>

<script setup lang="ts">
import type { ClientInfoResponse } from '~/api/models/Client/Client'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import type { z } from 'zod'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import { getFullName } from '~/lib/Helper/PersonHelper'
import { goToClient } from '@/lib/core/helper/RouteNavigationHelper'
import { $confirm } from '@/plugins/ConfirmPlugin'
import ClientSelectModal from '~/modals/ClientSelectModal.vue'
import type { ModelAttributes } from '~types/lib/Model'

defineOptions({
    name: 'ClientCabinetInfoSection',
})

const props = defineProps<{
    clientPk: PrimaryKey,
    clientCabinetId: number
}>()

const emit = defineEmits<{
    'balance': [{ active: number, hold: number }]
}>()

const { useModel, hasPermission } = await useNewContext('Client', props.clientPk)

const clientInfo = ref<z.infer<typeof ClientInfoResponse>>()

const suspense = useSuspensableComponent(async () => {
    await fetchExternalClientInfo()
})

const fetchExternalClientInfo = async () => {
    const response: z.infer<typeof ClientInfoResponse> = await useModel('Client').actions.getCabinetInfo({
        pk: props.clientPk,
    })
    clientInfo.value = response
    emit('balance', {
        active: response.referral.active, hold: response.referral.hold,
    })
}
const fetchDebounced = useDebounceFn(fetchExternalClientInfo, 500)

const clientSelectModal = useModal(ClientSelectModal)

async function loginAsClient() {
    const { url } = await useModel('Client').actions.loginAsClient({
        client_pk: props.clientPk,
    })

    window.open(url, '_blank')
}

async function linkClient() {
    const to_link_client: ModelAttributes<'Client'> = await clientSelectModal.open({
        applyCondition: (and) => {
            and.ne('id', props.clientPk)
            and.eq('client_cabinet_id', null)
        },
    })

    if (to_link_client) {
        await useModel('Client').actions.moveClientLink({
            pk: usePk(to_link_client),
            client_cabinet_id: props.clientCabinetId,
        })

        toastSuccess(`Profile was linked to ${getFullName(to_link_client)}. Redirecting...`)
        await goToClient(usePk(to_link_client))
    }
}

async function restoreAccount() {
    await $confirm('Are you sure you want to restore deleted account?')

    await useModel('Client').actions.restoreAccount({
        pk: props.clientPk,
    })

    toastSuccess(`Client account was restored`)
}

useService('event').on('refreshClientCabinet', fetchDebounced)

onUnmounted(() => {
    useService('event').off('refreshClientCabinet', fetchDebounced)
})
</script>

