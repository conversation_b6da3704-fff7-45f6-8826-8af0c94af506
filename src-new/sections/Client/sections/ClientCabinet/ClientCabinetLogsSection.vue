<template>
    <SuspenseManual :state="suspense">
        <div
            class="card flex flex-col gap-2 max-h-full shadow-none rounded-[6px] border border-secondary-50 dark:border-gray-700"
        >
            <div class="flex-grow relative min-h-[440px] h-flex overflow-x-hidden overflow-y-auto fancy-scroll">
                <AppTable
                    zebra
                    :items="logRecords"
                    :columns="columns"
                    class="--fixed-header"
                >
                    <template #body>
                        <template v-for="log in logRecords" :key="log.id">
                            <tr>
                                <td>{{ $format.datetime(log.created_at, undefined, {format: 'MMM d, yyyy, HH:mm'}) }}</td>
                                <td class="capitalize">
                                    {{ log.action }} {{ log.model_name }} #{{ log.model_id }}
                                </td>
                                <td>
                                    <div class="whitespace-normal">
                                        {{ log.message }}
                                    </div>
                                </td>
                                <td>
                                    <AgentInfo v-if="log.agent_pk" :pk="log.agent_pk" />
                                </td>
                            </tr>
                        </template>
                    </template>
                </AppTable>
            </div>
        </div>
        <template #fallback>
            <PlaceholderBlock class="w-full h-[400px]" />
        </template>
    </SuspenseManual>
</template>

<script setup lang="ts">
import type { ClientInfoLog } from '~/api/models/Client/Client'
import AgentInfo from '~/sections/Agent/components/AgentInfo.vue'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import type { z } from 'zod'

defineOptions({
    name: 'ClientCabinetLogsSection',
})

const props = defineProps<{
    clientPk: PrimaryKey,
}>()

const { useModel } = await useNewContext('Client', props.clientPk)

const suspense = useSuspensableComponent(async () => {
    await fetchClientCabinetLogs()
})
const logRecords = ref<z.infer<typeof ClientInfoLog>[]>([])

const fetchClientCabinetLogs = async () => {
    const { logs } = await useModel('Client').actions.getCabinetLogs({
        pk: props.clientPk,
    })
    logRecords.value = logs
}

const columns = useTableColumns({
    date: {
        label: 'Date',
        width: '120px',
    },
    model: {
        label: 'Model',
        width: '120px',
    },
    message: {
        label: 'Message',
        width: '120px',
    },
    agent: {
        label: 'Agent',
        width: '120px',
    },
})
</script>
