<template>
    <div class="h-full whitespace-normal leading-5">
        <div class="flex flex-col h-full max-h-full">
            <Tabs
                v-model="currentTab"
                class="bg-white dark:bg-dark-3 dark:border-dark-6 px-3 pt-2 border-b-4 border-theme-39"
                :tabs="tabs"
            />

            <Dropdown
                teleport
                placement="bottom-end"
                class="absolute right-2 mt-1"
            >
                <template #toggle="{ toggle }">
                    <AppButton
                        v-if="canCreateVoucher"
                        @click="voucherModal.open()"
                    >
                        <BookIcon />
                        Create voucher
                    </AppButton>

                    <AppButton
                        v-if="canCloneSale"
                        class="ml-2"
                        @click="refundModal.open()"
                    >
                        <RefreshCcwIcon />
                        Create refund sale
                    </AppButton>
                    <button
                        type="button"
                        class="btn btn-outline-secondary btn-sm box ml-2"
                        @click="toggle"
                    >
                        <PlusIcon
                            class="w-4 h-4 text-slate-500"
                        />
                    </button>
                </template>
                <template #content="{ close }">
                    <ContextMenu
                        :options="contextMenuOptions"
                        @close="close"
                    />
                </template>
            </Dropdown>

            <div
                class="tabs-content flex items-start flex-1 h-px max-h-full bg-slate-50 border-t-4 border-theme-39 dark:bg-dark-6 dark:border-dark-6"
            >
                <div v-if="currentTab==='leads'" class="tab-content p-4 flex-grow h-full">
                    <ClientLeadSection
                        ref="leadSection"
                        :client-pk="clientPk"
                        class="h-full"
                        :selected-lead-pk="selectedLeadPk"
                        @select-lead="selectedLeadPk=$event"
                    />
                </div>
                <div v-if="currentTab==='sales'" class="tab-content p-4 flex-grow h-full">
                    <ClientSaleSection :client-pk="clientPk" class="h-full" />
                </div>
                <div v-if="currentTab==='passport'" class="tab-content p-4 flex-grow h-full">
                    <ClientPassportSection :client-pk="clientPk" class="h-full" />
                </div>
                <div v-if="currentTab==='travelerPassports'" class="tab-content p-4 flex-grow h-full">
                    <ClientTravelersSection :client-pk="clientPk" class="h-full" />
                </div>
                <div v-if="currentTab==='travelersInfo'" class="tab-content p-4 flex-grow h-full">
                    <ClientTravelersInfoSection :client-pk="clientPk" class="h-full" />
                </div>
                <div v-if="projectFeatures.client_cabinet.enabled && currentTab==='clientCabinetInfo'" class="tab-content p-4 flex-grow h-full">
                    <ClientCabinetSection :client-pk="clientPk" class="h-full" />
                </div>

                <div class="h-full w-72 p-4 pl-0">
                    <div class="h-full max-h-full overflow-y-auto fancy-scroll box p-4 space-y-5">
                        <ClientContactsSection :client-pk="clientPk" :lead-pk="selectedLeadPk" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import Tabs from '@/components/tabs/Tabs.vue'
import ClientCabinetSection from '~/sections/Client/sections/ClientCabinet/ClientCabinetSection.vue'
import ClientTravelersSection from '~/sections/Client/sections/ClientTravelers/ClientTravelersSection.vue'
import ClientPassportSection from '~/sections/Client/sections/ClientPassport/ClientPassportSection.vue'
import ClientTravelersInfoSection from '~/sections/Client/sections/ClientTravelersInfo/ClientTravelersInfoSection.vue'
import ChangeSaleTypeModal from '~/sections/Sale/modals/ChangeSaleTypeModal.vue'
import { SaleType } from '~/api/models/Sale/Sale'
import ClientTravelerInfoFormModal from '~/sections/Client/modals/ClientTravelerInfoFormModal.vue'
import ClientPassportModal from '~/modals/ClientPassportModal.vue'
import ClientSaleSection from '~/sections/Client/sections/ClientSales/ClientSaleSection.vue'
import ClientLeadSection from '~/sections/Client/sections/ClientLeads/ClientLeadSection.vue'
import LeadCreateModal from '~/sections/Lead/modals/Create/LeadCreateModal.vue'
import ClientContactsSection from '~/sections/Client/sections/ClientContactsSection.vue'
import ContextMenu from '@/components/ContextMenu/ContextMenu.vue'
import VoucherClientCreateModal from '~/sections/Voucher/modals/VoucherClientCreateModal.vue'
import ExpectedAmountEditModal from '~/sections/ExpectedAmount/modals/ExpectedAmountEditModal.vue'

defineOptions({
    name: 'ClientInfoSection',
})

const props = defineProps<{
    clientPk: PrimaryKey,
}>()

const { useModel, hasPermission, useDictionary } = useContext()

const clientModel = useModel('Client')

const client = clientModel.useRecord()

await client.fetch(props.clientPk)

const projectFeatures = useDictionary('Project').findOrFail(client.record.value.project_pk).features

const leadSection = ref<ClientLeadSection>()

const canManageAll = computed(() => hasPermission('manage', 'all'))
const canCloneSale = computed(() => hasPermission('clone', 'Sale'))
const canCreateVoucher = computed(() => {
    return  hasPermission('create', 'Voucher')
})

const refundModal = useModal(ChangeSaleTypeModal, {
    type: SaleType.Refund,
    clientPk: props.clientPk,
    mode: 'clone',
})

const voucherModal = useModal(VoucherClientCreateModal, {
    clientPk: props.clientPk,
})
const expectedAmountModal = useModal(ExpectedAmountEditModal)

const selectedLeadPk = ref<PrimaryKey|undefined>()

const updateTravelerModal = useModal(ClientTravelerInfoFormModal, {
    clientPk: props.clientPk,
})

const clientPassportModal = useModal(ClientPassportModal, {
    clientPk: props.clientPk,
}, {
    promise: true,
    workspace: getWorkspaceFromObject(client.record.value),
})

const leadCreateModal = useModal(LeadCreateModal, {
    populate: {
        clientPk: props.clientPk,
    },
    hideClientRelatedFields: true,
})

const canWorkWithLeads = hasPermission('openPageLead', 'all') || hasPermission('openPageLeadManagement', 'all')

const tabs = computed(() => {
    return [
        canWorkWithLeads ? {
            id: 'leads',
            title: 'Leads',
        } : undefined,
        {
            id: 'sales',
            title: 'Sales',
        },
        {
            id: 'passport',
            title: 'Passport',
        },
        {
            id: 'travelerPassports',
            title: 'Traveler Passports',
        },
        {
            id: 'travelersInfo',
            title: 'Travelers Info',
        },
        canManageAll.value ? {
            id: 'clientCabinetInfo',
            title: 'Cabinet Info',
        } : undefined,
    ].filter(Boolean)
})

const contextMenuOptions = computed(() => {
    return [
        canWorkWithLeads ? {
            text: 'Add lead',
            onClick: async () => await addLead(),
        } : undefined,
        {
            text: 'Add passport',
            onClick: () => createClientPassport('passport'),
        },
        {
            text: 'Add traveler passport',
            onClick: () => createClientPassport('travelerPassports'),
        },
        {
            text: 'Add traveler info',
            onClick: () => updateTravelerModal.open(),
        },
    ].filter(Boolean)
})

//

const currentTab = ref(getDefaultTab())

function getDefaultTab() {
    const hash = window.location.hash
    const match = hash.match(/\btab-([\w-]+)/)

    const tab = match && match[1]

    if (tab && tabs.value.some(t => t.id === tab)) {
        return tab
    }

    return tabs.value[0].id
}

const changeTab = (tabId: string) => {
    currentTab.value = tabId
}

const createClientPassport = (activeTabName: 'passport' | 'travelerPassports') => {
    changeTab(activeTabName)

    clientPassportModal.open()
}

const addLead = async () => {
    await leadCreateModal.open()
}

watch(() => currentTab.value, () => {
    window.location.hash = '#tab-' + currentTab.value
})
</script>
