<template>
    <AppButton
        v-tooltip="{ content: buttonDisabled ? 'No client data available' : 'Client info'}"
        class="--only"
        :class="{
            '--outline --muted': isDark,
            'relative overflow-visible --warning': showAnimation
        }"
        :disabled="buttonDisabled"
        @click="openClientEnrichedInfoModal"
    >
        <div
            v-if="showAnimation && highlightVisibility"
            class="absolute inline-flex w-full h-full rounded-[4px] bg-orange-300 animate-ping-small z-1"
            style="animation-duration: 1.5s;"
        />
        <InfoIcon class="dark:text-white absolute z-[2]" />
    </AppButton>
</template>

<script setup lang="ts">
import ClientEnrichedInfoModal from '~/sections/Client/modals/ClientEnrichedInfoModal.vue'
import { DepartmentName } from '~/api/models/Department/Department'

defineOptions({
    name: 'ClientEnrichedButton',
})

const props = withDefaults(defineProps<{
    clientPk: PrimaryKey,
    highlightVisibility?: boolean,
}>(), {
    highlightVisibility: false,
})

const { useModel, record: client, currentUserPk, useCurrentUser } = await useNewContext('ClientAdditionalInfo', props.clientPk)
const { isDark } = useDarkMode()
const enrichmentModel = useModel('ClientAdditionalEnrichmentInfo').useRecord()

const suspense = useSuspensableComponent(async () => {
    if (props.highlightVisibility && client.has_enriched_info) {
        const enrichmentPk = composeResourcePk('ClientAdditionalEnrichmentInfo', {
            auth_pk: currentUserPk,
            pk: props.clientPk,
        })
        await enrichmentModel.fetch(enrichmentPk)
    }
})

const enriched = computed(() => {
    return enrichmentModel.record.value
})

const currentUser = useCurrentUser()

const isUserInSalesDepartment = computed(() => {
    return isUserInDepartment(currentUser, DepartmentName.Sales)
})

const showAnimation = computed(() => {
    if (!enriched.value) {
        return false
    }

    return !enriched.value.has_seen && props.highlightVisibility && client.has_enriched_info && isUserInSalesDepartment.value
})

const buttonDisabled = computed(() => {
    return !client.has_enriched_info
})

const clientEnrichedInfoModal = useModal(ClientEnrichedInfoModal)

function openClientEnrichedInfoModal() {
    if (props.highlightVisibility && !enriched.value) {
        return
    }

    clientEnrichedInfoModal.open({
        clientPk: props.clientPk,
    })
}
</script>

