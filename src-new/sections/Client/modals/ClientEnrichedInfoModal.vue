<template>
    <AppModalWrapper :header="header" class="!max-w-[900px]">
        <div class="card-body card__body--partholder p-2">
            <div class="card p-4 flex flex-col gap-3">
                <div class="font-semibold text-xs">
                    Contacts
                </div>
                <div class="grid grid-cols-2 gap-2">
                    <div v-if="phone" class="border rounded py-2 px-2.5 relative dark:border-gray-700">
                        <div
                            class="badge --xs absolute -top-2 right-4 --soft --outline"
                            :class="{
                                '--success': phone.phone_valid && phone.is_processed,
                                '--danger': !phone.phone_valid && phone.is_processed,
                                '--warning': !phone.is_processed,
                                '!bg-gray-800': isDark,
                                '!bg-white': !isDark
                            }"
                        >
                            {{ !phone.is_processed ? 'Not processed' : phone.phone_valid ? 'Valid' : 'Not valid' }}
                        </div>
                        <div class="grid grid-cols-2 gap-2 divided-list">
                            <div>
                                <div class="text-2xs text-gray-500">
                                    Phone
                                </div>
                                <div class="text-xs">
                                    {{ phone.phone }}
                                </div>
                            </div>
                            <div v-if="phone.phone_location">
                                <div class="text-2xs text-gray-500">
                                    Location
                                </div>
                                <div class="text-xs">
                                    {{ phone.phone_location }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if="email" class="border rounded py-2 px-2.5 relative dark:border-gray-700">
                        <div class="absolute -top-2 right-4 flex gap-2">
                            <div
                                class="badge --xs --soft --outline"
                                :class="{
                                    '--success': email.email_valid && email.is_processed,
                                    '--danger': !email.email_valid && email.is_processed,
                                    '--warning': !email.is_processed,
                                    '!bg-gray-800': isDark,
                                    '!bg-white': !isDark
                                }"
                            >
                                {{ !email.is_processed ? 'Not processed' : email.email_valid ? 'Valid' : 'Not valid' }}
                            </div>
                            <div
                                v-if="email.email_business"
                                class="badge --xs --soft --outline --success"
                                :class="{
                                    '!bg-gray-800': isDark,
                                    '!bg-white': !isDark
                                }"
                            >
                                Business
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-2 divided-list">
                            <div>
                                <div class="text-2xs text-gray-500">
                                    Email
                                </div>
                                <div class="text-xs">
                                    {{ email.email }}
                                </div>
                            </div>
                            <div v-if="email.email_provider">
                                <div class="text-2xs text-gray-500">
                                    Provider
                                </div>
                                <div class="text-xs">
                                    {{ email.email_provider }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Business Information Section -->
            <div v-if="business" class="card p-4 flex flex-col gap-3">
                <div class="font-semibold text-xs">
                    Business Information
                </div>
                <div v-if="business.business_title" class="flex gap-4 items-center">
                    <div class="flex-grow grid grid-cols-2 gap-4 divided-list">
                        <div class="">
                            <div class="text-2xs text-gray-500">
                                Title
                            </div>
                            <div class="text-xs">
                                {{ business.business_title }}
                            </div>
                        </div>
                        <div v-if="business.business_name">
                            <div class="text-2xs text-gray-500">
                                Name
                            </div>
                            <div class="text-xs">
                                {{ business.business_name }}
                            </div>
                        </div>
                        <div v-if="business.business_headline">
                            <div class="text-2xs text-gray-500">
                                Headline
                            </div>
                            <div class="text-xs">
                                {{ business.business_headline }}
                            </div>
                        </div>
                        <div v-if="business.business_location">
                            <div class="text-2xs text-gray-500">
                                Location
                            </div>
                            <div class="text-xs">
                                {{ business.business_location }}
                            </div>
                        </div>
                        <div v-if="business.business_linkedin_url">
                            <div class="text-2xs text-gray-500">
                                Linkedin
                            </div>
                            <div class="text-xs">
                                <a
                                    :href="business.business_linkedin_url"
                                    target="_blank"
                                    class="text-blue-600 hover:underline text-2xs flex items-center gap-1"
                                >
                                    <ExternalLinkIcon class="w-3 h-3" />
                                    View Profile
                                </a>
                            </div>
                        </div>
                        <div v-if="business.business_tags?.length">
                            <div class="text-2xs text-gray-500">
                                Tags
                            </div>
                            <div class="flex flex-wrap gap-1 items-center">
                                <div
                                    v-for="tag in business.business_tags"
                                    :key="tag"
                                    class="badge --xs --secondary --outline"
                                >
                                    {{ getTagTitle(tag) }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-[150px] flex items-center justify-center">
                        <div
                            v-if="business?.business_photo_url"
                            class="aspect-square relative overflow-hidden rounded w-[100px]"
                        >
                            <img
                                :src="business.business_photo_url"
                                alt="Business Photo"
                                class="w-full h-full object-cover"
                                loading="lazy"
                            >
                        </div>
                    </div>
                </div>
            </div>

            <div v-if="google || whatsapp" class="card p-4 flex flex-col gap-3">
                <div class="font-semibold text-xs">
                    Social
                </div>

                <div class="flex items-start gap-2">
                    <div v-if="google" class="border rounded py-2 px-2.5 flex item-center gap-2 dark:border-gray-700">
                        <div class="flex flex-col gap-0.5 w-[100px]">
                            <div>
                                <div class="text-2xs text-gray-500">
                                    Source
                                </div>
                                <div class="text-xs">
                                    Google
                                </div>
                            </div>

                            <div v-if="google?.google_contributor_url">
                                <div class="text-2xs text-gray-500">
                                    Contributor
                                </div>
                                <div class="text-xs">
                                    <a
                                        :href="google.google_contributor_url"
                                        target="_blank"
                                        class="text-blue-600 hover:underline text-2xs flex items-center gap-1"
                                    >
                                        <ExternalLinkIcon class="w-3 h-3" />
                                        View Profile
                                    </a>
                                </div>
                            </div>

                            <div v-if="google?.google_apps" class="">
                                <div class="text-2xs text-gray-500">
                                    Apps
                                </div>
                                <div class="flex flex-wrap gap-1 items-center">
                                    <div
                                        v-for="tag in google.google_apps"
                                        :key="tag"
                                        class="badge --xs --secondary --outline"
                                    >
                                        {{ getTagTitle(tag) }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div v-if="google?.google_profile_photo_url" class="w-[100px]">
                            <div class="text-2xs text-gray-500">
                                Profile
                            </div>
                            <div class="aspect-square relative overflow-hidden rounded">
                                <img
                                    :src="google.google_profile_photo_url"
                                    alt="Profile Photo"
                                    class="w-full h-full object-cover block"
                                    loading="lazy"
                                >
                            </div>
                        </div>
                        <div v-if="google?.google_cover_photo_url" class="w-[100px]">
                            <div class="text-2xs text-gray-500">
                                Cover
                            </div>
                            <div class="aspect-square relative overflow-hidden rounded">
                                <img
                                    :src="google.google_cover_photo_url"
                                    alt="Cover Photo"
                                    class="w-full h-full object-cover block"
                                    loading="lazy"
                                >
                            </div>
                        </div>
                    </div>

                    <div
                        v-if="whatsapp?.whatsapp_profile_photo_url"
                        class="col-span-4 border rounded py-2 px-2.5 flex item-center gap-2 dark:border-gray-700"
                    >
                        <div class="flex flex-col gap-0.5 w-[100px]">
                            <div>
                                <div class="text-2xs text-gray-500">
                                    Source
                                </div>
                                <div class="text-xs">
                                    Whatsapp
                                </div>
                            </div>
                        </div>
                        <div v-if="whatsapp?.whatsapp_profile_photo_url" class="w-[100px]">
                            <div class="text-2xs text-gray-500">
                                Profile
                            </div>
                            <div class="aspect-square relative overflow-hidden rounded">
                                <img
                                    :src="whatsapp.whatsapp_profile_photo_url"
                                    alt="Profile Photo"
                                    class="w-full h-full object-cover block"
                                    loading="lazy"
                                >
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import { getFullName } from '~/lib/Helper/PersonHelper'
import { capitalize } from '~/lib/Helper/StringHelper'

defineOptions({
    name: 'ClientEnrichedInfoModal',
})

const props = defineProps<{
    clientPk: PrimaryKey,
}>()

const { useModel, record: client, currentUserPk } = await useNewContext('ClientAdditionalInfo', props.clientPk)

const enrichmentModel = useModel('ClientAdditionalEnrichmentInfo').useRecord()

const { isDark } = useDarkMode()
const header = computed(() => {
    return `Client Enrichment Information for ${getFullName(client)}`
})

const suspense = useSuspensableComponent(async () => {
    const enrichmentPk = composeResourcePk('ClientAdditionalEnrichmentInfo', {
        auth_pk: currentUserPk,
        pk: props.clientPk,
    })
    await enrichmentModel.fetch(enrichmentPk)
})

const enriched = computed(() => {
    return enrichmentModel.record.value
})

const phone = computed(() => {
    if (!enriched.value) return null

    const data = enriched.value.data.result

    if (data?.phone) {
        return {
            phone: data.phone,
            phone_location: data.phone_location,
            phone_valid: data.phone_valid,
            is_processed: data.phone_valid !== null,
        }
    }

    return null
})

const email = computed(() => {
    if (!enriched.value) return null

    const data = enriched.value.data.result

    if (data?.email) {
        return {
            email: data.email,
            email_valid: data.email_valid,
            email_business: data.email_business,
            email_provider: data.email_provider,
            is_processed: data.email_valid !== null,
        }
    }

    return null
})

function getTagTitle(tag: string) {
    const known_tags = {
        business_development: 'Business Development',
        owner: 'Owner',
    }

    return known_tags[tag] ?? capitalize(tag.replace(/_/g, ' '))
}

const business = computed(() => {
    if (!enriched.value) return null

    const data = enriched.value.data.result

    if (data?.business_title || data?.business_name) {
        return {
            business_title: data.business_title,
            business_name: data.business_name,
            business_headline: data.business_headline,
            business_location: data.business_location,
            business_linkedin_url: data.business_linkedin_url,
            business_photo_url: data.business_photo_url,
            business_tags: data.business_tags,
        }
    }

    return null
})

const google = computed(() => {
    if (!enriched.value) return null

    const data = enriched.value.data.result

    if (data?.google_profile_photo_url) {
        return {
            google_profile_photo_url: data.google_profile_photo_url,
            google_cover_photo_url: data.google_cover_photo_url,
            google_contributor_url: data.google_contributor_url,
            google_apps: data.google_apps,
        }
    }

    return null
})

const whatsapp = computed(() => {
    if (!enriched.value) return null

    const data = enriched.value.data.result

    if (data?.whatsapp_profile_photo_url) {
        return {
            whatsapp_profile_photo_url: data.whatsapp_profile_photo_url,
        }
    }

    return null
})

const sendHasSeen = async () => {
    await useModel('Client').actions.agentHasSeenEnriched({
        client_pk: props.clientPk,
    })
}

if (!enriched.value?.has_seen) {
    sendHasSeen()
}
</script>

<style scoped lang="postcss">
.divided-list {
    > div {
        &:nth-child(even) {
            @apply border-l px-2 dark:border-gray-700;
        }
    }
}
</style>

