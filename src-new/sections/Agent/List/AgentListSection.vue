<template>
    <div>
        <div class="card">
            <div class="p-2 flex items-center justify-end gap-4">
                <SearchPresetsList class="mr-auto -m-2 -my-3" :search-controller="searchController" />

                <AppPaginationCompact class="flex-none" :pagination="agentList.pagination" />
                <AppPageSize class="flex-none" :pagination="agentList.pagination" />

                <TableColumnsConfigurationButton :controller="columnsController" />
            </div>

            <SuspenseManual :state="suspense">
                <template #default>
                    <div class="overflow-x-auto fancy-scroll-x">
                        <AgentTableWrapper
                            :columns="columnsController"
                            :items="agentList.records"
                            :search-tags="searchController.tags"
                            :sort-controller="sortController"
                            zebra
                        >
                            <template #id="{ item: agent }: { item: Item }">
                                <td>{{ agent.id }}</td>
                            </template>
                            <template #is_online="{ item: agent }: { item: Item }">
                                <td>
                                    <div class="flex items-center gap-2">
                                        <div class="w-fit relative">
                                            <AppAvatar class="h-6 w-6" :model="agent" />

                                            <span
                                                :class="{
                                                    'bg-danger': !agent.state?.is_online,
                                                    'bg-success-400': agent.state?.is_online,
                                                }"
                                                class="block w-2 h-2 rounded-lg absolute -top-px -right-px"
                                            />
                                        </div>

                                        <LockIcon
                                            v-if="!agent.is_enabled"
                                            v-tooltip="'Agent is disabled'"
                                            class="text-secondary-400 ml-auto !stroke-2.5 icon --small"
                                        />
                                    </div>
                                </td>
                            </template>
                            <template #fullname="{ item: agent }: { item: Item }">
                                <TableCellLink
                                    v-if="canViewAgentDetails"
                                    :to="withCurrentQuery(routeToAgent(agent.pk))"
                                >
                                    <div class="flex items-center gap-1 max-w-[170px]">
                                        <span class="truncate">{{ getFullName(agent) }}</span>
                                        <BotIcon
                                            v-if="hasPermission('manage', 'all') && agent.is_ai_agent"
                                            class="w-4 h-4 text-primary"
                                        />
                                    </div>
                                </TableCellLink>
                                <td v-else class="truncate max-w-[170px]">
                                    {{ getFullName(agent) }}
                                </td>
                            </template>
                            <template #email="{ item: agent }: { item: Item }">
                                <td>
                                    <div class="flex items-center">
                                        <div
                                            class="truncate max-w-[170px]"
                                            :content="agent.email"
                                        >
                                            {{ agent.email }}
                                        </div>
                                        <AppCopy v-copy="agent.email" />
                                    </div>
                                </td>
                            </template>
                            <template #personal_fullname="{ item: agent }: { item: Item }">
                                <td class="text-success truncate max-w-[170px]">
                                    {{ agent.personal_fullname }}
                                </td>
                            </template>
                            <template #birthday_at="{ item: agent }: { item: Item }">
                                <td>
                                    {{ $format.date(agent.birthday_at) }}
                                </td>
                            </template>
                            <template #phone="{ item: agent }: { item: Item }">
                                <td>
                                    <div class="flex items-center">
                                        {{ agent.phone }}
                                        <span v-if="agent.phone"><AppCopy v-copy="agent.phone" /></span>
                                    </div>
                                </td>
                            </template>
                            <template #department="{ item: agent }: { item: Item }">
                                <td>
                                    {{ agent.department?.name }}
                                </td>
                            </template>
                            <template #position="{ item: agent }: { item: Item }">
                                <td>
                                    {{ getPositionFull(agent) }}
                                </td>
                            </template>
                            <template #team="{ item: agent }: { item: Item }">
                                <td class="truncate max-w-[140px]">
                                    {{ agent.team?.name }}
                                </td>
                            </template>
                            <template #sabre_id="{ item: agent }: { item: Item }">
                                <td>
                                    <div
                                        :content="agent.gds.sabre_id"
                                        class="truncate max-w-[80px]"
                                    >
                                        {{ agent.gds.sabre_id }}
                                    </div>
                                </td>
                            </template>
                            <template #sabre_initials="{ item: agent }: { item: Item }">
                                <td>
                                    <div
                                        :content="agent.gds.sabre_initials"
                                        class="truncate max-w-[80px]"
                                    >
                                        {{ agent.gds.sabre_initials }}
                                    </div>
                                </td>
                            </template>
                            <template #status="{ item: agent }: { item: Item }">
                                <td class="text-center">
                                    <span v-if="agent.is_enabled" class="colored-text --success">Enabled</span>
                                    <span v-else class="colored-text --danger">Disabled</span>
                                </td>
                            </template>
                            <template #hiring_source="{ item: agent }: { item: Item }">
                                <td>
                                    {{ hiringSourceDictionary.find(agent.hiring_source)?.title }}
                                </td>
                            </template>
                            <template #voip_extension="{ item: agent }: { item: Item }">
                                <td>
                                    {{ agent.voip_extension }}
                                </td>
                            </template>
                            <template #created_at="{ item: agent }: { item: Item }">
                                <td>
                                    {{ $format.date(agent.created_at) }}
                                </td>
                            </template>
                            <template #requested_hired_at="{ item: agent }: { item: Item }">
                                <td>
                                    {{ !agent.hired_at && agent.requested_hired_at ? $format.date(agent.requested_hired_at) : '' }}
                                </td>
                            </template>
                            <template #requested_dismissed_at="{ item: agent }: { item: Item }">
                                <td>
                                    {{ !agent.dismissed_at && agent.requested_dismissed_at ? $format.date(agent.requested_dismissed_at) : '' }}
                                </td>
                            </template>
                            <template #last_login_at="{ item: agent }: { item: Item }">
                                <td>
                                    {{ $format.datetime(agent.state?.last_login_at) }}
                                </td>
                            </template>
                            <template #office_pk="{ item: agent }: { item: Item }">
                                <td>
                                    {{ agent.office?.name }}
                                </td>
                            </template>
                        </AgentTableWrapper>
                    </div>
                </template>
                <template #fallback>
                    <PlaceholderBlock class="w-full h-[300px]" />
                </template>
            </SuspenseManual>
        </div>

        <AppTablePagination class="mt-6 mx-6" :pagination="agentList.pagination" />
    </div>
</template>

<script setup lang="ts">
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import SearchController from '~/lib/Search/SearchController'
import SortController from '~/lib/Search/SortController'
import NumberSearchTag from '~/lib/Search/Tag/NumberSearchTag'
import TextSearchTag from '~/lib/Search/Tag/TextSearchTag'
import SelectSearchTag from '~/lib/Search/Tag/SelectSearchTag'
import BooleanSearchTag from '~/lib/Search/Tag/BooleanSearchTag'
import DepartmentSearchTag from '~/lib/Search/Tag/Preset/DepartmentSearchTag'
import PositionSearchTag from '~/lib/Search/Tag/Preset/PositionSearchTag'
import TeamSearchTag from '~/lib/Search/Tag/Preset/TeamSearchTag'
import OfficeSearchTag from '~/lib/Search/Tag/Preset/OfficeSearchTag'
import DateRangeSearchTag from '~/lib/Search/Tag/DateRangeSearchTag'
import BooleanSelectSearchTag from '~/lib/Search/Tag/BooleanSelectSearchTag'
import { getFullName } from '~/lib/Helper/PersonHelper'
import SearchPresetsList from '~/components/Search/SearchPresetsList.vue'
import { TableColumnsController } from '~/lib/Table/TableColumnsController'
import AgentTableWrapper from '~/sections/Agent/AgentTableWrapper.vue'
import TableColumnsConfigurationButton from '~/components/Table/TableColumnsConfigurationButton.vue'
import { isUserInDepartment } from '~/composables/useCurrentUser'
import { DepartmentName } from '~/api/models/Department/Department'
import type { ModelRef } from '~types/lib/Model'
import type SelectOption from '~types/structures/SelectOption'
import BotIcon from '~assets/icons/BotIcon.svg?component'

defineOptions({
    name: 'AgentListSection',
})

//

const {
    useModel,
    workspace,
    hasPermission,
    useCurrentUser,
    useDictionary,
} = useContext()

//

const currentUser = useCurrentUser()

const isHRMode = isUserInDepartment(currentUser, DepartmentName.HR)
const hiringSourceDictionary = useDictionary('HiringSource')
const hiringSourceOptions: SelectOption[] = hiringSourceDictionary.mapRecords.forSelect()

const suspense = useSuspensableComponent(async () => {
    await agentList.fetch()
    await columnsController.syncColumns()
})

//

const canViewGdsInfo = computed(() => {
    return hasPermission('gdsInfo', 'Agent')
})

const canViewAgentDetails = computed(() => {
    return hasPermission('viewAgentDetails', 'Agent') // user is CustomerSupport Agent -> reduce access
})

const canEditHiringSource = computed(() => {
    return hasPermission('editHiringSource', 'Agent')
})

/**
 * @generateTable ~/sections/Agent/AgentTableWrapper.vue
 */
const columnsController = markRaw(new TableColumnsController(useTableColumns({
    id: {
        label: '#',
        sortable: true,
        width: 5,
    },
    is_online: {
        label: 'Online',
        sortable: true,
        width: 'min',
    },
    fullname: {
        label: 'Full name',
        sortable: true,
        width: 16,
    },
    email: {
        label: 'Email',
        sortable: true,
        width: 16,
        enabled: canViewAgentDetails,
    },
    personal_fullname: {
        label: 'Personal name',
        sortable: true,
        enabled: canViewAgentDetails,
    },
    birthday_at: {
        label: 'Birth Date',
        sortable: true,
        enabled: canViewAgentDetails,
    },
    phone: {
        label: 'Phone',
        sortable: true,
        width: 16,
        enabled: canViewAgentDetails,
    },
    department: {
        label: 'Department',
        sortable: true,
        width: 16,
    },
    position: {
        label: 'Position',
        sortable: true,
        width: 12,
    },
    team: {
        label: 'Team',
        sortable: true,
        width: 14,
    },
    sabre_id: {
        label: 'Sabre ID',
        sortable: true,
        width: 8,
        enabled: canViewGdsInfo,
        neededRelations: ['gds'],
    },
    sabre_initials: {
        label: 'Sabre initials',
        sortable: true,
        width: 8,
        enabled: canViewGdsInfo,
        neededRelations: ['gds'],
    },
    status: {
        label: 'Status',
        sortable: true,
        width: 7,
    },
    hiring_source: {
        label: 'Hiring source',
        width: 7,
        enabled: canEditHiringSource,
        sortable: true,
    },
    voip_extension: {
        label: 'Voip ext.',
        width: 4,
        enabled: canViewGdsInfo,
    },
    created_at: {
        label: 'Created at',
        sortable: true,
        width: 11,
    },
    requested_hired_at: {
        label: 'Req. hired',
        sortable: true,
    },
    requested_dismissed_at: {
        label: 'Req. dismissed',
        sortable: true,
    },
    last_login_at: {
        label: 'Last login',
        sortable: true,
        width: 15,
    },
    office_pk: {
        label: 'Office',
        sortable: true,
    },
}), {
    id: { active: true },
    is_online: { active: true },
    fullname: { active: true, required: true },
    email: { active: true },
    personal_fullname: { active: true },
    birthday_at: { active: true },
    phone: { active: true },
    department: { active: true },
    position: { active: true },
    team: { active: true },
    sabre_id: { active: !isHRMode },
    sabre_initials: { active: !isHRMode },
    status: { active: true },
    hiring_source: { active: isHRMode },
    voip_extension: { active: !isHRMode },
    created_at: { active: true },
    requested_hired_at: { active: isHRMode },
    requested_dismissed_at: { active: isHRMode },
    last_login_at: { active: true },
    office_pk: { active: true },
}, 'agents', 'agents'))

const columns = columnsController.getColumnsDefinition()

const sortController = SortController.fromColumns(columns, {
    default: { 'id': 'desc' },
})

//

const agentList = useModel('Agent').useList({
    where: (and) => searchController.applyCondition(and),
    with: ['team', 'department', 'position', 'state', 'office'],
    withOptional: {
        gds: () => [
            columnsController.needToShowColumn('sabre_id'),
            columnsController.needToShowColumn('sabre_initials'),
        ].some(Boolean),
    },
    sort: sortController,
    pageSize: 40,
})

columnsController.useList(agentList)

type Item = typeof agentList.records[0]

//

const searchController = SearchController.forModel('Agent', {
    id: new NumberSearchTag('ID'),
    is_online: new BooleanSelectSearchTag('Online', ['Online', 'Offline']),
    fullname: new TextSearchTag('Full name'),
    email: new TextSearchTag('Email'),
    personal_fullname: new TextSearchTag('Personal name'),
    birthday_at: new DateRangeSearchTag('Birthday at'),
    incoming_birthday_at: new DateRangeSearchTag('Incoming birthday at'),
    phone: new TextSearchTag('Phone'),
    department: new DepartmentSearchTag(),
    position: new PositionSearchTag(),
    team: new TeamSearchTag(workspace),
    sabre_id: new TextSearchTag('Sabre ID'),
    sabre_initials: new TextSearchTag('Sabre initials'),
    status: new BooleanSelectSearchTag('Status', ['Enabled', 'Disabled']),
    voip_extension: new TextSearchTag('Voip ext.'),
    created_at: new DateRangeSearchTag('Created at'),
    requested_hired_at: new DateRangeSearchTag('Req. hired'),
    requested_dismissed_at: new DateRangeSearchTag('Req. dismissed'),
    last_login_at: new DateRangeSearchTag('Last login'),
    office_pk: new OfficeSearchTag(),
    hiring_source: new SelectSearchTag('Hiring source', hiringSourceOptions).hideInSearch(!canEditHiringSource.value),
    is_ai_agent: new BooleanSearchTag('Include AI agent', undefined, {
        applyCondition: (tag, field, and) => {
            if (!tag.isActive) {
                and.eq(field, 0)
            }
        },
    }).setHasOneState(),

}, {
    presets: {
        key: 'agents',
    },
}).useList(agentList)

//

defineExpose({
    searchController,
})

useService('event').on('refreshAgentListOnAddAgent', () => {
    agentList.fetch()
})

function getPositionFull(agent: ModelRef<'Agent', 'position'>) {
    const agent_pk = usePk(agent)

    if (['474'].includes(agent_pk)) {
        return ''
    }

    return agent?.position.name || ''
}
</script>
