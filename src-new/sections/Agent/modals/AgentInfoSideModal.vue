<template>
    <AppModalWrapper
        class="!min-w-[1200px] app-modal__container--flex"
        stick-to-side
        outer-button
    >
        <AppModalHeader close-button class="!h-0" />

        <div class="card__header pl-0">
            <div class="card__title">
                <UserIcon />
                {{ header }}
                <AgentIsBeginnerBadge v-if="showBeginnerStatus" :is-beginner="agent.is_beginner" />
                <div
                    v-if="hasPermission('manage', 'all') && agent.is_ai_agent"
                    class="badge --small --rounded --primary --soft"
                >
                    <BotIcon class="stroke-current stroke-1.5 text-primary" />
                    <span>AI Agent</span>
                </div>
            </div>
        </div>

        <AgentInfoSection :agent-pk="pk" />
    </AppModalWrapper>
</template>

<script setup lang="ts">
import { getFullName } from '~/lib/Helper/PersonHelper'
import AgentIsBeginnerBadge from '~/sections/Agent/components/AgentIsBeginnerBadge.vue'
import { DepartmentName } from '~/api/models/Department/Department'
import AgentInfoSection from '~/sections/Agent/AgentInfoSection.vue'
import BotIcon from '~assets/icons/BotIcon.svg?component'

defineOptions({
    name: 'AgentInfoSideModal',
    modal: {
        position: 'right',
        overlay: false,
    },
})

//

const props = defineProps<{
    pk: PrimaryKey,
}>()

//

const { hasPermission, record: agent } = await useNewContext('Agent', props.pk)

const header = computed(() => getFullName(agent) + ' #' + agent.id)

const showBeginnerStatus = computed(() => {
    return isUserInDepartment(agent, DepartmentName.Sales)
})
</script>
