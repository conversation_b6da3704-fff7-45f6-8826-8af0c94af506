<template>
    <OrganizerCollapseWrapper v-if="leadOfferList.records.length">
        <template #icon="{classes}">
            <CheckIcon :class="classes" />
        </template>
        <template #title>
            Sent options
        </template>
        <div class="organizer-offers">
            <div
                v-for="offer in leadOfferList.records"
                :key="offer.id"
                class="organizer-offer"
                :class="{
                    'organizer-offer--is-award': offer.is_award_offer,
                }"
            >
                <div class="organizer-offer__status relative">
                    <LeadOfferClientViewed
                        class="pl-1"
                        :offer="offer"
                    />
                    <div
                        v-if="offer.drop_off_point"
                        class="absolute badge --xs left-[-15px] !p-1 !text-[8px]"
                        :class="{
                            '--success': offer.drop_off_point === 5,
                            '--primary': offer.drop_off_point !== 5,
                        }"
                    >
                        {{ offer.drop_off_point }}
                    </div>
                </div>

                <a
                    v-if="offer.is_published && !resource.archived"
                    class="organizer-offer__link flex items-center gap-1"
                    :href="offer.links[0] || '#'"
                    target="_blank"
                >
                    Offer #{{ offer.id }}
                </a>
                <span
                    v-else
                    class="organizer-offer__link"
                    :class="{'text-secondary-300': resource.archived}"
                >
                    Offer #{{ offer.id }}
                </span>

                <div
                    v-if="!offer.is_to_client"
                    v-tooltip="{ content: 'Sent to agent' }"
                    class="organizer-offer__sent-to-agent"
                >
                    <UserLeft2 />
                </div>

                <div class="organizer-offer__info">
                    <span class="organizer-offer__info__time">
                        {{ $format.datetime(offer.created_at) }}
                    </span>
                    <span class="organizer-offer__info__creator">
                        by {{ getShortName(offer.createdBy) }}
                    </span>
                </div>

                <div class="organizer-offer__actions">
                    <AppButton
                        v-tooltip="{ content: 'Copy link' }"
                        type="button"
                        class="--ghost --small --neutral --only"
                        @click="copyToClipboard(offer.links[0])"
                    >
                        <CopyIcon />
                    </AppButton>
                </div>
            </div>
        </div>
    </OrganizerCollapseWrapper>
</template>

<script setup lang="ts">
import OrganizerCollapseWrapper from '@/components/Organizer/Components/OrganizerCollapseWrapper.vue'
import UserLeft2 from '~assets/icons/UserLeft2.svg?component'
import { getShortName } from '~/lib/Helper/PersonHelper'
import LeadOfferClientViewed from '~/sections/LeadOffer/components/LeadOfferClientViewed.vue'

const props = defineProps<{
    resource: {
        name: DefinedResourceName,
        pk: PrimaryKey
        archived?: boolean,
    },
}>()

//

const { useModel } = useContext()

//

useSuspensableComponent(async () => {
    await leadOfferList.fetch()
})

//

const leadOfferList = useModel('LeadOffer').useResourceList(props.resource, {
    withArchiveData: props.resource.archived,
    with: ['createdBy'],
})
</script>

