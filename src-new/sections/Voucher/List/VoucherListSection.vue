<template>
    <div>
        <div class="card">
            <div class="p-2 flex items-center justify-end gap-4">
                <AppPaginationCompact :pagination="voucherList.pagination" />
                <AppPageSize :pagination="voucherList.pagination" />
            </div>

            <SuspenseManual :state="suspense">
                <template #default>
                    <div class="overflow-x-auto fancy-scroll-x relative min-h-[390px]">
                        <AppTable
                            :columns="columns"
                            :items="voucherList.records"
                            :search-tags="searchController.tags"
                            :sort-controller="sortController"
                        >
                            <template #body>
                                <template v-for="(voucher, index) in voucherList.records" :key="voucher.pk">
                                    <tr
                                        :class="{
                                            'voucher-highlighted-row': (index + 1) % 2 === 0 && !isDarkTheme,
                                            'voucher-highlighted-row__dark': (index + 1) % 2 === 0 && isDarkTheme,
                                        }"
                                    >
                                        <td>
                                            <div
                                                v-if="voucher.is_sent ? hasPermission('editSentVoucher', 'Voucher') : hasPermission('edit', 'Voucher')"
                                                class="text-primary cursor-pointer"
                                                @click="openVoucherEditModal(usePk(voucher), voucher.model_name)"
                                            >
                                                {{ voucher.voucher_id }}
                                            </div>
                                            <div v-else>
                                                {{ voucher.voucher_id }}
                                            </div>
                                        </td>

                                        <TableCellLink
                                            v-if="voucher.model_name === 'Sale'"
                                            :to="linkToSale(voucher.model_pk)"
                                            :target="'_blank'"
                                        >
                                            {{ voucher.model_pk }}
                                        </TableCellLink>
                                        <td v-else />

                                        <td>
                                            <div>
                                                {{ getFullName(voucher.client) }}
                                            </div>
                                        </td>

                                        <TableCellLink
                                            v-if="voucher.expected_amount_pk"
                                            :to="linkToExpectedAmountPk(voucher.expected_amount_pk)"
                                            :target="'_blank'"
                                        >
                                            {{ voucher.expected_amount_pk }}
                                        </TableCellLink>
                                        <td v-else />

                                        <TableCellLink
                                            v-if="canViewAgentPage"
                                            :to="linkToAgent(voucher.created_by_pk)"
                                            :target="'_blank'"
                                            class="max-w-[150px]"
                                        >
                                            <div class="truncate">
                                                {{ getFullName(voucher.createdBy) }}
                                            </div>
                                        </TableCellLink>
                                        <td v-else class="max-w-[150px]">
                                            <div class="truncate">
                                                {{ getFullName(voucher.createdBy) }}
                                            </div>
                                        </td>

                                        <TableCellLink
                                            v-if="canViewAgentPage"
                                            :to="linkToAgent(voucher.executor_pk)"
                                            class="max-w-[150px]"
                                            :target="'_blank'"
                                        >
                                            <div class="truncate">
                                                {{ getFullName(voucher.executor) }}
                                            </div>
                                        </TableCellLink>
                                        <td v-else class="max-w-[150px]">
                                            <div class="truncate">
                                                {{ getFullName(voucher.executor) }}
                                            </div>
                                        </td>

                                        <td>
                                            <Dropdown teleport>
                                                <template #toggle="{ toggle }">
                                                    <div
                                                        class="flex items-center justify-between "
                                                        :class="{
                                                            'cursor-pointer': voucher.pnr.length > 1
                                                        }"
                                                        @click="() => {
                                                            if (voucher.pnr.length > 1) {
                                                                toggle()
                                                            }
                                                        }"
                                                    >
                                                        {{ voucher.pnr[0] }}

                                                        <ChevronDownIcon
                                                            v-if="voucher.pnr.length > 1"
                                                            class="dropdown-button__icon"
                                                        />
                                                    </div>
                                                </template>
                                                <template #content>
                                                    <div
                                                        class="dropdown-menu__content my-1 p-2 space-y-1 box dark:bg-dark-1"
                                                    >
                                                        <div
                                                            v-for="pnr in voucher.pnr.slice(1, voucher.pnr.length)"
                                                            :key="pnr"
                                                            class="flex gap-1 cursor-pointer text-2xs"
                                                        >
                                                            <div>{{ pnr }}</div>
                                                        </div>
                                                    </div>
                                                </template>
                                            </Dropdown>
                                        </td>
                                        <td>
                                            {{ $format.money(voucher.net_price) }}
                                        </td>
                                        <td>
                                            {{ $format.money(voucher.emd) }}
                                        </td>
                                        <td>
                                            {{ $format.money(voucher.points_trade) }}
                                        </td>
                                        <td>
                                            {{ $format.money(voucher.upgrade_amount) }}
                                        </td>
                                        <td v-tooltip="{content: voucher.fop}">
                                            {{ voucher.fop }}
                                        </td>
                                        <td>
                                            <Dropdown teleport>
                                                <template #toggle="{ toggle }">
                                                    <div
                                                        class="flex items-center justify-between "
                                                        :class="{
                                                            'cursor-pointer': formatVoucherPcc(voucher.pcc).length > 1
                                                        }"
                                                        @click="() => {
                                                            if (formatVoucherPcc(voucher.pcc).length > 1) {
                                                                toggle()
                                                            }
                                                        }"
                                                    >
                                                        {{ formatVoucherPcc(voucher.pcc)[0] }}

                                                        <ChevronDownIcon
                                                            v-if="formatVoucherPcc(voucher.pcc).length > 1"
                                                            class="dropdown-button__icon"
                                                        />
                                                    </div>
                                                </template>
                                                <template #content>
                                                    <div
                                                        class="dropdown-menu__content my-1 p-2 space-y-1 box dark:bg-dark-1"
                                                    >
                                                        <div
                                                            v-for="pcc in formatVoucherPcc(voucher.pcc).slice(1, voucher.pnr.length)"
                                                            :key="pcc"
                                                            class="flex gap-1 cursor-pointer text-2xs"
                                                        >
                                                            <div>{{ pcc }}</div>
                                                        </div>
                                                    </div>
                                                </template>
                                            </Dropdown>
                                        </td>
                                        <td>
                                            <div class="flex justify-center items-center">
                                                <div
                                                    class="flex gap-x-1 items-center"
                                                    :class="`text-${getVoucherStatus(voucher.status).style}`"
                                                >
                                                    <span
                                                        class="w-1 h-1 rounded-full bg-secondary"
                                                        :class="`bg-${getVoucherStatus(voucher.status).style}`"
                                                    />
                                                    {{ getVoucherStatus(voucher.status).title }}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            {{ getVoucherCreditWith(voucher.credit_with).title }}
                                        </td>
                                        <td>
                                            {{ $format.money(voucher.voucher_price) }}
                                        </td>

                                        <td>
                                            {{ $format.date(voucher.issued_at, 'UTC', {full: true}) }}
                                        </td>
                                        <td>
                                            {{ $format.date(voucher.expires_at, 'UTC', {full: true}) }}
                                        </td>
                                        <TableCellLink
                                            v-if="voucher.sale_reflected_pk"
                                            :to="linkToSale(voucher.sale_reflected_pk)"
                                            :target="'_blank'"
                                        >
                                            {{ voucher.sale_reflected_pk }}
                                        </TableCellLink>
                                        <td v-else />
                                        <td>
                                            <div v-if="voucher.is_sent" class="flex justify-between items-center">
                                                <span class="pr-1">Sent to Client</span>
                                                <div class="text-primary">
                                                    <IsSentIcon class="w-4 h-4" />
                                                </div>
                                            </div>
                                            <div v-else class="flex justify-between items-center">
                                                <span>Temporary</span>
                                                <CheckIcon class="w-4 h-4 --muted" />
                                            </div>
                                        </td>
                                        <td>
                                            <div class="flex justify-between">
                                                <AppButton
                                                    v-if="voucher.issue_pk"
                                                    v-tooltip="'View discussion'"
                                                    class="--ghost --small !px-1 text-success --only"
                                                    :class="{
                                                        'text-warning': !voucher.is_verified,
                                                    }"
                                                    @click="openIssue(voucher.issue_pk)"
                                                >
                                                    <MessageSquareIcon class="icon --xs" />
                                                </AppButton>
                                                <AppButton
                                                    v-tooltip="{ content: 'Preview' }"
                                                    class="--ghost --small !px-1 --muted --only"
                                                    @click="openVoucherPreviewModal(usePk(voucher), voucher.voucher_id)"
                                                >
                                                    <EyeIcon class="icon --xs" />
                                                </AppButton>
                                                <AppButton
                                                    v-if="voucher.is_verified || hasPermission('manageVoucher', 'Voucher')"
                                                    v-tooltip="{ content: 'Use' }"
                                                    class="--ghost --small text-success --only"
                                                    :class="{
                                                        'text-danger': !voucher.is_verified
                                                    }"
                                                    @click="openVoucherUseVoucherModal(voucher)"
                                                >
                                                    <UseVoucherIcon class="icon --xs" />
                                                </AppButton>
                                                <AppButton
                                                    v-if="hasPermission('delete', 'Voucher')"
                                                    v-tooltip="{ content: 'Delete' }"
                                                    class="--ghost --small text-danger --only !px-1 "
                                                    @click="deleteVoucher(voucher)"
                                                >
                                                    <TrashIcon class="icon --xs" />
                                                </AppButton>
                                                <AppButton
                                                    v-if="voucher.is_verified || hasPermission('manageVoucher', 'Voucher')"
                                                    v-tooltip="{ content: 'Send to Client'}"
                                                    class="--ghost text-primary --only --small !px-1"
                                                    :class="{
                                                        'text-success': voucher.is_sent,
                                                        'text-danger': !voucher.is_verified
                                                    }"
                                                    @click="sendVoucherToClient(voucher)"
                                                >
                                                    <SendIcon class="icon --xs" />
                                                </AppButton>
                                            </div>
                                        </td>
                                    </tr>
                                    <template v-if="voucher.additional.length > 1">
                                        <tr
                                            v-for="additional in voucher.additional"
                                            :key="usePk(additional)"
                                            :class="{
                                                'voucher-highlighted-row': (index + 1) % 2 === 0 && !isDarkTheme,
                                                'voucher-highlighted-row__dark': (index + 1) % 2 === 0 && isDarkTheme,
                                            }"
                                        >
                                            <td colspan="6" />
                                            <td>{{ additional.pnr }}</td>
                                            <td>{{ $format.money(additional.net_amount) }}</td>
                                            <td>{{ $format.money(additional.emd_amount) }}</td>
                                            <td>{{ $format.money(additional.points_trade_amount) }}</td>
                                            <td>{{ $format.money(additional.upgrade_amount) }}</td>
                                            <td>{{ additional.fop }}</td>
                                            <td>
                                                <Dropdown teleport>
                                                    <template #toggle="{ toggle }">
                                                        <div
                                                            class="flex items-center justify-between "
                                                            :class="{
                                                                'cursor-pointer': additional.pcc.length > 1
                                                            }"
                                                            @click="() => {
                                                                if (additional.pcc.length > 1) {
                                                                    toggle()
                                                                }
                                                            }"
                                                        >
                                                            {{ formatVoucherPcc(additional.pcc)[0] }}

                                                            <ChevronDownIcon
                                                                v-if="additional.pcc.length > 1"
                                                                class="dropdown-button__icon"
                                                            />
                                                        </div>
                                                    </template>
                                                    <template #content>
                                                        <div
                                                            class="dropdown-menu__content my-1 p-2 space-y-1 box dark:bg-dark-1"
                                                        >
                                                            <div
                                                                v-for="pcc in formatVoucherPcc(additional.pcc).slice(1, additional.pcc.length)"
                                                                :key="pcc"
                                                                class="flex gap-1 cursor-pointer text-2xs"
                                                            >
                                                                <div>{{ pcc }}</div>
                                                            </div>
                                                        </div>
                                                    </template>
                                                </Dropdown>
                                            </td>
                                            <td />
                                            <td>{{ getVoucherCreditWith(additional.credit_with).title }}</td>
                                            <td>{{ $format.money(additional.voucher_price) }}</td>
                                            <td colspan="5" />
                                        </tr>
                                    </template>
                                </template>
                            </template>
                        </AppTable>
                    </div>
                </template>
                <template #fallback>
                    <PlaceholderBlock class="w-full h-[300px]" />
                </template>
            </SuspenseManual>
        </div>
        <AppTablePagination class="mt-6 mx-6" :pagination="voucherList.pagination" />
    </div>
</template>

<script setup lang="ts">
import AppTable from '~/components/Table/AppTable.vue'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import type { VoucherCreditWith, VoucherStatus } from '~/api/models/Voucher/Voucher'
import { getFullName } from '~/lib/Helper/PersonHelper'
import type {
    VoucherColumns,
    VoucherSearchController,
    VoucherSortController,
} from '~/sections/Voucher/composable/useVoucherSearchController'
import UseVoucherIcon from '~/assets/icons/UseVoucherIcon.svg?component'
import VoucherEditModal from '~/sections/Voucher/modals/VoucherEditModal.vue'
import VoucherPreviewModal from '~/sections/Voucher/modals/VoucherPreviewModal.vue'
import VoucherUseVoucherModal from '~/sections/Voucher/modals/VoucherUseVoucherModal.vue'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import { $confirm, $confirmDelete } from '@/plugins/ConfirmPlugin'
import type { ModelAttributes, ModelRef } from '~types/lib/Model'
import IsSentIcon from '~/assets/icons/IsSentIcon.svg?component'
import { linkToAgent, linkToExpectedAmountPk, linkToSale } from '@/lib/core/helper/RouteNavigationHelper'
import Dropdown from '~/components/Dropdown.vue'
import VoucherClientEditModal from '~/sections/Voucher/modals/VoucherClientEditModal.vue'

defineOptions({
    name: 'VoucherListSection',
})

const props = defineProps<{
    searchController: VoucherSearchController,
    sortController: VoucherSortController,
    voucherColumns: VoucherColumns
}>()

const { useModel, useDictionary, hasPermission } = useContext()

//

const suspense = useSuspensableComponent(async () => {
    await voucherList.fetch()
})

const voucherCreditWithDictionary = useGeneralDictionary('VoucherCreditWith')
const voucherStatusDictionary = useGeneralDictionary('VoucherStatus')

function getVoucherCreditWith(status: VoucherCreditWith) {
    return voucherCreditWithDictionary.find(status)
}

function getVoucherStatus(status: VoucherStatus) {
    return voucherStatusDictionary.find(status)
}

//

const canViewAgentPage = computed(() => {
    return hasPermission('viewAgentDetails', 'Agent')
})

const consolidatorAreaDictionary = useDictionary('ConsolidatorArea')

const sortController = props.sortController
const columns = props.voucherColumns

const voucherList = useModel('Voucher').useList({
    where: (and) => searchController.applyCondition(and),
    with: ['executor', 'createdBy', 'additional', 'client'],
    sort: sortController,
    pageSize: 10,
})

const searchController = props.searchController.useList(voucherList)

// edit modal

const voucherClientEditModal = useModal(VoucherClientEditModal)
const voucherEditModal = useModal(VoucherEditModal)

function openVoucherEditModal(pk: PrimaryKey, model_name: string) {
    if (model_name === 'Sale') {
        voucherEditModal.open({
            voucherPk: pk,
        })
    } else {
        voucherClientEditModal.open({
            voucherPk: pk,
        })
    }
}

// preview modal

const previewVoucherModal = useModal(VoucherPreviewModal)

async function openVoucherPreviewModal(pk: PrimaryKey, id: string, sendToClient: boolean = false, clientEmail?: string) {
    await previewVoucherModal.open({
        voucherPk: pk,
        voucherId: id,
        sendToClient,
        clientEmail,
    })
}

// use voucher modal

const useVoucherModal = useModal(VoucherUseVoucherModal)

async function openVoucherUseVoucherModal(voucher: ModelAttributes<'Voucher'>) {
    if (!voucher.is_verified) {
        await $confirm({
            text: 'Are you sure you want to use this voucher?',
            confirmButton: 'Yes, use voucher',
        })
    }

    await useVoucherModal.open({
        voucherPk: usePk(voucher),
    })
}

// send voucher to client

async function sendVoucherToClient(voucher: ModelRef<'Voucher', 'client'>) {
    if (!voucher.is_verified) {
        await $confirm({
            text: 'Are you sure you want to send this voucher?',
            confirmButton: 'Yes, send voucher',
        })
    }

    const clientRecord = await useModel('Client').useRecord().fetch(voucher.client_pk)
    await openVoucherPreviewModal(usePk(voucher), voucher.voucher_id, true, clientRecord.value.email || '')

    toastSuccess('Voucher is sent')

    await voucherList.fetch()
}

// delete voucher

async function deleteVoucher(voucher: ModelAttributes<'Voucher'>) {
    await $confirmDelete(`Confirm deleting ${voucher.voucher_id} voucher`)
    await useModel('Voucher').actions.deleteVoucher({
        pk: usePk(voucher),
    })
    toastSuccess('Voucher is deleted')

    await voucherList.fetch()
}

//

const eventService = useService('event')

//

function formatVoucherPcc(pcc: PrimaryKey[]) {
    return pcc.map(consolidatorAreaPk => (consolidatorAreaDictionary.find(consolidatorAreaPk))?.name)
}

//

onMounted(() => {
    eventService.on('voucherListUpdate', suspense.fetch)
})

onUnmounted(() => {
    eventService.off('voucherListUpdate', suspense.fetch)
})

defineExpose({
    searchController,
})

const issueModal = useGlobalModal('IssueSideModal')

async function openIssue(issue_pk: PrimaryKey) {
    await issueModal.open(issue_pk)
}

//

const darkMode = useDarkMode()

const isDarkTheme = computed(() => {
    return darkMode.isDark.value
})
</script>

<style scoped>
.voucher-highlighted-row td {
    background-color: #f5f7fa;
}

.voucher-highlighted-row:hover td {
    background-color: #eff4ff !important;
}

.voucher-highlighted-row__dark td {
    background-color: #313a55;
}

.voucher-highlighted-row__dark:hover td {
    background-color: #2b3348 !important;
}
</style>
