<template>
    <AppModalWrapper
        class="!max-w-[800px]"
        :header="header"
        close-button
    >
        <div class="p-2 bg-secondary-50 dark:bg-dark-7">
            <div class="h-full w-full  flex flex-col gap-y-2">
                <div class="bg-white dark:bg-dark-6 p-3 rounded-lg">
                    <p class="w-full border-b-2 border-secondary-100 m-0 pb-3 text-sm font-semibold">
                        Non editable info
                    </p>
                    <div class="mt-4 grid grid-cols-3 gap-x-4 gap-y-3">
                        <div class="flex justify-between border-b border-secondary-100 pb-1">
                            <span class="text-secondary text-xs">Voucher ID</span>
                            <span class="text-xs">{{ voucher.voucher_id }}</span>
                        </div>
                        <div class="flex justify-between border-b border-secondary-100 pb-1">
                            <span class="text-secondary text-xs">{{ voucher.model_name }} ID</span>
                            <span class="text-xs">{{ voucher.model_pk }}</span>
                        </div>

                        <div class="flex justify-between border-b border-secondary-100 pb-1">
                            <span class="text-secondary text-xs">Created by</span>
                            <span class="text-xs">{{ getFullName(createdByInfo) }}</span>
                        </div>

                        <div
                            v-if="voucher.sale_reflected_pk"
                            class="flex justify-between border-b border-secondary-100 pb-1"
                        >
                            <span class="text-secondary text-xs">Sale Reflected ID</span>
                            <span class="text-xs">{{ voucher.sale_reflected_pk }}</span>
                        </div>
                        <div class="flex justify-between border-b border-secondary-100 pb-1">
                            <span class="text-secondary text-xs">Voucher price</span>
                            <span class="text-xs">{{ $format.money(summary.voucher_price) }}</span>
                        </div>

                        <div class="flex justify-between border-b border-secondary-100 pb-1">
                            <span class="text-secondary text-xs">Net</span>
                            <span class="text-xs">{{ $format.money(summary.net_amount) }}</span>
                        </div>

                        <div class="flex justify-between border-b border-secondary-100 pb-1">
                            <span class="text-secondary text-xs">Points trade</span>
                            <span class="text-xs">{{ $format.money(summary.points_trade_amount) }}</span>
                        </div>

                        <div class="flex justify-between border-b border-secondary-100 pb-1">
                            <span class="text-secondary text-xs">EMD</span>
                            <span class="text-xs">{{ $format.money(summary.emd_amount) }}</span>
                        </div>

                        <div class="flex justify-between border-b border-secondary-100 pb-1">
                            <span class="text-secondary text-xs">Upgrade</span>
                            <span class="text-xs">{{ $format.money(summary.upgrade_amount) }}</span>
                        </div>
                    </div>
                </div>
                <div class="bg-white dark:bg-dark-6 p-3 rounded-lg">
                    <p class="w-full border-b-2 border-secondary-100 m-0 pb-3 text-sm font-semibold">
                        Editable info
                    </p>
                    <div class="mt-4 grid grid-cols-2 gap-3">
                        <FormField
                            class="text-xs font-medium"
                            :form="form"
                            :field="'issued_at'"
                            label="Issued at"
                        >
                            <InputDate
                                v-model:timestamp="form.data.issued_at"
                                placeholder="Issued at"
                                timezone="UTC"
                                @update:timestamp="issuedUpdate"
                            />
                        </FormField>
                        <FormField
                            class="text-xs font-medium"
                            :form="form"
                            :field="'expires_at'"
                            label="Expires at"
                        >
                            <InputDate
                                v-model:timestamp="form.data.expires_at"
                                placeholder="Expires at"
                                timezone="UTC"
                                @update:timestamp="expirationUpdate"
                            />
                        </FormField>
                        <FormField
                            class="text-xs font-medium"
                            :form="form"
                            :field="'executor_pk'"
                            label="Executor"
                        >
                            <InputAgent
                                v-model="form.data.executor_pk"
                                placeholder="Type here..."
                            />
                        </FormField>
                        <FormField
                            v-if="hasPermission('manageVoucher', 'Voucher')"
                            class="text-xs font-medium"
                            :form="form"
                            :field="'status'"
                            label="Status"
                        >
                            <InputSelect
                                v-model="form.data.status"
                                :options="voucherStatusOptions"
                                with-empty
                            />
                        </FormField>
                    </div>
                    <FormField
                        class="text-xs font-medium flex flex-col col-span-3 mt-2"
                        :form="form"
                        :field="'description'"
                        label="Description"
                    >
                        <InputTextarea
                            v-model="form.data.description"
                            rows="6"
                        />
                    </FormField>
                </div>

                <div class="bg-white dark:bg-dark-6 p-3 rounded-lg flex flex-col gap-y-3">
                    <p class="w-full border-b-2 border-secondary-100 m-0 pb-3 text-sm font-semibold">
                        PNR info
                    </p>
                    <div
                        v-for="(additional, $i) in additionalForm.data.pnrs"
                        :key="additional.id"
                        class="w-full min-h-[50px] border border-dashed rounded-lg relative px-4 py-2"
                    >
                        <div class="mt-3 flex flex-col gap-y-2">
                            <div class="flex justify-between items-start pb-4 border-b border-secondary-100">
                                <div class="text-xs font-medium">
                                    Passengers:
                                    <div class="flex gap-2">
                                        <div
                                            v-for="passenger_pk in additional.passengers_pks"
                                            :key="passenger_pk"
                                            class="mt-3 last:border-none border-r pr-2 border-secondary-100"
                                        >
                                            {{ getFullName(getPassenger(passenger_pk)) }}
                                        </div>
                                    </div>
                                </div>
                                <div class="text-xs font-medium px-2.5 py-1 rounded-md bg-secondary-50 text-primary">
                                    PNR: {{ additional.pnr }}
                                </div>
                            </div>
                            <div class="grid grid-cols-3 gap-3 mt-4">
                                <FormField
                                    class="text-xs font-medium"
                                    :form="additionalForm"
                                    :field="`pnrs.${$i}.voucher_price`"
                                    label="Voucher price"
                                >
                                    <InputMoney
                                        v-model="additional.voucher_price"
                                        placeholder="Type here..."
                                    />
                                </FormField>
                                <FormField
                                    class="text-xs font-medium"
                                    :form="additionalForm"
                                    :field="`pnrs.${$i}.net_amount`"
                                    label="Net"
                                >
                                    <InputMoney
                                        v-model="additional.net_amount"
                                        placeholder="Type here..."
                                    />
                                </FormField>
                                <FormField
                                    class="text-xs font-medium"
                                    :form="additionalForm"
                                    :field="`pnrs.${$i}.points_trade_amount`"
                                    label="Points trade"
                                >
                                    <InputMoney
                                        v-model="additional.points_trade_amount"
                                        placeholder="Type here..."
                                    />
                                </FormField>
                                <FormField
                                    class="text-xs font-medium"
                                    :form="additionalForm"
                                    :field="`pnrs.${$i}.emd_amount`"
                                    label="EMD"
                                >
                                    <InputMoney
                                        v-model="additional.emd_amount"
                                        placeholder="Type here..."
                                    />
                                </FormField>
                                <FormField
                                    class="text-xs font-medium"
                                    :form="additionalForm"
                                    :field="`pnrs.${$i}.upgrade_amount`"
                                    label="Upgrade"
                                >
                                    <InputMoney
                                        v-model="additional.upgrade_amount"
                                        placeholder="Type here..."
                                    />
                                </FormField>

                                <FormField
                                    class="text-xs font-medium"
                                    :form="additionalForm"
                                    :field="`pnrs.${$i}.credit_with`"
                                    label="Credit with"
                                >
                                    <InputSelect
                                        v-model="additional.credit_with"
                                        :options="[
                                            {title:'Airline',value: VoucherCreditWith.Airline},
                                            {title: 'InHouse', value: VoucherCreditWith.InHouse},
                                            {title: 'Company', value: VoucherCreditWith.Company}
                                        ]"
                                        with-empty
                                        placeholder="Credit with"
                                    />
                                </FormField>

                                <FormField
                                    class="text-xs font-medium"
                                    :form="additionalForm"
                                    :field="`pnrs.${$i}.fop`"
                                    label="Form of payment"
                                >
                                    <InputText v-model="additional.fop" />
                                </FormField>
                                <FormField
                                    class="text-xs font-medium"
                                    :form="form"
                                    :field="`airline_pks`"
                                    label="Airlines"
                                >
                                    <div class="flex flex-wrap gap-1">
                                        <div
                                            v-for="airline_pk in additional.airline_pks"
                                            :key="airline_pk"
                                            class="badge --primary"
                                        >
                                            {{ getAirlineByPk(airline_pk) }}
                                        </div>
                                    </div>
                                </FormField>

                                <FormField
                                    class="text-xs font-medium"
                                    :form="additionalForm"
                                    :field="`pnrs.${$i}.pcc`"
                                    label="PCC"
                                >
                                    <div class="flex flex-wrap gap-1">
                                        <div
                                            v-for="consolidator_area_pk in additional.pcc"
                                            :key="consolidator_area_pk"
                                            class="badge --primary"
                                        >
                                            {{ consolidatorAreaDictionary.find(consolidator_area_pk)?.name }}
                                        </div>
                                    </div>
                                </FormField>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="w-full flex justify-between">
                <AppButton @click="closeModal">
                    Close
                </AppButton>
                <AppButton class="--primary" @click="submit">
                    Apply
                </AppButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import { toastInfo, toastSuccess, toastWarning } from '@/lib/core/helper/ToastHelper'
import { getFullName } from '~/lib/Helper/PersonHelper'
import FormField from '~/components/Form/FormField.vue'
import type SelectOption from '~types/structures/SelectOption'
import { useVoucherFormHelper } from '~/sections/Voucher/composable/useVoucherFormHelper'
import { unique } from '~/lib/Helper/ArrayHelper'
import { VoucherCreditWith } from '~/api/models/Voucher/Voucher'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'

defineOptions({
    name: 'VoucherEditModal',
})
const props = defineProps<{
    voucherPk: PrimaryKey,
}>()

const emit = defineEmits<{
    'close': []
}>()

type FormDataPnrItem = {
    id: number,
    pnr: string,
    voucher_price: number | undefined,
    net_amount: number | undefined,
    emd_amount: number | undefined,
    points_trade_amount: number | undefined,
    upgrade_amount: number | undefined,
    fop: string | undefined,
    pcc: PrimaryKey[] | undefined,
    credit_with: string | undefined,
    airline_pks: PrimaryKey[],
}

const { record: voucher, useModel, useDictionary, hasPermission } = await useNewContext('Voucher', props.voucherPk)

const voucherModel = useModel('Voucher').useRecord({
    with: ['additional'],
})

const passengersModel = useModel('SaleVersionPassenger').useList()

const agentDictionary = useDictionary('Agent')
const consolidatorAreaDictionary = useDictionary('ConsolidatorArea')
const airlineDictionary = useDictionary('Airline')

const voucherStatusOptions = useGeneralDictionary('VoucherStatus').mapRecords.forSelect() as SelectOption[]
const createdByInfo = computed(() => {
    return agentDictionary.find(voucher.created_by_pk)
})

const {
    modifyDescriptionWarning,
    initialVoucherDescription,
    composeDescriptionText,
    getVoucherExpiresDateInSeconds,
} = useVoucherFormHelper()

const header = computed(() => {
    return `Edit Voucher #${voucher.voucher_id}`
})

//
initialVoucherDescription.value = voucher.description
const initialExpireAt = ref(voucher.expires_at)

const form = useForm({
    voucher_pk: props.voucherPk,
    status: voucher.status ?? '',
    credit_with: voucher.credit_with ?? '',
    issued_at: voucher.issued_at,
    expires_at: voucher.expires_at,
    executor_pk: voucher.executor_pk,
    voucher_price: voucher.voucher_price,
    description: voucher.description,
})

const additionalForm = useForm<{ pnrs: FormDataPnrItem[] }>({
    pnrs: [],
}, {
    pnrs: ValidationRules.Array(
        {
            voucher_price: ValidationRules.Required(),
            net_amount: ValidationRules.Required('Net price required', true),
            emd_amount: ValidationRules.Required('EMD amount is required', true),
            points_trade_amount: ValidationRules.Required('Points trade amount is required', true),
            upgrade_amount: ValidationRules.Required('Upgrade amount is required', true),
            credit_with: ValidationRules.Required(),
            fop: ValidationRules.Required(),
            issued_at: ValidationRules.Required(),
            expires_at: ValidationRules.Required(),
            description: ValidationRules.Required(),
        },
    ),
})

const suspense = useSuspensableComponent(async () => {
    await voucherModel.fetch(props.voucherPk)

    const passengers_pks = voucherModel.record.value.additional.map((pnr) => {
        return pnr.passengers_pks
    }).flatMap(item => item)
    await passengersModel.fetch({
        pks: unique(passengers_pks),
    })

    additionalForm.data.pnrs = voucherModel.record.value.additional.map((additional) => {
        return {
            ...additional,
            airline_pks: additional.airline_pks.length == 0 ? ['Any'] : additional.airline_pks,
        }
    })
})

const summary = computed(() => {
    const result = {
        voucher_price: 0,
        net_amount: 0,
        emd_amount: 0,
        points_trade_amount: 0,
        upgrade_amount: 0,
    }

    for (const pnr of additionalForm.data.pnrs) {
        result.voucher_price += pnr.voucher_price
        result.net_amount += pnr.net_amount
        result.emd_amount += pnr.emd_amount
        result.points_trade_amount += pnr.emd_amount
        result.upgrade_amount += pnr.upgrade_amount
    }

    return result
})

const submit = form.useSubmit(async (data) => {
    await useModel('Voucher').actions.editVoucher({
        pk: data.voucher_pk,
        data: {
            status: data.status,
            issued_at: data.issued_at,
            executor_pk: data.executor_pk,
            description: data.description,
            expires_at: data.expires_at,
        },
        pnrs: additionalForm.data.pnrs.map((formItem: FormDataPnrItem) => {
            return {
                id: formItem.id,
                voucher_price: formItem.voucher_price ?? 0,
                net_amount: formItem.net_amount ?? 0,
                emd_amount: formItem.emd_amount ?? 0,
                points_trade_amount: formItem.points_trade_amount ?? 0,
                upgrade_amount: formItem.upgrade_amount ?? 0,
                fop: formItem.fop,
                credit_with: formItem.credit_with,
            }
        }),
    })

    toastSuccess('Voucher is updated')

    useService('event').emit('voucherListUpdate')

    closeModal()
}, {
    onValidate: () => additionalForm.validate(),
})

//

function closeModal() {
    emit('close')
}

function expirationUpdate(timestamp: number) {
    if (initialVoucherDescription.value !== composeDescriptionText(initialExpireAt.value)) {
        toastWarning(modifyDescriptionWarning)

        return
    }

    if (initialVoucherDescription.value !== form.data.description) {
        toastWarning(modifyDescriptionWarning)

        return
    }
    const description = composeDescriptionText(timestamp)
    initialVoucherDescription.value = description
    initialExpireAt.value = timestamp
    form.data.description = description
    toastInfo('Voucher description was updated')
}

function issuedUpdate(timestamp: number) {
    form.data.expires_at = getVoucherExpiresDateInSeconds(timestamp)
    expirationUpdate(form.data.expires_at)
    toastInfo('Voucher Expiration date was updated')
}

function getPassenger(pk: PrimaryKey) {
    const result = passengersModel.records.find(passenger => pk === usePk(passenger))

    return result
}

function getAirlineByPk(pk: PrimaryKey) {
    return pk === 'Any' ? pk : airlineDictionary.find(pk)?.code
}
</script>
