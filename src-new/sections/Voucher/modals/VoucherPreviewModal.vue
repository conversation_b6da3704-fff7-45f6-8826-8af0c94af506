<template>
    <AppModalWrapper
        class="!max-w-[800px]"
        :header="header"
    >
        <div class="flex flex-col gap-y-2">
            <FormField
                v-if="sendToClient"
                :form="form"
                :field="'client_email'"
                label="Email"
                required
                class="max-w-[250px] pl-4"
            >
                <InputText v-model="form.data.client_email" />
            </FormField>
            <div class="border-t">
                <EmailTemplatePreview
                    class="h-[730px]"
                    :email-template="EmailTemplateName.Voucher"
                    :context="context"
                />
            </div>
        </div>
        <template v-if="sendToClient" #footer>
            <div class="flex justify-between">
                <AppButton @click="close">
                    Close
                </AppButton>
                <div class="flex gap-2">
                    <AppButton
                        class="--success"
                        :loading="downloadIsLoading"
                        @click="download"
                    >
                        Download
                    </AppButton>
                    <AppButton
                        class="--primary"
                        :loading="sendToMeIsLoading"
                        @click="sendToMe"
                    >
                        Send to me
                    </AppButton>
                    <AppButton class="--primary" @click="submit">
                        Send to Client
                    </AppButton>
                </div>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import EmailTemplatePreview from '~/components/Page/Email/EmailTemplatePreview.vue'
import { EmailTemplateName } from '~/api/models/Email/EmailTemplate'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import FormField from '~/components/Form/FormField.vue'
import { downloadBlob } from '@/lib/core/helper/File/FileHelpers'
import { base64ToBlob } from '~/lib/Helper/File/FileHelper'
import { preventDuplication } from '~/lib/Helper/PromiseHelper'

defineOptions({
    name: 'VoucherPreviewModal',
    modal: {
        promise: true,
    },
})

const props = defineProps<{
    voucherPk: PrimaryKey,
    voucherId: string,
    sendToClient: boolean,
    clientEmail?: string,
}>()

const emit = defineEmits<{
    'close': [],
    'resolve': [],

}>()

const { useModel } = useContext()

const context = computed(() => {
    return { voucher_pk: props.voucherPk }
})

const header = computed(() => {
    return `Preview Voucher #${props.voucherId}`
})

function close() {
    emit('close')
}

const form = useForm({
    client_email: props.clientEmail ?? '',
    voucher_pk: props.voucherPk,
}, {
    client_email: ValidationRules.Required('Email is required'),
})

const submit = form.useSubmit(async (data) => {
    await useModel('Voucher').actions.sendToClient({
        pk: data.voucher_pk,
        email: data.client_email,
    })

    emit('resolve')
})

const sendToMeIsLoading = ref(false)

const sendToMe = async () => {
    await preventDuplication(async () => {
        await useModel('Voucher').actions.sendToMe({
            pk: form.data.voucher_pk,
        })
    }, sendToMeIsLoading)

    emit('resolve')
}

const downloadIsLoading = ref(false)

const download = async () => {
    await preventDuplication(async () => {
        const { result } = await useModel('Voucher').actions.download({
            pk: form.data.voucher_pk,
        })

        downloadBlob(base64ToBlob(result), `${props.voucherId}.pdf`)
    }, downloadIsLoading)
}
</script>
