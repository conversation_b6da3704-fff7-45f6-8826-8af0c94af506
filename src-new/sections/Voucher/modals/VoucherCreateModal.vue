<template>
    <AppModalWrapper
        class="!max-w-[1340px]"
        :header="header"
    >
        <SuspenseManual :state="suspense">
            <template #default>
                <div class="p-2 bg-secondary-50 dark:bg-dark-7 border-t">
                    <div class="grid grid-cols-2 gap-x-2">
                        <div class="flex flex-col gap-y-2">
                            <div class="px-5 py-4 rounded-md bg-white dark:bg-dark-6 mr-3">
                                <FormField
                                    class="text-xs font-medium"
                                    :form="form"
                                    :field="'project_pk'"
                                >
                                    <div class="flex items-center">
                                        <span class="font-bold">
                                            Select Company
                                        </span>
                                        <InputRadioButton
                                            v-for="project in projectOptions"
                                            :key="project.value"
                                            v-model="form.data.project_pk"
                                            class="ml-4"
                                            name="project_select"
                                            :value="project.value"
                                            @update:model-value="() => fetchVoucherId()"
                                        >
                                            {{ project.title }}
                                        </InputRadioButton>
                                    </div>
                                </FormField>
                            </div>

                            <div class="flex flex-col gap-y-4 px-5 py-4 rounded-md bg-white dark:bg-dark-6 mr-3">
                                <div class="flex items-center justify-between">
                                    <div class="font-medium text-sm">
                                        Voucher <span class="font-semibold">#{{ voucherId }}</span>
                                    </div>
                                    <div class="flex gap-4">
                                        <FormField
                                            class="text-xs font-medium flex gap-2 mt-3"
                                            :form="form"
                                            :field="'send_to_client'"
                                            label="Send to client"
                                        >
                                            <InputCheckbox
                                                v-model="form.data.send_to_client"
                                                :disabled="!hasPermission('manageVoucher', 'Voucher')"
                                            />
                                        </FormField>

                                        <label class="text-xs font-medium flex gap-2 mt-3">
                                            Create expected amount
                                            <InputCheckbox
                                                v-model="createExpectedAmount"
                                            />
                                        </label>
                                    </div>
                                </div>
                                <div class="mt-3 grid grid-cols-3 gap-x-3 gap-y-2">
                                    <div class="flex justify-between border-b border-secondary-100 pb-1">
                                        <span class="text-secondary text-xs">Voucher price</span>
                                        <span class="text-xs">{{ $format.money(summary.voucher_price) }}</span>
                                    </div>

                                    <div class="flex justify-between border-b border-secondary-100 pb-1">
                                        <span class="text-secondary text-xs">Net</span>
                                        <span class="text-xs">{{ $format.money(summary.net_amount) }}</span>
                                    </div>

                                    <div class="flex justify-between border-b border-secondary-100 pb-1">
                                        <span class="text-secondary text-xs">Points trade</span>
                                        <span class="text-xs">{{
                                            $format.money(summary.points_trade_amount)
                                        }}</span>
                                    </div>

                                    <div class="flex justify-between border-b border-secondary-100 pb-1">
                                        <span class="text-secondary text-xs">EMD</span>
                                        <span class="text-xs">{{ $format.money(summary.emd_amount) }}</span>
                                    </div>

                                    <div class="flex justify-between border-b border-secondary-100 pb-1">
                                        <span class="text-secondary text-xs">Upgrade</span>
                                        <span class="text-xs">{{ $format.money(summary.upgrade_amount) }}</span>
                                    </div>
                                </div>
                                <div
                                    v-if="validateAmounts.length"
                                    class="w-full border border-dashed border-warning-600 dark:border-warning-600 rounded-lg relative px-4 py-3"
                                >
                                    <div class="badge --warning absolute -top-2 --xs">
                                        Warning
                                    </div>
                                    <div
                                        v-for="warning in validateAmounts"
                                        :key="warning.title"
                                        class="flex gap-2"
                                    >
                                        <div>{{ warning.title }} more than in sale:</div>
                                        <div>Max: {{ $format.money(ammountsValidation[warning.key]) }}</div>
                                        <div>Overflow: {{ $format.money(warning.error_value) }}</div>
                                    </div>
                                </div>
                            </div>

                            <div class="overflow-y-auto fancy-scroll h-flex pr-2 flex flex-col gap-y-2">
                                <div class="rounded-md px-5 py-4 bg-white dark:bg-dark-6 grid grid-cols-2 gap-3">
                                    <FormField
                                        class="text-xs font-medium"
                                        :form="form"
                                        :field="'executor_pk'"
                                        label="Executor"
                                    >
                                        <InputAgent
                                            v-model="form.data.executor_pk"
                                            placeholder="Executor"
                                        />
                                    </FormField>
                                    <div />
                                    <FormField
                                        class="text-xs font-medium"
                                        :form="form"
                                        :field="'issued_at'"
                                        label="Issued at"
                                    >
                                        <InputDate
                                            v-model:timestamp="form.data.issued_at"
                                            placeholder="Issued at"
                                            timezone="UTC"
                                            @update:timestamp="issuedUpdate"
                                        />
                                    </FormField>
                                    <FormField
                                        class="text-xs font-medium"
                                        :form="form"
                                        :field="'expires_at'"
                                        label="Expires at"
                                    >
                                        <InputDate
                                            v-model:timestamp="form.data.expires_at"
                                            placeholder="Expires at"
                                            timezone="UTC"
                                            @update:timestamp="expirationUpdate"
                                        />
                                    </FormField>

                                    <FormField
                                        class="text-xs font-semibold"
                                        :form="form"
                                        :field="'sale_card_pk'"
                                        label="Card"
                                    >
                                        <InputSelect
                                            v-model="form.data.sale_card_pk"
                                            :options="selectCardOptions"
                                            with-empty
                                        />
                                    </FormField>
                                    <FormField
                                        class="text-xs font-medium"
                                        :form="form"
                                        :field="'card_holder_name'"
                                        label="Card holder name"
                                    >
                                        <InputText
                                            v-model="form.data.card_holder_name"
                                            placeholder="Card holder"
                                        />
                                    </FormField>

                                    <FormField
                                        class="text-xs font-medium"
                                        :form="form"
                                        :field="'first_address'"
                                        label="Address line 1"
                                    >
                                        <InputText
                                            v-model="form.data.first_address"
                                            placeholder="Address"
                                        />
                                    </FormField>
                                    <FormField
                                        class="text-xs font-medium"
                                        :form="form"
                                        :field="'second_address'"
                                        label="Address line 2"
                                    >
                                        <InputText
                                            v-model="form.data.second_address"
                                            placeholder="Address"
                                        />
                                    </FormField>
                                </div>
                                <FormField
                                    v-if="form.data.passengers"
                                    :form="form"
                                    :field="'passengers'"
                                    class="flex flex-col gap-y-2"
                                >
                                    <div
                                        v-for="(group, index) in Object.keys(form.data.passengers)"
                                        :key="index"
                                        class="w-full min-h-[50px] relative rounded-md px-5 py-4 bg-white dark:bg-dark-6"
                                    >
                                        <div
                                            v-for="(passenger, passengerIndex) in form.data.passengers[group]"
                                            :key="passengerIndex"
                                            class="flex justify-between items-center w-full mb-2 last:mb-0"
                                        >
                                            <FormField
                                                class="text-xs font-medium "
                                                :form="form"
                                                :field="`passengers.${group}.${passengerIndex}.is_selected`"
                                            >
                                                <label class="flex gap-4">
                                                    <InputCheckbox
                                                        v-model="passenger.is_selected"
                                                        :disabled="!passenger.is_available"
                                                    />

                                                    <div class="text-xs pr-8 font-semibold w-[200px] truncate">
                                                        {{ getFullName(passenger) }}
                                                    </div>
                                                </label>
                                            </FormField>

                                            <div class="flex gap-x-6 items-center">
                                                <FormField
                                                    class="text-xs font-medium min-w-[250px]"
                                                    :form="form"
                                                    :field="`passengers.${group}.${passengerIndex}.airline_pks`"
                                                >
                                                    <InputSelect
                                                        v-model="passenger.airline_pks"
                                                        :options="getAirlineOptions(passenger.airline_pks)"
                                                        class="max-w-[250px]"
                                                        with-checkbox
                                                        multiple
                                                        filter
                                                        :disabled="!passenger.is_available"
                                                        :class="{
                                                            '!border-gray-300': passenger.airline_pks?.length > 0 || !passenger.is_available
                                                        }"
                                                        :with-empty="{ title: '--------', value: undefined }"
                                                        placeholder="Select airline"
                                                        @update:model-value="updatePassengerAirlines($event, passenger)"
                                                    />
                                                </FormField>
                                                <span
                                                    class="badge --xs --primary --soft ml-auto"
                                                    :class="{
                                                        'invisible': passengerIndex !== 0
                                                    }"
                                                >
                                                    {{ group }}
                                                </span>
                                            </div>
                                        </div>
                                        <AppTip
                                            type="info"
                                            :show-icon="false"
                                            class="items-center mt-4 px-4 py-1.5 dark:bg-dark-3 dark:text-secondary-100 dark:border-info-200"
                                        >
                                            Calculation is done per PAX
                                        </AppTip>
                                        <div class="grid grid-cols-4 mt-2 gap-3">
                                            <FormField
                                                class="text-xs font-medium"
                                                :form="form"
                                                :field="`pnrs.${group}.voucher_price`"
                                                label="Voucher price"
                                            >
                                                <InputMoney
                                                    v-model="form.data.pnrs[group].voucher_price"
                                                    placeholder="Voucher price"
                                                    :disabled="form.data.passengers[group].every( passenger => !passenger.is_selected)"
                                                />
                                            </FormField>
                                            <FormField
                                                class="text-xs font-medium"
                                                :form="form"
                                                :field="`pnrs.${group}.net_amount`"
                                                label="Net"
                                            >
                                                <InputMoney
                                                    v-model="form.data.pnrs[group].net_amount"
                                                    placeholder="Net amount"
                                                    :disabled="form.data.passengers[group].every( passenger => !passenger.is_selected)"
                                                />
                                            </FormField>
                                            <FormField
                                                class="text-xs font-medium"
                                                :form="form"
                                                :field="`pnrs.${group}.points_trade_amount`"
                                                label="Points trade"
                                            >
                                                <InputMoney
                                                    v-model="form.data.pnrs[group].points_trade_amount"
                                                    placeholder="Points trade"
                                                    :disabled="form.data.passengers[group].every( passenger => !passenger.is_selected)"
                                                />
                                            </FormField>
                                            <FormField
                                                class="text-xs font-medium"
                                                :form="form"
                                                :field="`pnrs.${group}.emd_amount`"
                                                label="EMD"
                                            >
                                                <InputMoney
                                                    v-model="form.data.pnrs[group].emd_amount"
                                                    placeholder="Emd amount"
                                                    :disabled="form.data.passengers[group].every( passenger => !passenger.is_selected)"
                                                />
                                            </FormField>

                                            <FormField
                                                class="text-xs font-medium"
                                                :form="form"
                                                :field="`pnrs.${group}.upgrade_amount`"
                                                label="Upgrade"
                                            >
                                                <InputMoney
                                                    v-model="form.data.pnrs[group].upgrade_amount"
                                                    placeholder="Upgrade amount"
                                                    :disabled="form.data.passengers[group].every( passenger => !passenger.is_selected)"
                                                />
                                            </FormField>

                                            <FormField
                                                class="text-xs font-medium"
                                                :form="form"
                                                :field="`pnrs.${group}.credit_with`"
                                                label="Credit with"
                                            >
                                                <InputSelect
                                                    v-model="form.data.pnrs[group].credit_with"
                                                    :options="[
                                                        {title:'Airline', value: VoucherCreditWith.Airline},
                                                        {title: 'InHouse', value: VoucherCreditWith.InHouse},
                                                        {title: 'Company', value: VoucherCreditWith.Company}
                                                    ]"
                                                    with-empty
                                                    placeholder="--------"
                                                    :disabled="form.data.passengers[group].every( passenger => !passenger.is_selected)"
                                                />
                                            </FormField>

                                            <FormField
                                                class="text-xs font-medium"
                                                :form="form"
                                                :field="`pnrs.${group}.pcc`"
                                                label="PCC"
                                            >
                                                <InputSelect
                                                    v-model="form.data.pnrs[group].pcc"
                                                    :options="consolidatorAreaOptions"
                                                    multiple
                                                    with-checkbox
                                                    with-empty
                                                    placeholder="--------"
                                                    :disabled="form.data.passengers[group].every( passenger => !passenger.is_selected)"
                                                />
                                            </FormField>

                                            <FormField
                                                class="text-xs font-medium "
                                                :form="form"
                                                :field="`pnrs.${group}.fop`"
                                                label="Form of payment"
                                            >
                                                <InputText
                                                    v-model="form.data.pnrs[group].fop"
                                                    :disabled="form.data.passengers[group].every( passenger => !passenger.is_selected)"
                                                />
                                            </FormField>
                                        </div>
                                    </div>
                                </FormField>
                                <div class="rounded-md px-5 py-4 bg-white dark:bg-dark-6">
                                    <div class="grid grid-cols-2 gap-3">
                                        <FormField
                                            v-if="hasPermission('manageVoucher', 'Voucher')"
                                            class="text-xs font-medium"
                                            :form="form"
                                            :field="'status'"
                                            label="Status"
                                        >
                                            <InputSelect
                                                v-model="form.data.status"
                                                :options="voucherStatusOptions"
                                            />
                                        </FormField>

                                        <FormField
                                            class="text-xs font-medium flex flex-col col-span-2"
                                            :form="form"
                                            :field="'description'"
                                            label="Voucher description"
                                        >
                                            <InputTextarea
                                                v-model="form.data.description"
                                                placeholder="Voucher description"
                                                rows="6"
                                            />
                                        </FormField>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="context.voucher_id && form.data.project_pk" class="p-4 bg-white dark:bg-dark-6 rounded-lg min-w-[650px]">
                            <EmailTemplatePreview
                                ref="emailTemplate"
                                class="h-[700px]"
                                :email-template="EmailTemplateName.Voucher"
                                :context="context"
                                auto-update
                            />
                        </div>
                        <div v-else-if="!form.data.project_pk" class="flex items-center justify-center p-4 bg-white dark:bg-dark-6 rounded-lg min-w-[650px]">
                            {{ projectValidationErrorText }}
                        </div>
                    </div>
                </div>
            </template>
            <template #fallback>
                <PlaceholderBlock class="w-full h-64" />
            </template>
        </SuspenseManual>
        <template #footer>
            <div class="flex justify-between">
                <AppButton @click="close">
                    Close
                </AppButton>
                <div class="flex gap-4">
                    <div class="flex gap-4">
                        <AppButton
                            v-tooltip="{ content: !form.data.project_pk ? projectValidationErrorText : undefined }"
                            :disabled="!form.data.project_pk"
                            @click="updatePreview"
                        >
                            Update preview
                        </AppButton>
                        <AppButton
                            v-tooltip="{ content: !form.data.project_pk ? projectValidationErrorText : undefined }"
                            :disabled="!form.data.project_pk"
                            class="--primary"
                            @click="submit"
                        >
                            Create Voucher
                        </AppButton>
                    </div>
                </div>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import { EmailTemplateName } from '~/api/models/Email/EmailTemplate'
import EmailTemplatePreview from '~/components/Page/Email/EmailTemplatePreview.vue'
import type { ModelFields, ModelRef } from '~types/lib/Model'
import { VoucherCreditWith, VoucherStatus } from '~/api/models/Voucher/Voucher'
import FormField from '~/components/Form/FormField.vue'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import { getFullName } from '~/lib/Helper/PersonHelper'
import { groupBy } from '~/lib/Helper/ArrayHelper'
import { toastError, toastInfo, toastSuccess, toastWarning } from '@/lib/core/helper/ToastHelper'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import { getCurrentDate } from '@/lib/core/helper/DateHelper'
import { useVoucherFormHelper } from '~/sections/Voucher/composable/useVoucherFormHelper'
import type SelectOption from '~types/structures/SelectOption'

defineOptions({
    name: 'VoucherCreateModal',
    modal: {
        promise: true,
    },
})

const props = defineProps<{
    salePk: PrimaryKey
}>()

const emit = defineEmits<{
    resolve: [{ createExpectedAmount: boolean, pk: PrimaryKey, voucher_id: string }],
    reject: [],
    close: []
}>()

type FormDataPnrItem = {
    pnr: string,
    voucher_price: number | undefined,
    net_amount: number | undefined,
    emd_amount: number | undefined,
    points_trade_amount: number | undefined,
    upgrade_amount: number | undefined,
    fop: string | undefined,
    pcc: PrimaryKey[] | undefined,
    credit_with: string | undefined,
}
type FormDataPnr = {
    [Key in string]: FormDataPnrItem
}

type FormDataPassenger = {
    pk: PrimaryKey,
    related_pnr: string,
    first_name: string,
    last_name: string,
    middle_name: string,
    is_available: boolean,
    is_selected: boolean,
    airline_pks: PrimaryKey[]
}

type FormDataPassengers = {
    [Key in string]: FormDataPassenger[] | undefined
}
type FormDataType = {
    sale_pk: PrimaryKey,
    executor_pk: PrimaryKey | undefined,
    issued_at: number | undefined,
    expires_at: number | undefined,
    passengers: FormDataPassengers | undefined,
    pnrs: FormDataPnr | undefined,
    description: string | undefined,
    send_to_client: boolean,
    status: VoucherStatus,
    project_pk: PrimaryKey | undefined,
    //card
    sale_card_pk: PrimaryKey | undefined,
    card_holder_name: string | undefined,
    first_address: string | undefined,
    second_address: string | undefined,
    voucher_price: number
    net_price: number
}

const { useModel, useDictionary, hasPermission } = await useNewContext('Sale', props.salePk)

const saleRecord = ref<ModelRef<'Sale', 'info' | 'saleVersion' | 'saleVersion.cards'> | undefined>()
const voucherId = ref<PrimaryKey>()

const projectValidationErrorText = 'Please choose a project type (TBC or ABT)'

const projectDictionary = useDictionary('Project')
const projectOptions = projectDictionary.mapRecords.forSelect() as SelectOption[]

// template text
const {
    modifyDescriptionWarning,
    initialVoucherDescription,
    composeDescriptionText,
    getVoucherExpiresDateInSeconds,
} = useVoucherFormHelper()

const now = getCurrentDate('UTC')
const nowInSeconds = now.unixTimestamp()
const voucherExpiresDateInSeconds = getVoucherExpiresDateInSeconds(nowInSeconds)

initialVoucherDescription.value = composeDescriptionText(voucherExpiresDateInSeconds)

const voucherStatusDictionary = useDictionary('VoucherStatus')
const voucherStatusOptions = voucherStatusDictionary.mapRecords.forSelect() as SelectOption[]

//

const ammountsValidation = ref<FormDataPnrItem>({} as FormDataPnrItem)
const validateAmounts = computed(() => {
    const result = []
    const fields = {
        voucher_price: {
            key: 'voucher_price',
            title: 'Total voucher price',
            error_value: 0,
        },
        net_amount: {
            key: 'net_amount',
            title: 'Total Net',
            error_value: 1,
        },
        emd_amount: {
            key: 'emd_amount',
            title: 'Total EMD',
            error_value: 0,
        },
        points_trade_amount: {
            key: 'points_trade_amount',
            title: 'Total Points trade',
            error_value: 0,
        },
        upgrade_amount: {
            key: 'upgrade_amount',
            title: 'Total Upgrade',
            error_value: 0,
        },
    }
    for (const key of Object.keys(fields)) {
        const validMax = ammountsValidation.value[key]
        let summ = 0
        for (const formPnr of selectedPnrs.value) {
            summ += formPnr[key]
        }

        if (summ > validMax) {
            fields[key].error_value = summ - validMax
            result.push(fields[key])
        }
    }

    return result
})

const airlineDictionary = useDictionary('Airline')

const suspense = useSuspensableComponent(async () => {
    await fetchVoucherInfo()
})

const emailTemplate = ref<{
    // eslint-disable-next-line
    updateTemplate: () => void
}>()

const selectCardOptions = computed(() => {
    if (!saleRecord.value) {
        return []
    }

    return saleRecord.value.saleVersion.cards.map((card) => {
        return {
            title: card.strip,
            value: usePk(card),
        }
    })
})

const availableAirlinePks = ref<PrimaryKey[]>()

const availableAirlineOptions = computed(() => {
    const options = [
        { title: `Any`, value: '_any_' },
    ]

    if (!availableAirlinePks.value) {
        return options
    }

    const mappedPks = availableAirlinePks.value.map((pk) => {
        const airline = airlineDictionary.findOrFail(pk)

        return { title: `(${airline.code}) ${airline.name}`, value: pk, disabled: false }
    })

    return [
        ...options,
        ...mappedPks,
    ]
})

const getAirlineOptions = (pks) => {
    return availableAirlineOptions.value.map((option) => ({
        ...option,
        disabled: option.value == '_any_' ? false : pks.includes('_any_'),
    }))
}

const header = computed(() => {
    return 'Create Voucher'
})

const context = computed(() => {
    return {
        sale_card_pk: form.data.sale_card_pk,
        first_address: form.data.first_address,
        second_address: form.data.second_address,
        card_holder_name: form.data.card_holder_name,
        card_number: (saleRecord.value?.saleVersion.cards.find(card => usePk(card) === form.data.sale_card_pk))?.strip || '',
        voucher_id: voucherId.value,
        issued_at: form.data.issued_at,
        expires_at: form.data.expires_at,
        model_name: 'Sale',
        model_pk: props.salePk,
        passengers: selectedPassengers.value,
        description: form.data.description,
        voucher_price: summary.value.voucher_price, //
        total_price: summary.value.total_price,
        project_pk: form.data.project_pk,
    }
})

const summary = computed(() => {
    const result = {
        voucher_price: 0,
        net_amount: 0,
        emd_amount: 0,
        points_trade_amount: 0,
        upgrade_amount: 0,
        total_price: 0,
    }

    result.total_price = selectedPassengers.value.reduce((acc, cur) => {
        const pnr = selectedPnrs.value.find(pnr => pnr.pnr === cur.related_pnr)

        if (pnr && pnr.voucher_price) {
            return acc += pnr.voucher_price
        }

        return acc
    }, 0)

    for (const pnr of selectedPnrs.value) {
        result.voucher_price += pnr.voucher_price || 0
        result.net_amount += pnr.net_amount || 0
        result.emd_amount += pnr.emd_amount || 0
        result.points_trade_amount += pnr.emd_amount || 0
        result.upgrade_amount += pnr.upgrade_amount || 0
    }

    return result
})

const validatePassengers = (value) => {
    const passengerSelected = Object.keys(value).some(key => {
        return value[key].some(item => item.is_selected)
    })

    if (!passengerSelected) {
        return 'At least one passenger must be selected'
    }

    const allSelectedHaveSelectedAirlines = Object.keys(value).every(key => {
        return value[key].every(item => {
            if (item.is_selected) {
                return item.airline_pks.length > 0
            }

            return true
        })
    })

    if (!allSelectedHaveSelectedAirlines) {
        return 'At least one airline should be select for every selected passenger'
    }
}

const validatePnrs = (value, field, form) => {
    const result = {}
    for (const pnr of Object.keys(value)) {
        const is_selected = selectedPnrs.value.find(item => item.pnr === pnr)

        if (is_selected) {
            if (!is_selected.voucher_price) {
                const errKey = `${pnr}.voucher_price`
                result[errKey] = () => 'Is required'
            }

            if (is_selected.emd_amount === undefined) {
                const errKey = `${pnr}.emd_amount`
                result[errKey] = () => 'Is required'
            }

            if (is_selected.points_trade_amount === undefined) {
                const errKey = `${pnr}.points_trade_amount`
                result[errKey] = () => 'Is required'
            }

            if (is_selected.upgrade_amount === undefined) {
                const errKey = `${pnr}.upgrade_amount`
                result[errKey] = () => 'Is required'
            }

            if (is_selected.net_amount === undefined) {
                const errKey = `${pnr}.net_amount`
                result[errKey] = () => 'Is required'
            }

            if (!is_selected.credit_with) {
                const errKey = `${pnr}.credit_with`
                result[errKey] = () => 'Is required'
            }

            if (!is_selected.fop) {
                const errKey = `${pnr}.fop`
                result[errKey] = () => 'Is required'
            }

            if (!is_selected.pcc || is_selected.pcc?.length === 0) {
                const errKey = `${pnr}.pcc`
                result[errKey] = () => 'Is required'
            }

            if (is_selected.voucher_price === 0) {
                const errKey = `${pnr}.voucher_price`
                result[errKey] = () => 'Is required'
            }
        }
    }

    if (Object.keys(result).length) {
        return result
    }

    return
}

const createExpectedAmount = ref(true)
const form = useForm<FormDataType>({
    sale_pk: props.salePk,
    executor_pk: undefined as PrimaryKey | undefined,
    issued_at: nowInSeconds,
    expires_at: voucherExpiresDateInSeconds || undefined,
    passengers: undefined,
    pnrs: undefined,

    description: initialVoucherDescription.value || undefined,
    send_to_client: false,
    status: VoucherStatus.Active,
    project_pk: undefined as PrimaryKey | undefined,
    //card
    sale_card_pk: undefined as PrimaryKey | undefined,
    card_holder_name: '',
    first_address: '',
    second_address: '',
    voucher_price: 0,
    net_price: 0,
}, {
    // voucher_price: ValidationRules.Required('Voucher price is required', true),
    credit_with: ValidationRules.Required(),
    issued_at: ValidationRules.Required(),
    expires_at: ValidationRules.Required(),
    passengers: [(value) => validatePassengers(value)],
    pnrs: [(value, field, form) => validatePnrs(value, field, form)],
    description: ValidationRules.Required(),
    sale_card_pk: ValidationRules.Required(),
    card_holder_name: ValidationRules.Required(),
    first_address: ValidationRules.Required('Address line 1 is required'),
    second_address: ValidationRules.Required('Address line 2 is required'),
    executor_pk: ValidationRules.Required(),
    project_pk: ValidationRules.Required(projectValidationErrorText),
})

// card selection

watch(() => form.data.sale_card_pk, (currentCardPk, previousCardPk) => {
    if (currentCardPk !== previousCardPk) {
        const selectedCard: ModelFields<'SaleVersionCard'> = saleRecord.value?.saleVersion.cards.find((card) => usePk(card) === currentCardPk)

        if (selectedCard) {
            form.data.card_holder_name = getFullName(selectedCard)
            form.data.first_address = selectedCard.street
            form.data.second_address = `${selectedCard.city}, ${selectedCard.state}, ${selectedCard.postal_code}`
        }
    }
}, { immediate: true })

//

function updatePreview() {
    emailTemplate.value?.updateTemplate()
}

async function fetchVoucherId() {
    const { voucher_id } = await useModel('Voucher').actions.getVoucherInfo({
        sale_pk: props.salePk,
        project_pk: form.data.project_pk!,
    })

    voucherId.value = voucher_id
}

async function fetchVoucherInfo() {
    saleRecord.value = (await useModel('Sale').useRecord({
        with: ['info', 'saleVersion', 'saleVersion.cards'],
    }).fetch(props.salePk)).value

    if (!form.data.project_pk) {
        form.data.project_pk =  saleRecord.value?.project_pk
    }

    const {
        voucher_id,
        passengers_info,
        available_airline_pks,
        sale_info,
    } = await useModel('Voucher').actions.getVoucherInfo({
        sale_pk: props.salePk,
        project_pk: form.data.project_pk!,
    })

    if (passengers_info.length === 0) {
        toastError('Please check sale tickets GDS PNRs')
    }

    voucherId.value = voucher_id

    form.data.pnrs = {}

    for (const passengerInfo of passengers_info) {
        if (form.data.pnrs[passengerInfo.related_pnr] === undefined) {
            form.data.pnrs[passengerInfo.related_pnr] = {
                net_amount: 0,
                emd_amount: 0,
                points_trade_amount: 0,
                upgrade_amount: 0,
                pnr: passengerInfo.related_pnr,
                voucher_price: 0,
                fop: undefined,
                pcc: undefined,
                credit_with: undefined,
            }
        }
    }

    form.data.passengers = groupBy(passengers_info.map(passengerInfo => {
        return { ...passengerInfo, is_selected: false, airline_pks: [passengerInfo.airline_pk]}
    }), 'related_pnr')

    form.data.executor_pk = sale_info.executor_pk

    form.data.voucher_price = saleRecord.value?.info.sell_price || 0
    form.data.net_price = saleRecord.value?.info.net_price || 0

    availableAirlinePks.value = available_airline_pks

    ammountsValidation.value = {
        emd_amount: sale_info.emd_amount || 0,
        pnr: '',
        points_trade_amount: sale_info.points_trade_amount || 0,
        upgrade_amount: sale_info.upgrade_amount || 0,
        voucher_price: saleRecord.value?.info.sell_price || 0,
        net_amount: saleRecord.value?.info.net_price || 0,
        fop: undefined,
        pcc: undefined,
        credit_with: undefined,
    }
}

const selectedPassengers = computed(() => {
    return form.data.passengers ? Object.values(form.data.passengers).flat().filter((passenger) => passenger.is_selected) : []
})

const selectedPnrs = computed(() => {
    const result = []

    for (const passenger of selectedPassengers.value) {
        if (!result.find(item => item.pnr === passenger.related_pnr)) {
            const pnrItem = form.data.pnrs[passenger.related_pnr]
            result.push(pnrItem)
        }
    }

    return result as FormDataPnrItem[]
})

const submit = form.useSubmit(async (data) => {
    // const selectedPassengers = data.passengers ? Object.values(data.passengers).flat().filter((passenger) => passenger.is_selected) : []

    if (Object.keys(data.passengers).length === 0) {
        toastError('Please check sale tickets GDS PNRs')

        return
    }

    if (selectedPassengers.value.length === 0) {
        toastError('Select at least one passenger')

        return
    }

    const pnrs_is_valid = selectedPnrs.value.every((pnr) => {
        return Object.keys(pnr).every((pnrKey) => {
            if (['voucher_price', 'net_amount', 'emd_amount', 'points_trade_amount', 'upgrade_amount'].includes(pnrKey)) {
                return true
            } else {
                return !!pnr[pnrKey] || pnr[pnrKey] === 0
            }
        })
    })

    if (!pnrs_is_valid) {
        toastError('Please check pnr values')

        return
    }

    const filteredAny = selectedPassengers.value.map(item => ({
        ...item,
        airline_pks: item.airline_pks.includes('_any_') ? [] : item.airline_pks,
    }))

    const { pk, voucher_id } = await useModel('Voucher').actions.createVoucher({
        sale_pk: props.salePk,
        executor_pk: data.executor_pk,
        expires_at: data.expires_at,
        issued_at: data.issued_at,
        status: data.status,
        passengers: filteredAny,
        pnrs: selectedPnrs.value.map((pnr: FormDataPnrItem) => pnr),
        description: data.description || '',
        send_to_client: data.send_to_client,
        project_pk: data.project_pk,
        // @ts-ignore
        template_context: context.value,
    })

    emit('resolve', {
        createExpectedAmount: createExpectedAmount.value,
        pk,
        voucher_id,
    })

    toastSuccess('Voucher is created')

    close()
}, {
    resetOnSuccess: false,
})

//

const consolidatorAreaDictionary = useDictionary('ConsolidatorArea')
const consolidatorAreaOptions = consolidatorAreaDictionary.mapRecords.forSelect()

//

function close() {
    emit('close')
}

//

function expirationUpdate(timestamp: number) {
    if (initialVoucherDescription.value !== form.data.description) {
        toastWarning(modifyDescriptionWarning)

        return
    }
    const description = composeDescriptionText(timestamp)
    initialVoucherDescription.value = description
    form.data.description = description
    toastInfo('Voucher description was updated')
}

function issuedUpdate(timestamp: number) {
    form.data.expires_at = getVoucherExpiresDateInSeconds(timestamp)
    expirationUpdate(form.data.expires_at)
    toastInfo('Voucher Expiration date was updated')
}

function updatePassengerAirlines(event: string[] | undefined, passenger) {
    if (!event) {
        passenger.airline_pks = []
    }

    if (event && event.includes('_any_')) {
        passenger.airline_pks = ['_any_']
    }
}
</script>
