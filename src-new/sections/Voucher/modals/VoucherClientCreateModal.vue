<template>
    <AppModalWrapper
        class="!max-w-[1340px]"
        :header="header"
    >
        <SuspenseManual :state="suspense">
            <template #default>
                <div class="p-2 bg-secondary-50 dark:bg-dark-7 border-t">
                    <div class="grid grid-cols-2 gap-x-2">
                        <div class="flex flex-col gap-y-2">
                            <div class="px-5 py-4 rounded-md bg-white dark:bg-dark-6 mr-3">
                                <FormField
                                    class="text-xs font-medium"
                                    :form="form"
                                    :field="'project_pk'"
                                >
                                    <div class="flex items-center">
                                        <span class="font-bold">
                                            Select Company
                                        </span>
                                        <InputRadioButton
                                            v-for="project in projectOptions"
                                            :key="project.value"
                                            v-model="form.data.project_pk"
                                            class="ml-4"
                                            name="project_select"
                                            :value="project.value"
                                            @update:model-value="() => fetchVoucherInfo()"
                                        >
                                            {{ project.title }}
                                        </InputRadioButton>
                                    </div>
                                </FormField>
                            </div>

                            <div class="flex items-center justify-between px-5 py-5 rounded-md bg-white dark:bg-dark-6 mr-3">
                                <div class="font-medium text-sm">
                                    Voucher <span class="font-semibold">#{{ voucherId }}</span>
                                </div>
                                <FormField
                                    class="text-xs font-medium flex gap-2"
                                    :form="form"
                                    :field="'send_to_client'"
                                    label="Send to client"
                                >
                                    <InputCheckbox
                                        v-model="form.data.send_to_client"
                                        :disabled="!hasPermission('manageVoucher', 'Voucher')"
                                    />
                                </FormField>
                            </div>

                            <div class="overflow-y-auto fancy-scroll h-flex min-h-[500px] pr-2 flex flex-col gap-y-2">
                                <div class="rounded-md px-5 py-4 bg-white dark:bg-dark-6 grid grid-cols-2 gap-3">
                                    <FormField
                                        class="text-xs font-medium"
                                        :form="form"
                                        :field="'executor_pk'"
                                        label="Executor"
                                    >
                                        <InputAgent
                                            v-model="form.data.executor_pk"
                                            placeholder="Executor"
                                        />
                                    </FormField>
                                    <div />
                                    <FormField
                                        class="text-xs font-medium"
                                        :form="form"
                                        :field="'issued_at'"
                                        label="Issued at"
                                    >
                                        <InputDate
                                            v-model:timestamp="form.data.issued_at"
                                            placeholder="Issued at"
                                            timezone="UTC"
                                            @update:timestamp="issuedUpdate"
                                        />
                                    </FormField>
                                    <FormField
                                        class="text-xs font-medium"
                                        :form="form"
                                        :field="'expires_at'"
                                        label="Expires at"
                                    >
                                        <InputDate
                                            v-model:timestamp="form.data.expires_at"
                                            placeholder="Expires at"
                                            timezone="UTC"
                                            @update:timestamp="expirationUpdate"
                                        />
                                    </FormField>

                                    <FormField
                                        class="text-xs font-semibold"
                                        :form="form"
                                        :field="'sale_card_pk'"
                                        label="Card"
                                    >
                                        <InputSelect
                                            v-model="form.data.sale_card_pk"
                                            :options="selectCardOptions"
                                            with-empty
                                            @update:model-value="cardSelected"
                                        />
                                    </FormField>
                                    <FormField
                                        class="text-xs font-medium"
                                        :form="form"
                                        :field="'card_holder_name'"
                                        label="Card holder name"
                                    >
                                        <InputText
                                            v-model="form.data.card_holder_name"
                                            placeholder="Card holder"
                                        />
                                    </FormField>

                                    <FormField
                                        class="text-xs font-medium"
                                        :form="form"
                                        :field="'first_address'"
                                        label="Address line 1"
                                    >
                                        <InputText
                                            v-model="form.data.first_address"
                                            placeholder="Address"
                                        />
                                    </FormField>
                                    <FormField
                                        class="text-xs font-medium"
                                        :form="form"
                                        :field="'second_address'"
                                        label="Address line 2"
                                    >
                                        <InputText
                                            v-model="form.data.second_address"
                                            placeholder="Address"
                                        />
                                    </FormField>
                                </div>

                                <div class="rounded-md px-5 py-4 bg-white dark:bg-dark-6">
                                    <div v-if="passengers.length" class="grid grid-cols-4 mt-2 gap-3">
                                        <FormField
                                            :form="form"
                                            field="passenger"
                                            label="Passenger"
                                            class="text-xs font-medium col-span-2"
                                        >
                                            <InputSelect
                                                v-model="form.data.passenger_pk"
                                                filter
                                                free-input
                                                with-empty
                                                :options="passengers"
                                                @update:model-value="onPassengerChange"
                                            />
                                        </FormField>
                                    </div>
                                    <div class="grid grid-cols-4 mt-2 gap-3">
                                        <FormField
                                            class="text-xs font-medium"
                                            :form="form"
                                            :field="`voucher_price`"
                                            label="Voucher price"
                                        >
                                            <InputMoney
                                                v-model="form.data.voucher_price"
                                                placeholder="Voucher price"
                                            />
                                        </FormField>
                                        <FormField
                                            class="text-xs font-medium"
                                            :form="form"
                                            :field="`net_amount`"
                                            label="Net"
                                        >
                                            <InputMoney
                                                v-model="form.data.net_amount"
                                                placeholder="Net amount"
                                            />
                                        </FormField>
                                        <FormField
                                            class="text-xs font-medium"
                                            :form="form"
                                            :field="`.points_trade`"
                                            label="Points trade"
                                        >
                                            <InputMoney
                                                v-model="form.data.points_trade_amount"
                                                placeholder="Points trade"
                                            />
                                        </FormField>
                                        <FormField
                                            class="text-xs font-medium"
                                            :form="form"
                                            :field="`emd_amount`"
                                            label="EMD"
                                        >
                                            <InputMoney
                                                v-model="form.data.emd_amount"
                                                placeholder="Emd amount"
                                            />
                                        </FormField>

                                        <FormField
                                            class="text-xs font-medium"
                                            :form="form"
                                            :field="`upgrade_amount`"
                                            label="Upgrade"
                                        >
                                            <InputMoney
                                                v-model="form.data.upgrade_amount"
                                                placeholder="Upgrade amount"
                                            />
                                        </FormField>

                                        <FormField
                                            class="text-xs font-medium"
                                            :form="form"
                                            :field="`credit_with`"
                                            label="Credit with"
                                        >
                                            <InputSelect
                                                v-model="form.data.credit_with"
                                                :options="[
                                                    {title:'Airline', value: VoucherCreditWith.Airline},
                                                    {title: 'InHouse', value: VoucherCreditWith.InHouse},
                                                    {title: 'Company', value: VoucherCreditWith.Company}
                                                ]"
                                                with-empty
                                            />
                                        </FormField>

                                        <FormField
                                            class="text-xs font-medium"
                                            :form="form"
                                            :field="`pcc`"
                                            label="PCC"
                                        >
                                            <InputSelect
                                                v-model="form.data.pcc"
                                                :options="consolidatorAreaOptions"
                                                multiple
                                                with-checkbox
                                                with-empty
                                            />
                                        </FormField>

                                        <FormField
                                            class="text-xs font-medium "
                                            :form="form"
                                            :field="`fop`"
                                            label="Form of payment"
                                        >
                                            <InputText
                                                v-model="form.data.fop"
                                            />
                                        </FormField>
                                    </div>
                                </div>

                                <div class="rounded-md px-5 py-4 bg-white dark:bg-dark-6">
                                    <div class="grid grid-cols-2 gap-3">
                                        <FormField
                                            v-if="hasPermission('manageVoucher', 'Voucher')"
                                            class="text-xs font-medium"
                                            :form="form"
                                            :field="'status'"
                                            label="Status"
                                        >
                                            <InputSelect
                                                v-model="form.data.status"
                                                :options="voucherStatusOptions"
                                            />
                                        </FormField>

                                        <FormField
                                            class="text-xs font-medium flex flex-col col-span-2"
                                            :form="form"
                                            :field="'description'"
                                            label="Voucher description"
                                        >
                                            <InputTextarea
                                                v-model="form.data.description"
                                                placeholder="Voucher description"
                                                rows="6"
                                            />
                                        </FormField>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="context.voucher_id && form.data.project_pk" class="p-4 bg-white dark:bg-dark-6 rounded-lg min-w-[650px]">
                            <EmailTemplatePreview
                                ref="emailTemplate"
                                class="h-[700px]"
                                :email-template="EmailTemplateName.Voucher"
                                :context="context"
                                auto-update
                            />
                        </div>
                        <div v-else-if="!form.data.project_pk" class="flex items-center justify-center p-4 bg-white dark:bg-dark-6 rounded-lg min-w-[650px]">
                            {{ projectValidationErrorText }}
                        </div>
                    </div>
                </div>
            </template>

            <template #fallback>
                <PlaceholderBlock class="w-full h-64" />
            </template>
        </SuspenseManual>

        <template #footer>
            <div class="flex justify-between">
                <AppButton @click="emit('close')">
                    Close
                </AppButton>
                <div class="flex gap-4">
                    <AppButton
                        v-tooltip="{ content: !form.data.project_pk ? projectValidationErrorText : undefined }"
                        :disabled="!form.data.project_pk"
                        @click="updatePreview"
                    >
                        Update preview
                    </AppButton>
                    <AppButton
                        v-tooltip="{ content: !form.data.project_pk ? projectValidationErrorText : undefined }"
                        :disabled="!form.data.project_pk"
                        class="--primary"
                        @click="submit"
                    >
                        Create Voucher
                    </AppButton>
                </div>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import { getFullName } from '~/lib/Helper/PersonHelper'
import { VoucherCreditWith, VoucherStatus } from '~/api/models/Voucher/Voucher'
import { EmailTemplateName } from '~/api/models/Email/EmailTemplate'
import EmailTemplatePreview from '~/components/Page/Email/EmailTemplatePreview.vue'
import FormField from '~/components/Form/FormField.vue'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import { useVoucherFormHelper } from '~/sections/Voucher/composable/useVoucherFormHelper'
import { getCurrentDate } from '@/lib/core/helper/DateHelper'
import type SelectOption from '~types/structures/SelectOption'
import { toastInfo, toastSuccess, toastWarning } from '@/lib/core/helper/ToastHelper'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'

defineOptions({
    name: 'VoucherClientCreateModal',
    modal: {
        promise: true,
    },
})

const props = defineProps<{
    clientPk: PrimaryKey
}>()

const emit = defineEmits<{
    resolve: [],
    reject: [],
    close: []
}>()

const { useModel, useDictionary, hasPermission, record: client, currentUserPk } = await useNewContext('Client', props.clientPk)

const clientWithTravelers = await useModel('Client').useRecord({ with: ['travelers']}).fetch(props.clientPk)

const passengers = computed(() => {
    return clientWithTravelers.value.travelers.map((passenger) => {
        return {
            title: getFullName(passenger),
            value: usePk(passenger),
        }
    })
})

const voucherId = ref<PrimaryKey>()
const clientCards = ref<{
    pk: PrimaryKey,
    strip: string,
    card_holder_name: string,
    first_address: string,
    second_address: string,
}[]>()

const suspense = useSuspensableComponent(async () => {
    await fetchVoucherInfo()
})

//

const consolidatorAreaDictionary = useDictionary('ConsolidatorArea')
const consolidatorAreaOptions = consolidatorAreaDictionary.mapRecords.forSelect()

// template text
const {
    modifyDescriptionWarning,
    initialVoucherDescription,
    composeDescriptionText,
    getVoucherExpiresDateInSeconds,
} = useVoucherFormHelper()

const now = getCurrentDate('UTC')
const nowInSeconds = now.unixTimestamp()
const voucherExpiresDateInSeconds = getVoucherExpiresDateInSeconds(nowInSeconds)

initialVoucherDescription.value = composeDescriptionText(voucherExpiresDateInSeconds)

const voucherStatusDictionary = useDictionary('VoucherStatus')
const voucherStatusOptions = voucherStatusDictionary.mapRecords.forSelect() as SelectOption[]

type FormDataType = {
    executor_pk: PrimaryKey | undefined,
    issued_at: number | undefined,
    expires_at: number | undefined,
    description: string | undefined,
    send_to_client: boolean,
    status: VoucherStatus,
    //card
    sale_card_pk: PrimaryKey | undefined,
    card_holder_name: string | undefined,
    first_address: string | undefined,
    second_address: string | undefined,

    // price
    passenger_pk: PrimaryKey | undefined,
    passenger_name: string | undefined,
    voucher_price: number | undefined,
    net_amount: number | undefined,
    emd_amount: number | undefined,
    points_trade_amount: number | undefined,
    upgrade_amount: number | undefined,
    fop: string | undefined,
    pcc: PrimaryKey[] | undefined,
    credit_with: string | undefined,
    project_pk: PrimaryKey | undefined,
}

const projectValidationErrorText = 'Please choose a project type (TBC or ABT)'

const projectDictionary = useDictionary('Project')
const projectOptions = projectDictionary.mapRecords.forSelect() as SelectOption[]

const form = useForm<FormDataType>({
    // sale_pk: props.salePk,
    executor_pk: currentUserPk,
    issued_at: nowInSeconds,
    expires_at: voucherExpiresDateInSeconds || undefined,

    description: initialVoucherDescription.value || undefined,
    send_to_client: false,
    status: VoucherStatus.Active,

    //card
    sale_card_pk: undefined as PrimaryKey | undefined,
    card_holder_name: '',
    first_address: '',
    second_address: '',

    passenger_pk: undefined as PrimaryKey | undefined,
    passenger_name: undefined as string | undefined,
    voucher_price: 0,
    net_amount: 0,
    emd_amount: 0,
    points_trade_amount: 0,
    upgrade_amount: 0,
    fop: undefined,
    pcc: undefined,
    credit_with: undefined,
    project_pk: undefined as PrimaryKey | undefined,
}, {
    credit_with: ValidationRules.Required(),

    voucher_price: ValidationRules.Required(),
    net_amount: ValidationRules.Required('Net price required', true),
    emd_amount: ValidationRules.Required('EMD required', true),
    points_trade_amount: ValidationRules.Required('Points trade required', true),
    upgrade_amount: ValidationRules.Required('Upgrade required', true),
    fop: ValidationRules.Required(),
    pcc: ValidationRules.Required(),
    issued_at: ValidationRules.Required(),
    expires_at: ValidationRules.Required(),
    description: ValidationRules.Required(),
    sale_card_pk: ValidationRules.Required(),
    card_holder_name: ValidationRules.Required(),
    first_address: ValidationRules.Required('Address line 1 is required'),
    second_address: ValidationRules.Required('Address line 2 is required'),
    executor_pk: ValidationRules.Required(),
    project_pk: ValidationRules.Required(projectValidationErrorText),
})

const emailTemplate = ref<{
    // eslint-disable-next-line
    updateTemplate: () => void
}>()

const selectCardOptions = computed(() => {
    if (!clientCards.value) {
        return []
    }

    return clientCards.value.map((card) => {
        return {
            title: card.strip,
            value: card.pk,
        }
    })
})

async function onPassengerChange(value: string | PrimaryKey) {
    const match = passengers.value.find(p => p.value === value)

    if (match) {
        form.data.passenger_pk = match.value
        form.data.passenger_name = match.title
    } else {
        form.data.passenger_pk = null
        form.data.passenger_name = value as string
    }
}

const header = computed(() => {
    if (form.data.passenger_name) {
        return `Create voucher for ${form.data.passenger_name}`
    }

    return `Create voucher for ${getFullName(client)}`
})

function cardSelected(pk: PrimaryKey) {
    const card = clientCards.value?.find((card) => card.pk === pk)

    if (!card) {
        return
    }

    form.data.card_holder_name = card.card_holder_name
    form.data.first_address = card.first_address
    form.data.second_address = card.second_address
}

async function fetchVoucherInfo() {
    if (!form.data.project_pk) {
        return
    }

    const {
        voucher_id,
        cards,
    } = await useModel('Voucher').actions.getVoucherClientInfo({
        client_pk: props.clientPk,
        project_pk: form.data.project_pk,
    })
    voucherId.value = voucher_id
    clientCards.value = cards
}

const context = computed(() => {
    return {
        sale_card_pk: form.data.sale_card_pk,
        first_address: form.data.first_address,
        second_address: form.data.second_address,
        card_holder_name: form.data.card_holder_name,
        card_number: (clientCards.value?.find(card => card.pk === form.data.sale_card_pk))?.strip || '',
        voucher_id: voucherId.value,
        issued_at: form.data.issued_at,
        expires_at: form.data.expires_at,
        description: form.data.description,
        voucher_price: form.data.voucher_price, //
        total_price: form.data.voucher_price,
        model_name: 'Client',
        model_pk: props.clientPk,
        project_pk: form.data.project_pk,
        passenger_pk: form.data.passenger_pk ?? null,
        passenger_name: form.data.passenger_pk ? null : form.data.passenger_name,
    }
})

function updatePreview() {
    emailTemplate.value?.updateTemplate()
}

const submit = form.useSubmit(async (data) => {
    //

    // createClientVoucher
    await useModel('Voucher').actions.createClientVoucher({
        client_pk: props.clientPk,
        data: {
            ...data,
            voucher_price: data.voucher_price ?? 0,
            net_amount: data.net_amount ?? 0,
            emd_amount: data.emd_amount ?? 0,
            points_trade_amount: data.points_trade_amount ?? 0,
            upgrade_amount: data.upgrade_amount ?? 0,
        },
    })

    toastSuccess('Voucher was created')
    emit('close')
})

//

function expirationUpdate(timestamp: number) {
    if (initialVoucherDescription.value !== form.data.description) {
        toastWarning(modifyDescriptionWarning)

        return
    }
    const description = composeDescriptionText(timestamp)
    initialVoucherDescription.value = description
    form.data.description = description
    toastInfo('Voucher description was updated')
}

function issuedUpdate(timestamp: number) {
    form.data.expires_at = getVoucherExpiresDateInSeconds(timestamp)
    expirationUpdate(form.data.expires_at)
    toastInfo('Voucher Expiration date was updated')
}
</script>

