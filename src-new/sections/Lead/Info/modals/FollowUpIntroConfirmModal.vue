<template>
    <AppModalWrapper
        :header="title || 'Email preview'"
        close-button
    >
        <template #header-append>
            <div class="ml-auto flex justify-end" />
        </template>
        <div v-if="emailTemplate !== EmailTemplateName.FollowUpTravelCash" class="border-t p-4">
            <div class="grid grid-cols-3 gap-5">
                <FormField
                    class="text-xs font-medium"
                    :form="form"
                    :field="'email'"
                    label="Client email"
                >
                    <ClientEmailDataDropdown
                        ref="emailRef"
                        class="max-w-[fit-content]"
                        :client-pk="lead.client_pk"
                        :with-edit="$can('edit', 'Lead', lead)"
                        with-remark
                        with-title
                        :default-email="lead.email.value"
                        :selected-email-pk="form.data.email_pk"
                        :on-select="({ email }) => {
                            form.data.email_pk = email.pk
                            emailRef?.close()
                        }"
                    />
                </FormField>
                <FormField
                    v-if="emailTemplate !== EmailTemplateName.FollowUpIntro"
                    class="text-xs font-medium"
                    :form="form"
                    :field="'subject'"
                    label="Subject Options"
                >
                    <InputSelect
                        v-model="form.data.subject"
                        :options="[
                            {title: 'Have you received the quotes?', value: EmailSubject.Quotes},
                            {title: 'Are you ready to secure your booking?', value: EmailSubject.Booking},
                            {title: 'I just tried calling you', value: EmailSubject.Call},
                            {title: 'Custom...', value: EmailSubject.Custom},
                        ]"
                    />
                </FormField>
                <FormField
                    class="text-xs font-medium"
                    :form="form"
                    :field="'type'"
                    label="Template type"
                >
                    <InputSelect
                        v-model="form.data.type"
                        :options="[
                            {title: 'Default', value: EmailType.Default},
                            {title: 'Simplified', value: EmailType.Simplified},
                        ]"
                    />
                </FormField>

                <FormField
                    v-if="form.data.subject === EmailSubject.Custom"
                    class="text-xs font-medium col-span-2"
                    :form="form"
                    :field="'custom_subject'"
                    label="Custom subject"
                >
                    <InputText
                        v-model="form.data.custom_subject"
                        placeholder="Default lead email"
                        :maxlength="250"
                    />
                </FormField>

                <div v-if="emailTemplate === EmailTemplateName.FollowUpIntro" />

                <div
                    class="h-full"
                    :class="{
                        'flex items-start pt-5': form.data.subject === EmailSubject.Custom
                    }"
                >
                    <div
                        class="flex justify-start gap-2"
                        :class="{
                            '!justify-center': form.data.subject === EmailSubject.Custom
                        }"
                    >
                        <AppButton @click.stop.prevent="openSelectPqsModal">
                            <PlusIcon />
                            Include PQ's
                        </AppButton>
                        <div
                            v-if="selectedPQPks"
                            class="text-xs text-secondary my-auto"
                        >
                            {{ selectedPQPks.length }} PQ's Included
                        </div>
                    </div>
                </div>
                <div v-if="projectFeatures.client_cabinet.enabled && emailTemplate === EmailTemplateName.FollowUpIntro" class="flex items-center gap-2 col-span-2">
                    <label class="text-xs font-medium">Add Travel Cash information</label>
                    <InputCheckbox v-model="form.data.include_travel_cash" />
                </div>
            </div>
        </div>

        <div class="border-t">
            <EmailTemplatePreview
                class="h-[630px]"
                :email-template="emailTemplate"
                :context="context"
                :email-type="form.data.type"
                :email-subject="form.data.subject"
                :email-selected-pq-pks="selectedPQPks ?? []"
                :include-travel-cash="form.data.include_travel_cash"
            />
        </div>

        <template #footer="{ close }">
            <div class="flex items-center gap-4">
                <AppButton class="mr-auto" @click="close">
                    Cancel
                </AppButton>
                <AppButton
                    class="--success"
                    @click="submitToMe"
                >
                    Send to me
                </AppButton>
                <AppButton class="--primary" @click="submit">
                    Send
                </AppButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import EmailTemplatePreview from '~/components/Page/Email/EmailTemplatePreview.vue'
import { EmailSubject, EmailTemplateName, EmailType } from '~/api/models/Email/EmailTemplate'
import FormField from '~/components/Form/FormField.vue'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import SelectPqModal from '~/sections/Lead/Info/modals/SelectPqModal.vue'
import ClientEmailDataDropdown from '~/components/ClientEmailData/ClientEmailDataDropdown.vue'

defineOptions({
    name: 'FollowUpIntroConfirmModal',
    modal: {
        promise: true,
    },
})

const props = defineProps<{
    taskPk: PrimaryKey,
    emailTemplate: EmailTemplateName,
    context?: any,
    leadPk: PrimaryKey,
}>()

const emit = defineEmits<{
    close: [],
}>()

const { useModel, useDictionary } = await useNewContext('Lead', props.leadPk)

//

const emailRef = ref<{
    close(): void
}>()

//

const { record: lead, fetch: fetchLead } = useModel('Lead').useRecord({
    with: ['email'],
}).destructable()

await fetchLead(props.context.lead_pk)

const projectFeatures = useDictionary('Project').findOrFail(lead.value.project_pk).features

const form = useForm({
    email_pk: lead.value.client_email_pk ?? '',
    subject: EmailSubject.Quotes,
    type: EmailType.Default,
    custom_subject: '',
    include_travel_cash: false,
}, {
    email: [ValidationRules.Required(), ValidationRules.Email()],
    custom_subject: ValidationRules.RequiredWhen(() => form.data.subject === EmailSubject.Custom, 'Subject can\'t be empty'),
})

//

const title = computed(() => {
    if (props.emailTemplate === EmailTemplateName.FollowUpIntro) {
        return 'Send follow-up intro email'
    } else if (props.emailTemplate === EmailTemplateName.FollowUp1) {
        return 'Send 1<sup>st</sup> follow-up email'
    } else if (props.emailTemplate === EmailTemplateName.FollowUp2) {
        return 'Send 2<sup>nd</sup> follow-up email'
    } else if (props.emailTemplate === EmailTemplateName.FollowUp3) {
        return 'Send 3<sup>rd</sup> follow-up email'
    }
})

const submit = form.useSubmit(async () => {
    await useModel('Task').actions.setFollowUpTaskStatus({
        task_pk: props.taskPk,
        email_pk: form.data.email_pk,
        send_to_me: false,
        selected_pqs: selectedPQPks.value?.map(Number) ?? null,
        subject: form.data.subject,
        type: form.data.type,
        custom_subject: form.data.custom_subject,
        include_travel_cash: form.data.include_travel_cash,
    })

    toastSuccess('FollowUp is sent')

    emit('close')
})

const submitToMe = form.useSubmit(async () => {
    await useModel('Task').actions.setFollowUpTaskStatus({
        task_pk: props.taskPk,
        email_pk: null,
        send_to_me: true,
        selected_pqs: selectedPQPks.value?.map(Number) ?? null,
        subject: form.data.subject,
        type: form.data.type,
        custom_subject: form.data.custom_subject,
        include_travel_cash: form.data.include_travel_cash,
    })

    toastSuccess('FollowUp sent to your email')

    emit('close')
})

// select pq's modal

const selectedPQPks = ref<PrimaryKey[]>()

const selectPqsModal = useModal(SelectPqModal)

async function openSelectPqsModal() {
    selectedPQPks.value = await selectPqsModal.open({
        leadPk: props.leadPk,
        selectedPqPks: selectedPQPks.value,
    })
}
</script>
