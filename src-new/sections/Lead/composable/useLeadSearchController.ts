import SearchController from '~/lib/Search/SearchController'
import NumberSearchTag from '~/lib/Search/Tag/NumberSearchTag'
import TextSearchTag from '~/lib/Search/Tag/TextSearchTag'
import DateRangeSearchTag from '~/lib/Search/Tag/DateRangeSearchTag'
import SelectSearchTag from '~/lib/Search/Tag/SelectSearchTag'
import AgentOrTeamSearchTag from '~/lib/Search/Tag/AgentOrTeamSearchTag'
import BooleanSelectSearchTag from '~/lib/Search/Tag/BooleanSelectSearchTag'
import NumberRangeSearchTag from '~/lib/Search/Tag/NumberRangeSearchTag'
import BooleanSearchTag from '~/lib/Search/Tag/BooleanSearchTag'
import { DepartmentName } from '~/api/models/Department/Department'
import LeadStatusTableColumn from '~/sections/Lead/components/LeadStatusTableColumn.vue'
import SortController from '~/lib/Search/SortController'
import { useLeadBulkStatusChange } from '~/sections/Lead/composable/useLeadBulkStatusChange'
import CountryOrRegionSearchTag from '~/lib/Search/Tag/CountryOrRegionSearchTag'
import { useRoute } from 'vue-router'
import useLeadManagementMode from '~/composables/useLeadManagementMode'
import BreakOutIcon from '~assets/icons/BreakOutIcon.svg?component'
import { TableColumnsController } from '~/lib/Table/TableColumnsController'

export function useLeadSearchController() {
    const {
        hasPermission,
        currentUserPk,
    } = useContext()

    const route = useRoute()

    const externalResourceDictionary = useGeneralDictionary('ExternalResource')
    const itineraryClassDictionary = useGeneralDictionary('ItineraryClass')
    const leadStatusDictionary = useGeneralDictionary('LeadStatus')
    const leadStarTypeDictionary = useGeneralDictionary('LeadStarType')
    const clientStatusDictionary = useGeneralDictionary('ClientStatus')
    const airlineDictionary = useGeneralDictionary('Airline')

    const projectsForSelect = useService('workspace').availableWorkspaces.map(info => {
        return {
            value: info.pk,
            title: info.title,
        }
    })
    const agent = useDictionary('Agent', {
        workspace: useService('workspace').getSelectedWorkspace(), // @todo Make it beautiful
    }).find(currentUserPk)!

    const isMarketingUser = computed(() => {
        if (!agent) {
            return false
        }

        return isUserInDepartment(agent, DepartmentName.Marketing)
    })

    const isLeadManagementMode = useLeadManagementMode(route)

    const searchController = SearchController.forModel('Lead', {
        id: new NumberSearchTag('ID'),
        from_iata_code: new TextSearchTag('Departure city'),
        to_iata_code: new TextSearchTag('Destination city'),
        pq_count: new NumberRangeSearchTag('PQ\'s count'),
        spq_count: new NumberRangeSearchTag('Sent PQ\'s count'),
        departure_date: new DateRangeSearchTag('Departure date'),
        return_date: new DateRangeSearchTag('Arrival date'),
        session_expire_at: new DateRangeSearchTag('Time left', {
            datePresets: ['oneDayAfter', 'twoDaysAfter', 'threeDaysAfter', 'oneWeekAfter', 'twoWeeksAfter', 'oneMonthAfter'],
        }).hideInSearch(isMarketingUser.value),
        external_resource: new SelectSearchTag('Resource', externalResourceDictionary.mapRecords.forSelect()),
        main_project_pk: new SelectSearchTag('Project', projectsForSelect),
        itinerary_class: new SelectSearchTag('Class', itineraryClassDictionary.mapRecords.forSelect()),
        follow_up_progress: new NumberRangeSearchTag('Progress'),
        created_at: new DateRangeSearchTag('Created at'),
        taken_date: new DateRangeSearchTag('Taken at'),
        client_fullname: new TextSearchTag('Client'),
        client_email: new TextSearchTag('Email'),
        client_phone: new TextSearchTag('Phone'),
        utm_source: new TextSearchTag('Source').hideInSearch(!hasPermission('readUTM', 'all')),
        utm_campaign: new TextSearchTag('Camp').hideInSearch(!isMarketingUser.value),
        utm_ga: new TextSearchTag('G.A.').hideInSearch(!isMarketingUser.value),
        utm_medium: new TextSearchTag('Medium').hideInSearch(!isMarketingUser.value),
        utm_term: new TextSearchTag('Term').hideInSearch(!isMarketingUser.value),
        remark: new TextSearchTag('Remark'),
        executor_pk: new AgentOrTeamSearchTag('Agent', { multiple: true }),
        status: new SelectSearchTag('Status', leadStatusDictionary.mapRecords.forSelect()),
        is_bonus: new BooleanSelectSearchTag('Bonus', ['Yes', 'No']).hideInSearch(isMarketingUser.value),
        star_type: new SelectSearchTag('Star type', leadStarTypeDictionary.mapRecords.forSelect()),
        is_to_be_lost: new BooleanSelectSearchTag(toBeLostLabel, ['Yes', 'No']).hideInSearch(true),
        client_status_pk: new SelectSearchTag('Client Status', clientStatusDictionary.mapRecords.forSelect()),
        is_client_reached: new BooleanSelectSearchTag('Client reached', ['Reached', 'Not Reached']),
        departure_country: new CountryOrRegionSearchTag('Departure country', 'departure').disable(!hasPermission('filterByCountryOrRegion', 'Lead')),
        destination_country: new CountryOrRegionSearchTag('Destination country', 'destination').disable(!hasPermission('filterByCountryOrRegion', 'Lead')),
        airline_pk: new SelectSearchTag('Airline', airlineDictionary.mapRecords.forSelectWithCode(), {
            applyCondition(tag, field, and) {
                tag.values && and.inCompact(field, tag.values)
            },
        }),
        is_in_queue: new BooleanSearchTag(isLeadManagementMode ? 'Is in queue' : 'Is in LM queue')
            .disable(!hasPermission('openPageLeadManagement', 'all'))
            .setDefaultValue(
                isLeadManagementMode ? [1] : undefined,
            ).setVisualGroup('Other'),
        use_archive: new BooleanSearchTag('Use archive').setVisualGroup('Other'),
        is_keep_client: new BooleanSearchTag('Keep Client').setVisualGroup('Other'),
        has_track_pq: new BooleanSearchTag('Has Track PQ').setVisualGroup('Other'),
        is_published: new BooleanSearchTag('Published Lead').setVisualGroup('Other').disable(!hasPermission('manage', 'all')),
        prediction_score: new NumberRangeSearchTag('Conversion probability (%)').disable(!hasPermission('manage', 'all')),
    }, {
        presets: {
            key: isLeadManagementMode ? 'leads-management' : 'leads',
        },
    })

    // sort controller

    const workspaces = useGeneralDictionary('Workspace').records

    const canReadUtm = computed(() => {
        return hasPermission('readUTM', 'all')
    })

    const statusChangeStore = useLeadBulkStatusChange()

    const columnsController = markRaw(new TableColumnsController(useTableColumns({
        id: {
            label: '#',
            sortable: true,
            width: 7,
        },
        from_iata_code: {
            label: 'From',
            sortable: true,
            width: 4,
        },
        to_iata_code: {
            label: 'To',
            sortable: true,
            width: 4,
        },
        pq_count: {
            label: 'PQs',
            sortable: true,
            tooltip: 'Total PQ\'s count',
            width: 3,
        },
        spq_count: {
            label: 'SPQs',
            tooltip: 'Sent PQ\'s count',
            sortable: true,
            width: 3,
        },
        departure_date: {
            label: 'Departure',
            sortable: true,
            width: 5,
        },
        return_date: {
            label: 'Return',
            sortable: true,
            width: 5,
        },
        session_expire_at: {
            label: 'Time left',
            sortable: true,
            width: 5,
        },
        external_resource: {
            label: 'Resource',
            sortable: true,
            width: 'min',
        },
        main_project_pk: {
            label: 'Project',
            sortable: Number,
            width: 'min',
            enabled: workspaces.length > 1,
        },
        social: {
            icon: h('div', { class: ' flex justify-between w-full' }, [h(MessageCircleIcon), ' / ', h(MailIcon), ' / ', h(PhoneIcon)]),
            tooltip: 'Messages/Emails/Calls',
            width: 'min',
        },
        itinerary_class: {
            icon: StarIcon,
            tooltip: 'Class',
            sortable: true,
            width: 1,
        },
        follow_up_progress: {
            icon: BreakOutIcon,
            tooltip: 'Follow-up progress',
            sortable: true,
            width: 1,
        },
        created_at: {
            label: 'Created',
            sortable: true,
            width: 5,
        },
        taken_date: {
            label: 'Taken',
            sortable: true,
            width: 5,
        },
        client_fullname: {
            label: 'Client',
            sortable: true,
            width: 14,
        },
        client_email: {
            label: 'Email',
            sortable: true,
            width: 15,
            enabled: () => !isMarketingUser.value,
        },
        client_phone: {
            label: 'Phone',
            sortable: true,
            width: 10,
            enabled: () => !isMarketingUser.value,
        },
        utm_source: {
            label: 'Src',
            tooltip: 'Utm source',
            sortable: true,
            width: 'min',
            enabled: canReadUtm.value,
        },
        utm_campaign: {
            label: 'Camp',
            sortable: true,
            width: 'min',
            enabled: () => isMarketingUser.value,
        },
        utm_ga: {
            label: 'G.A.',
            sortable: true,
            width: 'min',
            enabled: () => isMarketingUser.value,
        },
        utm_medium: {
            label: 'Medium',
            tooltip: 'Medium',
            sortable: true,
            width: 'min',
            enabled: () => isMarketingUser.value,
        },
        utm_term: {
            label: 'Term',
            sortable: true,
            width: 'min',
            enabled: () => isMarketingUser.value,
        },
        remark: {
            label: 'Remark',
            width: 9,
            sortable: true,
        },
        executor_pk: {
            label: 'Agent',
            sortable: true,
            width: 10,
        },
        status: {
            label: 'Status',
            sortable: false,
            width: 'min',
            component: (props: any) => h(LeadStatusTableColumn, {
                ...props,
                actionDisabled: workspaces.length > 1,
                statusChangeStore,
            }),
        },
    }, {
        pqs: {
            columns: [
                {
                    column: 'pq_count',
                    label: 'Total PQs count',
                },
                {
                    column: 'spq_count',
                    label: 'Sent PQs count',
                },
            ],
        },
    }), {
        id: { active: true, required: true },
        from_iata_code: { active: true },
        to_iata_code: { active: true },
        pq_count: { active: true },
        spq_count: { active: true },
        departure_date: { active: true },
        return_date: { active: true },
        session_expire_at: { active: true },
        external_resource: { active: true },
        main_project_pk: { active: true },
        social: { active: true },
        itinerary_class: { active: true },
        follow_up_progress: { active: true },
        created_at: { active: true },
        taken_date: { active: true },
        client_fullname: { active: true },
        client_email: { active: true },
        client_phone: { active: true },
        utm_source: { active: true },
        utm_campaign: { active: true },
        utm_ga: { active: true },
        utm_medium: { active: true },
        utm_term: { active: true },
        remark: { active: true },
        executor_pk: { active: true },
        status: { active: true },

    },
        isLeadManagementMode ? 'lead-management-list-page' : 'lead-list-page',
        isLeadManagementMode ? 'lead-management-list-page' : 'lead-list-page',
    {
        pqs: { active: true },
    }))

    const columns = columnsController.getColumnsDefinition()

    const sortController = SortController.fromColumns(columns, {
        syncWithQuery: true,
        autoHydrate: true,
    })

    return {
        searchController,
        sortController,
        columns,
        columnsController,
        statusChangeStore,
    }
}

const toBeLostLabel = 'To be lost'
export const toBeLostTagQuery = 'To be lost: Yes'

export type LeadSearchController = ReturnType<typeof useLeadSearchController>['searchController']
export type LeadSortController = ReturnType<typeof useLeadSearchController>['sortController']
export type LeadColumns = ReturnType<typeof useLeadSearchController>['columns']
export type LeadColumnsController = ReturnType<typeof useLeadSearchController>['columnsController']
