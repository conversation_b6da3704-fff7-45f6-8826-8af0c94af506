<template>
    <div>
        <div class="card">
            <div class="flex items-center justify-end gap-4 w-full">
                <SearchPresetsList class="mr-auto" :search-controller="searchController" />

                <div class="flex items-center gap-4 p-2 flex-none">
                    <AppPaginationInfo :pagination="leadsList?.pagination" />
                    <AppPaginationCompact :pagination="leadsList?.pagination" />
                    <div v-if="hasPermission('openPageLeadManagement')" class="button-group">
                        <AppButton
                            class="--small"
                            @click="openCheckLeadModal"
                        >
                            <SearchIcon />
                            Check lead
                        </AppButton>
                    </div>
                    <div v-if="!isMarketingUser" class="button-group">
                        <AppButton
                            v-if="!isFollowUpVisible"
                            class="--small"
                            @click="openFollowUp"
                        >
                            <MailIcon />
                            Follow-up
                        </AppButton>
                        <AppButton
                            v-if="isFollowUpVisible"
                            class="--small --primary"
                            @click="sendFollowUp"
                        >
                            <SendIcon />
                            Send follow-up
                        </AppButton>
                        <AppButton
                            v-if="isFollowUpVisible"
                            class="--small --only"
                            @click="closeFollowUp"
                        >
                            <XIcon />
                        </AppButton>
                    </div>

                    <AppPageSize :pagination="leadsList?.pagination" :options="[10, 19, 40, 100]" />
                    <TableColumnsConfigurationButton :controller="columnsController" />
                </div>
            </div>
            <div class="list-table-v2 table-fixed relative">
                <SuspenseManual :state="suspense">
                    <template #default>
                        <div class="overflow-x-auto fancy-scroll-x relative min-h-[390px]">
                            <LeadTableWrapper
                                :items="items"
                                :columns="columnsController"
                                :sort-controller="sortController"
                                :search-tags="searchController.tags"
                                :zebra="zebra"
                            >
                                <template #id="{item: leadRecord}: { item: Item }">
                                    <TableCellLink :to="linkToLead(leadRecord.pk)">
                                        {{ leadRecord.pk }}

                                        <div
                                            v-if="leadRecord.is_test"
                                            class="absolute top-0.5 right-0.5 bg-danger rounded text-3xs w-[fit-content] text-white font-semibold px-1.5 leading-normal self-center ml-2"
                                        >
                                            Test
                                        </div>
                                    </TableCellLink>
                                </template>
                                <template #from_iata_code="{item: leadRecord}: { item: Item }">
                                    <td>
                                        {{ leadRecord.from_iata_code }}
                                    </td>
                                </template>
                                <template #to_iata_code="{item: leadRecord}: { item: Item }">
                                    <td>
                                        {{ leadRecord.to_iata_code }}
                                    </td>
                                </template>
                                <template #pq_count="{item: leadRecord}: { item: Item }">
                                    <PQsDetails
                                        :pk="String(leadRecord.id)"
                                        :project-pk="Number(leadRecord.project_pk)"
                                        :price-quote-count="leadRecord.listInformation.count"
                                        :price-quote-last-time="leadRecord.listInformation.price_quote_last_time"
                                        :is-possible-to-add-pq="leadRecord.is_possible_to_add_pq"
                                        :is-mark-up-alert="leadRecord.listInformation.is_mark_up_alert"
                                    />
                                </template>
                                <template #spq_count="{item: leadRecord}: { item: Item }">
                                    <PQsDetails
                                        :pk="String(leadRecord.id)"
                                        :project-pk="Number(leadRecord.project_pk)"
                                        :price-quote-count="leadRecord.listInformation.sent_count"
                                        :price-quote-last-time="leadRecord.listInformation.price_quote_last_time"
                                        :is-possible-to-add-pq="leadRecord.is_possible_to_add_pq"
                                        only-sent-pq
                                    />
                                </template>
                                <template #departure_date="{item: leadRecord}: { item: Item }">
                                    <td>
                                        {{ $format.date(leadRecord.departure_date, 'UTC') }}
                                    </td>
                                </template>
                                <template #return_date="{item: leadRecord}: { item: Item }">
                                    <td>
                                        {{
                                            leadRecord.return_date ? $format.date(leadRecord.return_date, 'UTC') :
                                            dateFormatItinerary('', leadRecord.itinerary_type)
                                        }}
                                    </td>
                                </template>
                                <template #session_expire_at="{item: leadRecord}: { item: Item }">
                                    <td>
                                        <LeadClientSessionActivity
                                            v-if="leadRecord.client_session_activity_pk"
                                            :client-session-activity-pk="leadRecord.client_session_activity_pk"
                                            :is-bonus="leadRecord.is_bonus"
                                        />
                                    </td>
                                </template>
                                <template #external_resource="{item: leadRecord}: { item: Item }">
                                    <td class="text-primary">
                                        {{ externalResourceDictionary.tryFind(leadRecord.external_resource)?.title }}
                                    </td>
                                </template>
                                <template #main_project_pk="{item: leadRecord}: { item: Item }">
                                    <td v-if="workspaces.length > 1" class="text-center">
                                        <WorkspaceLabelComponent short :project-pk="leadRecord.project_pk" />
                                    </td>
                                </template>
                                <template #social="{item: leadRecord}: { item: Item }">
                                    <td>
                                        <div class="flex justify-between gap-2">
                                            <div
                                                class="text-center cursor-pointer "
                                                @click="openOrganizer(leadRecord, 'chat')"
                                            >
                                                <PlaceholderText v-if="leadMessageCounterManager.loading.value" :chars="2" />
                                                <template v-else>
                                                    {{ formatCount(leadMessageCounterManager.data.value[leadRecord.pk]) ?? 0 }}
                                                </template>
                                            </div>
                                            <div
                                                class="text-center cursor-pointer border-r border-l px-2.5"
                                                @click="openOrganizer(leadRecord, 'messages')"
                                            >
                                                <PlaceholderText v-if="leadMailCountersManager.loading.value" :chars="2" />
                                                <template v-else>
                                                    {{ formatCount(leadMailCountersManager.data.value[leadRecord.pk]) ?? 0 }}
                                                </template>
                                            </div>
                                            <div class="text-center cursor-pointer " @click="openOrganizer(leadRecord, 'sms')">
                                                <PlaceholderText v-if="leadCallCountersManager.loading.value" :chars="2" />
                                                <template v-else>
                                                    {{ formatCount(leadCallCountersManager.data.value[leadRecord.pk]) ?? 0 }}
                                                </template>
                                            </div>
                                        </div>
                                    </td>
                                </template>
                                <template #itinerary_class="{item: leadRecord}: { item: Item }">
                                    <td
                                        v-tooltip="itineraryClassDictionary.tryFind(leadRecord.itinerary_class)?.title"
                                        class="text-center"
                                    >
                                        {{ itineraryClassDictionary.tryFind(leadRecord.itinerary_class)?.title[0] }}
                                    </td>
                                </template>
                                <template #follow_up_progress="{item: leadRecord}: { item: Item }">
                                    <td
                                        class="text-xs cursor-pointer"
                                        @click="leadFollowUpProgressModel.open({
                                            leadPk: usePk(leadRecord)
                                        })"
                                    >
                                        <div class="flex gap-1">
                                            <span
                                                class="font-medium text-center min-w-[16px]"
                                                :class="leadRecord.listInformation.follow_up_completed_points === 0 ? 'text-secondary-300' : 'text-primary'"
                                            >{{ leadRecord.listInformation.follow_up_completed_points }}</span>
                                            /
                                            <span>{{ followUpMaxPoints }}</span>
                                        </div>
                                    </td>
                                </template>
                                <template #created_at="{item: leadRecord}: { item: Item }">
                                    <td>
                                        {{ $format.datetime(leadRecord.created_at) }}
                                    </td>
                                </template>
                                <template #taken_date="{item: leadRecord}: { item: Item }">
                                    <td>
                                        {{ $format.datetime(leadRecord.taken_date) }}
                                    </td>
                                </template>
                                <template #client_fullname="{item: leadRecord}: { item: Item }">
                                    <TableCellLink :to="linkToClient(leadRecord.client_pk)" @click="emit('close')">
                                        <div class="flex gap-1">
                                            <div
                                                :content="getClientFullName(leadRecord.clientPreview)"
                                                class="truncate max-w-[115px]"
                                            >
                                                {{ getClientFullName(leadRecord.clientPreview) }}
                                            </div>
                                            <div
                                                v-if="isMarketingUser"
                                                v-tooltip="{ content: leadRecord.email.value }"
                                                @click.stop.prevent="copyToClipboard(leadRecord.email.value)"
                                            >
                                                <MailIcon
                                                    class="w-3.5 h-3.5 !stroke-2 text-gray-500 hover:text-gray-700 cursor-pointer"
                                                />
                                            </div>
                                            <div
                                                v-if="isMarketingUser && leadRecord.phone?.value"
                                                v-tooltip="{ content: leadRecord.phone.value }"
                                                @click.prevent="copyToClipboard(leadRecord.phone.value)"
                                            >
                                                <SmartphoneIcon
                                                    class="w-3.5 h-3.5 !stroke-2 text-gray-500 hover:text-gray-700 cursor-pointer"
                                                />
                                            </div>
                                        </div>
                                    </TableCellLink>
                                </template>
                                <template #client_email="{item: leadRecord}: { item: Item }">
                                    <td v-if="!props.isMarketingUser">
                                        <HiddenDataComponent :hidden-value="leadRecord.email.value">
                                            <template #default>
                                                <a
                                                    class="cursor-pointer"
                                                    @click="copyLeadInfo(leadRecord.email.value, SuspiciousAction.LeadCopy, usePk(leadRecord))"
                                                    @copy="copyLeadInfo(leadRecord.email.value, SuspiciousAction.LeadCopy, usePk(leadRecord))"
                                                >
                                                    <div
                                                        :content="leadRecord.email.value"
                                                        class="truncate max-w-[115px]"
                                                    >
                                                        {{ leadRecord.email.value }}
                                                    </div>
                                                </a>
                                            </template>
                                            <template #hidden="{ formatted }">
                                                <a
                                                    class="cursor-pointer"
                                                    @click="copyLeadInfo(leadRecord.email.value, SuspiciousAction.LeadShow, usePk(leadRecord))"
                                                >
                                                    {{ formatted }}
                                                </a>
                                            </template>
                                        </HiddenDataComponent>
                                    </td>
                                </template>
                                <template #client_phone="{item: leadRecord}: { item: Item }">
                                    <td v-if="!props.isMarketingUser">
                                        <HiddenDataComponent
                                            v-if="leadRecord.phone"
                                            :hidden-value="leadRecord.phone.value"
                                        >
                                            <template #default>
                                                <a
                                                    class="cursor-pointer"
                                                    :href="getPhoneLink(leadRecord.phone.value)"
                                                    @click.prevent="copyLeadInfo(leadRecord.phone.value, SuspiciousAction.LeadCopy, usePk(leadRecord))"
                                                    @copy.prevent="copyLeadInfo(leadRecord.phone.value, SuspiciousAction.LeadCopy, usePk(leadRecord))"
                                                >
                                                    <div
                                                        :content="leadRecord.phone.value"
                                                        class="truncate max-w-[75px]"
                                                    >
                                                        {{ leadRecord.phone.value }}
                                                    </div>
                                                </a>
                                            </template>
                                            <template #hidden="{ formatted }">
                                                <a
                                                    class="cursor-pointer"
                                                    @click.prevent="copyLeadInfo(leadRecord.phone.value, SuspiciousAction.LeadShow, usePk(leadRecord))"
                                                >
                                                    {{ formatted }}
                                                </a>
                                            </template>
                                        </HiddenDataComponent>
                                        <span v-else class="text-danger">
                                            Phone not set
                                        </span>
                                    </td>
                                </template>
                                <template #utm_source="{item: leadRecord}: { item: Item }">
                                    <td
                                        v-if="canReadUtm"
                                        class="text-center"
                                        :class="{
                                            'cursor-pointer': props.isMarketingUser
                                        }"
                                        @click="props.isMarketingUser ? editUtm(usePk(leadRecord)) : null"
                                    >
                                        <div>
                                            <img
                                                v-if="leadRecord.utm.utm_source && getIcon(leadRecord.utm.utm_source)"
                                                v-tooltip="{ content: getUtmInformation(leadRecord.utm) }"
                                                :src="getIcon(leadRecord.utm.utm_source)"
                                                class="w-4 h-4 inline-block"
                                                alt=""
                                            >
                                            <span
                                                v-tooltip="{ content: getIcon(leadRecord.utm.utm_source) ? '' : getUtmFullSrc(leadRecord.utm) }"
                                            >
                                                {{ truncateText(getUtmFullSrc(leadRecord.utm), 5) }}
                                            </span>
                                        </div>
                                    </td>
                                </template>
                                <template #utm_campaign="{item: leadRecord}: { item: Item }">
                                    <td
                                        v-if="props.isMarketingUser"
                                        v-tooltip="{ content: leadRecord.utm.utm_campaign }"
                                        class="truncate max-w-[100px]"
                                        :class="{
                                            'cursor-pointer': props.isMarketingUser
                                        }"
                                        @click="editUtm(usePk(leadRecord))"
                                    >
                                        {{ leadRecord.utm.utm_campaign }}
                                    </td>
                                </template>
                                <template #utm_ga="{item: leadRecord}: { item: Item }">
                                    <td
                                        v-if="props.isMarketingUser"
                                        v-tooltip="{ content: leadRecord.utm.utm_ga }"
                                        class="truncate max-w-[100px]"
                                        :class="{
                                            'cursor-pointer': props.isMarketingUser
                                        }"
                                        @click="editUtm(usePk(leadRecord))"
                                    >
                                        {{ leadRecord.utm.utm_ga }}
                                    </td>
                                </template>
                                <template #utm_medium="{item: leadRecord}: { item: Item }">
                                    <td
                                        v-if="props.isMarketingUser"
                                        class="text-center"
                                        :class="{
                                            'cursor-pointer': props.isMarketingUser
                                        }"
                                        @click="editUtm(usePk(leadRecord))"
                                    >
                                        {{ leadRecord.utm.utm_medium }}
                                    </td>
                                </template>
                                <template #utm_term="{item: leadRecord}: { item: Item }">
                                    <td
                                        v-if="props.isMarketingUser"
                                        :class="{
                                            'cursor-pointer': props.isMarketingUser
                                        }"
                                        @click="editUtm(usePk(leadRecord))"
                                    >
                                        {{ leadRecord.utm.utm_term }}
                                    </td>
                                </template>
                                <template #remark="{item: leadRecord}: { item: Item }">
                                    <td class="cursor-pointer" @click.right.prevent="openMenu($event, leadRecord)">
                                        <div
                                            v-tooltip="{ content: leadRecord.remark, html: false }"
                                            :content="leadRecord.remark"
                                            class="truncate max-w-[100px]"
                                            @click.stop="copyToClipboard(leadRecord.remark ?? '')"
                                        >
                                            {{ leadRecord.remark }}
                                        </div>
                                    </td>
                                </template>
                                <template #executor_pk="{item: leadRecord}: { item: Item }">
                                    <td @click="assignAgent(leadRecord.executor_pk ?? null, leadRecord)">
                                        <div class="truncate max-w-[120px]">
                                            <span v-if="leadRecord.executor_pk">
                                                {{ getFullName(leadRecord.executor) }}
                                            </span>
                                            <span v-else class="text-danger">
                                                Not assigned
                                            </span>
                                        </div>
                                    </td>
                                </template>
                                <template #status="{item: leadRecord}: { item: Item }">
                                    <td class="h-full">
                                        <div class="flex items-center">
                                            <InputCheckbox
                                                v-if="statusChangeStore.enabled.value"
                                                v-model="statusChangeStore.state[leadRecord.pk]"
                                                size="xs"
                                                class="mr-1.5 --secondary"
                                            />
                                            <div class="flex justify-center items-center gap-1 w-full">
                                                <span
                                                    v-if="leadRecord.status?.name"
                                                    v-tooltip="{ content: getLeadStatusTooltip(leadRecord), html: false }"
                                                >
                                                    <LeadStatusBadge
                                                        :status-pk="leadRecord.status_pk"
                                                        class="cursor-pointer"
                                                        small
                                                    />
                                                </span>
                                                <span v-if="leadRecord.is_bonus" v-tooltip="{ content: 'Bonus' }">
                                                    <LeadBonusBadge class="cursor-pointer" small />
                                                </span>
                                                <LeadClientSessionActivityStatus
                                                    :client-session-activity-pk="leadRecord.client_session_activity_pk"
                                                />
                                            </div>
                                        </div>
                                    </td>
                                </template>
                            </LeadTableWrapper>
                        </div>
                    </template>
                    <template #fallback>
                        <div class="p-4 w-full">
                            <PlaceholderBlock class="w-full h-64" />
                        </div>
                    </template>
                </SuspenseManual>
                <LeadListFollowUpSection
                    v-if="isFollowUpVisible"
                    ref="followUp"
                    :leads="items"
                />
            </div>
        </div>
        <AppTablePagination class="mt-6 px-6" :pagination="leadsList.pagination" />
    </div>
</template>

<script setup lang="ts">
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import WorkspaceLabelComponent from '@/components/Workspace/WorkspaceLabelComponent.vue'
import TableCellLink from '~/components/Table/TableCellLink.vue'
import PQsDetails from '~/sections/Lead/List/components/PQsDetails.vue'
import UtmSourceHelper from '@/lib/core/helper/UtmSourceHelper'
import type { LeadBulkStatusChange } from '~/sections/Lead/composable/useLeadBulkStatusChange'
import LeadBonusBadge from '~/components/Page/Lead/LeadBonusBadge.vue'
import LeadStatusBadge from '~/components/Page/Lead/LeadStatusBadge.vue'
import type { ModelAttributes, ModelRef } from '~types/lib/Model'
import LeadListFollowUpSection from '~/sections/Lead/List/LeadListFollowUpSection.vue'
import UTMEditModal from '@/views/leads/modals/UTMEditModal.vue'
import type {
    LeadColumns,
    LeadColumnsController,
    LeadSearchController,
    LeadSortController,
} from '~/sections/Lead/composable/useLeadSearchController'
import HiddenDataComponent from '~/sections/Lead/List/components/HiddenDataComponent.vue'
import type { AssignmentResult } from '~/modals/AssignAgentModal.vue'
import AssignAgentModal from '~/modals/AssignAgentModal.vue'
import LeadQueueOptionsModal from '~/sections/Lead/modals/LeadQueueOptionsModal.vue'
import CheckLeadModal from '~/components/Page/Lead/CheckLeadModal.vue'
import LeadFollowUpProgressModal from '~/sections/Lead/modals/LeadFollowUpProgressModal.vue'
import { AssignLogColumn } from '~/api/models/AssignLog/AssignLog'
import { toastError, toastSuccess } from '@/lib/core/helper/ToastHelper'
import { convertLeadDataV2ToV1, dateFormatItinerary } from '~/lib/Helper/LeadHelper'
import { ExternalResource } from '~/api/dictionaries/Static/ExternalResourceDictionary'
import { getClientFullName, getFullName } from '~/lib/Helper/PersonHelper'
import { ClosingReasons, followUpMaxPoints } from '~/api/models/Lead/Lead'
import { copyToClipboard } from '@/lib/core/helper/ClipboardHelper'
import { SuspiciousAction, useSuspiciousActivityTracker } from '~/sections/Lead/composable/useSuspiciousActivityTracker'
import { useModel as useGlobalModel } from '~/composables/useModel'
import { LeadStatusName } from '~/api/models/Lead/LeadStatus'
import { useRoute } from 'vue-router'
import { routeToLead, routeToLeadManagement, withCurrentQuery } from '@/lib/core/helper/RouteNavigationHelper'
import useLeadManagementMode from '~/composables/useLeadManagementMode'
import SearchPresetsList from '~/components/Search/SearchPresetsList.vue'
import { useLeadCallCounter } from '~/composables/useLeadCallCounter'
import PlaceholderText from '@/components/Placeholder/PlaceholderText.vue'
import { useLeadMailCounter } from '~/composables/useLeadMailCounter'
import { useLeadMessageCounter } from '~/composables/useLeadMessageCounter'
import LeadOrganizerModal from '~/sections/Organizer/modals/LeadOrganizerModal.vue'
import LeadClientSessionActivity from '~/sections/Lead/List/components/LeadClientSessionActivity.vue'
import LeadClientSessionActivityStatus from '~/sections/Lead/List/components/LeadClientSessionActivityStatus.vue'
import LeadTableWrapper from '~/sections/Lead/List/LeadTableWrapper.vue'
import TableColumnsConfigurationButton from '~/components/Table/TableColumnsConfigurationButton.vue'
import { openContextMenu } from '@/plugins/ContextMenuPlugin'
import PromptModal from '~/modals/PromptModal.vue'

const props = defineProps<{
    isMarketingUser: boolean,
    searchController: LeadSearchController,
    sortController: LeadSortController,
    leadTableColumns: LeadColumns,
    leadColumnsController: LeadColumnsController,
    statusChangeStore: LeadBulkStatusChange,
}>()

const emit = defineEmits<{
    'close': [],
}>()

const { useModel, hasPermission } = useContext()

// permissions

function canAssignAgentToLead(lead: ModelAttributes<'Lead'>) {
    return hasPermission('setAppointment', 'Lead', lead)
}

function canViewAppointmentLogs(lead: ModelAttributes<'Lead'>) {
    return hasPermission('viewAppointmentLogs', 'Lead', lead)
}

function canUnAssignAgentFromLead(lead: ModelAttributes<'Lead'>) {
    return hasPermission('unAssignAppointment', 'Lead', lead)
}

const canReadUtm = computed(() => {
    return hasPermission('readUTM', 'all')
})

const canEditUtm = computed(() => {
    return hasPermission('editUTM', 'all')
})

//

const leadModel = useModel('Lead')

const assignAgentModal = useModal(AssignAgentModal)
const checkLeadModal = useModal(CheckLeadModal)
const leadFollowUpProgressModel = useModal(LeadFollowUpProgressModal)

const itineraryClassDictionary = useGeneralDictionary('ItineraryClass')
const externalResourceDictionary = useGeneralDictionary('ExternalResource')

const workspaces = useGeneralDictionary('Workspace').records

const suspense = useSuspensableComponent(async () => {
    await leadsList.fetch()
    leadCallCountersManager.fetch()
    leadMailCountersManager.fetch()
    leadMessageCounterManager.fetch()
    await columnsController.syncColumns()
})

const isFollowUpVisible = ref<boolean>(false)
const followUp = ref<InstanceType<any> | null>(null)

//
const route = useRoute()

const sortController = props.sortController

const statusChangeStore = props.statusChangeStore
const isLeadManagementPage = useLeadManagementMode(route)

function linkToLead(leadPk: PrimaryKey) {
    if (isLeadManagementPage) {
        return withCurrentQuery(routeToLeadManagement(leadPk))
    }

    return withCurrentQuery(routeToLead(leadPk))
}

const leadsList = leadModel.useList({
    with: ['clientPreview', 'executor', 'email', 'phone', 'utm', 'listInformation', 'status', 'createdBy'],
    sort: sortController,
    where: (and) => {
        searchController.applyCondition(and)
    },
    pageSize: 19,
})

const searchController = props.searchController.useList(leadsList)

const zebra = useZebraClasses(() => leadsList.records, (item) => {
    if (item.is_test) {
        return ['border border-danger']
    }
})

type Item = typeof leadsList.records[0]

const columnsController = props.leadColumnsController

columnsController.useList(leadsList)

const leadCallCountersManager = useLeadCallCounter(leadsList.records)
const leadMailCountersManager = useLeadMailCounter(leadsList.records)
const leadMessageCounterManager = useLeadMessageCounter(leadsList.records)

const items = computed(() => sortController.sort(leadsList.records))

// Closing reason

const closingReasonDictionary = useGeneralDictionary('ClosingReason')

const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text

    return text.substring(0, maxLength) + '...'
}

const getLeadStatusTooltip = (leadRecord: ModelRef<'Lead', 'status'>) => {
    const closingReason = leadRecord.closing_reason
    const closingReasonRemark = leadRecord.closing_reason_remark ?? ''

    if ([LeadStatusName.Lost, LeadStatusName.Closed, LeadStatusName.Fraud].includes(leadRecord.status.system_name) && closingReason) {
        const baseResult = leadRecord.status.name + ' (reason: '

        if (closingReason === ClosingReasons.Other) {
            return baseResult + truncateText(closingReasonRemark, 150) + ')'
        }

        const reasonTitle = closingReasonDictionary.find(closingReason).title

        return baseResult + reasonTitle + ')'
    }

    return leadRecord.status.name
}

function openFollowUp() {
    isFollowUpVisible.value = true
}

function closeFollowUp() {
    isFollowUpVisible.value = false
}

function sendFollowUp() {
    followUp.value.send()
}

function openCheckLeadModal() {
    checkLeadModal.open()
}

// utm

const utmModal = useModal(UTMEditModal)

function editUtm(leadPk: PrimaryKey) {
    if (canEditUtm.value) {
        utmModal.open({ leadId: Number(leadPk) })
    }
}

const getIcon = (leadUtmSource: string) => {
    return UtmSourceHelper.getImage(leadUtmSource)
}

const getUtmInformation = (utmInformation: ModelAttributes) => {
    return `Campaign: ${utmInformation.utm_campaign} <br>Ga: ${utmInformation.utm_ga}<br>Medium: ${utmInformation.utm_medium}<br>Source: ${utmInformation.utm_source}<br>Term: ${utmInformation.utm_term}`
}

const getPhoneLink = (phone: string) => {
    return `tel:+${phone.replace(/\D+/g, '')}`
}

const getUtmFullSrc = (utmInformation: ModelAttributes) => {
    let utmSrc = getIcon(utmInformation.utm_source) ? '' : utmInformation.utm_source

    if (utmInformation.utm_ip_country === 'MD') utmSrc += utmInformation.utm_source ? `/${utmInformation.utm_ip_country}` : `${utmInformation.utm_ip_country}`

    return utmSrc
}

// assign agent modal

const leadQueueSelectionModal = useModal(LeadQueueOptionsModal)

function assignAgent(agentPk: PrimaryKey | null, lead: ModelAttributes<'Lead'>) {
    if (!canAssignAgentToLead(lead) && !canViewAppointmentLogs(lead)) {
        toastError('You can\'t change curator or see logs')

        return
    }
    const externalResourceData = externalResourceDictionary.tryFind(lead.external_resource)
    const leadV1ExternalResourceData = externalResourceData?.value !== ExternalResource.BackOffice ? externalResourceData : {
        title: 'Back Office',
        value: 'bo_agent',
    }
    const leadV1Data = convertLeadDataV2ToV1(lead, leadV1ExternalResourceData)
    assignAgentModal.open({
        model: leadV1Data,
        column: AssignLogColumn.Executor,
        defaultAgentPk: agentPk ?? null,
        canUnAssign: canUnAssignAgentFromLead(lead),
        onAssign: async (response: AssignmentResult) => {
            if (response.agent_pk === undefined) {
                const clientSessionAgentPk = await useModel('Lead').actions.getLeadClientSessionAgent({
                    lead_pk: usePk(lead),
                })

                if (clientSessionAgentPk.agent_pk === null) {
                    return leadQueueSelectionModal.open({
                        lead: lead,
                    })
                } else {
                    return setExecutor(clientSessionAgentPk.agent_pk, lead)
                }
            } else {
                return setExecutor(response.agent_pk, lead)
            }
        },
        canAssign: canAssignAgentToLead(lead),
    }, {
        workspace: getWorkspaceFromObject(lead),
    })
}

async function setExecutor(agentPk: PrimaryKey | null, lead: ModelAttributes<'Lead'>) {
    return await useGlobalModel('Lead', {
        http: {
            workspace: getWorkspaceFromObject(lead),
        },
    }).actions.assignExecutor({
        lead_pk: usePk(lead),
        executor_pk: agentPk,
        lead_queue: null,
    })
}

// Suspicious Activity Monitoring

const { trackSuspiciousAction } = useSuspiciousActivityTracker()

function copyLeadInfo(value: string, userAction: SuspiciousAction, pk: PrimaryKey) {
    if (userAction === SuspiciousAction.LeadCopy) {
        copyToClipboard(value)
    }
    trackSuspiciousAction(userAction, { pk })
}

//

watch(() => leadsList.records, () => {
    for (const record of leadsList.records) {
        if (!statusChangeStore.state[record.pk]) {
            delete statusChangeStore.state[record.pk]
        }
    }
}, { deep: true })

const formatCount = (count: number) => {
    if (count < 0) {
        return ''
    }

    return count > 99 ? '+99' : count
}

const openOrganizer = (lead: ModelAttributes<'Lead'>, tab: 'sms' | 'chat' | 'messages') => {
    const { open } = useModal(LeadOrganizerModal)

    open({  lead_pk: usePk(lead), tab },
         { workspace: getWorkspaceFromObject(lead),
         })
}

const remarkModal = useModal(PromptModal)

function openMenu(event: MouseEvent, leadRecord) {
    openContextMenu(event, [
        {
            text: 'Edit remark',
            icon: markRaw(Edit2Icon),
            onClick: () => updateRemark(leadRecord),
        },
    ])
}

async function updateRemark(leadRecord) {
    const result = await remarkModal.open({
        title: 'Edit remark',
        input: true,
        inputType: 'textarea',
        value: leadRecord.remark,
        inputPlaceholder: 'Enter remark',
        inputRows: 5,
    })

    await useDetachedContext(leadRecord).useModel('Lead').actions.updateRemark({
        lead_pk: usePk(leadRecord),
        remark: result,
    })

    toastSuccess('Remark was updated successfully')
}
</script>
