<template>
    <div
        class="relative flex cursor-pointer flex-col border border-secondary-100 rounded-lg dark:bg-dark-1 shadow-sm"
        :class="{
            'bg-white shadow-lg': isLastOpened,
            '!border-primary-500 !bg-secondary-50 dark:!bg-dark-4': isActive,
            'bg-danger-100 bg-opacity-50 !border-danger-200': isAwardLead,
            'opacity-30': lead.isDeleted,
        }"
    >
        <div class="flex items-center justify-between text-xs py-1.5 leading-5 px-1.5 rounded-lg shadow">
            <div
                class="absolute -top-1.5 left-5 flex gap-2"
                :class="{'!-top-2': isActive}"
            >
                <WorkspaceLabelComponentAlternative v-if="workspaces.length > 1" :project-id="Number(lead.project_pk)" />

                <div
                    v-if="lead.is_test"
                    class="bg-danger rounded text-3xs w-[fit-content] text-white font-semibold px-1.5 leading-normal self-center"
                >
                    Test
                </div>
                <LeadScore
                    v-if="hasPermission('manage', 'all')"
                    class="--small !h-3.5"
                    :score="leadScore"
                />
            </div>

            <div class="flex items-center gap-1">
                <div class="select-none" @click.stop="changeStarColor()">
                    <StarIcon
                        class="w-3.5 h-3.5 stroke-2"
                        :class="shownStarColor"
                    />
                </div>
                <div class="flex gap-0.5">
                    <div v-tooltip="{content: getLeadStatusTooltip(lead), html: false}">
                        <LeadStatusBadge
                            :status-pk="lead.status_pk"
                            small
                            class="w-3.5 h-3.5"
                        />
                    </div>
                    <div
                        v-if="lead.is_bonus"
                        v-tooltip="{ content: 'Bonus' }"
                    >
                        <LeadBonusBadge small class="w-3.5 h-3.5" />
                    </div>
                </div>
                <span class="ml-0.5 text-2xs font-medium max-w-[115px] truncate">
                    {{ getClientFullName(lead.clientPreview) }}
                </span>
            </div>
            <div class="flex items-center gap-1">
                <PriceQuoteMarkUpAlert v-if="lead.listInformation.is_mark_up_alert" />
                <div v-if="isRush" v-tooltip="{content: 'Rush Lead'}">
                    <RushLeadIcon class="w-3 h-3 text-secondary-400" />
                </div>
                <ChatMessagesCount
                    class="!ml-0 !p-0"
                    :count="lead.chat.info.messages_count ?? 0"
                    :mentioned="lead.chat.info.is_mentioned"
                    :muted="chatIsMuted && !lead.chat.info.is_mentioned"
                    :new-count="lead.chat.info.new_messages_count"
                    :with-styles="false"
                    with-custom-icon
                />
                <div
                    v-tooltip="{content: 'PQs'}"
                    class="flex items-center gap-0.5"
                    :style="pqStatus"
                    :class="lead.listInformation.count === 0 ? 'text-secondary-300 dark:text-secondary-600' : 'text-secondary-600 dark:text-secondary-300'"
                >
                    <span class="text-2xs">{{ lead.listInformation.count < 0 ? '-' : lead.listInformation.count }}</span>
                    <PQIcon
                        class="w-3 h-3 stroke-1"
                    />
                </div>

                <Dropdown>
                    <template #toggle="{toggle}">
                        <button class="flex items-center p-0.5 rounded hover:bg-secondary-100" @click.stop="toggle">
                            <MoreVerticalIcon class="w-3.5 h-3.5 text-secondary-400" />
                        </button>
                    </template>
                    <template #content="{close}">
                        <div class="p-1 card flex flex-col gap-y-1 min-w-[100px] border border-secondary-200">
                            <button
                                v-if="isLastOpened"
                                class="dropdown-menu__item p-2.5 flex w-full text-secondary"
                                @click.stop="() => {
                                    removeLeadFromLastOpened()
                                    close()
                                }"
                            >
                                <LinkIcon class="w-3 h-3 mr-1.5" />
                                Unlink
                            </button>
                            <button
                                v-if="!isExpertPage"
                                class="dropdown-menu__item p-2.5 flex w-full text-secondary"
                                @click.stop="() => {
                                    const action = isPinned ? 'remove' : 'add'
                                    changeStatus(action)
                                    close()
                                }"
                            >
                                <PinIcon class="w-3 h-3 mr-1.5" />
                                {{ isPinned ? 'Unpin' : 'Pin' }}
                            </button>
                            <button
                                v-if="!isExpertPage && hasPermission('edit', 'Lead', lead)"
                                v-tooltip="{content: `Lead is ${lead.status.name.toLowerCase()}`, disabled: !isEditDisabled}"
                                class="dropdown-menu__item p-2.5 flex w-full text-secondary"
                                :disabled="isEditDisabled"
                                :style="isEditDisabled ? {opacity: '0.5', cursor: 'not-allowed'} : {}"
                                @click.stop="() => {
                                    setLeadCardEdit()
                                    close()
                                }"
                            >
                                <Edit3Icon class="w-3 h-3 mr-1.5" />
                                Edit
                            </button>
                            <button
                                class="dropdown-menu__item p-2.5 flex w-full text-secondary"
                                @click.stop="() => {
                                    close()
                                    copy()
                                }"
                            >
                                <CopyIcon class="w-3 h-3 mr-1.5" />
                                <span class="whitespace-nowrap">Copy Link</span>
                            </button>
                        </div>
                    </template>
                </Dropdown>
            </div>
        </div>
        <div class="px-2 py-2.5">
            <div class="relative">
                <div class="flex items-center justify-between text-2xs">
                    <div class="text-base leading-6 font-semibold">
                        {{ lead.from_iata_code }}
                    </div>
                    <div class="flex flex-col justify-center gap-y-1 min-w-[155px]">
                        <div class="w-full flex justify-between items-center h-[12px]">
                            <div class="text-2xs flex gap-1 min-w-[75px]">
                                <span class="font-semibold">
                                    {{ departureDateFormatted.departureDateDayMonth }}
                                </span>
                                <span class="text-secondary">
                                    {{ departureDateFormatted.departureDateYear }}</span>
                            </div>
                            <div class="flex gap-1 text-2xs">
                                <template v-if="lead.itinerary_type === ItineraryType.RoundTrip">
                                    <span class="font-semibold">
                                        {{ returnDateFormatted.returnDateDayMonth }}
                                    </span>
                                    <span class="text-secondary">
                                        {{ returnDateFormatted.returnDateYear }}
                                    </span>
                                </template>

                                <span
                                    v-else-if="lead.itinerary_type === ItineraryType.MultiCity"
                                    v-tooltip="{content: multiCityText}"
                                    class="text-2xs font-semibold text-right text-primary cursor-pointer dark:text-primary-300"
                                >
                                    Multi City
                                </span>
                                <span v-else class="text-2xs font-semibold">
                                    One way
                                </span>
                            </div>
                        </div>
                        <div class="w-[calc(100%-0.8rem)] h-[1px] border-t-2 border-dotted relative ml-1.5">
                            <PlaneIcon
                                class="absolute top-[-8px] left-[calc(50%-7px)] w-3.5 h-3.5 text-primary dark:text-primary-300"
                                :class="{
                                    '!text-danger': isAwardLead
                                }"
                            />
                            <div class="absolute border-2 border-secondary-200 rounded-full h-[5px] w-[5px] left-[-6px] top-[-3px] dark:border-secondary-300" />
                            <div
                                class="absolute bg-primary rounded-full h-[5px] w-[5px] right-[-6px] top-[-3px] dark:bg-primary-300"
                                :class="{
                                    '!bg-danger': isAwardLead
                                }"
                            />
                        </div>
                        <div class="w-full flex justify-between h-[12px]">
                            <div class="flex items-center gap-0.5 text-2xs">
                                <div class="flex items-center gap-1 ">
                                    <AdultIcon class="h-3.5" />
                                    <span :class="{'text-secondary-500':lead.adult_count==0}">{{ lead.adult_count }}</span>
                                    <ChildIcon class="h-3.5" />
                                    <span :class="{'text-secondary-500':lead.child_count==0}">{{ lead.child_count }}</span>
                                    <InfantIcon class="h-3.5" />
                                    <span :class="{'text-secondary-500':lead.infant_count==0}">{{ lead.infant_count }}</span>
                                </div>
                            </div>
                            <div class="flex items-center gap-0.5 text-2xs">
                                <span class="font-medium ">{{ lead?.itinerary_class[0]?.toUpperCase() }}</span>
                                <span class="text-secondary-400">Class</span>
                            </div>
                        </div>
                    </div>
                    <div class="text-base leading-6 font-semibold">
                        {{ lead.to_iata_code }}
                    </div>
                </div>
            </div>
        </div>

        <div class="relative px-2">
            <div class="px-1 w-full">
                <div
                    class="w-full border-t border-secondary-100"
                    :class="{
                        'border-danger-200':isAwardLead
                    }"
                />
            </div>
            <div class="py-1.5">
                <div class="flex justify-between">
                    <div class="flex flex-col gap-0 items-center">
                        <span
                            class="self-start text-2xs text-primary font-medium dark:text-primary-300 leading-tight"
                            :class="{ 'text-danger':isAwardLead }"
                        >
                            #{{ lead.pk }}
                        </span>
                        <span class="text-2xs font-normal text-secondary-500">
                            {{ $format.datetime(lead.created_at) }}
                        </span>
                    </div>
                    <div
                        v-if="lead.expert_pk"
                        v-tooltip="{content: getFullName(lead.expert!)}"
                        class="customer-bar-agent p-0 px-0.5 flex-col"
                        @click.stop="setExpert"
                    >
                        <div class="text-theme-38 text-2xs self-end leading-tight">
                            Expert
                        </div>
                        <div class="flex items-center  gap-x-0.5">
                            <div class="text-2xs font-medium max-w-14 truncate">
                                {{ getShortName(lead.expert!) }}
                            </div>
                            <AppAvatar
                                v-if="lead.expert?.avatar"
                                :model="lead.expert"
                                class="customer-bar-agent-avatar w-4 h-4 !m-0 border-2 border-primary dark:border-primary hover:bg-none"
                            />
                        </div>
                    </div>

                    <div
                        v-if="lead.executor_pk"
                        v-tooltip="{content: getFullName(lead.executor!)}"
                        class="customer-bar-agent p-0 px-0.5 flex-col"
                        @click.stop="assignExecutor(lead.executor_pk)"
                    >
                        <div class="text-theme-38 text-2xs self-end leading-tight">
                            Agent
                        </div>
                        <div class="flex items-center gap-x-0.5">
                            <div class="text-2xs font-medium max-w-14 truncate">
                                {{ getShortName(lead.executor!) }}
                            </div>
                            <AppAvatar
                                v-if="lead.executor?.avatar"
                                :model="lead.executor"
                                class="customer-bar-agent-avatar w-4 h-4 !m-0 border border-secondary-100"
                            />
                        </div>
                    </div>
                    <div
                        v-else
                        class="customer-bar-agent p-0 relative z-1 mt-1 hover:bg-white"
                        @click.stop="assignExecutor()"
                    >
                        <div class="customer-bar-agent-add !m-0 h-[24px] w-[24px] dark:bg-dark-1 dark:border-secondary-500">
                            <PlusIcon class="w-3 h-3 !stroke-2" />
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="showSessionTimer || showPendingBonusLabel" class="absolute left-[75px] bottom-[-14px]">
                <div v-if="showSessionTimer" class="flex items-center gap-0.5 shadow px-1 text-2xs rounded-lg text-pending bg-white whitespace-nowrap dark:bg-dark-1">
                    <AlertCircleSolidIcon class="text-pending w-3 h-3" /> Remaining: {{ leadGetTimeLeft(lead.session_expire_at) }}
                </div>
                <div v-if="showPendingBonusLabel" class="flex items-center gap-0.5 shadow px-1 text-2xs rounded-lg text-pending bg-white whitespace-nowrap dark:bg-dark-1">
                    <AlertCircleSolidIcon class="text-pending w-3 h-3" />  Pending bonus
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import WorkspaceLabelComponentAlternative from '@/components/Workspace/WorkspaceLabelComponentAlternative.vue'
import type { ModelAttributes, ModelRef } from '~types/lib/Model'
import LeadStatusBadge from '~/components/Page/Lead/LeadStatusBadge.vue'
import LeadBonusBadge from '~/components/Page/Lead/LeadBonusBadge.vue'
import PinIcon from '@/assets/icons/PinIcon.svg?component'
import RushLeadIcon from '~/assets/icons/RushLeadIcon.svg?component'
import PQIcon from '~/assets/icons/PQIcon.svg?component'
import PlaneIcon from '~/assets/icons/PlaneIcon.svg?component'
import AdultIcon from '~/assets/icons/AdultIcon.svg?component'
import ChildIcon from '~/assets/icons/ChildIcon.svg?component'
import InfantIcon from '~/assets/icons/InfantIcon.svg?component'
import AlertCircleSolidIcon from '~/assets/icons/AlertCircleSolidIcon.svg?component'

import {
    convertLeadDataV2ToV1,
    getLeadPqCounterColor,
    isRushLead,
    leadGetTimeLeft,
    leadMultiCityInfo,
} from '~/lib/Helper/LeadHelper'
import LeadCreateModal from '~/sections/Lead/modals/Create/LeadCreateModal.vue'
import { goTo, linkToLead, routeToLead } from '@/lib/core/helper/RouteNavigationHelper'
import { copyToClipboard } from '~/lib/Helper/CopyToClipboardHelper'
import { StarColor } from '~/api/dictionaries/Static/Lead/LeadStarTypeDictionary'
import { useProgress } from '@marcoschulte/vue3-progress'
import { getClientFullName, getFullName, getShortName } from '~/lib/Helper/PersonHelper'
import { ClosingReasons, ItineraryType } from '~/api/models/Lead/Lead'
import { LeadStatusName } from '~/api/models/Lead/LeadStatus'
import { toastError } from '@/lib/core/helper/ToastHelper'
import { ExternalResource } from '~/api/dictionaries/Static/ExternalResourceDictionary'
import { AssignLogColumn } from '~/api/models/AssignLog/AssignLog'
import type { AssignmentResult } from '~/modals/AssignAgentModal.vue'
import AssignAgentModal from '~/modals/AssignAgentModal.vue'
import { useModel as useGlobalModel } from '~/composables/useModel'
import ChatMessagesCount from '@/modules/chat/components/ChatMessagesCount.vue'
import { useRouter } from 'vue-router'
import PriceQuoteMarkUpAlert from '~/sections/PriceQuote/components/PriceQuoteMarkUpAlert.vue'
import { useExpertAssignment } from '~/sections/ExpertLead/composable/useExpertAssignment'
import LeadScore from '~/sections/Lead/components/LeadScore.vue'

const props = withDefaults(defineProps<{
    lead: ModelRef<'Lead', 'clientPreview' | 'executor' | 'listInformation' | 'status' | 'expert' | 'agentLeadInfo' |
        'chat' | 'chat.info' | 'createdBy' | 'prediction'>,
    isActive?: boolean,
    isPinned?: boolean,
    isLastOpened?: boolean,
}>(), {
    isLastOpened: false,
    isActive: false,
    isPinned: false,
})

const emit = defineEmits<{
    'update:removeLeadFromLastOpenedBlock': [value:PrimaryKey]
}>()

// chat info

const chatManager = useService('chat')

const chatIsMuted = computed(() => {
    const chat = chatManager.getOrCreateChat(props.lead.chat)

    return  chat.isMuted
})

const { assignExpert } = useExpertAssignment()

// lead model

const { useModel, hasPermission } = useNewContext(props.lead)

const leadModel = useModel('Lead')

//

const updateLeadModal = useModal(LeadCreateModal)

const externalResourceDictionary = useGeneralDictionary('ExternalResource')

const workspaces = useGeneralDictionary('Workspace').records

// should be removed
const formatter = useService('formatter')

const departureDateFormatted = computed(() => {
    return {
        departureDateDayMonth: formatter.date(props.lead.departure_date, 'UTC', {
            format: 'dd MMM',
        }),
        departureDateYear: formatter.date(props.lead.departure_date, 'UTC', {
            full: true,
            format: 'yyyy',
        }),
    }
})

const returnDateFormatted = computed(() => {
    if (!props.lead.return_date) {
        return {
            returnDateDayMonth: '',
            returnDateYear: '',
        }
    }

    return {
        returnDateDayMonth: formatter.date(props.lead.return_date, 'UTC', {
            format: 'dd MMM',
        }),
        returnDateYear: ' ' + formatter.date(props.lead.return_date, 'UTC', {
            full: true,
            format: 'yyyy',
        }),
    }
})

function pqStatus() {
    return getLeadPqCounterColor(props.lead.listInformation.count, props.lead.listInformation.price_quote_last_time)
}

async function changeStatus(action: string) {
    await leadModel.actions.setPinned({
        lead_pk: usePk(props.lead),
        state: action === 'add',
    })
}

const closingReasonDictionary = useGeneralDictionary('ClosingReason')

const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text

    return text.substring(0, maxLength) + '...'
}

const getLeadStatusTooltip = (leadRecord: ModelRef<'Lead', 'status'>) => {
    const closingReason = leadRecord.closing_reason
    const closingReasonRemark = leadRecord.closing_reason_remark ?? ''

    if ([LeadStatusName.Lost, LeadStatusName.Closed, LeadStatusName.Fraud].includes(leadRecord.status.system_name) && closingReason) {
        const baseResult = leadRecord.status.name + ' (reason: '

        if (closingReason === ClosingReasons.Other) {
            return baseResult + truncateText(closingReasonRemark, 150) + ')'
        }

        const reasonTitle = closingReasonDictionary.find(closingReason).title

        return baseResult + reasonTitle + ')'
    }

    return leadRecord.status.name
}

const isEditDisabled = computed(() => {
    return ([LeadStatusName.Lost, LeadStatusName.Sold, LeadStatusName.SaleRejected, LeadStatusName.Fraud]
        .includes(props.lead.status.system_name) && !hasPermission('manage', 'all'))
})

const leadScore = computed(() => {
    return props.lead.prediction?.lead_score ?? null
})

function copy() {
    copyToClipboard(window.location.origin + linkToLead(props.lead.pk))
}

// star type

const starColors = {
    [StarColor.GRAY]: 'text-secondary',
    [StarColor.YELLOW]: 'text-warning',
    [StarColor.RED]: 'text-danger',
    [StarColor.GREEN]: 'text-success',
    [StarColor.BLUE]: 'text-primary-500',
    [StarColor.INDIGO]: 'text-indigo-500',
    [StarColor.PURPLE]: 'text-purple-500',
    [StarColor.PINK]: 'text-pink-500',
}

const leadStarColor = computed(() => {
    return props.lead.agentLeadInfo.star_type || StarColor.GRAY
})

const shownStarColor = computed(() => {
    if (props.lead.agentLeadInfo.star_type) {
        return starColors[leadStarColor.value as StarColor]
    }

    return starColors[StarColor.GRAY]
})

async function changeStarColor() {
    const enumValues = Object.values(StarColor)
    const currentIndex = enumValues.indexOf(leadStarColor.value)
    let newColor = ''

    if (currentIndex !== -1 && currentIndex < enumValues.length - 1) {
        newColor = enumValues.at(currentIndex + 1) as StarColor
    } else {
        newColor = enumValues.at(0) as StarColor
    }

    await useProgress().attach(
        useWaitForResourceEvent(
            leadModel.actions.changeLeadStarTypeForAgent({
                lead_pk: usePk(props.lead),
                star_type: newColor,
            }),
            'LeadAdditionalAgentInfo',
            'update',
            usePk(props.lead.agentLeadInfo),
        ),
    )
}

async function setLeadCardEdit() {
    await updateLeadModal.open({
        editLeadPk: usePk(props.lead),
    })

    await goTo(routeToLead(usePk(props.lead)))
}

// expert

const router = useRouter()

const isExpertPage = computed(() => {
    return String(router.currentRoute.value.name).includes('experts')
})

// multi city info

function multiCityText() {
    const leadMultiCityData: undefined | {
        iatas: string,
        departureDate: string | undefined,
        returnDate: string | undefined,
    }  = leadMultiCityInfo(props.lead)

    if (!leadMultiCityData) {
        return ''
    }

    return `<div class="flex flex-col text-xs text-left"><span class="max-w-[250px]">${leadMultiCityData.iatas}</span><span class="mt-0.5">${leadMultiCityData.departureDate} - ${leadMultiCityData.returnDate}</span></div>`
}

// assign agent
const assignAgentModal = useModal(AssignAgentModal)

const canAssignAgentToLead = computed(() => {
    return hasPermission('setAppointment', 'Lead', props.lead)
})

const canUnAssignAgentFromLead = computed(() => {
    return hasPermission('unAssignAppointment', 'Lead', props.lead)
})

const canViewAppointmentLogs = computed(() => {
    return hasPermission('viewAppointmentLogs', 'Lead', props.lead)
})

function assignExecutor(agentPk?: PrimaryKey) {
    if (!canViewAppointmentLogs.value) {
        toastError('You don\'t have permission to assign agent to lead')

        return
    }

    if (!canAssignAgentToLead.value && !canViewAppointmentLogs.value) {
        toastError('You can\'t change curator or see logs')

        return
    }

    const externalResourceData = externalResourceDictionary.tryFind(props.lead.external_resource)
    const leadV1ExternalResourceData = externalResourceData?.value !== ExternalResource.BackOffice ? externalResourceData : {
        title: 'Back Office',
        value: 'bo_agent',
    }

    const leadV1Data = convertLeadDataV2ToV1(props.lead, leadV1ExternalResourceData)

    assignAgentModal.open({
        model: leadV1Data,
        column: AssignLogColumn.Executor,
        defaultAgentPk: agentPk,
        canUnAssign: canUnAssignAgentFromLead.value,
        onAssign: (response: AssignmentResult) => setExecutor(response.agent_pk || null, props.lead),
        canAssign: canAssignAgentToLead.value,
    }, {
        workspace: getWorkspaceFromObject(props.lead),
    })
}

async function setExecutor(agentPk: PrimaryKey | null, lead: ModelAttributes<'Lead'>) {
    return await useGlobalModel('Lead', {
        http: {
            workspace: getWorkspaceFromObject(lead),
        },
    }).actions.assignExecutor({
        lead_pk: usePk(lead),
        executor_pk: agentPk,
    })
}

async function setExpert() {
    if (!canAssignAgentToLead.value) {
        toastError('You don\'t have permission to assign expert to lead')

        return
    }

    await assignExpert(usePk(props.lead))
}

// rush lead check

const isRush = computed(() => {
    return isRushLead(props.lead)
})

// show timer block

const showSessionTimer = computed(() => {
    return !!props.lead?.session_expire_at && leadGetTimeLeft(props.lead.session_expire_at) !== 'expired'
})

const showPendingBonusLabel = computed(() => {
    return !!props.lead?.session_expire_at && leadGetTimeLeft(props.lead.session_expire_at) === 'expired'
})

// award lead

const isAwardLead = computed(() => {
    return props.lead.is_award && !props.lead.session_expire_at
})

// remove lead from last opened

function removeLeadFromLastOpened() {
    emit('update:removeLeadFromLastOpenedBlock', usePk(props.lead))
}
</script>
