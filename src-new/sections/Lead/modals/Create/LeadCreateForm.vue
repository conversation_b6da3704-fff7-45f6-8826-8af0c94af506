<!-- eslint-disable vue/max-attributes-per-line -->
<template>
    <div v-if="isEditMode && !isFormPopulated" class="p-20 flex items-center justify-center gap-3">
        <Loader /> Loading data...
    </div>
    <form v-else class="card" @submit.prevent>
        <div class="card__body card__body--partholder">
            <div v-if="!isEditMode" class="card card__body p-3 grid grid-cols-2 gap-3">
                <FormField class="text-xs" :form="form" :field="'project_pk'" label="Project" required>
                    <InputSelect
                        v-model="form.data.project_pk" size="small" :options="projectOptions"
                        with-empty
                    />
                </FormField>
                <FormField class="text-xs" :form="form" :field="'external_resource'" label="External resource" required>
                    <InputSelect
                        v-model="form.data.external_resource" size="small" :options="externalResourceOptions"
                        with-empty
                    />
                </FormField>
                <FormField
                    v-if="isJivoResource"
                    :form="form"
                    :field="'jivo_link'"
                    label="Jivo link"
                    class="col-span-2 text-xs"
                >
                    <InputText
                        v-model="form.data.jivo_link"
                        placeholder="Link"
                        size="small"
                    />
                </FormField>
            </div>

            <div v-if="!hideClientRelatedFields" class="card card__body p-3 grid grid-cols-2 gap-3">
                <FormField :form="form" :field="'client.emails'" class="grid gap-2 text-xs">
                    <FormField
                        v-for="(_, index) of form.data.client.emails"
                        :key="index"
                        :form="form"
                        :field="`client.emails.${index}`"
                        :label="index === 0 ? 'Email' : undefined"
                        required
                    >
                        <InputText v-model="form.data.client.emails[index]" size="small" type="email" />
                    </FormField>
                </FormField>
                <FormField :form="form" :field="'client.phones'" class="grid gap-2 relative text-xs">
                    <label
                        class="absolute top-0 right-0 cursor-pointer select-none flex items-center gap-2 text-xs form-label text-secondary"
                    >
                        <InputCheckbox v-model="form.data.client.is_unknown_phone" class="dark:!ring-0" size="xs" /> Unknown phone
                    </label>

                    <FormField
                        v-for="(_, index) of form.data.client.phones"
                        :key="index"
                        class="text-xs"
                        :form="form"
                        :field="`client.phones.${index}`"
                        :label="index === 0 ? 'Phone' : undefined"
                    >
                        <InputPhone
                            v-model="form.data.client.phones[index]"
                            size="small"
                            :unknown-number="form.data.client.is_unknown_phone"
                            with-country-select
                        />
                    </FormField>
                </FormField>
                <FormField class="text-xs" :form="form" :field="'client.first_name'" label="First name" required>
                    <InputText v-model="form.data.client.first_name" size="small" />
                </FormField>
                <FormField class="text-xs" :form="form" :field="'client.last_name'" label="Last name">
                    <InputText v-model="form.data.client.last_name" size="small" />
                </FormField>
            </div>

            <div class="card card__body p-3 grid grid-cols-2 gap-3">
                <FormField class="text-xs" :form="form" :field="'from_iata_pk'" label="Departure" required>
                    <InputSelect
                        v-model="form.data.from_iata_pk"
                        size="small"
                        :options="iataDepartureOptions"
                        with-empty
                        search
                        @search="searchDepartureIata"
                    />
                </FormField>

                <FormField class="text-xs" :form="form" :field="'to_iata_pk'" label="Destination" required>
                    <InputSelect
                        v-model="form.data.to_iata_pk"
                        size="small"
                        :options="iataDestinationOptions"
                        with-empty
                        search
                        @search="searchDestinationIata"
                    />
                </FormField>

                <FormField class="text-xs" :form="form" :field="'departure_date'" label="Departure date" required>
                    <InputDate
                        v-model:timestamp="form.data.departure_date"
                        size="small"
                        placeholder="Select date"
                        :min-date="new Date()"
                        icon="date"
                        timezone="UTC"
                        teleport
                        @update:timestamp="onDatesChange"
                    />
                </FormField>

                <FormField class="text-xs" :form="form" :field="'return_date'" label="Return date">
                    <InputDate
                        v-model:timestamp="form.data.return_date"
                        size="small"
                        placeholder="No return? Leave it empty"
                        :min-date="returnDateMin"
                        icon="date"
                        timezone="UTC"
                        teleport
                        @update:timestamp="onDatesChange"
                    />
                </FormField>

                <FormField class="text-xs" :form="form" :field="'itinerary_class'" label="Class" required>
                    <InputSelect
                        v-model="form.data.itinerary_class" size="small" :options="itineraryClassOptions"
                        with-empty
                    />
                </FormField>

                <FormField :form="form" :field="'adult_count'" label="Passengers" class="text-xs">
                    <InputPassengers
                        :model-value="{
                            adult_count: form.data.adult_count,
                            child_count: form.data.child_count,
                            infant_count: form.data.infant_count,
                        }"
                        size="small"
                        class="gap-2 text-xs"
                        @update:model-value="({
                            adult_count,
                            child_count,
                            infant_count,
                        }) => {
                            form.data.adult_count = adult_count
                            form.data.child_count = child_count
                            form.data.infant_count = infant_count
                        }"
                    />
                </FormField>

                <FormField class="col-span-2 text-xs" :form="form" :field="'remark'" label="Remark">
                    <InputTextarea v-model="form.data.remark" size="small" placeholder="Leave your message here..." />
                </FormField>
            </div>
            <div v-if="!isEditMode && !hideClientRelatedFields" class="card card__body p-3 grid gap-3">
                <IssueResultClientStatusForm
                    ref="clientStatusForm"
                    class="text-xs"
                    size="small"
                />
                <IssueAdditionalDataForm
                    v-if="showClientStatusAdditionalForm"
                    ref="additionalData"
                    without-tips
                    class="text-xs"
                    size="small"
                />
            </div>
        </div>

        <TeleportDeferred to="#controls">
            <div class="flex items-center gap-3">
                <FormField
                    v-if="!isEditMode && isLeadManager"
                    v-tooltip="{
                        content: 'When checked, the lead will remain unassigned',
                        delay: { show: 700 }
                    }"
                    :form="form"
                    :field="'send_to_queue'"
                    class="w-fit"
                >
                    <label class="form-label flex items-center gap-2 cursor-pointer text-xs">
                        <InputCheckbox v-model="form.data.send_to_queue" size="small" class="dark:!ring-0" />
                        Send to queue
                    </label>
                </FormField>

                <AppButton
                    class="--primary"
                    :loading="form.loading.value"
                    @click="submit"
                >
                    {{ isEditMode ? 'Save' : 'Create' }}
                </AppButton>
            </div>
        </TeleportDeferred>
    </form>
</template>

<script lang="ts">
import type { ModelAttributes } from '~types/lib/Model'
import { ExternalResource } from '~/api/dictionaries/Static/ExternalResourceDictionary'
import type { ItineraryClass } from '~/api/dictionaries/Static/ItineraryClassDictionary'
import TeleportDeferred from '~/components/TeleportDeferred.vue'

export type LeadCreateFormProps = {
    // Enables edit mode
    editLeadPk?: PrimaryKey,

    populate?: {
        // Populate form from lead pk. (Does not change mode to edit)
        leadPk?: PrimaryKey,

        // Populate form from any data from lead DTO
        leadRecord?: Partial<ModelAttributes<'Lead'>>,

        // Populate from client pk
        clientPk?: PrimaryKey,

        // Populate directly
        formData?: Partial<FormPayload>,
    },

    /**
     * Hides fields related to client:
     * - phone
     * - email
     * - first_name
     * - last_name
     * - client status
     */
    hideClientRelatedFields?: boolean,
}

type FormPayload = {
    external_resource: ExternalResource | string,
    jivo_link: string,
    itinerary_class: ItineraryClass | string,
    from_iata_pk: PrimaryKey,
    to_iata_pk: PrimaryKey,
    departure_date: number,
    return_date: number | undefined,
    adult_count: number,
    child_count: number,
    infant_count: number,
    remark: string,
    project_pk: PrimaryKey,
    send_to_queue: boolean,

    // Client related fields
    client: {
        first_name: string
        last_name: string | undefined,
        emails: string[],
        phones: string[],
        is_unknown_phone: boolean,
    }
}
</script>

<script setup lang="ts">
import FormField from '~/components/Form/FormField.vue'
import LeadKeepClientCuratorModal from '~/sections/Lead/modals/Create/LeadKeepClientCuratorModal.vue'
import { filterUndefined, updatePropsWithCondition } from '~/lib/Helper/ObjectHelper'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import { useIataSelect } from '~/composables/useIataSelect'
import IssueResultClientStatusForm from '~/components/Page/Issue/Result/ClientStatus/IssueResultClientStatusForm.vue'
import IssueAdditionalDataForm from '~/components/Page/Issue/IssueAdditionalDataForm.vue'
import { ClientStatusName } from '~/api/models/Client/ClientStatus'
import type Form from '~/lib/Form/Form'
import { isUserHasPosition, isUserInDepartment } from '~/composables/useCurrentUser'
import { DepartmentName } from '~/api/models/Department/Department'
import { PositionName } from '~/api/models/Position/Position'
import type { IssueActionData, IssueCategory } from '~/api/models/Issue/Issue'
import { type IssueResult } from '~/api/models/Issue/Issue'
import type { z } from 'zod'
import { toastError, toastSuccess } from '@/lib/core/helper/ToastHelper'
import { leadJivoLinkValidation } from '~/api/models/Lead/Lead'
import { $confirm } from '@/plugins/ConfirmPlugin'

defineOptions({
    name: 'LeadCreateForm',
})
const props = defineProps<LeadCreateFormProps>()

const emit = defineEmits<{
    close: [],
    resolve: [],
}>()

const {
    useDictionary,
    useModel,
    useCurrentUser,
} = useContext()

//

const isEditMode = computed((() => Boolean(props.editLeadPk)))
const isFormPopulated = ref(false)

//

const projectDictionary = useDictionary('Project')
const projectOptions = computed(() => projectDictionary.mapRecords.forSelect({ full: true }))

//

const externalResourceDictionary = useDictionary('ExternalResource')
const externalResourceOptions = computed(() => {
    const options = externalResourceDictionary.mapEditableResources.forSelect()

    if (isUserInDepartment(currentUser, DepartmentName.LeadManagement)) {
        return options.filter(option => option.value !== ExternalResource.Social)
    }

    return options
})

//

const itineraryClassDictionary = useGeneralDictionary('ItineraryClass')
const itineraryClassOptions = computed(() => itineraryClassDictionary.mapRecords.forSelect())

//

const { selectOptions: iataDepartureOptions, search: searchDepartureIata, unknownIataPk } = useIataSelect()
const { selectOptions: iataDestinationOptions, search: searchDestinationIata } = useIataSelect()

//

const fromIataForSearch = ref('')
const toIataForSearch = ref('')

//

let keepCuratorData: {
    remark: string,
    keep: boolean,
} | null = null

//

const returnDateMin = computed(() => {
    const value = form.data.departure_date ? form.data.departure_date * 1000 : Date.now()

    return new Date(value)
})

//

const isJivoResource = computed(() => {
    const jivoResource = externalResourceOptions.value.find(resource => resource.value === 'jivo')

    if (!jivoResource) {
        return false
    }

    return form.data.external_resource === jivoResource.value
})

//

const form = useForm<Partial<Omit<FormPayload, 'client'>> & Pick<FormPayload, 'client'>>({
    external_resource: undefined,
    jivo_link: undefined,
    itinerary_class: undefined,
    from_iata_pk: undefined,
    to_iata_pk: undefined,
    departure_date: undefined,
    return_date: undefined,
    adult_count: 1,
    child_count: 0,
    infant_count: 0,
    remark: '',
    project_pk: projectOptions.value[0].value,
    send_to_queue: false,

    client: {
        first_name: '',
        last_name: undefined,
        emails: [''],
        phones: [''],
        is_unknown_phone: false,
    },
}, {
    external_resource: ValidationRules.RequiredWhen(() => !isEditMode.value, 'Resource is required'),
    jivo_link: (jivoLink, _, formData) => {
        if (isJivoResource.value && !isEditMode.value) {
            if (!jivoLink) {
                return 'Jivo Link is required'
            }

            const jivoLinkIsValid = new RegExp(leadJivoLinkValidation).test(jivoLink)

            if (!jivoLinkIsValid) {
                return 'Jivo Link is not valid'
            }
        }
    },
    itinerary_class: ValidationRules.Required('Class is required'),
    from_iata_pk: ValidationRules.Required('Departure is required'),
    to_iata_pk: [
        ValidationRules.Required('Destination is required'),
        (iata_pk, _, formData) => {
            if (iata_pk !== unknownIataPk.value && formData.from_iata_pk !== unknownIataPk) {
                if (iata_pk === formData.from_iata_pk) {
                    return 'Departure and destination can\'t be equal'
                }
            }
        },
    ],
    departure_date: ValidationRules.Required(),
    project_pk: ValidationRules.RequiredWhen(() => !isEditMode.value, 'Project is required'),
    client: {
        first_name: ValidationRules.RequiredWhen(() => !props.hideClientRelatedFields),
        emails: [
            ValidationRules.Required('You must provide an email'),
            ValidationRules.Array([
                ValidationRules.Required('Email is required'),
                ValidationRules.Email('Email is not valid'),
            ]),
        ],
    },
    adult_count: (adult_count, _, formData) => {
        if (!formData.adult_count && !formData.child_count && !formData.infant_count) {
            return 'Passenger is required'
        }
    },
})

//

onBeforeMount(async () => {
    await populateForm()
    isFormPopulated.value = true
    await Promise.all([
        searchDepartureIata(fromIataForSearch.value),
        searchDestinationIata(toIataForSearch.value),
    ])
})

function onDatesChange() {
    const departureDate = Date.fromUnixTimestamp(form.data.departure_date).setHours(0, 0, 0, 0)
    const returnDate = Date.fromUnixTimestamp(form.data.return_date).setHours(0, 0, 0, 0)

    if (departureDate > returnDate) {
        form.data.return_date = undefined
    }
}

/**
 *  Priority is defined based on props definition order
 */
async function populateForm() {
    const populate = props.populate

    const formData: Partial<FormPayload> = {}

    // Calculate lead record
    const leadRecord: Partial<ModelAttributes<'Lead'>> = {}

    const leadPk = props.editLeadPk || populate?.leadPk

    if (leadPk) {
        const lead = (await useModel('Lead').useRecord().fetch(leadPk)).value

        Object.assign(leadRecord, lead)
    }

    if (populate?.leadRecord) {
        Object.assign(leadRecord, filterUndefined(populate.leadRecord))
    }

    if (leadRecord.from_iata_code) {
        fromIataForSearch.value = leadRecord.from_iata_code

        formData.from_iata_pk = tryUsePk((await useModel('Iata').useRecord({
            where: (and) => and.eq('code', leadRecord.from_iata_code!),
        }).fetch()).value)
    }

    if (leadRecord.to_iata_code) {
        toIataForSearch.value = leadRecord.to_iata_code

        formData.to_iata_pk = tryUsePk((await useModel('Iata').useRecord({
            where: (and) => and.eq('code', leadRecord.to_iata_code!),
        }).fetch()).value)
    }

    // Update form data from calculated lead record
    updatePropsWithCondition(formData, {
        external_resource: leadRecord.external_resource || undefined,
        itinerary_class: leadRecord.itinerary_class || undefined,
        departure_date: leadRecord.departure_date || Date.now() / 1000,
        return_date: leadRecord.return_date || undefined,
        adult_count: leadRecord.adult_count ?? undefined,
        child_count: leadRecord.child_count ?? undefined,
        infant_count: leadRecord.infant_count ?? undefined,
        remark: leadRecord.remark || undefined,
        project_pk: leadRecord.project_pk || undefined,
    } satisfies Partial<FormPayload>, (newPropValue) => newPropValue !== undefined)

    // Update form data based on client
    const clientPk = populate?.clientPk || leadRecord.client_pk

    // Create empty client object to fill
    formData.client = {
        first_name: '',
        last_name: '',
        emails: [''],
        phones: [''],
        is_unknown_phone: false,
    }

    if (clientPk) {
        const client = (await useModel('Client').useRecord().fetch(clientPk)).value

        formData.project_pk = client.project_pk

        formData.client.first_name = client.first_name
        formData.client.last_name = client.last_name || undefined
        formData.client.phones = [client.phone]
        formData.client.emails = [client.email]
    }

    if (leadRecord.client_email_pk) {
        const email = (await useModel('Email').useRecord().fetch(leadRecord.client_email_pk)).value

        formData.client.emails = [email.value]
    }

    if (leadRecord.client_phone_pk) {
        const phone = (await useModel('Phone').useRecord().fetch(leadRecord.client_phone_pk)).value

        formData.client.phones = [phone.value]
    }

    // Update form data from direct prop
    if (populate?.formData) {
        Object.assign(formData, toRaw(populate.formData))
    }

    // last validation on unknown number '****** 000 0000'
    formData.client.is_unknown_phone = leadPk ? !formData.client.phones[0] : false

    const preparedUnknownPhoneNumbers = config.phoneNumber.unknown.map(number => number.replace(/ /g, ''))

    if (formData.client.phones[0] && preparedUnknownPhoneNumbers.includes(formData.client.phones[0].replace(/ /g, ''))) {
        formData.client.is_unknown_phone = true
        formData.client.phones[0] = ''
    }

    // Apply changes
    form.updateInitialData(toRaw(formData) as any)
}

//

const clientStatusForm = ref<{
    form: Form<z.infer<typeof IssueResult[IssueCategory.ClientStatus]>>
}>()

const additionalData = ref<{
    form: Form<z.infer<typeof IssueActionData>>
}>()

const clientStatusDictionary = useGeneralDictionary('ClientStatus')

const showClientStatusAdditionalForm = computed(() => {
    const clientStatusPk = clientStatusForm.value?.form.data.client_status_pk

    const newClientStatusPk = usePk(clientStatusDictionary.findByName(ClientStatusName.New))

    return clientStatusPk !== newClientStatusPk
})

//

const hiddenClientRelatedFields = [
    'client.emails.0',
    'client.phones.0',
    'client.first_name',
    'client.last_name',
]

const fraudCheck = async (data: FormPayload) => {
    const fraudEmailList = await useModel('Email').actions.checkIfFraud({ value: data.client.emails })

    if (fraudEmailList.length) {
        await $confirm({
            text: 'The following email addresses have been flagged as potentially fraudulent. Do you want to proceed?',
            description: fraudEmailList.join(', '),
        })
    }

    const phones = data.client.phones.filter(phone => phone != '' && phone != null)

    if (phones.length > 0) {
        const fraudPhoneList = await useModel('Phone').actions.checkIfFraud({ value: phones })

        if (fraudPhoneList.length) {
            await $confirm({
                text: 'The following phones have been flagged as potentially fraudulent. Do you want to proceed?',
                description: fraudPhoneList.join(', '),
            })
        }
    }
}

const submit = form.useSubmit(async () => {
    const data = form.data as FormPayload

    if (props.hideClientRelatedFields) {
        await fraudCheck(data)
    }

    if (isEditMode.value) {
        await handleUpdate(data)
    } else {
        await handleCreate(data)
    }
}, {
    resetOnSuccess: false,
    onValidate: () => {
        if (props.hideClientRelatedFields && form.errors.any()) {
            hiddenClientRelatedFields.forEach((error) => {
                if (form.errors.has(error)) {
                    toastError(form.errors.get(error).join('<br>'))
                }
            })
        }
    },
})

async function handleCreate(data: FormPayload) {
    const clientCuratorPk = await getClientActiveCurator()

    if (clientCuratorPk && clientCuratorPk !== usePk(currentUser)) {
        try {
            keepCuratorData = await keepClientCuratorModal.open({
                curatorPk: clientCuratorPk,
                remark: keepCuratorData?.remark || undefined,
                keep: keepCuratorData?.keep || undefined,
            })
        } catch (rejectedData: any) {
            keepCuratorData = rejectedData

            return
        }
    } else {
        keepCuratorData = null
    }

    const statusFormData = clientStatusForm.value?.form.data
    const additionalFormData = additionalData.value?.form.data

    const { lead_pk } = await useModel('Lead').actions.create({
        lead: {
            external_resource: data.external_resource,
            itinerary_class: data.itinerary_class,
            from_iata_pk: data.from_iata_pk,
            to_iata_pk: data.to_iata_pk,
            departure_date: data.departure_date,
            return_date: data.return_date || null,
            adult_count: data.adult_count || 0,
            child_count: data.child_count || 0,
            infant_count: data.infant_count || 0,
            remark: data.remark,
            project_pk: data.project_pk,
        },
        client: props.hideClientRelatedFields && props.populate?.clientPk
            ? props.populate.clientPk
            : {
                data: {
                    first_name: data.client.first_name,
                    last_name: data.client.last_name || null,
                    emails: data.client.emails,
                    phones: data.client.is_unknown_phone ? [] : data.client.phones.filter(phone => phone != '' && phone != null),
                    is_unknown_phone: data.client.is_unknown_phone,
                },
                status: {
                    result: statusFormData,
                    data: additionalFormData,
                },
            },
        keep_client_curator: keepCuratorData,
        send_to_queue: data.send_to_queue,
        jivo_link: data.jivo_link,
    })

    toastSuccess(`Lead #${lead_pk} added`)
    emit('resolve')
}

async function handleUpdate(data: FormPayload) {
    const lead_pk = props.editLeadPk!

    await useModel('Lead').actions.update({
        pk: lead_pk,
        lead: {
            itinerary_class: data.itinerary_class,
            from_iata_pk: data.from_iata_pk,
            to_iata_pk: data.to_iata_pk,
            departure_date: data.departure_date,
            return_date: data.return_date || null,
            adult_count: data.adult_count || 0,
            child_count: data.child_count || 0,
            infant_count: data.infant_count || 0,
            remark: data.remark,
        },
        client: {
            first_name: data.client.first_name,
            last_name: data.client.last_name || null,
            emails: data.client.emails,
            phones: data.client.is_unknown_phone ? [] : data.client.phones.filter(phone => phone != '' && phone != null),
            is_unknown_phone: data.client.is_unknown_phone,
        },
    })

    toastSuccess(`Lead #${lead_pk} updated`)
    emit('resolve')
}

// Keep curator logic

async function getClientActiveCurator() {
    const data = form.data as FormPayload

    const { curator_pk } = await useModel('Client').actions.getActiveCurator({
        project_pk: data.project_pk,
        client_pk: props.populate?.clientPk || null,
        emails: data.client.emails,
        phones: data.client.is_unknown_phone ? [] : data.client.phones,
        is_unknown_phone: data.client.is_unknown_phone,
    })

    return curator_pk
}

const keepClientCuratorModal = useModal(LeadKeepClientCuratorModal, undefined, {
    position: 'center',
})

//

const currentUser = useCurrentUser()

const isLeadManager = (
    isUserInDepartment(currentUser, DepartmentName.LeadManagement) ||
    isUserHasPosition(currentUser, PositionName.Supervisor) || (
        isUserInDepartment(currentUser, DepartmentName.IT) &&
        isUserHasPosition(currentUser, PositionName.Manager)
    )
)
</script>
