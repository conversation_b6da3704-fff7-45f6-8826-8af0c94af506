<template>
    <span
        v-if="score !== null"
        v-tooltip="`Lead to Sale conversion probability`"
        class="badge"
        :class="leadConversionClasses"
    >
        {{ props.score }}%
    </span>
</template>

<script setup lang="ts">
const props = defineProps<{
    score: number | null,
}>()

const leadConversionClasses = computed(() => {
    return props.score < 50 ? '--danger' : '--success'
})
</script>

