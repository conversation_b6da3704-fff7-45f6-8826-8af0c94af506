<template>
    <AppTable
        :columns="columns"
        :items="checkLeadsActionResponse.leads_info"
        zebra
    >
        {{ checkLeadsActionResponse.leads_info }} //
        <template #body>
            <tr
                v-for="item in checkLeadsActionResponse.leads_info"
                :key="item.id"
                :class="{'table-tr--highlighted table-tr--highlighted-primary': checkLeadsActionResponse.lead_pk === usePk('Lead', item)}"
            >
                <TableCellLink :to="linkToLead(item.id)" target="_blank">
                    {{ item.id }}
                </TableCellLink>

                <td>
                    {{ item.from_iata_code }}
                </td>

                <td>
                    {{ item.to_iata_code }}
                </td>

                <td>
                    {{ $format.date(item.departure_date, 'UTC') }}
                </td>
                <td>
                    {{ item.return_date ? $format.date(item.return_date, 'UTC') :
                        dateFormatItinerary('', item.itinerary_type) }}
                </td>

                <td>
                    <span>{{ item.pq_count }}</span>
                </td>

                <td>
                    <div class="truncate max-w-[120px]">
                        <span v-if="item.created_by_pk">
                            {{ getFullName(getExecutorByPk(item.created_by_pk)) }}
                        </span>
                        <span v-else class="text-danger">
                            Not assigned
                        </span>
                    </div>
                </td>
                <td>
                    {{ $format.datetime(item.created_at) }}
                </td>
                <td>
                    <div class="truncate max-w-[120px]">
                        <span v-if="item.executor_pk">
                            {{ getFullName(getExecutorByPk(item.executor_pk)) }}
                        </span>
                        <span v-else class="text-danger">
                            Not assigned
                        </span>
                    </div>
                </td>
                <td class="h-full">
                    <div class="flex items-center">
                        <div class="flex justify-center w-full space-x-1.5">
                            <div
                                v-tooltip="{ content: getLeadStatusTooltip(item), html: false }"
                            >
                                <LeadStatusBadge
                                    :status-pk="item.status_pk"
                                    class="cursor-pointer"
                                    small
                                />
                            </div>
                            <div v-if="item.is_bonus" v-tooltip="{ content: 'Bonus' }">
                                <LeadBonusBadge class="cursor-pointer" small />
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        </template>
    </AppTable>
</template>

<script setup lang="ts">
import { dateFormatItinerary } from '~/lib/Helper/LeadHelper'
import { getFullName } from '~/lib/Helper/PersonHelper'
import TableCellLink from '~/components/Table/TableCellLink.vue'
import LeadStatusBadge from '~/components/Page/Lead/LeadStatusBadge.vue'
import type { ActionResponse } from '~types/lib/Model'
import LeadBonusBadge from '~/components/Page/Lead/LeadBonusBadge.vue'
import { LeadStatusName } from '~/api/models/Lead/LeadStatus'
import { ClosingReasons } from '~/api/models/Lead/Lead'

defineOptions({
    name: 'CheckLeadTable',
})

withDefaults(defineProps<{
    checkLeadsActionResponse: ActionResponse<'Lead', 'checkLead'>
}>(), {})

const { useDictionary } = useContext()

const agentDictionary = useDictionary('Agent')
const leadStatusDictionary = useGeneralDictionary('LeadStatus')

const getExecutorByPk = (pk: PrimaryKey) => {
    return agentDictionary.find(pk)
}

//

const getStatusByPk = (pk: PrimaryKey) => {
    return leadStatusDictionary.findOrFail(pk)
}

const closingReasonDictionary = useGeneralDictionary('ClosingReason')

const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text

    return text.substring(0, maxLength) + '...'
}

const getLeadStatusTooltip = (leadRecord: ActionResponse<'Lead', 'checkLead'>['leads_info'][number]) => {
    const status = getStatusByPk(leadRecord.status_pk)

    const closingReason = leadRecord.closing_reason
    const closingReasonRemark = leadRecord.closing_reason_remark ?? ''

    if ([LeadStatusName.Lost, LeadStatusName.Closed, LeadStatusName.Fraud].includes(status.system_name) && closingReason) {
        const baseResult = status.name + ' (reason: '

        if (closingReason === ClosingReasons.Other) {
            return baseResult + truncateText(closingReasonRemark, 150) + ')'
        }

        const reasonTitle = closingReasonDictionary.find(closingReason).title

        return baseResult + reasonTitle + ')'
    }

    return status.name
}

//

const columns = useTableColumns({
    id: {
        label: '#',
        sortable: true,
        width: 4,
    },
    from_iata_code: {
        label: 'From',
        sortable: true,
        width: 5,
    },
    to_iata_code: {
        label: 'To',
        sortable: true,
        width: 5,
    },
    departure_date: {
        label: 'Departure',
        sortable: true,
        width: 5,
    },
    return_date: {
        label: 'Return',
        sortable: true,
        width: 5,
    },
    pq_count: {
        label: 'PQs',
        sortable: true,
        tooltip: 'Total PQ\'s count',
        width: 3,
    },
    created_by: {
        label: 'Created by',
        width: 5,
    },
    created_at: {
        label: 'Created at',
        sortable: true,
        width: 5,
    },
    executor_pk: {
        label: 'Agent',
        sortable: true,
        width: 10,
    },
    status: {
        label: 'Status',
        sortable: false,
        width: 'min',
    },
})
</script>
