<template>
    <div>
        <div class="flex justify-between">
            <div class="flex flex-col">
                <span class="text-base font-semibold">
                    {{ index }}. {{ releasePostItem.title }}
                </span>
                <span v-if="taskNumber" class="text-secondary-400">
                    Ref: {{ taskNumber }}
                </span>
            </div>
            <AppButton class="--ghost --small --neutral -my-0.5" @click="localExpanded = !localExpanded">
                {{ localExpanded ? 'Show less' : 'Show more' }}
                <ChevronUpIcon v-if="localExpanded" />
                <ChevronDownIcon v-else />
            </AppButton>
        </div>
        <div
            v-if="localExpanded"
            class="mt-2 border dark:border-secondary-600 rounded"
        >
            <div
                class=" release-note-content ck-content"
                v-html="releasePostItem.description"
            />

            <ReleasePostItemFeedbacksSection
                :can-add-feedback="canLeaveComment"
                :can-reply="canLeaveComment"
                :release-post-item-pk="usePk(releasePostItem)"
            />

            <div v-if="agents.length" class="m-4 flex gap-2">
                <div class="flex items-start">
                    <span class="rounded-full p-1 bg-secondary mt-1" />
                </div>
                <span class=" text-xs">
                    Requested by:
                    <span class="font-medium" :class="{'text-primary': viewerIsRequester}">{{ agents }}</span>
                </span>
            </div>

            <div v-if="filteredAvailableFor.length" class="m-4 flex gap-2">
                <div class="flex items-start">
                    <span class="rounded-full p-1 bg-success mt-1" />
                </div>

                <span class="text-xs">
                    Available to: <span class="font-medium">{{ filteredAvailableFor }}</span>
                </span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { ModelRef } from '~types/lib/Model'
import { useRulesController } from '~/sections/ReleasePost/composable/useRulesController'
import { getFullName } from '~/lib/Helper/PersonHelper'
import ReleasePostItemFeedbacksSection from '~/sections/ReleasePost/ReleasePostItemFeedbacksSection.vue'

const props = defineProps<{
    releasePostItem: ModelRef<'ReleasePostItem', 'info'>,
    expanded?: boolean
    canLeaveComment?: boolean
    index: number
}>()

const { useDictionary, currentUserPk } = useContext()
const agentDictionary = useDictionary('Agent')

const rulesController = useRulesController()

if (props.releasePostItem.info.condition) {
    rulesController.initControllers(props.releasePostItem.info.condition)
}

const filteredAvailableFor = computed(() => {
    return rulesController.getAvailableFor(props.releasePostItem)
})

const localExpanded = ref<boolean>(props.expanded)

const taskNumber = computed(() => {
    if (props.releasePostItem.ref_task_url) {
        return props.releasePostItem.ref_task_url.match(/TD-\d{3,}/g)?.pop() || ''
    } else {
        return null
    }
})

const viewerIsRequester = computed(() => props.releasePostItem.requested_by_pks.includes(currentUserPk))
const agents = props.releasePostItem.requested_by_pks.map(agentPk => agentDictionary.findOrFail(agentPk)).map(getFullName).join(', ')
</script>
