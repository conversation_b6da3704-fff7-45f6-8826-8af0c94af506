<template>
    <div class="flex gap-2.5 items-center">
        <div class="w-fit relative">
            <AppAvatar
                :image="agentShortInfo.image"
                :name="agentShortInfo.fullName"
                class="w-9 h-9 mr-3"
            />
            <span
                v-if="state"
                :class="{
                    'bg-danger': !state.is_online,
                    'bg-success-400': state.is_online,
                }"
                class="block w-2 h-2 rounded-lg absolute -top-px -right-px"
            />
        </div>

        <div class="flex flex-col text-xs">
            <div class="font-medium">
                {{ agentShortInfo.fullName }}
            </div>
            <div class="text-secondary-400">
                {{ subtitle }}
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { getFullName } from '~/lib/Helper/PersonHelper'
import type { ModelAttributes } from '~types/lib/Model'

const props = defineProps<{
    agent: ModelAttributes<'Agent'>
    state?: ModelAttributes<'AgentAdditionalState'>
}>()

const { useDictionary } = useContext()

const departmentDictionary = useDictionary('Department')
const positionDictionary = useDictionary('Position')
const teamDictionary = useDictionary('Team')

const agentShortInfo = computed(() => ({
    image: props.agent.avatar,
    fullName: getFullName(props.agent),
    department: departmentDictionary.findOrFail(props.agent.department_pk).name,
    position: positionDictionary.findOrFail(props.agent.position_pk).name,
    team: props.agent.team_pk ? teamDictionary.findOrFail(props.agent.team_pk).name : null,
}))

const subtitle = computed(() => [agentShortInfo.value.department, agentShortInfo.value.team, agentShortInfo.value.position].filter(Boolean).join(', '))
</script>
