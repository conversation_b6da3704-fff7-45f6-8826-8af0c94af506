<template>
    <div class="card card__body">
        <div class="flex justify-between">
            <div class="flex gap-4">
                <span class="text-xl font-semibold">
                    {{ releasePost.title }}
                </span>
                <span v-if="latest" class="badge --primary --soft --normal">
                    New
                </span>
            </div>

            <AppButton
                v-if="canNote"
                class="--small"
                :class="{
                    '--primary': releaseCheckService.lastNotedReleasePost.value.value === releasePost.id
                }"
                @click="releaseCheckService.updateLastNotedReleasePost(releasePost)"
            >
                Noted
                <CheckIcon v-if="releaseCheckService.isNotedReleasePost(releasePost)" class="icon" />
            </AppButton>
        </div>
        <div class="text-xs mt-1.5">
            {{ releasePost.description }}
        </div>

        <div v-if="isExpanded && releasePostItemList.records.length">
            <hr class="mt-4 dark:border-secondary-600">
            <div class="flex flex-col gap-3 my-4">
                <ReleasePostItemSection
                    v-for="(releasePostItem, index) in releasePostItemList.records"
                    :key="index"
                    :release-post-item="releasePostItem"
                    :expanded="expanded"
                    :can-leave-comment="latest"
                    :index="index + 1"
                />
            </div>
        </div>

        <AppButton
            class="mt-4"
            :loading="releasePostItemList.loading"
            @click="expand"
        >
            {{ isExpanded ? 'Show less' : 'Show more' }}
            <ChevronUpIcon v-if="isExpanded" />
            <ChevronDownIcon v-else />
        </AppButton>
    </div>
</template>

<script setup lang="ts">
import type { ModelAttributes } from '~types/lib/Model'
import ReleasePostItemSection from '~/sections/ReleasePost/ReleasePostItemSection.vue'

const props = defineProps<{
    releasePost: ModelAttributes<'ReleasePost'>
    latest?: boolean,
    expanded?: boolean,
}>()

const isExpanded = ref<boolean>(false)

const { useModel } = useContext()
const releaseCheckService = useService('releaseCheckService')

const releasePostItemModel = useModel('ReleasePostItem')
const releasePostItemList = releasePostItemModel.useList({
    where: and => {
        and.eq('release_post_pk', usePk(props.releasePost))
    },
    sort: { position: 'asc' },
    with: ['info'],
    customParams: {
        scenario: 'modal',
    },
    limit: false,
})

const expand = async () => {
    isExpanded.value = !isExpanded.value

    if (isExpanded.value) {
        await releasePostItemList.fetch()
    }
}

if (props.expanded) {
    expand()
}

const canNote = !releaseCheckService.isNotedReleasePost(props.releasePost)
</script>
