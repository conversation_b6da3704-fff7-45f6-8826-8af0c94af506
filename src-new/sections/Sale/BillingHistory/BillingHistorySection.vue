<template>
    <div>
        <div
            class="flex items-center justify-between mt-5"
        >
            <h6 class="m-0">
                Billing history
            </h6>
        </div>

        <div class="card mt-2">
            <div class="card__body m-0 p-0 max-h-60 w-full overflow-y-auto fancy-scroll">
                <AppTable
                    :columns="columns"
                    class="--compact"
                >
                    <template #body>
                        <BillingHistoryNotificationWrapper
                            v-for="saleTransaction in saleTransactions"
                            :key="saleTransaction.id"
                            :model-value="saleTransaction"
                        >
                            <td class="cursor-pointer whitespace-normal" @click="openBillingHistory(usePk(saleTransaction))">
                                <div class="block text-2xs">
                                    {{ TransactionService.getTransactionDescription(saleTransaction) }}
                                </div>
                                <div class="block text-primary">
                                    {{ saleTransaction.external_id }}
                                </div>
                            </td>
                            <td
                                v-tooltip="`${saleTransaction.status === SaleTransactionStatus.Error ? saleTransaction.system_message : ''}`"
                                class="text-center"
                            >
                                <div :class="saleTransactionController.getSaleTransactionStatusTitleStyleClass(saleTransaction)" class="rounded-full py-0.5 px-1">
                                    {{ saleTransactionController.getSaleTransactionStatusTitle(saleTransaction) }}
                                </div>
                            </td>
                            <td class="text-center">
                                {{ saleTransactionActionTypeDictionary.find(saleTransaction.action_type).title }}
                            </td>
                            <td class="text-2xs">
                                <div class="font-semibold">
                                    <span>{{ creditCardTypeDictionary.find(saleTransaction.card.credit_card_type).title }} **** {{ saleTransaction.card.strip.slice(-4) }}</span>
                                    <span class="ml-1.5">({{ saleTransaction.paymentGateway.abbreviation }})</span>
                                </div>
                                <div class="text-muted">
                                    Expiry {{ saleTransaction.card.expiration }}
                                </div>
                            </td>
                            <td class="text-right">
                                {{ saleTransaction.direction_type === SaleTransactionDirectionType.Outcome ? '-' : '' }}{{ formatter.money(saleTransaction.amount, {withCurrency: true}) }}
                            </td>
                            <td>
                                <div v-tooltip="`${formatter.datetime(saleTransaction.created_at, 'Europe/Chisinau', { format: 'dd MMM yyyy HH:mm:ss' })} (${chisinauGmtTime})`" class="cursor-pointer">
                                    {{ Date.fromUnixTimestamp(saleTransaction.created_at).toFormat('dd MMM yyyy') }}
                                </div>
                                <div class="text-muted">
                                    {{ Date.fromUnixTimestamp(saleTransaction.created_at).toFormat('HH:mm:ss') }}
                                </div>
                            </td>
                            <td class="font-semibold">
                                <div
                                    class="w-3 h-3 mr-1"
                                    :class="{'flex align-middle text-orange-500 !font-bold': saleTransactionController.isPartialAuthorized(saleTransaction)}"
                                >
                                    <AlertTriangleIcon v-if="saleTransactionController.isPartialAuthorized(saleTransaction)" />
                                    <span>{{ saleTransaction.settlement_amount ? `${formatter.money(saleTransaction.settlement_amount, {withCurrency: true})}` : '' }}</span>
                                </div>
                            </td>
                            <td>
                                <div v-tooltip="`${formatter.datetime(saleTransaction.settlement_at, 'Europe/Chisinau', { format: 'dd MMM yyyy HH:mm:ss' })} (${chisinauGmtTime})`" class="cursor-pointer">
                                    {{ !!saleTransaction.settlement_at ? Date.fromUnixTimestamp(saleTransaction.settlement_at).toFormat('dd MMM yyyy') : '' }}
                                </div>
                                <div class="text-muted">
                                    {{ !!saleTransaction.settlement_at ? Date.fromUnixTimestamp(saleTransaction.settlement_at).toFormat('HH:mm:ss') : '' }}
                                </div>
                            </td>
                            <td>
                                {{ saleTransaction.createdBy.first_name }} {{ saleTransaction.createdBy.last_name ? `${saleTransaction.createdBy.last_name[0]}.` : '' }}
                            </td>
                            <td v-if="canEdit" class="text-center">
                                <div class="flex flex-col gap-2">
                                    <AppButton
                                        v-if="saleTransactionController.canRepeat(saleTransaction)"
                                        @click="repeatSaleTransaction(usePk(saleTransaction))"
                                    >
                                        Repeat
                                    </AppButton>
                                    <AppButton
                                        v-else-if="saleTransactionController.canRefund(saleTransaction)"
                                        @click="refundSaleTransaction(usePk(saleTransaction))"
                                    >
                                        Refund
                                    </AppButton>

                                    <AppButton
                                        v-if="saleTransactionController.canCancelOrCapture(saleTransaction) && saleTransactionController.canCancel(saleTransaction)"
                                        class="--outline --primary"
                                        @click="cancelSaleTransaction(usePk(saleTransaction))"
                                    >
                                        Void
                                    </AppButton>
                                    <AppButton
                                        v-if="saleTransactionController.canCancelOrCapture(saleTransaction) && saleTransactionController.canCapture(saleTransaction)"
                                        class="--primary"
                                        @click="captureSaleTransaction(usePk(saleTransaction))"
                                    >
                                        Capture
                                    </AppButton>
                                </div>
                            </td>
                            <td>
                                <InputCheckbox @update:model-value="($event) => saleTransactionCheckBoxChanged(saleTransaction, $event)" />
                            </td>
                        </BillingHistoryNotificationWrapper>
                    </template>
                </AppTable>
            </div>
        </div>
        <BillingHistorySectionToCopy
            ref="toCopy"
            class="hidden"
            :sale-transactions="selectedItems"
        />
    </div>
</template>

<script setup lang="ts">
import AppTable from '~/components/Table/AppTable.vue'
import TransactionService from '@/lib/service/TransactionService'
import { useSaleTransactionController } from '~/sections/Sale/BillingHistory/composable/useSaleTransactionController'
import { SaleTransactionDirectionType, SaleTransactionStatus } from '~/api/models/Sale/SaleTransaction'
import { withDirectives, resolveDirective } from 'vue'
import { copyElementToClipboard } from '@/lib/core/helper/ClipboardHelper'
import type { ModelRef } from '~types/lib/Model'
import BillingHistoryModal from '~/sections/Sale/BillingHistory/Modals/BillingHistoryModal.vue'
import RefundTransactionModal from '~/sections/Sale/BillingHistory/Modals/RefundTransactionModal.vue'
import BillingHistorySectionToCopy from '~/sections/Sale/BillingHistory/BillingHistorySectionToCopy.vue'
import { toastDefault } from '@/lib/core/helper/ToastHelper'
import BillingHistoryNotificationWrapper
    from '~/sections/Sale/BillingHistory/Wrappers/BillingHistoryNotificationWrapper.vue'
import { getChisinauGmtTime } from '@/lib/core/helper/DateHelper'
import { useSaleTransactions } from '~/sections/Sale/composable/useSaleTransactions'

const props = defineProps<{
    salePk: PrimaryKey
}>()

const formatter = useService('formatter')

const { useModel, useDictionary, hasPermission, record: sale } = await useNewContext('Sale', props.salePk)

const saleModel = useModel('Sale')
const saleTransactionModel = useModel('SaleTransaction')

// @todo Does not rerenders when salePk changes
const { transactions, fetch: fetchTransactions } = useSaleTransactions(props.salePk)

await fetchTransactions()

const saleTransactions = computed(() => {
    return transactions.value.toSorted((a, b) => b.created_at - a.created_at)
})

const canEdit = computed(() => {
    return hasPermission('editPayments', 'Sale', sale) && hasPermission('editCards', 'Sale', sale)
})

const vTooltip = resolveDirective('tooltip')

const selectedItems = ref<ModelRef<'SaleTransaction', 'card' | 'createdBy'>[]>([])

const saleTransactionCheckBoxChanged = (saleTransaction: ModelRef<'SaleTransaction', 'card' | 'createdBy'>, insert: boolean) => {
    if (insert) {
        selectedItems.value.push(saleTransaction)
    } else {
        selectedItems.value = selectedItems.value.filter((item) => item.pk !== saleTransaction.pk)
    }
}

const columns = useTableColumns({
    id: {
        label: 'Product / Trans. id',
        sortable: true,
        width: 20,
    },
    status: {
        label: 'Status',
        width: 'min',
    },
    type: {
        label: 'Type',
        width: 'min',
    },
    payment_method: {
        label: 'Payment Method',
    },
    amount: {
        label: 'Amount',
        width: 'min',
    },
    date: {
        label: 'Date',
        width: 'min',
    },
    sell_amount: {
        label: 'S.Amount',
        width: 'min',
    },
    sell_date: {
        label: 'S.Date',
        width: 'min',
    },
    agent: {
        label: 'Agent',
    },
    actions: {
        label: 'Actions',
        enabled: canEdit.value,
        width: 'min',
    },
    copy: {
        headingComponent: () => h('th', [
            withDirectives(
                h(CopyIcon, { onClick: () => copySelected() }),
                [[vTooltip, 'Copy selected rows']],
            ),
        ]),
        width: 'min',
    },
})

const saleTransactionActionTypeDictionary = useDictionary('SaleTransactionActionType')
const creditCardTypeDictionary = useDictionary('CreditCardType')

const saleTransactionController = useSaleTransactionController()

const openBillingHistory = (saleTransactionPk: PrimaryKey) => {
    useModal(BillingHistoryModal).open({ saleTransactionPk })
}

const refundSaleTransaction = async (pk: PrimaryKey) => {
    await useModal(RefundTransactionModal).open({
        saleTransactionPk: pk,
        mode: 'refund',
    })
}

const cancelSaleTransaction = async (pk: PrimaryKey) => {
    await useModal(RefundTransactionModal).open({
        saleTransactionPk: pk,
        mode: 'cancel',
    })
}

const repeatSaleTransaction = async (pk: PrimaryKey) => {
    await saleTransactionModel.actions.repeat({ pk })
}

const captureSaleTransaction = async (pk: PrimaryKey) => {
    await saleTransactionModel.actions.capture({ pk })
}

const toCopy = ref()

const copySelected = () => {
    if (selectedItems.value.length) {
        nextTick(() => {
            const el = toCopy.value.$el

            if (el) {
                copyElementToClipboard(el)
            }
        })
    } else {
        toastDefault('Please select transactions')
    }
}

const chisinauGmtTime = computed(() => getChisinauGmtTime())
</script>
