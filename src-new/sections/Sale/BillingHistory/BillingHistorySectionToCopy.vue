<template>
    <table
        border="1"
        cellpadding="5"
        cellspacing="0"
    >
        <thead>
            <tr>
                <th>Product / Trans. id</th>
                <th align="center">
                    Status
                </th>
                <th align="center">
                    Type
                </th>
                <th>Payment method</th>
                <th align="right">
                    Amount
                </th>
                <th>Date</th>
                <th align="right">
                    S. amount
                </th>
                <th>S. date</th>
                <th>Agent</th>
            </tr>
        </thead>
        <tbody>
            <tr
                v-for="item in saleTransactions"
                :key="item.id"
            >
                <td>
                    <div style="font-weight: bold">
                        {{ TransactionService.getTransactionDescription(item) }}
                    </div>
                    <span>{{ item.external_id }}</span>
                </td>
                <td align="center">
                    {{ saleTransactionController.getSaleTransactionStatusTitle(item) }}
                </td>
                <td align="center">
                    {{ saleTransactionActionTypeDictionary.find(item.action_type).title }}
                </td>
                <td>
                    <div>
                        <div style="font-weight: bold">
                            <span>{{ creditCardTypeDictionary.find(item.card.credit_card_type).title }} {{ item.card.strip }}</span>
                            <span>&nbsp;({{ item.paymentGateway.abbreviation }})</span>
                        </div>
                        <div>
                            Expiry {{ item.card.expiration }}
                        </div>
                    </div>
                </td>
                <td
                    align="right"
                    class="font-semibold"
                >
                    {{ item.direction_type === SaleTransactionDirectionType.Outcome ? '-' : '' }}{{ formatter.money(item.amount) }}
                </td>
                <td>
                    {{ Date.fromUnixTimestamp(item.created_at).formatDateTime() }}
                </td>
                <td
                    align="right"
                    class="font-semibold "
                >
                    <span>{{ item.settlement_amount ? `${formatter.money(item.settlement_amount)}` : '' }}</span>
                </td>
                <td data-copy="false">
                    {{ Date.fromUnixTimestamp(item.settlement_at).formatDateTime() }}
                </td>
                <td>
                    {{ item.createdBy.first_name }} {{ item.createdBy.last_name ? `${item.createdBy.last_name[0]}.` : '' }}
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script setup lang="ts">
import type { ModelRef } from '~types/lib/Model'
import TransactionService from '@/lib/service/TransactionService'
import { useSaleTransactionController } from '~/sections/Sale/BillingHistory/composable/useSaleTransactionController'
import { SaleTransactionDirectionType } from '~/api/models/Sale/SaleTransaction'

const props = defineProps<{
    saleTransactions: ModelRef<'SaleTransaction', 'card' | 'createdBy' | 'paymentGateway'>[]
}>()

const formatter = useService('formatter')

const { useDictionary } = useContext()
const saleTransactionActionTypeDictionary = useDictionary('SaleTransactionActionType')

const saleTransactionController = useSaleTransactionController()
const creditCardTypeDictionary = useDictionary('CreditCardType')
</script>
