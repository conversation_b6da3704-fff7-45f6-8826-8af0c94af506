<template>
    <SuspenseManual :state="suspense">
        <template #default>
            <div class="flex items-center justify-between mt-5">
                <h6 class="m-0">
                    Pending Requests
                </h6>
                <AppButton class="dark:bg-dark-3 dark:text-secondary-200" @click="openModal">
                    <SendIcon />Send TP/BG Payment
                </AppButton>
            </div>
            <div class="card card__body mt-2 p-0">
                <AppTable
                    :columns="columns"
                    :items="saleClientRequestRecords"
                    zebra
                    class="--rounded --bordered"
                >
                    <template #row="{item}">
                        <td>
                            {{ item.id }}
                        </td>
                        <td>
                            {{ getFullName(item.createdBy) }}
                        </td>
                        <td>
                            {{ $format.datetime(item.created_at) }}
                        </td>
                        <td>
                            {{ saleClientRequestStatusDictionary.find(item.status).title }}
                        </td>
                        <td>
                            {{ saleClientRequestCategoryDictionary.find(item.category).title }}
                        </td>
                        <td>
                            {{ $format.money(item.offered_price) }}
                        </td>
                        <td v-if="item.offer_link">
                            <a
                                :href="item.offer_link"
                                class="text-primary underline"
                                target="_blank"
                            >
                                Offer #{{ item.id }}
                            </a>
                        </td>
                        <td
                            v-else
                            v-tooltip="'Preparing offer ...'"
                        >
                            <Loader />
                        </td>

                        <td class="text-center">
                            <AppButton
                                v-if="item.status === SaleClientRequestStatus.Pending"
                                class="--danger --ghost --only"
                                @click="remove(usePk(item)) "
                            >
                                <Trash2Icon />
                            </AppButton>
                        </td>
                    </template>

                    <template #not-found>
                        <div class="list-table-v2__not-found">
                            There are no requests
                        </div>
                    </template>
                </AppTable>
            </div>
        </template>
        <template #fallback>
            <Loader />
        </template>
    </SuspenseManual>
</template>

<script setup lang="ts">
import { getFullName } from '~/lib/Helper/PersonHelper'
import ProtectionOfferModal from '~/sections/Sale/modals/SaleClientRequest/ProtectionOfferModal.vue'
import { SaleClientRequestStatus } from '~/api/models/Client/SaleClientRequest'
import { $confirmDelete } from '@/plugins/ConfirmPlugin'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'

const props = defineProps<{
    saleVersionPk: PrimaryKey
}>()

const { useModel, useDictionary } = await useNewContext('SaleVersion', props.saleVersionPk)

const saleClientRequestStatusDictionary = useDictionary('SaleClientRequestStatus')
const saleClientRequestCategoryDictionary = useDictionary('SaleClientRequestCategory')

const saleClientRequestModel = useModel('SaleClientRequest')

const saleClientRequestList = saleClientRequestModel.useResourceList({
    name: 'SaleVersionSaleClientRequestList',
    pk: props.saleVersionPk,
}, {
    with: ['createdBy'],
})

const suspense = useSuspensableComponent(async () => {
    await saleClientRequestList.fetch()
})

const saleClientRequestRecords = computed(() => saleClientRequestList.records)

const columns = useTableColumns({
    id: {
        label: '#',
        width: 6,
    },
    create_by: {
        label: 'Created by',
        width: 'auto',
    },
    created_at: {
        label: 'Created at',
    },
    status: {
        label: 'Status',
    },
    category: {
        label: 'Category',
    },
    offered_price: {
        label: 'Offered Price',
    },
    link: {
        label: 'Link',
    },
    action: {
        label: 'Actions',
        center: true,
        width: 'min',
    },
})

const modal = useModal(ProtectionOfferModal)

const openModal = () => {
    modal.open({
        saleVersionPk: props.saleVersionPk,
    })
}

const remove = async (pk: PrimaryKey) => {
    await $confirmDelete('Are you sure you want to delete this request?')

    await saleClientRequestModel.actions.remove({
        pk,
    })

    toastSuccess('Request was deleted')
}
</script>
