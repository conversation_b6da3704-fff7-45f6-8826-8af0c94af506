<template>
    <SuspenseManual :state="suspense">
        <template #default>
            <SalePnrInfoSectionItemsItem
                v-for="(pnrInfoRecord, index) in pnrInfoMappedRecords"
                :key="index"
                :pnr-info-record="pnrInfoRecord"
                :can-edit-pnr-info="canEditPnrInfo"
                :is-pnr-info-queue="isPnrInfoQueue"
                :is-highlighted="isHighlighted"
                :fop-for-select-options="fopForSelectOptions"
                @update-disclaimers-info="()=>{
                    loadDisclaimersInfo()
                }"
            />
            <tr v-if="!isPnrInfoQueue">
                <td :colSpan="disclaimersColumns" class="!p-0">
                    <div
                        class="bg-secondary-50 dark:bg-dark-3 p-1"
                        @click="()=>{
                            if(disclaimersInfo){
                                toggleDisclaimers()
                            }else{
                                loadDisclaimersForPnr()
                            }
                        }"
                    >
                        <div class="flex items-center justify-center gap-x-2 text-primary-600 py-1 cursor-pointer select-none">
                            <span>{{ isShownInformation ? 'Hide information' : 'Show information' }}</span>
                            <div
                                v-if="!disclaimersInfoIsLoading"
                                class="rotate-180"
                                :class="{
                                    'rotate-0': isShownInformation
                                }"
                            >
                                <ChevronUpIcon />
                            </div>
                            <div v-else>
                                <Loader />
                            </div>
                        </div>
                    </div>
                    <div v-if="isShownInformation">
                        <div v-if="disclaimersInfo.length === 0" class="px-2.5 py-2 text-dark-3 dark:text-white font-semibold text-sm">
                            Fill in the information on PNR
                        </div>
                        <div
                            v-for="(disclaimer, index) in disclaimersInfo"
                            v-else
                            :key="index"
                            class="flex flex-col px-1.5 py-1 text-2xs"
                        >
                            <div class="flex gap-x-2.5">
                                <div class="h-6 w-6 max-w-[16px] text-primary-600">
                                    <InfoIcon class="!mt-0" />
                                </div>
                                <div>
                                    <span class="bold text-dark-3 dark:text-white">{{ disclaimer.title }}. </span>
                                    <span class="whitespace-normal text-secondary dark:text-secondary-300" v-html="disclaimer.value" />
                                </div>
                            </div>
                        </div>
                        <div class="px-2 pb-1 w-full">
                            <div
                                v-if="!isEditingRemark && pnrRecord.remark"
                                class="w-full whitespace-normal px-2 py-1 border border-dashed border-danger dark:border-danger-500 rounded-lg"
                            >
                                {{ pnrRecord.remark }}
                            </div>
                            <InputTextarea
                                v-else-if="isEditingRemark"
                                v-model="form.data.remark"
                                class="--small"
                            />
                            <AppButton
                                v-if="!isEditingRemark && canEditPnrInfo"
                                class="--info mt-1 --small"
                                @click="editRemark"
                            >
                                {{ pnrRecord.remark ? 'Update remark' : 'Leave Remark' }}
                            </AppButton>
                            <div v-else-if="canEditPnrInfo" class="flex gap-x-2 mt-1">
                                <AppButton class="--small" @click="editRemark">
                                    Cancel
                                </AppButton>
                                <AppButton
                                    class="--primary --small"
                                    @click="submit"
                                >
                                    Save
                                </AppButton>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
            <tr v-if="!isPnrInfoQueue" class="border-b-8 border-secondary-50" />
        </template>
        <template #fallback>
            <PlaceholderBlock class="h-[20px]" />
        </template>
    </SuspenseManual>
</template>

<script setup lang="ts">
import type { ModelAttributes, ModelRef } from '~types/lib/Model'
import SalePnrInfoSectionItemsItem from '~/sections/Sale/sections/SalePnrInfoSectionItemsItem.vue'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import { PnrInfoType } from '~/api/models/Sale/SaleVersionPnrInfo'
import { useSaleFop } from '~/sections/Sale/composable/useSaleFop'
import { ProductPayType } from '~/api/models/Product/Product'

const props = withDefaults(defineProps<{
    pnrRecord: ModelRef<'SaleVersionPnr', 'saleVersion'>,
    canEditValues: boolean
    isPnrInfoQueue?: boolean
    shouldHighlight?: boolean
}>(), {
    isPnrInfoQueue: false,
    shouldHighlight: false,
})

const emit = defineEmits<{
    'update:contains-edited-pnrs': [value: boolean]
}>()

const isHighlighted = computed(() => {
    return props.shouldHighlight
})

//

const { useModel } = useContext()

const { fopForSelectOptions, fetchLists, getFopInfoFromString, getFopInfoFromCardIdentity } = await
    useSaleFop(props.pnrRecord.saleVersion.sale_pk)

const disclaimersColumns = computed(() => {
    return props.canEditValues ? 9 : 8
})

// fetch pnrs info

const list = useModel('SaleVersionPnrInfo').useResourceList({
    name: 'SaleVersionPnrSaleVersionPnrInfoList',
    pk: usePk(props.pnrRecord),
})

const suspense = useSuspensableComponent(async () => {
    await list.fetch()
    await fetchLists()
})

const listRecords = computed((): ModelAttributes<'SaleVersionPnrInfo'>[] => {
    return list.records
})

watch(() => usePk(props.pnrRecord), () => {
    suspense.fetch()
})

// pnr was edited

watch(() => listRecords.value, () => {
    if (!!listRecords.value.find((record) => record.processed_by_pk !== undefined)) {
        emit('update:contains-edited-pnrs', true)
    }
}, { deep: true })

// ticket with product

const ticketModel = useModel('Ticket').useRecord({
    with: ['product'],
})

if (props.pnrRecord.last_ticket_pk) {
    await ticketModel.fetch(props.pnrRecord.last_ticket_pk)
}

const ticketRecord = computed(() => {
    return ticketModel.record.value
})

//

const canEditPnrInfo = computed(() => {
    return props.canEditValues
})

const pnrInfoMappedRecords = computed(() => {
    return Object.values(PnrInfoType).map((type, index) => {
        const pnr = listRecords.value.find(infoRecord => infoRecord.type === type)

        if (!!pnr) {
            return {
                ...pnr,
                fop: getFopInfoFromString(pnr.fop),
                pnr_pk: usePk(props.pnrRecord),
                pnr: props.pnrRecord.pnr,
                sale_pk: props.pnrRecord.saleVersion.sale_pk,
                lastTicketPk: props.pnrRecord.last_ticket_pk,
                departure_date: props.pnrRecord.saleVersion.departure_at,
                is_processed: props.pnrRecord.is_processed,
                is_started_by_pk: props.pnrRecord.is_started_by_pk,
                is_started_at: props.pnrRecord.is_started_at,
                shouldShowPnr: index === 0,
            }
        }

        return {
            pnr_pk: usePk(props.pnrRecord),
            pnr: props.pnrRecord.pnr,
            sale_pk: props.pnrRecord.saleVersion.sale_pk,
            lastTicketPk: props.pnrRecord.last_ticket_pk,
            departure_date: props.pnrRecord.saleVersion.departure_at,
            is_processed: props.pnrRecord.is_processed,
            is_started_by_pk: props.pnrRecord.is_started_by_pk,
            is_started_at: props.pnrRecord.is_started_at,
            processed_by_pk: undefined,
            shouldShowPnr: index === 0,
            type: type,
            eligibility: undefined,
            penalty: undefined,
            status: undefined,
            commission: undefined,
            refund_amount: undefined,
            fop: ticketRecord.value ? getFopInfoFromCardIdentity(ticketRecord.value.product?.card_identity, ticketRecord.value.product?.pay_type) : undefined,
            validity: undefined,
            non_refundable_tax: undefined,
        }
    })
})

//

const format = useService('formatter')

const isShownInformation = ref(false)

function toggleDisclaimers() {
    isShownInformation.value = !isShownInformation.value
}

const disclaimersInfoIsLoading = ref(false)

type DisclaimerInfo = {
    title: string,
    value: string,
}

const disclaimersInfo = ref<DisclaimerInfo[]>()

function loadDisclaimersInfo() {
    disclaimersInfo.value = disclaimersForPnrs(ticketRecord?.value?.product?.issued_at)
}

function loadDisclaimersForPnr() {
    disclaimersInfoIsLoading.value = true

    loadDisclaimersInfo()
    disclaimersInfoIsLoading.value = false

    toggleDisclaimers()
}

const disclaimersForPnrs = (issueDate: number | null | undefined): DisclaimerInfo[] => {
    const date =  issueDate ? Date.fromUnixTimestamp(issueDate) : undefined

    const issueDateInYear =  issueDate && date ? format.datetime(Date.fromUnixTimestamp(date.setUTCFullYear(date.getUTCFullYear() + 1, date.getMonth(), date.getDate()) / 1000)) : 'ISSUE DATE IS N/A ON PRODUCT'

    const disclaimers: DisclaimerInfo[] = []
    pnrInfoMappedRecords.value.forEach((record) => {
        // both refund and exchange

        if ((record.type === PnrInfoType.RefundAfterDeparture || record.type === PnrInfoType.RefundBeforeDeparture || record.type === PnrInfoType.NoShowRefund) &&
            (record.fop?.category === ProductPayType.ComVCC || record.fop?.category === ProductPayType.ComCC || record.fop?.category === ProductPayType.Invoice) && record.eligibility) {
            disclaimers.push({ title: 'Refund and FOP is anything but CC', value: 'In case funds are to be received in-house 20% extra profit from the total selling price is required.' })
        }

        if ((record.type === PnrInfoType.RefundAfterDeparture || record.type === PnrInfoType.RefundBeforeDeparture || record.type === PnrInfoType.NoShowRefund) &&
            record.fop?.category === ProductPayType.CC && record.eligibility) {
            disclaimers.push({ title: 'Refund and FOP is PAX CC', value: 'For tickets issued with pax CC, we require an upfront charge of 10% of the total selling price on top of the recalled commission after all CK and processing fees.' })
        }

        // for refund

        if (record.type === PnrInfoType.RefundBeforeDeparture && record.eligibility) {
            disclaimers.push({ title: 'Refund Before Departure with Eligibility', value: `Provided refund is processed before <b>${format.datetime(record.departure_date, 'UTC', { full: true })}</b> refundable amount is <b>${record.refund_amount ? format.money(record.refund_amount) : 'N/A'}</b>, recalled commission in the amount of <b>${record.commission ? format.money(record.commission) : 'N/A'}</b> will have to be charged upfront. Funds go to <b>${record.fop?.text}</b>` })
        } else if (record.type === PnrInfoType.RefundBeforeDeparture && record.eligibility === false) {
            disclaimers.push({ title: 'Refund Before Departure without Eligibility', value: 'Ticket is non-refundable, address Customer Support for tax refund calculations.' })
        }

        if (record.type === PnrInfoType.RefundAfterDeparture && record.eligibility) {
            disclaimers.push({ title: 'Refund After Departure with Eligibility', value: `Provided refund is processed before <b>${issueDateInYear}</b>, refundable amount is <b>${record.refund_amount ? format.money(record.refund_amount) : 'N/A'}</b>. Recalled commission in the amount of <b>${record.commission ? format.money(record.commission) : 'N/A'}</b> will have to be charged upfront. Funds go to <b>${record.fop?.text}</b>.` })
        } else if (record.type === PnrInfoType.RefundAfterDeparture && record.eligibility === false) {
            disclaimers.push({ title: 'Refund After Departure without Eligibility', value: `Ticket will not be refundable after ${format.datetime(record.departure_date, 'UTC')}.` })
        }

        if (record.type === PnrInfoType.NoShowRefund && record.eligibility) {
            disclaimers.push({ title: 'No-Show Refund with Eligibility', value: `Provided refund is processed before <b>${issueDateInYear}</b>, refundable amount is <b>${record.refund_amount ? format.money(record.refund_amount) : 'N/A'}</b>. Recalled commission in the amount of <b>${record.commission ? format.money(record.commission) : 'N/A'}</b> will have to be charged upfront. Funds go to <b>${record.fop?.text}</b>.` })
        } else if (record.type === PnrInfoType.NoShowRefund && record.eligibility === false) {
            disclaimers.push({ title: 'No-Show Refund without Eligibility', value: 'No-Show - nonrefundable' })
        }

        // for exchange

        if (record.type === PnrInfoType.ExchangeBeforeDeparture && record.eligibility) {
            disclaimers.push({ title: 'Exchange Before Departure with Eligibility', value: `Provided exchange is processed before <b>${format.datetime(record.departure_date, 'UTC')}</b>, a penalty in the amount of <b>${record.penalty ? format.money(record.penalty) : 'N/A'}</b> will apply, plus any difference in fare. Send a request to <a class="text-primary underline" href="mailto:<EMAIL>"><EMAIL></a> with the new itinerary to locate the difference in fare.` })
        } else if (record.type === PnrInfoType.ExchangeBeforeDeparture && record.eligibility === false) {
            disclaimers.push({ title: 'Exchange Before Departure without Eligibility', value: 'Ticket is non-exchangeable' })
        }

        if (record.type === PnrInfoType.ExchangeAfterDeparture && record.eligibility) {
            disclaimers.push({ title: 'Exchange After Departure with Eligibility', value: `Provided itinerary is canceled 72 hours prior to <b>${format.datetime(record.departure_date, 'UTC')}</b> and travel dates are no later than <b>${issueDateInYear}</b>, a penalty in the amount of <b>${record.penalty ? format.money(record.penalty) : 'N/A'}</b> plus any difference in fare will apply. Send a request to <a class="text-primary underline" href="mailto:<EMAIL>"><EMAIL></a> with the new itinerary to locate the difference in fare.` })
        } else if (record.type === PnrInfoType.ExchangeAfterDeparture && record.eligibility === false) {
            disclaimers.push({ title: 'Exchange After Departure without Eligibility', value: `Ticket will not be exchangeable after ${format.datetime(record.departure_date, 'UTC')}.` })
        }

        if (record.type === PnrInfoType.NoShowExchange && record.eligibility) {
            disclaimers.push({ title: 'No-Show Exchange with Eligibility', value: `Provided travel dates are no later than <b>${issueDateInYear}</b>, a penalty in the amount of <b>${record.penalty ? format.money(record.penalty) : 'N/A'}</b> plus any difference in fare will apply. Send a request to <a class="text-primary underline" href="mailto:<EMAIL>"><EMAIL></a> with the new itinerary to locate the difference.` })
        } else if (record.type === PnrInfoType.NoShowExchange && record.eligibility === false) {
            disclaimers.push({ title: 'No-Show Exchange without Eligibility', value: 'Ticket has no value in case of No-Show. ' })
        }
    })

    return disclaimers
}

//

const form = useForm({
    remark: props.pnrRecord.remark || '',
})

const submit = form.useSubmit(async () => {
    await useModel('SaleVersionPnr').actions.leaveRemark({
        pk: usePk(props.pnrRecord),
        remark: form.data.remark,
    })
    isEditingRemark.value = false
}, {
    resetOnSuccess: false,
})

const isEditingRemark = ref(false)

function editRemark() {
    isEditingRemark.value = !isEditingRemark.value
}
</script>
