<template>
    <div class="flex items-center justify-between gap-2 mt-5">
        <h6 class="m-0 mr-auto">
            Additional expenses
        </h6>
        <AppButton
            v-if="canEdit"
            class="dark:bg-dark-3 dark:text-white"
            @click="openCashUpgradeModal"
        >
            Cash Upgrade
        </AppButton>
        <AppButton
            v-if="canEdit"
            class="dark:bg-dark-3 dark:text-white"
            @click="openCreateModal"
        >
            <PlusIcon />
            Add expense
        </AppButton>
    </div>

    <SuspenseManual :state="suspense">
        <template #default>
            <div class="card mt-2">
                <div class="card__body overflow-x-auto fancy-scroll-x">
                    <table class="sales-incentive-table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Sell price</th>
                                <th>Value</th>
                                <th>Ck</th>
                                <th>Type</th>
                                <th>Payment type</th>
                                <th>Created by</th>
                                <th>Assigned to</th>
                                <th class="text-right">
                                    Approve
                                </th>
                                <th class="text-right">
                                    Action
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <SaleAdditionalExpenseTableRow
                                v-for="record in list.records"
                                :key="record.id"
                                :sale="sale"
                                :record="record"
                                :can-edit="canEdit"
                                :chat-room="chatRoom"
                                :fop-helper="fopHelper"
                            />
                        </tbody>
                        <tfoot>
                            <tr class="text-primary-1">
                                <td />
                                <td>{{ $format.money(totals.sell_price) }}</td>
                                <td>{{ $format.money(totals.fare) }}</td>
                                <td>{{ $format.money(totals.check_payment) }}</td>
                                <td colspan="2">
                                    <span class="text-gray-500">Profit:</span> {{ $format.money(totals.profit) }}
                                </td>
                                <td colspan="4" />
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </template>
        <template #fallback>
            <PlaceholderBlock class="mt-2 w-full h-[105px]" />
        </template>
    </SuspenseManual>
</template>

<script setup lang="ts">
import type ChatRoomModelDefinition from '@/modules/chat/types/ChatRoomModelDefinition'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import { useSaleFop } from '~/sections/Sale/composable/useSaleFop'
import CashUpgradeModal from '~/sections/Sale/modals/CashUpgradeModal.vue'
import SaleAdditionalExpenseTableRow from '~/sections/Sale/AdditionalExpense/SaleAdditionalExpenseTableRow.vue'
import SaleAdditionalExpenseCreateModal from '~/sections/Sale/AdditionalExpense/SaleAdditionalExpenseCreateModal.vue'

defineOptions({
    name: 'SaleAdditionalExpenseSection',
})

const props = defineProps<{
    saleVersionPk: PrimaryKey
}>()

const { useModel, record: saleVersion, hasPermission } = await useNewContext('SaleVersion', props.saleVersionPk)

const { record: sale, fetch: fetchSale } = useModel('Sale').useRecord({
    with: ['chat'],
}).destructable()

const list = useModel('AdditionalExpense').useResourceList({
    name: 'SaleVersionAdditionalExpenseList',
    pk: props.saleVersionPk,
}, {
    with: ['product', 'product.createdBy'],
})

//

const suspense = useSuspensableComponent(async () => {
    await Promise.all([
        fetchSale(saleVersion.sale_pk),
        list.fetch(),
        fopHelper.fetchLists(),
    ])
})

watch(() => props.saleVersionPk, () => {
    suspense.fetch()
})

//

const canEdit = computed(() => {
    return hasPermission('edit', 'Sale', sale.value) && !sale.value?.is_sale_closed && !sale.value?.is_sale_adjusted
})

//

const fopHelper = await useSaleFop(saleVersion.sale_pk)

//

const totals = computed(() => {
    const result = {
        sell_price: 0,
        fare: 0,
        check_payment: 0,
        profit: 0,
    }

    for (const incentive of list.records) {
        result.check_payment += incentive.product.check_payment
        result.fare += incentive.product.fare
        result.sell_price += incentive.product.sell_price
        result.profit += incentive.product.profit
    }

    return result
})

//

const chatRoom = computed(() => {
    const generalGroup: ChatRoomModelDefinition = {
        id: Number(sale.value.chat_pk),
        ringcentral_id: 0,
        gateway: {
            id: Number(sale.value.chat.gateway_pk),
        },
    }

    return generalGroup
})

//

const createModal = useModal(SaleAdditionalExpenseCreateModal)

function openCreateModal() {
    createModal.open({
        saleVersionPk: props.saleVersionPk,
    })
}

const cashUpgradeModal = useModal(CashUpgradeModal)

function openCashUpgradeModal() {
    cashUpgradeModal.open({
        salePk: saleVersion.sale_pk,
        saleVersionPk: props.saleVersionPk,
    })
}
</script>

