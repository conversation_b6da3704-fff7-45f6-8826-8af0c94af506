<template>
    <div
        v-for="(awardAccount, i) in form.data.points_trade"
        :key="i"
        class="card card__body relative dark:border dark:border-secondary-600 flex gap-3 mb-2"
    >
        <FormField
            :form="form"
            :field="`points_trade.${i}.award_account_pk`"
            label="Account №"
            class="text-xs flex-[4.2]"
            required
            hide-error
        >
            <InputSelect
                v-model="awardAccount.award_account_pk"
                :options="awardAccountsOptions"
                placeholder="Search"
                size="small"
                search
                debounce
                sync-on-options-change
                @search="handleSearch"
            />
        </FormField>

        <FormField
            :form="form"
            :field="`points_trade.${i}.miles_count`"
            label="Miles"
            class="text-xs flex-[2]"
            required
            hide-error
        >
            <InputMoney
                v-model="awardAccount.miles_count"
                step="10"
                size="small"
            />
        </FormField>

        <FormField
            v-tooltip="{ content: 'Price per point' }"
            :form="form"
            :field="`points_trade.${i}.price_per_mile`"
            label="CPM"
            class="text-xs flex-[2]"
            required
            hide-error
        >
            <InputMoney
                v-model="awardAccount.price_per_mile"
                size="small"
                :step="0.000001"
            />
        </FormField>
        <FormField
            :form="form"
            :field="`points_trade.${i}.rcpm`"
            label="RCPM"
            class="text-xs flex-[2]"
            hide-error
        >
            <InputMoney
                v-model="awardAccount.rcpm"
                size="small"
                :step="0.000001"
            />
        </FormField>
        <div
            class="absolute -top-2 -right-2 bg-white dark:bg-dark-1 cursor-pointer text-danger shadow-md p-2 border-transparent badge --xs --rounded  --only"
            @click="removeAwardAccount(Number(i))"
        >
            <XIcon />
        </div>
    </div>
    <AppButton
        class="w-full --small --primary --outline border-dashed !border-[1px]"
        @click="addAwardAccount"
    >
        Add award account <PlusIcon />
    </AppButton>
</template>

<script setup lang="ts">
import FormField from '~/components/Form/FormField.vue'
import type SelectOption from '~types/structures/SelectOption'
import type Form from '~/lib/Form/Form'

defineOptions({
    name: 'SaleAdditionalExpensePointsTrade',
})

const props = defineProps<{
    form: Form<{
        points_trade: PointsTradeItem[]
    }>
}>()

type PointsTradeItem = {
    award_account_pk: PrimaryKey,
    miles_count: number,
    price_per_mile: number,
    rcpm: number,
}

const form = props.form

//

const { useModel } = useContext()

//

const awardAccountsList = useModel('AwardAccount').useList()

const selectedAwardAccountPks = computed(() => {
    return form.data.points_trade.map((record) => record.award_account_pk).filter(Boolean)
})

const awardAccountsOptions = computed((): SelectOption[] => {
    return awardAccountsList.records.map((account) => ({
        title: account.account_number,
        value: usePk(account),
    }))
})

const handleSearch = (query: string) => {
    query = query.trim()

    if (query.length < 1) {
        return
    }

    awardAccountsList.fetch({
        where: (and) => {
            and.or(condition => {
                if (selectedAwardAccountPks.value.length) {
                    condition.in('id', selectedAwardAccountPks.value)
                }
                condition.search(query)
            })
        },
    })
}

const addAwardAccount = () => {
    form.data.points_trade.push({
        award_account_pk: '',
        miles_count: 0,
        price_per_mile: 0,
        rcpm: 0,
    })

    form.errors.clear('points_trade')
}

function removeAwardAccount(index: number) {
    form.data.points_trade = form.data.points_trade.filter((_, i) => i !== index)
}
</script>
