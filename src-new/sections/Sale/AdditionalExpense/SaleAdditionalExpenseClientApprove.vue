<template>
    <div class="-m-2 py-0.5">
        <div class="flex gap-1 justify-end items-center">
            <div v-if="clientApprove && !clientApprove?.is_sent_at && !clientApprove?.is_error" v-tooltip="'Processing'">
                <Loader />
            </div>
            <div
                v-if="clientApprove?.is_error"
                v-tooltip="'Something went wrong'"
                class="text-danger text-xs"
            >
                <AlertTriangleIcon />
            </div>

            <div
                v-if="clientApprove?.is_approved_at"
                v-tooltip="'Mail approved by client'"
                class="text-success"
            >
                <MailViewedIcon />
            </div>
            <div
                v-else-if="clientApprove?.is_sent_at"
                v-tooltip="'Mail sent'"
                class="text-primary dark:text-primary-400"
            >
                <MailSentIcon />
            </div>
            <div
                v-else-if="clientApprove?.is_viewed_at"
                v-tooltip="'Mail viewed'"
                class="text-primary dark:text-primary-400"
            >
                <MailViewedIcon />
            </div>

            <AppButton
                v-if="canEdit"
                v-tooltip="'Send to client for approve'"
                class="--ghost --only"
                @click="sendForApproveAdditionalExpense()"
            >
                <MailIcon />
            </AppButton>
        </div>
        <a
            v-if="clientApprove?.offer_url"
            :href="clientApprove.offer_url"
            target="_blank"
            class="link text-xs text-center block mb-1"
        >
            Offer #{{ clientApprove?.id }}
        </a>
    </div>
</template>

<script setup lang="ts">
import MailSentIcon from '~/assets/icons/MailSentIcon.svg?component'
import MailViewedIcon from '~/assets/icons/MailViewedIcon.svg?component'
import AdditionalExpensesSendForApproveModal from '~/sections/Sale/modals/AdditionalExpensesSendForApproveModal.vue'
import type { ModelAttributes } from '~types/lib/Model'

defineOptions({
    name: 'SaleAdditionalExpenseClientApprove',
})

const props = defineProps<{
    product: ModelAttributes<'Product'>,
    sale: ModelAttributes<'Sale'>,
    saleVersionPk: PrimaryKey,
    canEdit: boolean,
}>()

const { useModel } = useContext()

const { record: clientApprove, fetch: fetchApprove } = useModel('ProductClientApprove').useRecord().destructable()

const suspense = useSuspensableComponent(async () => {
    if (!props.product.client_approve_pk) {
        return
    }

    await fetchApprove(props.product.client_approve_pk)
})

watch(() => props.product.client_approve_pk, suspense.fetch)

//

const sendProductModal = useModal(AdditionalExpensesSendForApproveModal)

function sendForApproveAdditionalExpense() {
    sendProductModal.open({
        salePk: usePk(props.sale),
        saleVersionPk: props.saleVersionPk,
        productPk: usePk(props.product),
        sellPrice: props.product.sell_price,
    })
}
</script>

