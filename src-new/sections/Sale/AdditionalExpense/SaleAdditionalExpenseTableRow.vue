<template>
    <tr>
        <td>{{ record.id }}</td>
        <td>
            <template v-if="editEnabled">
                <FormField
                    :form="form"
                    field="sell_price"
                    hide-error
                >
                    <InputMoney
                        v-model="form.data.sell_price"
                        size="xs"
                        @update:model-value="recalculate"
                    />
                </FormField>
            </template>
            <template v-else>
                {{ $format.money(record.product.sell_price) }}
            </template>
        </td>
        <td>
            <template v-if="editEnabled">
                <FormField
                    :form="form"
                    field="fare"
                    hide-error
                >
                    <InputMoney
                        v-model="form.data.fare"
                        size="xs"
                        @update:model-value="recalculate"
                    />
                </FormField>
            </template>
            <template v-else>
                {{ $format.money(record.product.fare) }}
            </template>
        </td>
        <td>
            <template v-if="editEnabled">
                <div class="flex gap-2 items-center">
                    <InputCheckbox
                        v-model="form.data.check_payment_enabled"
                        size="xs"
                        @update:model-value="recalculate"
                    />
                    <FormField
                        :form="form"
                        field="ck_payment"
                        hide-error
                    >
                        <InputMoney
                            v-model="form.data.check_payment"
                            size="xs"
                            class="min-w-[40px]"
                            @change="ckWasChangedManually = true"
                        />
                    </FormField>
                </div>
            </template>
            <template v-else>
                {{ $format.money(record.product.check_payment) }}
            </template>
        </td>
        <td>
            <template v-if="editEnabled">
                <FormField
                    :form="form"
                    field="item_type"
                    hide-error
                >
                    <InputSelect
                        v-model="itemType"
                        :options="productTypeOptions"
                        placeholder="Type"
                        size="xs"
                        teleport
                        class="max-w-[120px]"
                    />
                </FormField>
            </template>
            <template v-else>
                {{ getProductTypeInfo(record.product.item_type)?.title }}
            </template>
        </td>
        <td>
            <template v-if="editEnabled">
                <FormField
                    :form="form"
                    field="for"
                    hide-error
                >
                    <InputFop
                        v-model="form.data.fop"
                        with-empty
                        :options="fopForSelectOptions"
                        :sale-pk="usePk(sale)"
                        class="w-full"
                        size="xs"
                        teleport
                        @update:model-value="recalculate"
                    />
                </FormField>
            </template>
            <template v-else>
                <div class="flex gap-2 items-center">
                    {{ fop?.text }}

                    <button
                        v-if="record.product.pay_type === ProductPayType.ComVCC && record.product.card_identity"
                        class="inline box rounded p-0.5 text-green-500"
                        @click="showCredentials"
                    >
                        <EyeIcon class="w-2 h-2 !stroke-2" />
                    </button>
                </div>
            </template>
        </td>
        <td class="max-w-[140px]">
            <div v-if="createdBy" class="max-w-[110px] truncate pr-2">
                {{ getShortName(createdBy) }}
            </div>
            <div class="max-w-[140px] truncate">
                {{ $format.datetime(record.product.created_at) }}
            </div>
        </td>
        <td class="max-w-[140px]">
            <AgentInfo v-if="record.executor_pk" :pk="record.executor_pk" />
        </td>
        <td>
            <SaleAdditionalExpenseClientApprove
                :product="record.product"
                :sale="sale"
                :sale-version-pk="record.sale_version_pk"
                :can-edit="canEdit"
            />
        </td>
        <td>
            <div class="flex items-center gap-0.5 justify-end -m-2">
                <template v-if="editEnabled">
                    <AppButton
                        class="--ghost --square --success"
                        :loading="form.loading"
                        @click="save"
                    >
                        <CheckIcon />
                    </AppButton>
                    <AppButton
                        class="--ghost --square --danger"
                        @click="cancelEdit"
                    >
                        <XIcon />
                    </AppButton>
                </template>
                <template v-else>
                    <ChatOpenButton
                        v-if="record.product.chat_branch_name"
                        v-slot="{ messagesCount, hasNewMessages }"
                        :branch="record.product.chat_branch_name"
                        :chat-room="chatRoom"
                        class="relative items-center p-1 -my-1 hover:bg-gray-200 rounded"
                    >
                        <MessageCircleIcon class="w-4 h-4" />
                        <div
                            v-if="messagesCount"
                            :class="[hasNewMessages ? 'bg-orange-400 text-white' : 'bg-slate-200 text-gray-700']"
                            class="sales-ticket-badge top-0 right-0"
                        >
                            {{ messagesCount }}
                        </div>
                    </ChatOpenButton>
                    <AppButton
                        v-if="canEdit"
                        class="--ghost --square"
                        @click="startEdit"
                    >
                        <EditIcon />
                    </AppButton>

                    <AppButton
                        v-if="canEdit"
                        class="--ghost --square --danger"
                        @click="deleteProduct"
                    >
                        <Trash2Icon />
                    </AppButton>
                </template>
            </div>
        </td>
    </tr>
</template>

<script setup lang="ts">
import type { ModelAttributes, ModelRef } from '~types/lib/Model'
import type { FopValueForSelectType, SaleFopComposable } from '~/sections/Sale/composable/useSaleFop'
import type { ProductType } from '~/api/models/Product/Product'
import { ProductPayType } from '~/api/models/Product/Product'
import ProductService from '~/lib/Product/ProductService'
import { $confirmDelete } from '@/plugins/ConfirmPlugin'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import type { SaleInsuranceType } from '~/api/dictionaries/Static/Sale/SaleInsuranceTypeDictionary'
import InputFop from '~/sections/Sale/components/InputFop.vue'
import { getShortName } from '~/lib/Helper/PersonHelper'
import type ChatRoomModelDefinition from '@/modules/chat/types/ChatRoomModelDefinition'
import ChatOpenButton from '@/modules/chat/components/ChatOpenButton.vue'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import FormField from '~/components/Form/FormField.vue'
import {
    useAdditionalExpenseProductTypeOptions,
} from '~/sections/Sale/AdditionalExpense/useAdditionalExpenseProductTypeOptions'
import AgentInfo from '~/sections/Agent/components/AgentInfo.vue'
import { useProductType } from '~/sections/Sale/composable/useProductType'
import SaleAdditionalExpenseClientApprove from '~/sections/Sale/AdditionalExpense/SaleAdditionalExpenseClientApprove.vue'

defineOptions({
    name: 'SaleAdditionalExpenseTableRow',
})

//

const props = defineProps<{
    sale: ModelAttributes<'Sale'>,
    record: ModelRef<'AdditionalExpense', 'product'>,
    canEdit: boolean,
    chatRoom: ChatRoomModelDefinition,
    fopHelper: SaleFopComposable,
}>()

//

const { useModel, useDictionary, hasPermission } = useContext()

//

const { fopForSelectOptions, getFopInfoFromCardIdentity } = props.fopHelper

const canManageSale = hasPermission('manage', 'Sale', props.sale)

const { options: productTypeOptions } = useAdditionalExpenseProductTypeOptions(canManageSale)
const { createWritableField } = useProductType()

const itemType = createWritableField(() => form.data)

const productTypeDictionary = useGeneralDictionary('ProductType')
const getProductTypeInfo = (productType: ProductType) => productTypeDictionary.find(productType)

//

const agentDictionary = useDictionary('Agent')

const createdBy = computed(() => {
    if (!props.record.product.created_by_pk) {
        return
    }

    return agentDictionary.find(props.record.product.created_by_pk)
})

//

type FormData = {
    fop: FopValueForSelectType | undefined
    item_type: ProductType | undefined,
    sub_type: SaleInsuranceType | undefined,
    sell_price: number,
    fare: number
    check_payment: number,
    check_payment_enabled: boolean,
}

const form = useForm<FormData>({
    fop: undefined,
    item_type: undefined,
    sub_type: undefined,
    sell_price: 0,
    check_payment: 0,
    fare: 0,
    check_payment_enabled: true,
}, {
    item_type: ValidationRules.Required('Type is required'),
})

const seedForm = () => {
    const product = props.record.product

    const fop = getFopInfoFromCardIdentity(product.card_identity || undefined, product.pay_type as ProductPayType)

    form.updateInitialData({
        fop: fop,
        item_type: product.item_type || undefined,
        sub_type: product.sub_type || undefined,
        sell_price: product.sell_price || 0,
        check_payment: product.check_payment || 0,
        fare: product.fare,
        check_payment_enabled: product.check_payment_ps > 0,
    })
}

//

const fop = computed(() => {
    const product = props.record.product

    return getFopInfoFromCardIdentity(product.card_identity || undefined, product.pay_type as ProductPayType)
})

const makeProductService = () => {
    return ProductService.makeAdditionalExpense({
        sell_price: form.data.sell_price,
        fare: form.data.fare,
        pay_type: form.data.fop?.category,
        check_payment: form.data.check_payment,
        check_payment_enabled: form.data.check_payment_enabled,
    })
}

//

watch(() => props.record.product, () => {
    seedForm()
})

const ckWasChangedManually = ref(false)

function recalculate() {
    const product = makeProductService()

    if (!ckWasChangedManually.value) {
        form.data.check_payment = product.getCalculateCk()
    }
}

///

const editEnabled = ref(false)

function startEdit() {
    editEnabled.value = true

    seedForm()
}

function cancelEdit() {
    editEnabled.value = false

    form.reset()
}

//

async function deleteProduct() {
    await $confirmDelete('Are you sure wan to delete this Additiona expense?')

    await useModel('AdditionalExpense').actions.delete({
        pk: usePk(props.record),
    })

    toastSuccess('Additionary expense was deleted')
}

const save = form.useSubmit(async () => {
    await useModel('AdditionalExpense').actions.update({
        pk: usePk(props.record),
        product: {
            item_type: form.data.item_type!,
            sub_type: form.data.sub_type || null,
            sell_price: form.data.sell_price,
            fare: form.data.fare,
            pay_type: form.data.fop?.category || null,
            card_identity: form.data.fop?.cardPk || null,
            check_payment: form.data.check_payment,
            check_payment_ps: makeProductService().check_payment_ps,
        },
    })

    editEnabled.value = false
})

async function showCredentials() {
    const productPk = usePk(props.record.product)

    if (!productPk) {
        return
    }

    const { url } = await useModel('ProjectCard').actions.getCredentialsUrl({
        product_pk: productPk,
    })

    window.open(url, '_blank')
}
</script>
