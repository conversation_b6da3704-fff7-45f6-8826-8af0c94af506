import type SelectOption from '~types/structures/SelectOption'
import type { ProductType } from '~/api/models/Product/Product'
import type { SaleInsuranceType } from '~/api/dictionaries/Static/Sale/SaleInsuranceTypeDictionary'
import { ProductTypeCategory } from '~/api/dictionaries/Static/Product/ProductTypeDictionary'
import type { MaybeRefOrGetter, WritableComputedRef } from 'vue'
import { sortByKeyFn } from '~/lib/Helper/ArrayHelper'

export function useAdditionalExpenseProductTypeOptions(
    canManageSale: MaybeRefOrGetter<boolean>,
) {
    const options = computed((): SelectOption<{
        item_type: ProductType,
        sub_type: SaleInsuranceType | undefined,
    }> => {
        const productTypeDictionary = useGeneralDictionary('ProductType')

        const baseRecords = productTypeDictionary
            .getCategoryRecords(ProductTypeCategory.AdditionalExpense)
            .filter((item) => {
                if (item?.access && item?.access.includes('manage') && !canManageSale) {
                    return false
                }

                return true
            }).sort(sortByKeyFn('title', 'asc'))

        const options = productTypeDictionary.adapter.mapRecords(baseRecords).forSelect()

        return options.map(option => ({
            ...option,
            value: {
                item_type: option.value,
                sub_type: undefined,
            },
        })).sort(sortByKeyFn('title', 'asc')) as SelectOption<{
            item_type: ProductType,
            sub_type: SaleInsuranceType | undefined,
        }>[]
    })

    return {
        options,
    }
}
