import type { ProductSubTypeValue } from '~/api/models/Product/Product'
import { type ProductType } from '~/api/models/Product/Product'
import type { MaybeRefOrGetter, WritableComputedRef } from 'vue'

export function useProductType() {
    const createWritableField = (mutableData: MaybeRefOrGetter<{
        item_type: ProductType | undefined,
        sub_type: ProductSubTypeValue | undefined,
    }>): WritableComputedRef<{
        item_type: ProductType | undefined,
        sub_type: ProductSubTypeValue | undefined,
    } | undefined> => {
        return computed({
            get() {
                const data = toValue(mutableData)

                if (!data || !data.item_type) {
                    return undefined
                }

                return {
                    item_type: data.item_type,
                    sub_type: data.sub_type,
                }
            },
            set(value) {
                const data = toValue(mutableData)

                if (!value) {
                    data.item_type = undefined
                    data.sub_type = undefined

                    return
                }

                data.item_type = value.item_type
                data.sub_type = value.sub_type
            },
        })
    }

    return {
        createWritableField,
    }
}
