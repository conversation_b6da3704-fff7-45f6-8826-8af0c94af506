let skipCache = true

export function useSaleTransactions(sale_pk: PrimaryKey) {
    const { useModel } = useContext()

    const list = useModel('SaleTransaction').useResourceList({
        name: 'SaleSaleTransactionList',
        pk: sale_pk,
    }, {
        with: ['card', 'paymentGateway', 'createdBy'],
    })

    const transactions = computed(() => list.records)

    const fetch = async () => {
        await list.fetch({
            skipResourceCache: skipCache && useService('router').isRefreshRequest() ? ['SaleSaleTransactionList'] : undefined,
        })

        skipCache = false
    }

    return {
        transactions,
        fetch,
    }
}

export type SaleTransactionsComposable = ReturnType<typeof useSaleTransactions>
