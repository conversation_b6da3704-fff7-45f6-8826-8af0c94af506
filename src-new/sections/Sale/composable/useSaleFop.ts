import { ProductPayType } from '~/api/models/Product/Product'
import type SelectOption from '~types/structures/SelectOption'
import type { ModelAttributes } from '~types/lib/Model'
import { useSaleCardList } from '~/sections/Sale/composable/useSaleCardList'

export type FopValueForSelectType = {
    text: string,
    cardPk: PrimaryKey | undefined,
    category: ProductPayType,
}

export async function useSaleFop(salePk: PrimaryKey) {
    const context = await useNewContext('Sale', salePk)

    const { useModel, workspace, record: sale, hasPermission } = context

    const currentCompanyProjects = useService('workspace').getWorkspaceInfo(workspace)?.project_pks

    const { fetchPaxCards, getPaxCardsSelectOptions } = useSaleCardList(sale.sale_version_pk, context)

    //

    const saleVCCProjectCardList = useModel('ProjectCard').useResourceList({
        name: 'SaleProjectCardList',
        pk: salePk,
    })

    const saleCompanyProjectCardList = useModel('ProjectCard').useResourceList({
        name: 'SaleCompanyCardList',
        pk: salePk,
    })

    //

    const canEditPaymentsForSale = computed(() => {
        return hasPermission('editPayments', 'Sale', sale)
    })

    const canEditCardsForSale = computed(() => {
        return hasPermission('editCards', 'Sale', sale)
    })

    const canEditPayments = computed(() => {
        return canEditPaymentsForSale.value && canEditCardsForSale.value && sale.is_connex_pay_enabled
    })

    //

    async function fetchLists() {
        await Promise.all([
            fetchPaxCards(),
            saleVCCProjectCardList.fetch(),
            saleCompanyProjectCardList.fetch(),
        ])
    }

    const paxCardsStringValue = computed<SelectOption<FopValueForSelectType>[]>(() => {
        const group = 'Pax CC'
        // const prefix = 'PAX **** -'

        const cards = getPaxCardsSelectOptions()

        cards.push({
            group,
            title: 'Check',
            value: {
                text: `Check`,
                cardPk: undefined,
                category: ProductPayType.Invoice,
            },
        })

        cards.push({
            group,
            title: 'Holder CC',
            value: {
                text: `Holder CC`,
                cardPk: undefined,
                category: ProductPayType.HolderCC,
            },
        })

        cards.push({
            group,
            title: 'Pax Wire',
            value: {
                text: `Pax Wire`,
                cardPk: undefined,
                category: ProductPayType.PaxWire,
            },
        })

        return cards
    })

    const vccCardsStringValue = computed(() => {
        const prefix = 'VCC **** -'
        const ComVCCGroup = 'Com VCC'
        const vccCards = saleVCCProjectCardList.records.map((card) => {
            return {
                group: currentCompanyProjects?.includes(card.project_pk) ? ComVCCGroup : 'Partners VCC',
                title: `${prefix} ${card.strip.slice(-4)}`,
                value: getVccForSelectValue(card),
            }
        })

        if (canEditPayments.value) {
            vccCards.push({
                group: ComVCCGroup,
                title: 'New',
                value: {
                    text: 'New',
                    cardPk: undefined,
                    category: ProductPayType.ComVCC,
                },
            })
        }

        return vccCards
    })

    const companyCardsStringValue = computed(() => {
        const prefix = 'CC **** -'

        return saleCompanyProjectCardList.records.map((card) => {
            return {
                group: currentCompanyProjects?.includes(card.project_pk) ? 'Com CC' : 'Partners CC',
                title: `${prefix} ${card.strip.slice(-4)}`,
                value: {
                    text: `${prefix} ${card.strip.slice(-4)}`,
                    cardPk: usePk(card),
                    category: ProductPayType.ComCC,
                },
            }
        }).sort((a, b) => a.group === 'Partners CC' ? 1 : -1)
    })

    const fopForSelectOptions = computed(() => {
        const fopCardsOptions = [...paxCardsStringValue.value, ...companyCardsStringValue.value, ...vccCardsStringValue.value]

        fopCardsOptions.push({
            group: '',
            title: 'Other',
            value: {
                text: 'Other',
                cardPk: undefined,
                category: ProductPayType.Other,
            },
        })

        return fopCardsOptions
    })

    // get fop value based on combined text

    function getFopInfoFromString(fopInString: string | Empty) {
        if (!fopInString) {
            return {
                text: 'Other',
                cardPk: undefined,
                category: ProductPayType.Other,
            }
        }

        return (fopForSelectOptions.value.find((fopOption) => fopOption.value?.text === fopInString))?.value ?? undefined
    }

    function getFopInfoFromCardIdentity(cardIdentity: PrimaryKey | Empty, category: ProductPayType) {
        if (!cardIdentity && category === ProductPayType.CC) {
            return {
                text: 'Pax CC',
                cardPk: undefined,
                category: ProductPayType.CC,
            }
        }

        if (!category && !cardIdentity) {
            return {
                text: '',
                cardPk: undefined,
                category: ProductPayType.Other,
            }
        }

        if (!category) {
            return {
                text: 'Other',
                cardPk: undefined,
                category: ProductPayType.Other,
            }
        }

        return (fopForSelectOptions.value.find((fopOption) => (fopOption.value?.cardPk === cardIdentity) && (fopOption.value?.category === category)))?.value ?? undefined
    }

    return {
        fopForSelectOptions,
        getFopInfoFromString,
        getFopInfoFromCardIdentity,
        fetchLists,
    }
}

export function getVccForSelectValue(card: ModelAttributes<'ProjectCard'>) {
    return {
        text: `VCC **** - ${card.strip.slice(-4)}`,
        cardPk: usePk(card),
        category: ProductPayType.ComVCC,
    }
}

export type SaleFopComposable = Awaited<ReturnType<typeof useSaleFop>>
