import {
    SaleTransactionActionType,
    SaleTransactionDirectionType,
    SaleTransactionStatus,
} from '~/api/models/Sale/SaleTransaction'
import type { ModelRef } from '~types/lib/Model'
import { ProductType } from '~/api/models/Product/Product'
import { useSaleTransactions } from '~/sections/Sale/composable/useSaleTransactions'

export function useSalePaymentCard(sale_pk: PrimaryKey, sale_version_pk: PrimaryKey) {
    const { useModel } = useContext()

    const saleVersionRecord = useModel('SaleVersion').useRecord({
        with: ['products', 'cards', 'cards.clientCard', 'cards.clientCard.fraudInfo'],
    })

    const transactionsController = useSaleTransactions(sale_pk)

    const saleVersion = computed(() => saleVersionRecord.record.value)
    const saleCards = computed(() => saleVersion.value.cards)

    const transactions = computed(() =>
        transactionsController.transactions.value.filter(
            t =>
                ![SaleTransactionStatus.Canceled, SaleTransactionStatus.Error].includes(t.status) &&
                t.action_type !== SaleTransactionActionType.Verify,
        ),
    )

    const tickets = computed(() =>
        saleVersion.value.products.filter(p => p.item_type === ProductType.Ticket),
    )

    const additionalExpenses = computed(() => {
        const excluded = new Set<ProductType>([
            ProductType.Tips,
            ProductType.Insurance,
            ProductType.ExtraGP,
            ProductType.FlexibleTicket,
            ProductType.Miles,
            ProductType.Ticket,
            ProductType.AirlineReimbursementFee,
        ])

        return saleVersion.value.products.filter(p => !excluded.has(p.item_type))
    })

    const calcCharged = (sale_version_card_pk: PrimaryKey, client_card_pk: PrimaryKey): number => {
        const ticketsSum = calcProductSumByCard(tickets.value, sale_version_card_pk)
        const extraSum = calcProductSumByCard(additionalExpenses.value, sale_version_card_pk)

        const transactionSum = transactions.value
            .filter(t => t.client_card_pk === client_card_pk)
            .reduce((sum, t) => sum + t.amount * getTransactionCoefficient(t.direction_type), 0)

        return (ticketsSum + extraSum + transactionSum).toMoney()
    }

    const fetchCards = async () => {
        await Promise.all([
            await saleVersionRecord.fetch(sale_version_pk),
            await transactionsController.fetch(),
        ])
    }

    return {
        calcCharged,
        fetchCards,
        saleCards,
    }
}

function calcProductSumByCard(products: ModelRef<'Product'>[], sale_version_card_pk: PrimaryKey): number {
    return products
        .filter(p => p.pay_type === 'CC' && p.card_identity === sale_version_card_pk)
        .reduce((sum, p) => {
            const base = p.is_award ? p.tax : p.fare + p.tax

            return sum + base
        }, 0)
}

function getTransactionCoefficient(direction: SaleTransactionDirectionType): number {
    return direction === SaleTransactionDirectionType.Outcome ? -1 : 1
}
