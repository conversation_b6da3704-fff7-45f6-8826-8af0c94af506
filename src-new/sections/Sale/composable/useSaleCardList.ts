import { ProductPayType } from '~/api/models/Product/Product'
import type Context from '~/lib/Context'

export function useSaleCardList(saleVersionPk: PrimaryKey, context?: Context) {
    const { useModel } = context ?? useContext()

    const saleCardList = useModel('SaleVersionCard').useResourceList({
        name: 'SaleVersionSaleVersionCardList',
        pk: saleVersionPk,
    })

    async function fetchPaxCards() {
        await saleCardList.fetch()
    }

    function getPaxCardsSelectOptions() {
        const group = 'Pax CC'
        const prefix = 'PAX **** -'

        return saleCardList.records.map((card) => {
            return {
                group,
                title: `${prefix} ${card.strip.slice(-4)}`,
                value: {
                    text: `${prefix} ${card.strip.slice(-4)}`,
                    cardPk: usePk(card),
                    category: ProductPayType.CC,
                },
            }
        })
    }

    return {
        fetchPaxCards,
        getPaxCardsSelectOptions,
        saleCardList,
    }
}
