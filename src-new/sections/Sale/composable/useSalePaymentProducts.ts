import { ProductType } from '~/api/models/Product/Product'
import type { ModelRef } from '~types/lib/Model'
import { SaleTransactionDirectionType, SaleTransactionStatus } from '#/src-new/api/models/Sale/SaleTransaction'
import { SalePaymentProductType } from '#/src-new/api/models/Sale/SaleTransaction'
import { useSaleTransactions } from '~/sections/Sale/composable/useSaleTransactions'

type SalePaymentProduct<T = unknown> = {
    id: T
    invoice: string
    description: string
    paid_amount: number
    total_amount: number
    debt_amount: number
}

type AllowedTypes =
    | SalePaymentProductType.Invoice
    | SalePaymentProductType.Tips
    | SalePaymentProductType.Insurance

const PaymentsToRender: AllowedTypes[] = [
    SalePaymentProductType.Invoice,
    SalePaymentProductType.Tips,
    SalePaymentProductType.Insurance,
]

type GroupedProductsMap = {
    [K in AllowedTypes]: SalePaymentProduct<K>
}

type GroupedTransactionsMap = {
    [K in AllowedTypes]: { amount: number }
}

export function useSalePaymentProducts(sale_pk: PrimaryKey, sale_version_pk: PrimaryKey) {
    const { useModel } = useContext()

    const saleVersionRecord = useModel('SaleVersion').useRecord({ with: ['products', 'cards']})

    const transactionsController = useSaleTransactions(sale_pk)

    const allTransactions = computed(() => transactionsController.transactions.value)

    const transactions = computed(() =>
        allTransactions.value.filter(t => ![SaleTransactionStatus.Canceled, SaleTransactionStatus.Error].includes(t.status)),
    )

    const saleVersion = computed(() => saleVersionRecord.record.value)

    const products = computed<Array<SalePaymentProduct>>(() => {
        const grouped: GroupedProductsMap = Object.fromEntries(
            PaymentsToRender.map(type => [type, initProduct(type, saleVersion.value)]),
        ) as GroupedProductsMap

        saleVersion.value.products.forEach(product => {
            const type = mapProductToGroup(product.item_type)
            grouped[type].total_amount += calculateTotalAmount(product)
        })

        const paid = calculatePaidAmount(transactions.value)

        PaymentsToRender.forEach(type => {
            grouped[type].paid_amount = paid[type].amount
        })

        return Object.values(grouped)
    })

    const allTicketsCCSet = computed(() =>
        saleVersion.value.products
            .filter(p => p.item_type === ProductType.Ticket)
            .every(isCardInfoValid),
    )

    const isValidSellPrices = computed(() =>
        saleVersion.value.products.every(isValidPrice),
    )

    const fetchProducts = async () => {
        await Promise.all([
            await saleVersionRecord.fetch(sale_version_pk),
            await transactionsController.fetch(),
        ])
    }

    return {
        products,
        fetchProducts,
        allTicketsCCSet,
        isValidSellPrices,
        allTransactions,
    }
}

const initProduct = (
    type: SalePaymentProductType,
    saleVersion: ModelRef<'SaleVersion'>,
): SalePaymentProduct<SalePaymentProductType> =>
    reactive({
        id: type,
        ...getProductText(type, saleVersion),
        paid_amount: 0,
        total_amount: 0,
        get debt_amount() {
            return this.total_amount - this.paid_amount
        },
    })

const getProductText = (type: SalePaymentProductType, saleVersion: ModelRef<'SaleVersion'>) => {
    const invoice = saleVersion.invoice
    const description = saleVersion.payment_description

    switch (type) {
        case SalePaymentProductType.Tips:
            return {
                invoice: `${invoice}/Tips`,
                description: `Tips - ${description}`,
            }
        case SalePaymentProductType.Insurance:
            return {
                invoice: `${invoice}/TP`,
                description: `Ticket Protection - ${description}`,
            }
        default:
            return { invoice, description }
    }
}

const mapProductToGroup = (itemType: ProductType): SalePaymentProductType => {
    switch (itemType) {
        case ProductType.Tips:
            return SalePaymentProductType.Tips
        case ProductType.Insurance:
            return SalePaymentProductType.Insurance
        default:
            return SalePaymentProductType.Invoice
    }
}

const calculateTotalAmount = (product: ModelRef<'Product'>): number => {
    if (product.pay_type === 'CC') {
        return Math.max(
            product.sell_price - (product.is_award ? product.tax : product.net_price_base),
            0,
        )
    }

    return product.sell_price
}

const calculatePaidAmount = (transactions: ModelRef<'SaleTransaction'>[]): GroupedTransactionsMap => {
    const result = Object.fromEntries(
        Object.values(SalePaymentProductType).map(type => [type, { amount: 0 }]),
    ) as GroupedTransactionsMap

    transactions.forEach(transaction => {
        const amount = isPartialAuth(transaction) ? transaction.settlement_amount || 0 : transaction.amount
        const type = (transaction.pay_type in result ? transaction.pay_type : SalePaymentProductType.Invoice) as SalePaymentProductType

        result[type].amount += amount * getTransactionCoefficient(transaction.direction_type)
    })

    return result
}

const isPartialAuth = (transaction: ModelRef<'SaleTransaction'>) => {
    return transaction?.settlement_amount && transaction.settlement_amount !== transaction.amount
}

const getTransactionCoefficient = (direction: SaleTransactionDirectionType) => {
    return direction === SaleTransactionDirectionType.Outcome ? -1 : 1
}

const isCardInfoValid = (product: ModelRef<'Product'>) => {
    return ['CC', 'comCC', 'comVCC'].includes(product.pay_type) ? !!product.card_identity : !!product.pay_type
}

const isValidPrice = (product: ModelRef<'Product'>) => {
    return product.pay_type !== 'CC' || (product.is_award ? product.sell_price >= product.tax : product.sell_price >= product.net_price_base)
}
