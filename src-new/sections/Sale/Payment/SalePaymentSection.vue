<template>
    <div>
        <div class="flex items-center justify-between">
            <h6 class="m-0">
                Payment
            </h6>
            <div class="flex items-center gap-2">
                <RequestCardAccessButton :sale-pk="salePk" />
                <AppButton
                    v-if="hasPermission('manage', 'all')"
                    @click="recoverCC"
                >
                    Recover CC
                </AppButton>
                <label v-if="canEditCards && canEditPayments" class="flex items-center gap-2">
                    {{ isManualPayment ? 'Manual payment' : 'Automatic' }}
                    <InputSwitch
                        v-model="isManualPayment"
                        size="small"
                        :disabled="!isEnableAutomaticTransactions"
                        @click="resetData"
                    />
                </label>
            </div>
        </div>
        <div class="card mt-2">
            <div v-if="canEditCards && canEditPayments" class="card__header grid grid-cols-4 gap-4 py-5 font-medium">
                <div>
                    Transaction type
                </div>
                <label class="flex items-center select-none" :class="{'text-primary': transactionType === 'capture'}">
                    <InputRadio
                        v-model="transactionType"
                        class="mr-2 dark:border-secondary-500"
                        value="capture"
                        name="action"
                    />
                    Authorize & Capture
                </label>
                <label class="flex items-center select-none" :class="{'text-primary': transactionType === 'authorize'}">
                    <InputRadio
                        v-model="transactionType"
                        class="mr-2 dark:border-secondary-500"
                        value="authorize"
                        name="action"
                    />
                    Authorize only
                </label>
            </div>
            <div v-if="paymentController.payments" class="card__body grid grid-cols-4 py-5">
                <div class="font-medium">
                    Select the product
                </div>
                <div class="col-span-3 relative">
                    <ManualSelectProductTable
                        v-if="isManualPayment"
                        ref="manualProductComponent"
                        :payment-controller="paymentController"
                        @select="($event) => selectedProductManual = $event"
                    />
                    <AutomaticSelectProductTable
                        v-else
                        ref="automaticProductComponent"
                        :is-edit-transaction-amount="isEditTransactionAmount"
                        :payment-controller="paymentController"
                        :can-edit="canEditPayments"
                    />
                </div>
            </div>
            <div v-if="canViewCards" class="card__body grid grid-cols-4 bg-slate-50 dark:bg-slate-900/20">
                <div class="font-medium">
                    Select credit card
                </div>
                <div class="flex flex-col gap-y-2 col-span-3">
                    <SaleCard
                        v-for="(saleCard, index) in paymentController.saleCards"
                        :key="index"
                        :sale-card="saleCard"
                        :can-edit="canEditCards && canEditPayments"
                        :can-edit-cards="canEditCards"
                        :can-view="canViewCards"
                        :is-edit-oaf-split="isEditOafSplit"
                        :is-manual-payment="isManualPayment"
                        :manual-payment-amount="manualPaymentAmount"
                        :payment-controller="paymentController"
                        @update:manual-payment-amount="($event) => manualPaymentAmount = $event"
                    />
                    <AddNewCreditCardComponent
                        v-if="addNewCreditCard && canEditCards"
                        :sale-version-pk="saleVersionPk"
                        :credit-cards="paymentController.saleCards"
                        @close="addNewCreditCard = false"
                    />
                    <AppButton
                        v-if="!addNewCreditCard && canEditCards"
                        class="--ghost justify-start --small"
                        @click="addNewCreditCard = true"
                    >
                        <PlusIcon
                            class="w-3.5 h-3.5 !stroke-2 mr-2"
                        />
                        Add new payment method
                    </AppButton>
                </div>
            </div>
            <div v-if="canEditPayments" class="grid grid-cols-4 gap-4 px-6 py-5">
                <div class="col-span-1 flex items-center">
                    <InputSelect
                        v-if="gatewayOptions"
                        v-model="selectedGateway"
                        :options="gatewayOptions"
                        size="small"
                        placeholder="Select gateway"
                    />
                    <div v-else>
                        <span class="text-xs text-gray-600">Gateway:</span>
                    </div>
                </div>
                <div class="col-span-3 col-start-2 space-x-2">
                    <AppButton
                        v-if="!isManualPayment"
                        v-tooltip="{ content: getPayButtonTooltip }"
                        class="px-4 font-semibold mr-1"
                        :disabled="isTransactionRequestLoading || !amount || !isTransactionsAmountValid || !selectedGateway || !selectCreditCard || !selectedProductsAuto.length"
                        :loading="isTransactionRequestLoading"
                        :class="{
                            '--danger': !isTransactionsAmountValid || !paymentController.isValidSellPrices,
                            '--primary': isTransactionsAmountValid && paymentController.isValidSellPrices
                        }"
                        @click="makeTransaction"
                    >
                        Pay {{ amount > 0 ? `$${formatter.money(amount)}` : '' }}
                    </AppButton>
                    <AppButton
                        v-else
                        v-tooltip="{ content: getPayButtonTooltip }"
                        class="px-4 font-semibold mr-1"
                        :disabled="isTransactionRequestLoading || manualPaymentAmount === undefined || manualPaymentAmount <= 0 || !selectCreditCard || !selectedProductManual.invoice"
                        :loading="isTransactionRequestLoading"
                        :class="{
                            '--danger': !paymentController.isValidSellPrices,
                            '--primary': paymentController.isValidSellPrices
                        }"
                        @click="makeTransaction"
                    >
                        Pay {{ manualPaymentAmount > 0 && manualPaymentAmount !== undefined ? `$${formatter.money(manualPaymentAmount)}` : '' }}
                    </AppButton>
                    <AppButton
                        v-if="!paymentController.isValidSellPrices"
                        class="--primary px-4 font-semibold mr-1"
                        @click="changeTicketsSellPrices"
                    >
                        Change Sell Price
                    </AppButton>
                    <AppButton
                        v-if="!isTransactionsAmountValid && !isEditTransactionAmount && !isEditOafSplit"
                        class="--primary px-4 font-semibold mr-1"
                        @click="isEditOafSplit = true"
                    >
                        Change OAF split
                    </AppButton>
                    <AppButton
                        v-if="!isTransactionsAmountValid && !isEditTransactionAmount && !isEditOafSplit"
                        class="--primary px-4 font-semibold mr-1"
                        @click="isEditTransactionAmount = true"
                    >
                        Change Transaction Amount
                    </AppButton>
                    <AppButton
                        v-if="isEditOafSplit"
                        v-tooltip="{ content: getOafTooltip }"
                        class="px-4 font-semibold mr-1"
                        :class="{
                            '--primary': oafSplitFullAmount === sellPriceAll,
                            '--danger': oafSplitFullAmount !== sellPriceAll
                        }"
                        :disabled="oafSplitFullAmount !== sellPriceAll || !paymentController.isOafSplitDataValid"
                        @click="saveOafSplit"
                    >
                        {{
                            oafSplitFullAmount !== sellPriceAll ? `Balance: ${formatter.money(sellPriceAll - oafSplitFullAmount)}` : 'Save OAF amount'
                        }}
                    </AppButton>
                    <AppButton
                        v-if="isEditOafSplit || isEditTransactionAmount"
                        class="--primary px-4 font-semibold"
                        @click="cancelChangeTransaction"
                    >
                        Cancel
                    </AppButton>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import RequestCardAccessButton from '~/sections/Sale/Payment/components/RequestCardAccessButton.vue'
import SaleCard from '~/sections/Sale/Payment/components/Card/SaleCard.vue'
import AddNewCreditCardComponent from '~/sections/Sale/Payment/components/Card/AddNewCreditCardComponent.vue'
import ManualSelectProductTable from '~/sections/Sale/Payment/components/ManualSelectProductTable.vue'
import SaleCardRecoverModal from '~/sections/Sale/Payment/components/modals/SaleCardRecoverModal.vue'
import AutomaticSelectProductTable from '~/sections/Sale/Payment/components/AutomaticSelectProductTable.vue'
import type { ActionResponse } from '~types/lib/Model'
import type { TransactionData } from '~/api/models/Sale/SaleTransaction.ts'
import { SalePaymentProductType } from '~/api/models/Sale/SaleTransaction.ts'
import { toastSuccess, toastWarning } from '@/lib/core/helper/ToastHelper'
import { useSalePaymentController } from '~/sections/Sale/composable/useSalePaymentController'
import type SaleHelper from '@/lib/core/helper/Sale/SaleHelper'
import ChangeSellPriceModal from '@/views/sales/sale/modals/ChangeSellPriceModal.vue'
import { preventDuplication } from '~/lib/Helper/PromiseHelper'

const props = defineProps<{
    salePk: PrimaryKey
    saleVersionPk: PrimaryKey
}>()

type TransactionType = 'capture' | 'authorize'

// @todo remove SaleHelper when we move SaleComponent to V2, + move modal ChangeSellPriceModal to v2
const saleHelper = inject<SaleHelper>('saleHelper')

if (!saleHelper) {
    throw new Error('Sale Helper is not provided')
}

const { hasPermission, useModel } = useContext()

const productModel = useModel('Product')
const saleCardModel = useModel('SaleVersionCard')
const saleVersionModel = useModel('SaleVersion')
const paymentGatewayModel = useModel('PaymentGateway')
const saleTransactionModel = useModel('SaleTransaction')

const formatter = useService('formatter')

const saleRecoverModal = useModal(SaleCardRecoverModal)

const changeSellPriceModal = useModal(ChangeSellPriceModal, {
    saleHelper: saleHelper,
}, {
    promise: true,
})

const manualProductComponent = ref<ManualSelectProductTable>()
const automaticProductComponent = ref<AutomaticSelectProductTable>()

const gateways = ref<ActionResponse<'PaymentGateway', 'getPaymentGatewaysForSale'>>([])

const gatewayOptions = ref<{ title: string, value: string }[]>([])
const selectedGateway = ref<PrimaryKey>()
const defaultGateway = ref<PrimaryKey>()

const saleVersion = await saleVersionModel.useRecord({
    with: ['sale'],
})

const paymentController = ref(useSalePaymentController(props.salePk, props.saleVersionPk))

await paymentController.value.fetchData()

const sale = computed(() => {
    return saleVersion.record.value?.sale
})

await saleVersion.fetch(props.saleVersionPk)

const canEditCards = computed(() => hasPermission('editCards', 'Sale', sale.value))
const canEditPayments = computed(() => hasPermission('editPayments', 'Sale', sale.value))
const canViewCards = computed(() => hasPermission('viewCards', 'Sale', sale.value))

const manualPaymentAmount = ref<number>(0)

const isEnableAutomaticTransactions = computed(() => {
    if (!canEditPayments.value) {
        return true
    }

    return sale.value?.is_enable_automatic_transactions
})

//

const addNewCreditCard = ref<boolean>(false)
const isManualPayment = ref<boolean>(false)
const isEditOafSplit = ref<boolean>(false)
const isEditTransactionAmount = ref<boolean>(false)
const payClickStarted = ref<boolean>(false)

//

const transactionType = ref<TransactionType>('capture')

const selectedProductsAuto = computed(() => {
    return paymentController.value.productsAutoToPay
})

// computed

const selectCreditCard = computed(() => {
    return paymentController.value.selectedCard
})

const amount = computed(() => {
    return paymentController.value.amount
})

const selectedProductManual = computed(() => {
    return paymentController.value.selectedProductManual
})

const oafSplitFullAmount = computed(() => {
    return paymentController.value.oafSplitFullAmount
})

const sellPriceAll = computed(() => {
    return paymentController.value.sellPriceAll
})

const paymentBalance = computed(() => {
    return paymentController.value.paymentBalance
})

const isTransactionsAmountValid = computed(() => {
    if (paymentController.value.saleCards?.length === 0) return true

    if (amount.value > 0) {
        const isValid = isEditTransactionAmount.value ? paymentController.value.isProductsValid : true

        return paymentBalance.value >= 0 && isValid
    }

    return true
})

const getPayButtonTooltip = computed(() => {
    if ((!selectedProductsAuto.value.length && !isManualPayment.value) || (!selectedProductManual.value.invoice && isManualPayment.value)) {
        return 'Please select products'
    }

    if (!selectedGateway.value) {
        return 'Please select payment gateway'
    }

    if (isManualPayment.value && (manualPaymentAmount.value === undefined || manualPaymentAmount.value <= 0)) {
        return 'Please check manual payment amount'
    }

    if (isManualPayment.value) {
        return ''
    }

    if (!paymentController.value.isValidSellPrices) {
        return 'Please check tickets "Sell Price"'
    }

    if (!isTransactionsAmountValid.value) {
        return 'Transaction amount is not valid'
    }

    if (!paymentController.value.allTicketsCCSet) {
        return 'Please select tickets "Issued with"'
    }

    return ''
})

const getOafTooltip = computed(() => {
    return oafSplitFullAmount.value == sellPriceAll.value && !paymentController.value.isOafSplitDataValid ? 'OAF split amount should not be less than charged amount' : ''
})

const getPreviousTransaction = () => {
    const saleLastTransaction = paymentController.value.allTransactions.at(-1)

    if  (!saleLastTransaction) {
        return null
    }

    return usePk(saleLastTransaction)
}

// methods

const changeTicketsSellPrices = async () => {
    const data = await changeSellPriceModal.open()

    await productModel.actions.sellPricesRebalance({
        data: data,
    })
}

const makeTransaction = () => {
    if (payClickStarted.value) {
        toastWarning('Attempt to make several payments in one time')

        return
    }

    if (!paymentController.value.allTicketsCCSet) {
        toastWarning('Please select tickets "Issued with"')

        return
    }

    if (!paymentController.value.isValidSellPrices) {
        toastWarning('Please check products "Sell Price"')

        return
    }

    payClickStarted.value = true

    processTransaction()

    setTimeout(() => {
        payClickStarted.value = false
    }, 2000)
}

const processTransaction = () => {
    const data: TransactionData = {
        sale_pk: props.salePk,
        sale_version_pk: props.saleVersionPk,
        sale_version_card_pk: selectCreditCard.value,
        amount: 0,
        action: transactionType.value,
        products: [],
        is_manual: isManualPayment.value,
        gateway_pk: selectedGateway.value,
        is_default: selectedGateway.value == defaultGateway.value,
        description: null,
        sale_last_transaction_pk: getPreviousTransaction(),
    }

    if (isManualPayment.value) {
        data.amount = manualPaymentAmount.value

        data.products.push({
            invoice: selectedProductManual.value.invoice,
            amount: data.amount,
            pay_type: selectedProductManual.value.pay_type,
        })

        if (selectedProductManual.value.custom) {
            data.description = selectedProductManual.value.description

            if (!manualProductComponent.value.validate()) {
                return
            }
        }
    } else {
        data.products = selectedProductsAuto.value
        data.amount = amount.value
    }

    createPaymentTransaction(data)
}

const isTransactionRequestLoading = ref(false)

const createPaymentTransaction = async (data: TransactionData) => {
    if (!selectedGateway.value) {
        return
    }

    toastWarning('Payment process started, please wait...')

    resetData()

    await preventDuplication(async () => {
        await saleTransactionModel.actions.addTransaction(data)
    }, isTransactionRequestLoading)

    toastSuccess('Successful charge')

    resetData()
    cancelChangeTransaction(true)
}

const saveOafSplit = async () => {
    if (!Object.values(paymentController.value.oafInputData).length) {
        return
    }

    const data = Object.fromEntries(
        Object.entries(paymentController.value.oafInputData).map(([key, { amount }]) => [key, amount]),
    )

    await saleCardModel.actions.updateOafSplit({
        data: data,
    })

    toastSuccess('OAF Split Changed')

    cancelChangeTransaction(true)
}

const resetData = () => {
    isEditOafSplit.value = false
    isEditTransactionAmount.value = false

    manualPaymentAmount.value = 0

    paymentController.value.selectedProductsAuto = {}
    paymentController.value.selectedProductManual = {
        description: undefined,
        invoice: undefined,
        custom: false,
        pay_type: SalePaymentProductType.Other,
    }

    if (manualProductComponent.value) {
        manualProductComponent.value.updateForm()
    }

    if (automaticProductComponent.value) {
        automaticProductComponent.value.updateForm()
    }
}

const cancelChangeTransaction = (clear: boolean = false) => {
    if (isEditTransactionAmount.value) {
        if (clear) {
            paymentController.value.transactionInputData = {}
        }

        isEditTransactionAmount.value = false
    }

    if (isEditOafSplit.value) {
        if (clear) {
            paymentController.value.oafInputData = {}
        }

        isEditOafSplit.value = false
    }
}

const recoverCC = () => {
    saleRecoverModal.open({
        saleVersionPK: usePk(saleVersion.record.value),
    })
}

useSuspensableComponent(async () => {
    if (!hasPermission('editPayments', 'Sale', sale.value)) {
        isManualPayment.value = false
    } else {
        isManualPayment.value = !sale.value.is_enable_automatic_transactions
    }

    gateways.value = await paymentGatewayModel.actions.getPaymentGatewaysForSale({
        sale_pk: props.salePk,
    })

    defaultGateway.value = String(gateways.value.find(gateway => gateway.is_default)?.id)
    selectedGateway.value = defaultGateway.value

    gatewayOptions.value = gateways.value.map(gateway => {
        return {
            title: gateway.name,
            value: String(gateway.id),
        }
    })
})

// watchers

watch(() => isEnableAutomaticTransactions.value, (value) => {
    isManualPayment.value  = !value
})

watch(() => isManualPayment.value, () => {
    resetData()
})
</script>
