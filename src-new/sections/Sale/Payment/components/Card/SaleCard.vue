<template>
    <div
        class="payment-card"
        :class="saleCardClass"
    >
        <div
            class="payment-card-label"
            :class="{
                '!cursor-default': !canEdit || saleCard.is_wrong
            }"
            @click="selectCard(usePk(saleCard))"
        >
            <input
                class="payment-card-check-input"
                :class="{
                    '!opacity-0': openedCardDetails || editingCardDetails
                }"
                name="paymentCard"
                type="radio"
                :disabled="!canEdit || saleCard.is_wrong"
                :checked="isSelected && canEdit"
                @click="selectCard(usePk(saleCard))"
            >
            <div class="payment-card-amount">
                <template v-if="isManualPayment">
                    <InputMoney
                        v-if="isSelected"
                        show-currency
                        :model-value="manualPaymentAmount"
                        :input-attrs="{
                            class:'payment-card-amount-input'
                        }"
                        size="small"
                        @update:model-value="($event: number) => emit('update:manualPaymentAmount', $event)"
                    />
                    <InputMoney
                        v-else
                        disabled
                        :input-attrs="{
                            class:'payment-card-amount-input'
                        }"
                        show-currency
                        placeholder="0.00"
                    />
                </template>
                <template v-else>
                    <InputMoney
                        v-if="isSelected"
                        :input-attrs="{
                            class:'payment-card-amount-input !text-primary'
                        }"
                        disabled
                        show-currency
                        show-cents
                        :model-value="paymentController.amount"
                    />
                    <InputMoney
                        v-else
                        :input-attrs="{
                            class:' payment-card-amount-input'
                        }"
                        disabled
                        show-currency
                        placeholder="0.00"
                    />
                </template>
            </div>
            <div
                class="payment-card-oaf"
                :class="{
                    'isOvercharge': charged > saleCard.amount
                }"
            >
                <div
                    class="payment-card-oaf-charge"
                >
                    <span>Charged:</span>
                    <span>${{ formatter.money(charged) }}</span>
                </div>
                <div class="payment-card-oaf-split">
                    <span>OAF Split:</span>
                    <span v-if="!isEditOafSplit">${{ formatter.money(saleCard.amount) }}</span>
                    <span v-else class="flex items-center">
                        <InputMoney
                            v-model="editAmountInput"
                            :min="0"
                            class="ml-2 h-3 w-14"
                            :input-attrs="{
                                class:'inline-edit text-3xs text-right' + (charged > saleCard.amount ? 'text-black': ''),
                            }"
                            size="small"
                        />
                        <span
                            class="text-2xs h-3 pt-0.5 w-3 leading-none ml-2 cursor-pointer"
                            :class="{
                                'text-black': charged > saleCard.amount
                            }"
                            @click.stop="addSplit()"
                        >
                            +
                        </span>
                    </span>
                </div>
            </div>
            <div
                v-if="!!getCardType(saleCard.credit_card_type)"
                class="payment-card-info"
            >
                <div class="payment-card-title">
                    <img
                        :src="getCreditCardImage(saleCard.credit_card_type)"
                        alt="Master Card"
                        class="payment-card-type h-3"
                        height="12"
                    >
                    {{ getCardType(saleCard.credit_card_type) }} **** {{ saleCard.strip?.slice(-4) }}
                </div>
                <div class="payment-card-expire">
                    Expiry {{ displayExpiration }}
                </div>
            </div>
            <div class="payment-card-checks px-0 flex-none ml-2">
                <SaleCreditCardVerificationComponent
                    :sale-pk="saleCard.sale_pk"
                    :sale-card-pk="usePk(saleCard)"
                />
                <TeleportWrapper
                    v-if="usedBeforePks.length > 0"
                    hover-enabled
                    class="tableTeleportCell !p-0 "
                    position="right"
                >
                    <template
                        #default="{
                            mouseover,
                            mouseleave
                        }"
                    >
                        <div
                            class="payment-card-checks-item --checked relative"
                            @mouseleave="mouseleave"
                            @mouseover="mouseover"
                        >
                            UB
                            <span
                                v-if="usedBeforePks.length > 1"
                                class="block absolute top-0 right-0 -mr-1.5 -mt-1.5 w-[12px] h-[12px] bg-pending rounded-full px-[4px] py-[2px]  text-white text-center text-[8px] shadow"
                            >{{ usedBeforePks.length }}</span>
                        </div>
                    </template>
                    <template
                        #content="{
                            mouseover,
                            mouseleave
                        }"
                    >
                        <CreditCardInfoPopover
                            :sale-pks="usedBeforePks"
                            @mouseleave="mouseleave"
                            @mouseover="mouseover"
                        />
                    </template>
                </TeleportWrapper>
            </div>
            <div class="payment-card-actions" @click.stop>
                <RatIcon
                    v-if="isFraud"
                    v-tooltip="{content: getFraudTooltip(saleCard.clientCard.fraudInfo)}"
                    class="text-danger h-5 w-5"
                />
                <AlertTriangleIcon
                    v-if="saleCard.is_wrong"
                    v-tooltip="{ content: 'Wrong payment info'}"
                    class="text-warning h-5 w-5"
                />
                <AppButton
                    class="box btn btn-outline-secondary"
                    @click="openVerifyModal"
                >
                    Verify
                </AppButton>
                <Dropdown>
                    <template #toggle="{ toggle }">
                        <AppButton
                            class="box btn btn-outline-secondary dropdown-toggle btn-sm"
                            @click="toggle"
                        >
                            <MoreVerticalIcon class="w-3.5 h-3.5 !stroke-2" />
                        </AppButton>
                    </template>
                    <template #content="{ close }">
                        <ContextMenu
                            :options="cardActions"
                            @close="close"
                        />
                    </template>
                </Dropdown>
            </div>
        </div>
        <AddNewCreditCardComponent
            v-if="editingCardDetails"
            :credit-card="saleCard"
            :charged="charged"
            @close="editingCardDetails = false"
        />
        <CreditCardDetails
            v-if="openedCardDetails"
            :credit-card="saleCard"
            :can-view="canView"
            :charged="charged"
            @close="openedCardDetails = false"
        />
    </div>
</template>

<script setup lang="ts">
import type { ModelRef } from '~types/lib/Model'
import ContextMenu from '@/components/ContextMenu/ContextMenu.vue'
import { $confirm, $confirmDelete } from '@/plugins/ConfirmPlugin'
import SaleCreditCardTestAmountPreview from '~/sections/Sale/modals/SaleCreditCardTestAmountPreview.vue'
import AddNewCreditCardComponent from '~/sections/Sale/Payment/components/Card/AddNewCreditCardComponent.vue'
import CreditCardDetails from '~/sections/Sale/Payment/components/Card/CreditCardDetails.vue'
import SaleCreditCardVerifyModal from '~/sections/Sale/Payment/components/modals/SaleCreditCardVerifyModal.vue'
import CreditCardInfoPopover from '~/sections/Sale/Payment/components/Card/CreditCardInfoPopover.vue'
import TeleportWrapper from '@/components/dropdown/TeleportWrapper.vue'
import SaleCreditCardVerificationComponent from '~/sections/Sale/components/SaleCreditCardVerificationComponent.vue'
import type { CreditCardType } from '~/api/models/Sale/SaleVersionCard'
import { SaleCardVerifyStatus } from '~/api/models/Sale/SaleVersionCard'
import { toastError, toastSuccess } from '@/lib/core/helper/ToastHelper'
import { getCreditCardImage, getExpiration } from '~/lib/Helper/CreditCardHelper'
import type { PaymentController } from '~/sections/Sale/composable/useSalePaymentController'
import RatIcon from '~assets/icons/RatIcon.svg?component'
import PromptModal from '~/modals/PromptModal.vue'

defineOptions({
    name: 'SaleCard',
})

const props = defineProps<{
    saleCard: ModelRef<'SaleVersionCard', 'clientCard' | 'clientCard.fraudInfo'>
    isEditOafSplit: boolean,
    isManualPayment: boolean
    manualPaymentAmount: number,
    canEdit: boolean,
    canEditCards: boolean,
    canView: boolean,
    paymentController: PaymentController,
}>()

const emit = defineEmits<{
    'update:manualPaymentAmount': [number]
}>()

const { hasPermission, useModel, useDictionary } = useContext()

const formatter = useService('formatter')

const creditCardTypeDictionary = useDictionary('CreditCardType')

//

const editingCardDetails = ref(false)
const openedCardDetails = ref(false)

//

const saleCardModel = useModel('SaleVersionCard')
const clientCardModel = useModel('ClientCreditCard')

const canMarkAsFraud = computed(() => {
    return hasPermission('markAsFraud', 'all')
})

const canManualVerify = computed(() => {
    return hasPermission('manualCardVerification', 'Sale')
})

const canViewTestAmount = computed(() => {
    return hasPermission('viewSaleCardTestAmount', 'Sale')
})

const displayExpiration = computed(() => {
    return props.canEdit ? getExpiration(props.saleCard.expiration) : getExpiration()
})

const haveVerification =  computed(() => {
    return props.saleCard.verify_status === SaleCardVerifyStatus.Waiting && !props.saleCard.is_verified
})

const editAmountInput = ref(props.saleCard.amount)

const usedBeforePks = ref<PrimaryKey[]>([])

const charged = computed(() => {
    return props.paymentController.calcCharged(usePk(props.saleCard), props.saleCard.client_card_pk)
})

const isSelected = computed(() => {
    return props.paymentController.selectedCard == usePk(props.saleCard)
})

const isFraud = computed(() => {
    return props.saleCard.clientCard.fraudInfo.is_fraud
})

const getFraudTooltip = (fraudInfo: ModelRef<'FraudInfo'> | undefined): string | undefined => {
    if (!fraudInfo || !fraudInfo.is_fraud) {
        return undefined
    }

    let tooltip = 'Fraud card'

    if (fraudInfo.remark) {
        tooltip += `: ${fraudInfo.remark}`
    }

    return tooltip
}

const saleCardClass = computed(() => {
    let styleClass = ''

    if (props.canEdit && isSelected.value) {
        styleClass += ' payment-card-selected'
    }

    if (openedCardDetails.value || editingCardDetails.value) {
        styleClass += ' payment-card-opened'
    }

    if (props.saleCard.is_wrong) {
        styleClass += ' is-wrong'
    }

    return styleClass
})

useSuspensableComponent(async () => {
    const response = await useModel('SaleVersionCard').actions.getUsedBeforeSalePks({
        pk: usePk(props.saleCard),
    })

    usedBeforePks.value = response.sale_pks
})

const selectCard = (pk: PrimaryKey) => {
    if (!props.canEdit || props.saleCard.is_wrong) {
        return
    }

    props.paymentController.selectCard(pk)
}

const changeIsWrong = async (isWrong: boolean) => {
    if (isWrong && isSelected.value) {
        props.paymentController.selectCard('')
    }

    await saleCardModel.actions.changeIsWrong({
        pk: usePk(props.saleCard),
        is_wrong: isWrong,
    })
}

const changeIsVerified = async () => {
    if (props.saleCard.is_wrong) {
        toastError('Can not mark this card as verified because card marked as wrong')

        return
    }
    await $confirm({
        text: 'Are you sure you want to as verified?',
    })

    await saleCardModel.actions.changeIsVerified({
        pk: usePk(props.saleCard),
    })
    toastSuccess('Card marked as verified')
}

const deleteCreditCard = async () => {
    await $confirmDelete({
        text: 'Delete card',
        description: `Are you sure you want to delete card?`,
    })
    await saleCardModel.actions.delete({
        pk: usePk(props.saleCard),
    })
}

//

const previewTestAmountModal = useModal(SaleCreditCardTestAmountPreview)

const openTestAmountDetails = () => {
    previewTestAmountModal.open({
        salePk: props.saleCard.sale_pk,
        saleCardPk: usePk(props.saleCard),
    })
}

const openCardDetails = () => {
    openedCardDetails.value = true
    editingCardDetails.value = false
}

const editCardDetails = () => {
    openedCardDetails.value = false
    editingCardDetails.value = true
}

//

const cardVerifyModal = useModal(SaleCreditCardVerifyModal)

const openVerifyModal = async () => {
    await cardVerifyModal.open({
        creditCard: props.saleCard,
        canViewExpiration: props.canEdit,
    })
}

const addSplit = () => {
    if (!Object.values(props.paymentController.oafInputData).length) {
        return
    }

    const balance = props.paymentController.sellPriceAll - props.paymentController.oafSplitFullAmount

    const inputAmount = isNaN(editAmountInput.value) ? 0 : editAmountInput.value
    const calculated = inputAmount + balance

    if (calculated <= 0) {
        toastError('Can\'t apply OAF Split less then 0')

        return
    }

    editAmountInput.value = Number(calculated).toMoney()
}

const detectChangeOafSplit = (amount: number) => {
    const valid = amount >= charged.value

    props.paymentController.updateOafInput(usePk(props.saleCard), amount, valid)
}

const getCardType = (creditCardType: CreditCardType): string => {
    return creditCardTypeDictionary.find(creditCardType).title
}

watch(() => props.isEditOafSplit, (value) => {
    if (!value) {
        editAmountInput.value = props.saleCard.amount
        detectChangeOafSplit(props.saleCard.amount)
    }
})

watch(() => props.saleCard.amount, () => {
    editAmountInput.value = props.saleCard.amount
    detectChangeOafSplit(props.saleCard.amount)
})

watch(editAmountInput, () => {
    const amount =  isNaN(editAmountInput.value) ? 0 : editAmountInput.value
    detectChangeOafSplit(amount)
})

onMounted(() => {
    detectChangeOafSplit(props.saleCard.amount)
})

const setFraud = async () => {
    const remark = props.saleCard.clientCard.fraudInfo.is_fraud ? null : await useModal(PromptModal).open({
        title: 'Please set reason',
    })

    await clientCardModel.actions.setFraud({ pk: props.saleCard.client_card_pk, value: !isFraud.value, remark })

    toastSuccess(`Card ${isFraud.value ? 'unmarked as Fraud' : 'marked as Fraud'}`)
}

const cardActions = computed(() => {
    return [
        {
            text: 'View',
            icon: EyeIcon,
            onClick: () => openCardDetails(),
        },
        props.canEditCards ? {
            text: 'Edit',
            icon: Edit3Icon,
            onClick: () => editCardDetails(),
        } : null,
        props.canEdit && !props.saleCard.is_wrong ? {
            text: 'Mark as wrong',
            icon: AlertTriangleIcon,
            class: 'text-warning',
            onClick: () => changeIsWrong(true),
        } : null,
        props.canEdit && props.saleCard.is_wrong ? {
            text: 'Unmark as wrong',
            icon: AlertTriangleIcon,
            class: 'text-warning w-[150px]',
            onClick: () => changeIsWrong(false),
        } : null,
        props.canEdit ? {
            text: 'Delete',
            icon: Trash2Icon,
            class: 'text-danger',
            onClick: () => deleteCreditCard(),
        } : null,
        canViewTestAmount.value && haveVerification.value ? {
            text: 'View test amount',
            icon: EyeIcon,
            class: 'w-[150px]',
            onClick: () => openTestAmountDetails(),
        } : null,
        canManualVerify.value && !props.saleCard.is_verified ? {
            text: 'Mark as verified',
            icon: CheckIcon,
            class: 'text-success',
            onClick: () => changeIsVerified(),
        } : null,
        canMarkAsFraud.value ? {
            text: isFraud.value ? 'Unmark as Fraud' : 'Mark as Fraud',
            icon: RatIcon,
            class: 'text-danger-600',
            onClick: () => setFraud(),
        } : null,
    ].filter(Boolean)
})
</script>
