<template>
    <div class="payment-card payment-card-opened">
        <div class="payment-card-edit">
            <button
                class="payment-card-edit-hide"
                @click="cancel"
            >
                <ChevronUpIcon
                    class="w-4 h-4 !stroke-2"
                />
            </button>
            <FormField
                hide-error
                :form="form"
                :field="'first_name'"
                class="col-span-2 text-2xs leading-normal text-slate-400 dark:text-gray-500"
                label="Card holder first name"
            >
                <InputText
                    v-model="form.data.first_name"
                    size="small"
                />
            </FormField>
            <FormField
                hide-error
                :form="form"
                :field="'card'"
                class="col-span-2  text-2xs leading-normal text-slate-400 dark:text-gray-500"
                label="Card number"
            >
                <InputText
                    v-model="form.data.card"
                    size="small"
                />
            </FormField>
            <FormField
                v-tooltip="{ content: form.errors.get('expiration')[0] }"
                hide-error
                :form="form"
                :field="'expiration'"
                class="col-span-1 text-2xs leading-normal text-slate-400 dark:text-gray-500"
                label="Expire"
            >
                <InputText
                    v-model="form.data.expiration"
                    size="small"
                />
            </FormField>
            <FormField
                v-tooltip="{ content: form.errors.get('cvv')[0] }"
                hide-error
                :form="form"
                :field="'cvv'"
                class="col-span-1 text-2xs leading-normal text-slate-400 dark:text-gray-500"
                label="CVV"
            >
                <InputText v-model="form.data.cvv" size="small" />
            </FormField>
            <FormField
                hide-error
                :form="form"
                :field="'last_name'"
                class="col-span-2 text-2xs leading-normal text-slate-400 dark:text-gray-500"
                label="Card holder last name"
            >
                <InputText v-model="form.data.last_name" size="small" />
            </FormField>
            <FormField
                hide-error
                :form="form"
                :field="'country'"
                class="col-span-2 text-2xs leading-normal text-slate-400 dark:text-gray-500"
                label="Country"
            >
                <InputText v-model="form.data.country" size="small" />
            </FormField>
            <FormField
                v-model="form.data.state"
                hide-error
                :form="form"
                :field="'state'"
                class="col-span-1 text-2xs leading-normal text-slate-400 dark:text-gray-500"
                label="State"
            >
                <InputText v-model="form.data.state" size="small" />
            </FormField>
            <FormField
                hide-error
                :form="form"
                :field="'zip'"
                class="col-span-1 text-2xs leading-normal text-slate-400 dark:text-gray-500"
                label="Postal code"
            >
                <InputText v-model="form.data.zip" size="small" />
            </FormField>
            <FormField
                hide-error
                :form="form"
                :field="'city'"
                class="col-span-2 text-2xs leading-normal text-slate-400 dark:text-gray-500"
                label="City"
            >
                <InputText v-model="form.data.city" size="small" />
            </FormField>
            <FormField
                hide-error
                :form="form"
                :field="'street'"
                class="col-span-2 text-2xs leading-normal text-slate-400 dark:text-gray-500"
                label="Street"
            >
                <template #error />
                <InputText v-model="form.data.street" size="small" />
            </FormField>
            <FormField
                hide-error
                :form="form"
                :field="'bank_phone'"
                class="col-span-2 text-2xs leading-normal text-slate-400 dark:text-gray-500"
                label="Bank phone"
            >
                <InputText v-model="form.data.bank_phone" size="small" />
            </FormField>
            <FormField
                hide-error
                :form="form"
                :field="'email'"
                class="col-span-2 text-2xs leading-normal text-slate-400 dark:text-gray-500"
                label="Card holder email"
            >
                <InputText v-model="form.data.email" size="small" />
            </FormField>
            <FormField
                hide-error
                :form="form"
                :field="'amount'"
                class="col-span-2 text-2xs leading-normal text-slate-400 dark:text-gray-500"
                label="OAF Split"
            >
                <InputMoney v-model="form.data.amount" size="small" />
            </FormField>
            <div v-if="isEditMode" class="col-span-2 text-xs">
                <label class="form-label text-2xs leading-normal text-slate-400 dark:text-gray-500">
                    OAF Charge
                </label>
                <InputMoney
                    :model-value="charged"
                    readonly
                    size="small"
                />
            </div>
            <div class="col-span-4 mt-2">
                <button
                    class="btn btn-primary text-xs w-20"
                    :disabled="!form.hasChanges"
                    @click="submit"
                >
                    Save
                </button>
                <button
                    class="btn btn-outline-secondary box text-xs w-20 ml-2.5"
                    @click="cancel"
                >
                    Cancel
                </button>
            </div>
            <div v-if="!isEditMode && creditCards?.length > 0" class="col-span-2 mt-2 flex justify-end">
                <button
                    class="btn btn-outline-secondary box text-xs px-2.5 py-1.5"
                    @click="clone"
                >
                    <CopyIcon
                        class="w-3.5 h-3.5 cursor-pointer hover:text-primary"
                    />
                </button>
            </div>
        </div>
    </div>
</template>

<script  setup lang="ts">
import { ChevronUpIcon, CopyIcon } from '@zhuowenli/vue-feather-icons'
import SaleCardCloneModal from '~/sections/Sale/Payment/components/modals/SaleCardCloneModal.vue'
import Form from '~/lib/Form/Form'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import type { ModelAttributes } from '~types/lib/Model'
import FormField from '~/components/Form/FormField.vue'
import { $confirm } from '@/plugins/ConfirmPlugin'

type SaleCardForm = {
    first_name: string,
    last_name: string,
    amount: number,
    card: string,
    expiration: string,
    cvv: string,
    street: string,
    city: string,
    state: string,
    zip: string,
    country: string,
    bank_phone: string,
    email: string,
}

const props = defineProps<{
    saleVersionPk?: PrimaryKey,
    creditCard?:  ModelAttributes<'SaleVersionCard'>,
    creditCards?:  ModelAttributes<'SaleVersionCard'>[],
    charged?: number,
}>()

const emit = defineEmits<{
    'close': [null]
}>()

const { useModel } = useContext()

const isEditMode = computed(() => {
    return !!props.creditCard?.sale_version_pk
})

const saleCardModel = useModel('SaleVersionCard')
const clientCreditCardModel = useModel('ClientCreditCard')

const cardCloneModal = useModal(SaleCardCloneModal)

const cardForEdit = props.creditCard

useSuspensableComponent(async () => {
    if (isEditMode.value) {
        const response = await saleCardModel.actions.requestCardSensitiveData({
            pk: usePk(cardForEdit),
        })

        if (response) {
            const formData = form.data as SaleCardForm
            formData.cvv = response.cvv
            formData.card = response.card

            form.updateInitialData(formData)
        }
    }
})

const form = new Form<SaleCardForm>({
    first_name: cardForEdit?.first_name ?? '',
    last_name: cardForEdit?.last_name ?? '',
    amount: cardForEdit?.amount ?? 0,
    card: '',
    expiration: cardForEdit?.expiration ?? '',
    cvv: '',
    street: cardForEdit?.street ?? '',
    city: cardForEdit?.city ?? '',
    state: cardForEdit?.state ?? '',
    zip: cardForEdit?.postal_code ?? '',
    country: cardForEdit?.country ?? '',
    bank_phone: cardForEdit?.bank_phone ?? '',
    email: cardForEdit?.email ?? '',
}, {
    first_name: ValidationRules.Required(),
    last_name: ValidationRules.Required(),
    amount: ValidationRules.Required('OAF Split is required', true),
    card: [ValidationRules.Required(), ValidationRules.Card()],
    expiration: [ValidationRules.Required(), ValidationRules.CardExpiration(false)],
    cvv: [ValidationRules.Required(), ValidationRules.Cvv()],
    street: ValidationRules.Required(),
    city: ValidationRules.Required(),
    state: ValidationRules.Required(),
    zip: ValidationRules.Required(),
    country: ValidationRules.Required(),
    bank_phone: ValidationRules.Required(),
    email: [ValidationRules.Required(), ValidationRules.Email()],
})

const clone = async () => {
    const card = await cardCloneModal.open({
        creditCards: props.creditCards,
    })

    if (card) {
        setClone(card)
    }
}

const setClone = (data) => {
    form.errors.clear()
    form.data.street = data.street
    form.data.city = data.city
    form.data.state = data.state
    form.data.zip = data.postal_code
    form.data.country = data.country
    form.data.bank_phone = data.bank_phone
    form.data.email = data.email
}

const cancel = () => {
    form.reset()
    emit('close')
}

const submit = form.useSubmit(async () => {
    const data = form.data as SaleCardForm

    if (isEditMode.value) {
        await update(data)
    } else {
        await create(data)
    }
})

const create = async (data: SaleCardForm) => {
    const isFraud = await clientCreditCardModel.actions.checkIfFraud({
        card_number: data.card,
        expiration: data.expiration,
    })

    if (isFraud) {
        await $confirm(`This card has been marked as fraudulent. Do you want to continue?`)
    }

    const card_pk = await saleCardModel.actions.create({
        sale_version_pk: cardForEdit?.sale_version_pk || props.saleVersionPk,
        data: data,
    })

    if (card_pk) {
        toastSuccess('New Credit Card added!')

        emit('close')
    }
}

const update = async (data: SaleCardForm) => {
    const isFraud = await clientCreditCardModel.actions.checkIfFraud({
        card_number: data.card,
        expiration: data.expiration,
    })

    if (isFraud) {
        await $confirm(`This card has been marked as fraudulent. Do you want to continue?`)
    }

    await saleCardModel.actions.update({
        card_pk: usePk(props.creditCard),
        data: data,
    })

    toastSuccess('Credit Card updated!')

    emit('close')
}
</script>
