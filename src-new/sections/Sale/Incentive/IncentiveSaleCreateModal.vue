<template>
    <AppModalWrapper
        header="Add incentive sale"
        class="!max-w-[500px]"
    >
        <div class="border-t p-4 grid grid-cols-2 gap-4">
            <FormField
                :form="form"
                :field="'net_price'"
                label="Net price"
                required
            >
                <InputMoney v-model="form.data.fare" @update:model-value="recalculate" />
            </FormField>
            <FormField
                :form="form"
                :field="'sell_price'"
                label="Sell price"
                required
            >
                <InputMoney v-model="form.data.sell_price" @update:model-value="recalculate" />
            </FormField>

            <FormField
                :form="form"
                :field="'item_type'"
                label="Type"
                required
            >
                <InputSelect
                    v-model="itemType"
                    with-empty
                    :options="productTypeOptions"
                />
            </FormField>

            <FormField
                :form="form"
                :field="'fop'"
                label="Payment type"
                required
            >
                <InputFop
                    v-model="form.data.fop"
                    :options="fopForSelectOptions"
                    :sale-pk="saleVersion.sale_pk"
                    class="w-full"
                    with-empty
                    @update:model-value="recalculate"
                />
            </FormField>

            <FormField
                :form="form"
                :field="'check_payment'"
                label="Ck"
            >
                <div class="flex gap-1 items-center">
                    <InputCheckbox v-model="form.data.check_payment_enabled" @update:model-value="recalculate" />
                    <InputMoney v-model="form.data.check_payment" :readonly="!form.data.check_payment_enabled" />
                </div>
            </FormField>

            <FormField
                :form="form"
                :field="'order_number'"
                label="Confirmation id - PNR:"
                required
            >
                <InputText
                    v-model="form.data.order_number"
                />
            </FormField>

            <FormField
                :form="form"
                :field="'consolidator_area_pk'"
                label="Consolidator"
                required
            >
                <InputSelect
                    v-model="form.data.consolidator_area_pk"
                    with-empty
                    :options="consolidatorAreaOptions"
                />
            </FormField>

            <FormField
                :form="form"
                :field="'remark'"
                label="Remark"
                class="col-span-full"
            >
                <InputTextarea
                    v-model="form.data.remark"
                    rows="6"
                    maxlength="1000"
                />
            </FormField>
        </div>

        <template #footer="{ close }">
            <div class="flex gap-2 justify-between">
                <AppButton @click="close">
                    Close
                </AppButton>
                <AppButton
                    :loading="form.loading"
                    class="--primary"
                    @click="submit"
                >
                    Submit
                </AppButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import FormField from '~/components/Form/FormField.vue'
import InputFop from '~/sections/Sale/components/InputFop.vue'
import type { FopValueForSelectType } from '~/sections/Sale/composable/useSaleFop'
import { useSaleFop } from '~/sections/Sale/composable/useSaleFop'
import type { ProductType } from '~/api/models/Product/Product'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import ProductService from '~/lib/Product/ProductService'
import { useIncentiveProductTypeOptions } from '~/sections/Sale/Incentive/useIncentiveProductTypeOptions'
import type { SaleInsuranceType } from '~/api/dictionaries/Static/Sale/SaleInsuranceTypeDictionary'
import { useProductType } from '~/sections/Sale/composable/useProductType'

defineOptions({
    name: 'IncentiveSaleCreateModal',
    modal: {
        promise: true,
    },
})

const props = defineProps<{
    saleVersionPk: PrimaryKey,
}>()

const emit = defineEmits<{
    close: [],
    resolve: [],
}>()

//

const { useModel, hasPermission, record: saleVersion } = await useNewContext('SaleVersion', props.saleVersionPk)

//

const { record: sale, fetch: fetchSale } = useModel('Sale').useRecord().destructable()

//

const canManageSale = computed(() => hasPermission('manage', 'Sale', sale))

const { options: productTypeOptions } = useIncentiveProductTypeOptions(canManageSale)
const { createWritableField } = useProductType()

const itemType = createWritableField(() => form.data)

const { fopForSelectOptions, fetchLists } = await useSaleFop(saleVersion.sale_pk)

const consolidatorAreaOptions = computed(() => {
    return useGeneralDictionary('ConsolidatorArea').mapRecords.forSelect()
})

await Promise.all([
    fetchSale(saleVersion.sale_pk),
    fetchLists(),
])

type FormData = {
    fop: FopValueForSelectType | undefined
    item_type: ProductType | undefined,
    sub_type: SaleInsuranceType | undefined,
    sell_price: number,
    fare: number
    check_payment: number,
    check_payment_enabled: boolean,
    //
    consolidator_area_pk: PrimaryKey | undefined,
    remark: string,
    order_number: string | undefined
}

const form = useForm<FormData>({
    fop: undefined,
    item_type: undefined,
    sub_type: undefined,
    sell_price: 0,
    check_payment: 0,
    fare: 0,
    check_payment_enabled: false,
    consolidator_area_pk: undefined,
    remark: '',
    order_number: undefined,
}, {
    // @note We make form fields with "required" star, but actually we don't require them. This was the request a long time ago.
    item_type: ValidationRules.Required('Type is required'),
})

const makeProductService = () => {
    return ProductService.makeIncentiveSale({
        sell_price: form.data.sell_price,
        fare: form.data.fare,
        pay_type: form.data.fop?.category,
        check_payment: form.data.check_payment,
        check_payment_enabled: form.data.check_payment_enabled,
    })
}

function recalculate() {
    form.data.check_payment = makeProductService().getCalculateCk()
}

const submit = form.useSubmit(async () => {
    await useModel('IncentiveSale').actions.create({
        sale_version_pk: props.saleVersionPk,
        product: {
            item_type: form.data.item_type!,
            sub_type: form.data.sub_type || null,
            sell_price: form.data.sell_price,
            fare: form.data.fare,
            pay_type: form.data.fop?.category || null,
            card_identity: form.data.fop?.cardPk || null,
            check_payment: form.data.check_payment,
            check_payment_ps: makeProductService().check_payment_ps,
            //
            consolidator_area_pk: form.data.consolidator_area_pk,
            remark: form.data.remark,
            order_number: form.data.order_number,
        },
    })

    toastSuccess('Incentive sale was created')

    emit('resolve')
})
</script>

