<template>
    <tr>
        <td>{{ incentive.id }}</td>
        <td>
            <template v-if="editEnabled">
                <FormField
                    :form="form"
                    field="sell_price"
                    hide-error
                >
                    <InputMoney
                        v-model="form.data.sell_price"
                        size="xs"
                        @update:model-value="recalculate"
                    />
                </FormField>
            </template>
            <template v-else>
                {{ $format.money(incentive.product.sell_price) }}
            </template>
        </td>
        <td>
            <template v-if="editEnabled">
                <FormField
                    :form="form"
                    field="fare"
                    hide-error
                >
                    <InputMoney
                        v-model="form.data.fare"
                        size="xs"
                        @update:model-value="recalculate"
                    />
                </FormField>
            </template>
            <template v-else>
                {{ $format.money(incentive.product.fare) }}
            </template>
        </td>
        <td>
            <template v-if="editEnabled">
                <div class="flex gap-2 items-center">
                    <InputCheckbox
                        v-model="form.data.check_payment_enabled"
                        size="xs"
                        @update:model-value="recalculate"
                    />
                    <FormField
                        :form="form"
                        field="ck_payment"
                        hide-error
                    >
                        <InputMoney
                            v-model="form.data.check_payment"
                            size="xs"
                            class="min-w-[40px]"
                            @change="ckWasChangedManually = true"
                        />
                    </FormField>
                </div>
            </template>
            <template v-else>
                {{ $format.money(incentive.product.check_payment) }}
            </template>
        </td>
        <td>
            {{ $format.money(profit) }}
        </td>
        <td>
            <template v-if="editEnabled">
                <FormField
                    :form="form"
                    field="item_type"
                    hide-error
                >
                    <InputSelect
                        v-model="itemType"
                        :options="productTypeOptions"
                        placeholder="Type"
                        size="xs"
                        teleport
                    />
                </FormField>
            </template>
            <template v-else>
                <span
                    v-if="incentive.product.item_type === 'Insurance' && incentive.product.sub_type &&
                        getInsuranceTypeInfo(incentive.product.sub_type)"
                    class="flex gap-1"
                >
                    {{
                        getInsuranceTypeInfo(incentive.product.sub_type)?.group === SaleInsuranceTypeGroup.BaggageProtection
                            ? 'Baggage Protection'
                            : 'TP'
                    }}
                    <span class="flex gap-1 items-center">
                        <span v-if="getInsuranceTypeInfo(incentive.product.sub_type)?.titleShort">
                            ({{ getInsuranceTypeInfo(incentive.product.sub_type)?.titleShort }})
                        </span>

                        <span
                            v-tooltip="'Read terms and conditions'"
                            class="text-gray-500 cursor-pointer"
                            @click="openSubTypeInfo(incentive.product.sub_type)"
                        >
                            <InfoIcon class="icon --small" />
                        </span>
                    </span>
                </span>
                <span v-else>{{ getProductTypeInfo(incentive.product.item_type).title }}</span>
            </template>
        </td>
        <td>
            <template v-if="editEnabled">
                <FormField
                    :form="form"
                    field="for"
                    hide-error
                >
                    <InputFop
                        v-model="form.data.fop"
                        with-empty
                        :options="fopForSelectOptions"
                        :sale-pk="usePk(sale)"
                        class="w-full max-w-[120px]"
                        size="xs"
                        teleport
                        @update:model-value="recalculate"
                    />
                </FormField>
            </template>
            <template v-else>
                <div class="flex gap-2 items-center">
                    {{ fop?.text }}

                    <button
                        v-if="incentive.product.pay_type === ProductPayType.ComVCC && incentive.product.card_identity"
                        class="inline box rounded p-0.5 text-green-500"
                        @click="showCredentials"
                    >
                        <EyeIcon class="w-2 h-2 !stroke-2" />
                    </button>
                </div>
            </template>
        </td>
        <td class="max-w-[140px]">
            <div v-if="incentive.product.createdBy" class="max-w-[110px] truncate pr-2">
                {{ getShortName(incentive.product.createdBy) }}
            </div>
            <div class="max-w-[140px] truncate">
                {{ $format.datetime(incentive.product.created_at) }}
            </div>
        </td>
        <td>
            <div class="flex items-center gap-0.5 justify-end -m-2">
                <template v-if="editEnabled">
                    <AppButton
                        class="--ghost --square --success"
                        :loading="form.loading"
                        @click="save"
                    >
                        <CheckIcon />
                    </AppButton>
                    <AppButton
                        class="--ghost --square --danger"
                        @click="cancelEdit"
                    >
                        <XIcon />
                    </AppButton>
                </template>
                <template v-else>
                    <ChatOpenButton
                        v-if="incentive.product.chat_branch_name"
                        v-slot="{messagesCount, hasNewMessages}"
                        :branch="incentive.product.chat_branch_name"
                        :chat-room="chatRoom"
                        class="relative items-center p-1 -my-1 hover:bg-gray-200 rounded"
                    >
                        <MessageCircleIcon class="w-4 h-4" />
                        <div
                            v-if="messagesCount"
                            :class="[hasNewMessages ? 'bg-orange-400 text-white' : 'bg-slate-200 text-gray-700']"
                            class="sales-ticket-badge top-0 right-0"
                        >
                            {{ messagesCount }}
                        </div>
                    </ChatOpenButton>
                    <AppButton
                        v-if="canEdit"
                        class="--ghost --square"
                        @click="startEdit"
                    >
                        <EditIcon />
                    </AppButton>

                    <AppButton
                        v-if="canEdit"
                        class="--ghost --square --danger"
                        @click="deleteProduct"
                    >
                        <Trash2Icon />
                    </AppButton>
                </template>
            </div>
        </td>
    </tr>
</template>

<script setup lang="ts">
import type { ModelAttributes, ModelRef } from '~types/lib/Model'
import type { FopValueForSelectType, SaleFopComposable } from '~/sections/Sale/composable/useSaleFop'
import type { ProductType } from '~/api/models/Product/Product'
import { ProductPayType } from '~/api/models/Product/Product'
import ProductService from '~/lib/Product/ProductService'
import { $confirmDelete } from '@/plugins/ConfirmPlugin'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import { SaleInsuranceType, SaleInsuranceTypeGroup } from '~/api/dictionaries/Static/Sale/SaleInsuranceTypeDictionary'
import InputFop from '~/sections/Sale/components/InputFop.vue'
import { getShortName } from '~/lib/Helper/PersonHelper'
import type ChatRoomModelDefinition from '@/modules/chat/types/ChatRoomModelDefinition'
import ChatOpenButton from '@/modules/chat/components/ChatOpenButton.vue'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import FormField from '~/components/Form/FormField.vue'
import { useIncentiveProductTypeOptions } from '~/sections/Sale/Incentive/useIncentiveProductTypeOptions'
import { useProductType } from '~/sections/Sale/composable/useProductType'

defineOptions({
    name: 'SaleIncentiveTableRow',
})

//

const props = defineProps<{
    sale: ModelAttributes<'Sale'>,
    incentive: ModelRef<'IncentiveSale', 'product' | 'product.createdBy'>,
    canEdit: boolean,
    chatRoom: ChatRoomModelDefinition,
    fopHelper: SaleFopComposable,
}>()

//

const { useModel, useDictionary, hasPermission } = useContext()

//

const { fopForSelectOptions, getFopInfoFromCardIdentity } = props.fopHelper

const canManageSale = hasPermission('manage', 'Sale', props.sale)

const { options: productTypeOptions } = useIncentiveProductTypeOptions(canManageSale)
const { createWritableField } = useProductType()

const itemType = createWritableField(() => form.data)

const productTypeDictionary = useGeneralDictionary('ProductType')
const getProductTypeInfo = (productType: ProductType) => productTypeDictionary.find(productType)

//

type FormData = {
    fop: FopValueForSelectType | undefined
    item_type: ProductType | undefined,
    sub_type: SaleInsuranceType | undefined,
    sell_price: number,
    fare: number
    check_payment: number,
    check_payment_enabled: boolean,
}

const form = useForm<FormData>({
    fop: undefined,
    item_type: undefined,
    sub_type: undefined,
    sell_price: 0,
    check_payment: 0,
    fare: 0,
    check_payment_enabled: true,
}, {
    item_type: ValidationRules.Required('Type is required'),
})

const seedForm = () => {
    const product = props.incentive.product

    const fop = getFopInfoFromCardIdentity(product.card_identity || undefined, product.pay_type as ProductPayType)

    form.updateInitialData({
        fop: fop,
        item_type: product.item_type || undefined,
        sub_type: product.sub_type || undefined,
        sell_price: product.sell_price || 0,
        check_payment: product.check_payment || 0,
        fare: product.fare,
        check_payment_enabled: product.check_payment_ps > 0,
    })
}

//

const fop = computed(() => {
    const product = props.incentive.product

    return getFopInfoFromCardIdentity(product.card_identity || undefined, product.pay_type as ProductPayType)
})

const makeProductService = () => {
    return ProductService.makeIncentiveSale({
        sell_price: form.data.sell_price,
        fare: form.data.fare,
        pay_type: form.data.fop?.category,
        check_payment: form.data.check_payment,
        check_payment_enabled: form.data.check_payment_enabled,
    })
}

const profit = computed(() => {
    if (editEnabled.value) {
        return makeProductService().getCalculateProfit() // @todo Rename this method
    } else {
        return props.incentive.product.profit
    }
})

//

watch(() => props.incentive.product, () => {
    seedForm()
})

const ckWasChangedManually = ref(false)

function recalculate() {
    const product = makeProductService()

    if (!ckWasChangedManually.value) {
        form.data.check_payment = product.getCalculateCk()
    }
}

///

const editEnabled = ref(false)

function startEdit() {
    editEnabled.value = true

    seedForm()
}

function cancelEdit() {
    editEnabled.value = false

    form.reset()
}

//

async function deleteProduct() {
    await $confirmDelete('Are you sure wan to delete this Incentive sale?')

    await useModel('IncentiveSale').actions.delete({
        pk: usePk(props.incentive),
    })

    toastSuccess('Incentive sale was deleted')
}

const save = form.useSubmit(async () => {
    await useModel('IncentiveSale').actions.update({
        pk: usePk(props.incentive),
        product: {
            item_type: form.data.item_type!,
            sub_type: form.data.sub_type || null,
            sell_price: form.data.sell_price,
            fare: form.data.fare,
            pay_type: form.data.fop?.category || null,
            card_identity: form.data.fop?.cardPk || null,
            check_payment: form.data.check_payment,
            check_payment_ps: makeProductService().check_payment_ps,
        },
    })

    editEnabled.value = false
})

async function showCredentials() {
    const productPk = usePk(props.incentive.product)

    if (!productPk) {
        return
    }

    const { url } = await useModel('ProjectCard').actions.getCredentialsUrl({
        product_pk: productPk,
    })

    window.open(url, '_blank')
}

function getInsuranceTypeInfo(subType: SaleInsuranceType) {
    return useGeneralDictionary('SaleInsuranceType').records.find((record) => record.id === subType)
}

//

const projectInfo = useDictionary('Project').find(props.sale.project_pk)!

async function openSubTypeInfo(subType: SaleInsuranceType) {
    const paySiteOrigin = '//' + projectInfo.pay_host

    let link: string | undefined

    if (subType === SaleInsuranceType.BaggageProtection) {
        link = `${paySiteOrigin}/protection-terms/baggage`
    } else if (subType === SaleInsuranceType.TicketProtectionPlan1) {
        link = `${paySiteOrigin}/protection-terms/plan1`
    } else if (subType === SaleInsuranceType.TicketProtectionPlan2) {
        link = `${paySiteOrigin}/protection-terms/plan2`
    } else if (subType === SaleInsuranceType.TicketProtectionPlan3) {
        link = `${paySiteOrigin}/protection-terms/plan3`
    }

    if (link) {
        window.open(link, '_blank')
    }
}
</script>

