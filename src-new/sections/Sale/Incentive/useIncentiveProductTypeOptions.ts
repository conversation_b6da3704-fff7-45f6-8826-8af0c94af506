import type SelectOption from '~types/structures/SelectOption'
import { ProductType } from '~/api/models/Product/Product'
import type { SaleInsuranceType } from '~/api/dictionaries/Static/Sale/SaleInsuranceTypeDictionary'
import { ProductTypeCategory } from '~/api/dictionaries/Static/Product/ProductTypeDictionary'
import type { MaybeRefOrGetter, WritableComputedRef } from 'vue'

export function useIncentiveProductTypeOptions(
    canManageSale: MaybeRefOrGetter<boolean>,
) {
    const options = computed((): SelectOption<{
        item_type: ProductType,
        sub_type: SaleInsuranceType | undefined,
    }> => {
        const productTypeDictionary = useGeneralDictionary('ProductType')

        const baseRecords = productTypeDictionary
            .getCategoryRecords(ProductTypeCategory.IncentiveSale)
            .filter((item) => {
                if (item?.access && item?.access.includes('manage') && !canManageSale) {
                    return false
                }

                return true
            })

        const baseOptions = productTypeDictionary.adapter.mapRecords(baseRecords).forSelect()

        const insuranceOptions = useGeneralDictionary('SaleInsuranceType').mapRecords.forSelect({
            grouped: true,
        })

        // @todo It should not be merged like this, should come from product type mapper.

        return [
            ...baseOptions.map(option => ({
                ...option,
                value: {
                    item_type: option.value,
                    sub_type: undefined,
                },
            })) as SelectOption[],
            ...insuranceOptions.map(option => ({
                ...option,
                value: {
                    item_type: ProductType.Insurance,
                    sub_type: option.value,
                },
            })) as SelectOption[],
        ] as SelectOption<{
            item_type: ProductType,
            sub_type: SaleInsuranceType | undefined,
        }>[]
    })

    return {
        options,
    }
}
