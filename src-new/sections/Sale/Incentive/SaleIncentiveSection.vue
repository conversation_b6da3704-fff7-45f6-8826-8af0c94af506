<template>
    <div class="flex items-center justify-between pt-5">
        <h6 class="m-0">
            Incentive Sales
        </h6>
        <AppButton
            v-if="canEdit"
            class="dark:bg-dark-3 dark:text-white"
            @click="create"
        >
            <PlusIcon />
            Add incentive
        </AppButton>
    </div>

    <SuspenseManual :state="suspense">
        <template #default>
            <div class="card mt-2">
                <div class="card__body overflow-x-auto fancy-scroll-x">
                    <table class="sales-incentive-table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th class="w-[160px]">
                                    Sell price
                                </th>
                                <th class="w-[160px]">
                                    Net price
                                </th>
                                <th class="w-[140px]">
                                    Ck
                                </th>
                                <th class="w-[200px]">
                                    Profit
                                </th>
                                <th class="w-[200px]">
                                    Type
                                </th>
                                <th class="w-[200px]">
                                    Payment type
                                </th>
                                <th>
                                    Created by
                                </th>
                                <th class="text-right">
                                    Action
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <SaleIncentiveTableRow
                                v-for="incentive in incentiveSalesList.records"
                                :key="incentive.id"
                                :sale="sale"
                                :incentive="incentive"
                                :can-edit="canEdit"
                                :chat-room="chatRoom"
                                :fop-helper="fopHelper"
                            />
                        </tbody>
                        <tfoot>
                            <tr class="text-primary-1">
                                <td />
                                <td>{{ $format.money(totals.sell_price) }}</td>
                                <td>{{ $format.money(totals.fare) }}</td>
                                <td>{{ $format.money(totals.check_payment) }}</td>
                                <td colspan="2">
                                    <span class="text-gray-500">Profit:</span> {{ $format.money(totals.profit) }}
                                </td>
                                <td colspan="4" />
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </template>
        <template #fallback>
            <PlaceholderBlock class="mt-2 w-full h-[105px]" />
        </template>
    </SuspenseManual>
</template>

<script setup lang="ts">
import SaleIncentiveTableRow from '~/sections/Sale/Incentive/SaleIncentiveTableRow.vue'
import IncentiveSaleCreateModal from '~/sections/Sale/Incentive/IncentiveSaleCreateModal.vue'
import type ChatRoomModelDefinition from '@/modules/chat/types/ChatRoomModelDefinition'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import { useSaleFop } from '~/sections/Sale/composable/useSaleFop'

defineOptions({
    name: 'SaleIncentiveSection',
})

const props = defineProps<{
    saleVersionPk: PrimaryKey
}>()

const { useModel, record: saleVersion, hasPermission } = await useNewContext('SaleVersion', props.saleVersionPk)

const { record: sale, fetch: fetchSale } = useModel('Sale').useRecord({
    with: ['chat'],
}).destructable()

const incentiveSalesList = useModel('IncentiveSale').useResourceList({
    name: 'SaleVersionIncentiveSaleList',
    pk: props.saleVersionPk,
}, {
    with: ['product', 'product.createdBy'],
})

//

const suspense = useSuspensableComponent(async () => {
    await Promise.all([
        fetchSale(saleVersion.sale_pk),
        incentiveSalesList.fetch(),
        fopHelper.fetchLists(),
    ])
})

watch(() => props.saleVersionPk, () => {
    suspense.fetch()
})

//

const canEdit = computed(() => {
    return hasPermission('edit', 'Sale', sale.value) && !sale.value?.is_sale_closed && !sale.value?.is_sale_adjusted
})

//

const fopHelper = await useSaleFop(saleVersion.sale_pk)

//

const createModal = useModal(IncentiveSaleCreateModal)

function create() {
    createModal.open({
        saleVersionPk: props.saleVersionPk,
    })
}

const totals = computed(() => {
    const result = {
        sell_price: 0,
        fare: 0,
        check_payment: 0,
        profit: 0,
    }

    for (const incentive of incentiveSalesList.records) {
        result.check_payment += incentive.product.check_payment
        result.fare += incentive.product.fare
        result.sell_price += incentive.product.sell_price
        result.profit += incentive.product.profit
    }

    return result
})

//

const chatRoom = computed(() => {
    const generalGroup: ChatRoomModelDefinition = {
        id: Number(sale.value.chat_pk),
        ringcentral_id: 0,
        gateway: {
            id: Number(sale.value.chat.gateway_pk),
        },
    }

    return generalGroup
})
</script>

