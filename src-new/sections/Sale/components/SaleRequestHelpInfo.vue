<template>
    <div class="flex flex-col">
        <div class="p-5 flex flex-col items-center justify-center gap-y-4 border-b dark:border-secondary-600">
            <div class="tip --danger p-0 border-none bg-white dark:bg-dark-3">
                <AppTipIcon type="danger" />
            </div>
            <div class="flex flex-col items-center gap-y-1 text-center">
                <span class="text-base font-semibold dark:text-secondary-50">Expert Request Incoming #{{ record.id }}</span>
                <span class="text-sm text-secondary dark:text-secondary-300">
                    {{ getFullName(agentInfo) }} is requesting expert help in <br><span class="text-secondary-900 dark:text-secondary-50">Sale</span> <span class="text-primary dark:text-primary-400">#{{ props.record.sale_pk }}</span>
                </span>
            </div>
            <div class="flex flex-col gap-y-1.5 w-full border-t pt-4 dark:border-secondary-600">
                <span class="text-secondary text-sm dark:text-secondary-300">Problem description:</span>
                <span class="text-sm text-secondary-900 dark:text-secondary-50">
                    {{ record.reason }}
                </span>
            </div>
        </div>

        <div class="p-5">
            <AppButton
                class="w-full --primary --large"
                :loading="form.loading"
                @click="submit"
            >
                Accept Request
            </AppButton>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { ModelAttributes } from '~types/lib/Model'
import { getFullName } from '~/lib/Helper/PersonHelper'
import { goToSale } from '@/lib/core/helper/RouteNavigationHelper'
import { useWaitForResourceEvent } from '~/composables/useWaitForResourceEvent'

const props = defineProps<{
    record: ModelAttributes<'SaleRequestHelp'>
}>()

const { useModel, useDictionary, workspace } = useContext()

const agentDictionary = useDictionary('Agent')

const agentInfo = computed(() => {
    return agentDictionary.find(props.record.created_by_pk)
})

const form = useForm({})

const submit = form.useSubmit(async () => {
    await useWaitForResourceEvent(
        await useModel('SaleRequestHelp').actions.takeRequest({
            request_pk: usePk(props.record),
        }),
        'SaleRequestHelpList',
        'update',
        `${workspace}:all`,
    )

    await goToSale(props.record.sale_pk)
})
</script>
