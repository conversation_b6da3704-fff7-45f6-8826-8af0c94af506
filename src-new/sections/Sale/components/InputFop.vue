<template>
    <InputSelect
        v-bind="selectProps"
        v-model="value"
        :options="options"
        filter
        v-on="inheritEvents([
            'update:modelValue',
            'change',
            'open',
            'close',
            'focus',
            'blur',
            'focusMove',
            'search',
            'clear',
            'keydown',
        ])"
    />
</template>

<script setup lang="ts">
import type { Emits, Props as SelectProps, Props } from '~/components/Input/InputSelect'
import type SelectOption from '~types/structures/SelectOption'
import { inheritEvents } from '@/lib/core/helper/VueComponentHelper'
import VCCSelectModal from '~/sections/Card/VirtualCard/modals/VCCSelectModal.vue'
import type { FopValueForSelectType } from '~/sections/Sale/composable/useSaleFop'
import { getVccForSelectValue } from '~/sections/Sale/composable/useSaleFop'

const props = withDefaults(defineProps<Props<FopValueForSelectType | undefined> & {
    options?: SelectOption[],
    salePk: PrimaryKey,
    ticketPk?: PrimaryKey,
}>(), {
    options: undefined,
    ticketPk: undefined,
})

const emit = defineEmits<Emits<FopValueForSelectType | undefined>>()

// context

const { useModel } = await useNewContext('Sale', props.salePk)

//

const selectProps = computed<SelectProps<FopValueForSelectType>>(() => ({
    ...props,
}))

// new vcc modal

const vccSelectModal = useModal(VCCSelectModal)

//

const value = computed({
    get() {
        return props.modelValue
    },
    set(value) {
        if (value?.text !== 'New') {
            emit('update:modelValue', value)
        } else {
            openCreateNewVccModal()
        }
    },
})

//

const openCreateNewVccModal = async () => {
    const newVccCardPk = await vccSelectModal.open({
        salePk: props.salePk,
        ticketPk: props.ticketPk,
    })
    const card = await useModel('ProjectCard').useRecord().fetch(newVccCardPk)
    emit('update:modelValue', getVccForSelectValue(card.value))
}
</script>
