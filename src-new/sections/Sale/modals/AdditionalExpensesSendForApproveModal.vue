<template>
    <AppModalWrapper
        header="Send to client for approve"
        :class="{
            '!max-w-[550px]': !previewUrl,
            '!max-w-[1200px]': previewUrl
        }"
    >
        <div class="flex gap-2 flex-nowrap w-full p-2 bg-secondary-100">
            <div
                class="w-[550px] "
                :class="{
                    'flex-none': previewUrl
                }"
            >
                <div class="card grid grid-cols-12 gap-2 p-4 mb-2">
                    <FormField
                        :form="form"
                        field="sell_price"
                        label="Sell Price"
                        class="text-xs col-span-6"
                    >
                        <InputMoney
                            v-model="form.data.sell_price"
                            size="small"
                            show-currency
                        />
                    </FormField>

                    <FormField
                        :form="form"
                        field="email"
                        label="Email"
                        class="text-xs col-span-6"
                    >
                        <InputText
                            v-model="form.data.email"
                            size="small"
                        />
                    </FormField>

                    <FormField
                        :form="form"
                        field="email_subject"
                        label="Email subject"
                        class="text-xs col-span-12"
                    >
                        <InputText
                            v-model="form.data.email_subject"
                            size="small"
                        />
                    </FormField>

                    <FormField
                        :form="form"
                        field="remark"
                        label="Message to client"
                        class="text-xs col-span-12"
                    >
                        <InputTextarea
                            v-model="form.data.remark"
                            size="small"
                            rows="4"
                        />
                    </FormField>
                </div>

                <div class="card p-4 flex flex-col gap-2">
                    <div class="font-bold text-xs">
                        Select a credit card if needed
                    </div>
                    <label
                        v-for="creditCard in creditCards"
                        :key="creditCard.id"
                        :class="{
                            'payment-card-selected': form.data.card_pks.includes(String(creditCard.id)),
                        }"
                        class="payment-card flex flex-row gap-4 p-4 items-center"
                    >
                        <div>
                            <InputCheckbox
                                :model-value="form.data.card_pks.includes(String(creditCard.id))"
                                @update:model-value="changeSelectCard(String(creditCard.id), $event)"
                            />
                        </div>

                        <div
                            v-if="!!getCardType(creditCard.credit_card_type)"
                            class="payment-card-info"
                        >
                            <div class="payment-card-title">
                                <img
                                    :src="getCreditCardImage(creditCard.credit_card_type)"
                                    alt="Master Card"
                                    class="payment-card-type h-3"
                                    height="12"
                                >
                                {{ getCardType(creditCard.credit_card_type) }} **** {{ creditCard.strip?.slice(-4) }}
                            </div>
                            <div class="payment-card-expire">
                                Expiry {{ displayExpiration(creditCard) }}
                            </div>
                        </div>

                        <SaleCreditCardVerificationComponent
                            :sale-pk="creditCard.sale_pk"
                            :sale-card-pk="usePk(creditCard)"
                        />

                    </label>
                </div>
            </div>

            <div v-if="previewUrl" class="card card w-full">
                <iframe
                    :src="previewUrl"
                    class="w-full min-h-[500px]"
                />
            </div>
        </div>
        <template #footer="{ close }">
            <div class="flex justify-between gap-4">
                <div class="flex gap-2">
                    <AppButton @click="close">
                        Cancel
                    </AppButton>
                </div>
                <div class="flex gap-2">
                    <AppButton class="--primary --outline" @click="getPreview">
                        Preview changes
                    </AppButton>

                    <AppButton class="--primary" @click="submit">
                        Send
                    </AppButton>
                </div>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import FormField from '~/components/Form/FormField.vue'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import { getCreditCardImage, getExpiration } from '~/lib/Helper/CreditCardHelper'
import { useSaleCardList } from '~/sections/Sale/composable/useSaleCardList'
import type { CreditCardType } from '~/api/models/Sale/SaleVersionCard'
import SaleCreditCardVerificationComponent from '~/sections/Sale/components/SaleCreditCardVerificationComponent.vue'
import type { ModelAttributes } from '~types/lib/Model'

defineOptions({
    name: 'AdditionalExpensesSendForApproveModal',
})

const props = defineProps<{
    salePk: PrimaryKey,
    saleVersionPk: PrimaryKey,
    productPk: PrimaryKey,
    sellPrice: number
}>()
const emit = defineEmits<{
    close: [],
}>()

const { useModel, record: saleRecord, hasPermission } = await useNewContext('Sale', props.salePk)

const canEditCards = computed(() => hasPermission('editCards', 'Sale', saleRecord))
const canEditPayments = computed(() => hasPermission('editPayments', 'Sale', saleRecord))
const canEdit = computed(() => {
    return canEditCards.value && canEditPayments.value
})
const { fetchPaxCards, saleCardList } = useSaleCardList(props.saleVersionPk)

const creditCardTypeDictionary = useGeneralDictionary('CreditCardType')

const getCardType = (creditCardType: CreditCardType): string => {
    return creditCardTypeDictionary.find(creditCardType).title
}

const displayExpiration = (saleVersionCard: ModelAttributes<'SaleVersionCard'>) => {
    return canEdit.value ? getExpiration(saleVersionCard.expiration) : getExpiration()
}

const clientModel = useModel('Client').useRecord()

const creditCards = computed(() => {
    return saleCardList.records
})

const suspense = useSuspensableComponent(async () => {
    await Promise.all([
        clientModel.fetch(saleRecord.client_pk),
        fetchPaxCards(),
    ])

    form.updateInitialData({
        pk: props.productPk,
        email: clientModel.record.value.email,
        email_subject: '',
        sell_price: props.sellPrice,
        remark: '',
        card_pks: [],
    })
})

const form = useForm({
    pk: props.productPk,
    email: '',
    email_subject: '',
    remark: '',
    sell_price: 0,
    card_pks: [],
}, {
    email: [ValidationRules.Required(), ValidationRules.Email('Email is required')],
    email_subject: ValidationRules.Required('Subject is required'),
    sell_price: [ValidationRules.Required(), ValidationRules.Number()],
    remark: ValidationRules.Required('Message to client is required'),
})

const previewUrl = ref('')

const getPreview = form.useSubmit(async () => {
    const data = {
        pk: form.data.pk,
        email: form.data.email,
        remark: form.data.remark,
        sell_price: form.data.sell_price,
    }

    const response = await useModel('ProductClientApprove').actions.getOfferPreview(data)
    previewUrl.value = response.url
}, {
    resetOnSuccess: false,
})

const submit = form.useSubmit(async () => {
    //
    await useModel('ProductClientApprove').actions.sendOffer(form.data)
    toastSuccess('Email was sent')
    //
    emit('close')
})

function changeSelectCard(card_pk: PrimaryKey, value: boolean) {
    if (value) {
        form.data.card_pks.push(card_pk)
    } else {
        form.data.card_pks = form.data.card_pks.filter(pk => pk !== card_pk)
    }
}
</script>
