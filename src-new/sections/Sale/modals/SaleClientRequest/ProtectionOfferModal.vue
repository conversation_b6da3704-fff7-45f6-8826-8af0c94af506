<template>
    <AppModalWrapper
        header="Send TP/BG Payment"
        close-button
        class="!max-w-[1350px]"
    >
        <div class="card card__body--partholder flex flex-row gap-1.5">
            <SuspenseManual :state="suspense">
                <template #default>
                    <div class="flex flex-col gap-1.5">
                        <div class="card card__body flex flex-col gap-3.5">
                            <div class="flex gap-4">
                                <FormField
                                    :form="form"
                                    class="w-[150px]"
                                    field="plan1"
                                >
                                    <label class="cursor-pointer select-none items-center flex gap-2">
                                        <InputRadio
                                            v-model="form.data.selected_by_default_plan"
                                            name="selected_by_default_plan"
                                            :value="'plan1'"
                                        />
                                        <span>
                                            Basic price
                                            <span class="text-danger text-xs">*</span>
                                        </span>
                                    </label>
                                    <InputNumber
                                        v-model="form.data.plan1"
                                        class="mt-2"
                                        icon="money"
                                    />
                                </FormField>

                                <FormField
                                    class="w-[150px]"
                                    :form="form"
                                    field="plan2"
                                >
                                    <label class="cursor-pointer select-none items-center flex gap-2">
                                        <InputRadio
                                            v-model="form.data.selected_by_default_plan"
                                            name="selected_by_default_plan"
                                            :value="'plan2'"
                                        />
                                        <span>
                                            Advantage price
                                            <span class="text-danger text-xs">*</span>
                                        </span>
                                    </label>
                                    <InputNumber
                                        v-model="form.data.plan2"
                                        class="mt-2"
                                        icon="money"
                                    />
                                </FormField>

                                <FormField
                                    class="w-[150px]"
                                    :form="form"
                                    field="plan3"
                                >
                                    <label class="cursor-pointer select-none items-center flex gap-2">
                                        <InputRadio
                                            v-model="form.data.selected_by_default_plan"
                                            name="selected_by_default_plan"
                                            :value="'plan3'"
                                        />
                                        <span>
                                            All include price
                                            <span class="text-danger text-xs">*</span>
                                        </span>
                                    </label>
                                    <InputNumber
                                        v-model="form.data.plan3"
                                        class="mt-2"
                                        icon="money"
                                    />
                                </FormField>

                                <FormField
                                    class="w-[150px]"
                                    :form="form"
                                    field="baggage_protection_price"
                                >
                                    <label>
                                        <span>
                                            Baggage price
                                            <span class="text-danger text-xs">*</span>
                                        </span>
                                    </label>

                                    <InputNumber
                                        v-model="form.data.baggage_protection_price"
                                        class="mt-2"
                                        icon="money"
                                    />
                                </FormField>
                            </div>

                            <FormField
                                :form="form"
                                field="email"
                                label="Email"
                                class="w-full"
                                required
                            >
                                <InputText v-model="form.data.email" />
                            </FormField>

                            <FormField
                                :form="form"
                                field="subject"
                                label="Email subject"
                                class="col-span-2"
                                required
                            >
                                <InputText v-model="form.data.subject" placeholder="Subject" />
                            </FormField>

                            <FormField
                                :form="form"
                                field="message"
                                label="Message to client"
                                class="col-span-2"
                                required
                            >
                                <InputTextarea
                                    v-model="form.data.message"
                                    maxlength="512"
                                    placeholder="Message to client"
                                />
                            </FormField>
                        </div>
                        <div class="card card__body flex flex-col gap-3 h-full">
                            <div>Select a credit card if needed</div>

                            <label
                                v-for="(saleVersionCard, index) in saleVersionCards"
                                :key="index"
                                :class="{
                                    'payment-card-selected': form.data.card_pks.includes(usePk(saleVersionCard)),
                                }"
                                class="payment-card flex flex-row gap-4 p-3 items-center justify-items-center"
                            >
                                <InputCheckbox
                                    :model-value="form.data.card_pks.includes(usePk(saleVersionCard))"
                                    @update:model-value="changeSelectCard(usePk(saleVersionCard), $event)"
                                />

                                <span
                                    v-if="!!getCardType(saleVersionCard.credit_card_type)"
                                    class="payment-card-info"
                                >
                                    <span class="payment-card-title">
                                        <img
                                            :src="getCreditCardImage(saleVersionCard.credit_card_type)"
                                            alt="Master Card"
                                            class="payment-card-type h-3"
                                            height="12"
                                        >
                                        {{ getCardType(saleVersionCard.credit_card_type) }} **** {{ saleVersionCard.strip?.slice(-4) }}
                                    </span>
                                    <span class="payment-card-expire">
                                        Expiry {{ getExpiration(saleVersionCard.expiration) }}
                                    </span>
                                </span>
                                <span
                                    v-for="item in getCardVerificationStatusLabels(saleVersionCard)"
                                    :key="item.key"
                                    v-tooltip="{content: item.tooltip}"
                                    class="payment-card-checks-item"
                                    :class="item.style"
                                >
                                    {{ item.label }}
                                </span>

                            </label>
                        </div>
                    </div>
                    <div class="card card__body flex-grow h-auto">
                        <iframe
                            :src="previewUrl"
                            class="w-full h-full min-h-[500px]"
                        />
                    </div>
                </template>
                <template #fallback>
                    <PlaceholderBlock class="w-full h-[400px]" />
                </template>
            </SuspenseManual>
        </div>
        <template #footer>
            <div class="flex justify-between">
                <AppButton
                    type="button"
                    :disabled="!form.hasChanges"
                    @click="form.reset()"
                >
                    Discard Changes
                </AppButton>
                <div class="flex gap-2">
                    <AppButton @click="getOfferPreview">
                        Preview changes
                    </AppButton>
                    <AppButton @click="submit">
                        Send
                    </AppButton>
                </div>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import FormField from '~/components/Form/FormField.vue'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import { getCardVerificationStatusLabels, getCreditCardImage, getExpiration } from '~/lib/Helper/CreditCardHelper'
import type { CreditCardType } from '~/api/models/Sale/SaleVersionCard'

defineOptions({
    name: 'ProtectionOfferModal',
})

const props = defineProps<{
    saleVersionPk: PrimaryKey
}>()

const emit = defineEmits<{
    'close': []
}>()

//

const { useModel, useDictionary } = await useNewContext('SaleVersion', props.saleVersionPk)

//

const creditCardTypeDictionary = useDictionary('CreditCardType')
const saleClientRequestModel = useModel('SaleClientRequest')
const saleVersionModel = useModel('SaleVersion')
const iataModel = useModel('Iata')

const saleVersionRecord = saleVersionModel.useRecord({
    with: ['cards', 'clientEmail', 'sale.client'],
})

const saleVersionCards = computed(() => {
    return saleVersionRecord.record.value.cards ?? []
})

const suspense = useSuspensableComponent(async () => {
    await saleVersionRecord.fetch(props.saleVersionPk)
    await populateForm()
    await getOfferPreview()
})

async function populateForm() {
    const {
        plan1,
        plan2,
        plan3,
        baggage_protection_price,
    } = await saleClientRequestModel.actions.getProtectionFormInfo({
        sale_version_pk: props.saleVersionPk,
    })

    const iataRecord = await iataModel.useRecord({
        where: (and) => {
            and.eq('code', saleVersionRecord.record.value.to_iata_code)
        },
    }).fetch()

    form.updateInitialData({
        selected_by_default_plan: 'plan1',
        plan1,
        plan2,
        plan3,
        baggage_protection_price,
        email: saleVersionRecord.record.value.sale.client.email ?? '',
        subject: `One-Click Cover for Your ${iataRecord.value?.city_name} Flight and Baggage`,
        message: 'Add Ticket Protection or Baggage Protection to your flight and travel with peace of mind.',
        card_pks: [],
    })
}

const previewUrl = ref('')

const form = useForm<{
    selected_by_default_plan: string,
    plan1: number,
    plan2: number,
    plan3: number,
    baggage_protection_price: number,
    email: string,
    subject: string,
    message: string,
    card_pks: PrimaryKey[],
}>({
    selected_by_default_plan: 'plan1',
    plan1: 0,
    plan2: 0,
    plan3: 0,
    baggage_protection_price: 0,
    email: '',
    subject: '',
    message: '',
    card_pks: [],
}, {
    plan1: [ValidationRules.Required('Price is required'), ValidationRules.MinNumber(0)],
    plan2: [ValidationRules.Required('Price is required'), ValidationRules.MinNumber(0)],
    plan3: [ValidationRules.Required('Price is required'), ValidationRules.MinNumber(0)],
    baggage_protection_price: ValidationRules.Required(),
    email: [ValidationRules.Required(), ValidationRules.Email()],
    subject: ValidationRules.Required(),
    message: [ValidationRules.Required(), ValidationRules.MaxLength(512, 'Message must be less than 512 characters')],
})

const submit = form.useSubmit(async (data) => {
    await saleClientRequestModel.actions.sendProtectionOffer({
        selected_by_default_plan: data.selected_by_default_plan,
        sale_version_pk: props.saleVersionPk,
        message: data.message,
        subject: data.subject,
        email: data.email,
        plan1: data.plan1,
        plan2: data.plan2,
        plan3: data.plan3,
        baggage_protection_price: data.baggage_protection_price,
        card_pks: data.card_pks,
    })

    emit('close')
}, {
    resetOnSuccess: false,
})

const getOfferPreview = form.useSubmit(async (data) => {
    const { url } = await saleClientRequestModel.actions.getProtectionOfferPreview({
        selected_by_default_plan: data.selected_by_default_plan,
        sale_version_pk: props.saleVersionPk,
        subject: data.subject,
        message: data.message,
        plan1: data.plan1,
        plan2: data.plan2,
        plan3: data.plan3,
        baggage_protection_price: data.baggage_protection_price,
    })

    previewUrl.value = url
}, {
    resetOnSuccess: false,
})

function changeSelectCard(card_pk: PrimaryKey, value: boolean) {
    if (value) {
        form.data.card_pks.push(card_pk)
    } else {
        form.data.card_pks = form.data.card_pks.filter(pk => pk !== card_pk)
    }
}

const getCardType = (creditCardType: CreditCardType): string => {
    return creditCardTypeDictionary.find(creditCardType).title
}
</script>
