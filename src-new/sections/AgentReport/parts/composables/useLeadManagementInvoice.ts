import type { AdditionalFormItem, InvoiceRef, SaleFormItem } from '~/sections/AgentReport/parts/helpers/agentInvoice'
import {
    calculateLeadManagementInvoiceTotalToPay,
    calculateSaleInvoiceTotal,
    calculateTpCount,
    countTotalLeadManagementSaleTpToPay,
} from '~/sections/AgentReport/parts/helpers/agentInvoiceCalculations'
import type { ModelAttributes, ModelRef } from '~types/lib/Model'
import type { ComputedRef } from 'vue'

export const useLeadManagementInvoice = (invoiceComputedRef: ComputedRef<InvoiceRef>) => {
    const { resourceContextOptions } = useContext()

    const target = useRef(resourceContextOptions, ['agent', 'sales', 'report', 'additional', 'department', 'ticketing']).modelRef('AgentReportInvoice', invoiceComputedRef.value.pk) as InvoiceRef

    const invoiceRecord = computed(() => target)

    const salesForm = useForm({
        assignments: [] as SaleFormItem[],
    })

    const additionalForm = useForm({
        assignments: [] as AdditionalFormItem[],
    })

    const salaryForm = useForm({
        base_salary: 0,
    })

    const bonusForm = useForm({
        bonus: 0,
    })

    const isSaleRowChanged = (assignment: SaleFormItem) => {
        const compareFields = ['client_status_pk', 'new', 'return_referral', 'non_company', 'tips', 'sale_tp', 'other_tp', 'additional_net', 'additional_gp', 'is_sale_tp_count', 'remark', 'special_services_fee']

        const data = invoiceRecord.value.sales.find(item => item.id === assignment.id)

        if (!data) {
            return false
        }

        return compareFields.some(field => {
            if (field === 'remark') {
                return (isEmpty(data[field]) ? '' : data[field]) !== (isEmpty(assignment[field]) ? '' : assignment[field])
            }

            // @ts-ignore
            return data[field] !== assignment[field]
        })
    }

    const isAdditionalRowChanged = (assignment: AdditionalFormItem) => {
        const compareFields = ['title', 'additional_net', 'additional_gp', 'remark']
        const data = invoiceRecord.value.additional.find(item => item.id === assignment.id)

        if (!data) {
            return false
        }

        return compareFields.some(field => {
            if (field === 'remark') {
                return (isEmpty(data[field]) ? '' : data[field]) !== (isEmpty(assignment[field]) ? '' : assignment[field])
            }

            // @ts-ignore
            return data[field] !== assignment[field]
        })
    }

    const hasAnyChanges = computed(() => {
        const dataToSave = salesForm.data.assignments.filter(assignment => {
            return isSaleRowChanged(assignment)
        })

        const dataAdditionalToSave = additionalForm.data.assignments.filter(assignment => {
            return isAdditionalRowChanged(assignment)
        })

        return dataToSave.length > 0 || dataAdditionalToSave.length > 0 || salaryForm.data.base_salary !== invoiceRecord.value.base_salary || bonusForm.data.bonus !== invoiceRecord.value.bonus
    })

    const tp_count = computed(() => {
        return calculateTpCount(salesForm.data.assignments)
    })

    const seedSalesForm = (sales: ModelRef<'AgentReportSale'>[]) => {
        salesForm.updateInitialData({
            assignments: sales.map((assignment) => {
                return {
                    id: assignment.id,
                    sale_pk: assignment.sale_pk,
                    client_status_pk: assignment.client_status_pk,
                    new: assignment.new,
                    return_referral: assignment.return_referral,
                    non_company: assignment.non_company,
                    tips: assignment.tips,
                    sale_tp: assignment.sale_tp,
                    other_tp: assignment.other_tp,
                    additional_gp: assignment.additional_gp,
                    additional_net: assignment.additional_net,
                    special_services_fee: assignment.special_services_fee,
                    airline_reimbursement_fee: assignment.airline_reimbursement_fee,
                    remark: assignment?.remark ? assignment.remark : '',
                    sale_type: assignment.sale_type,
                    is_sale_tp_count: assignment.is_sale_tp_count,
                    is_split: assignment.is_split,
                    sale_at: assignment.sale_at,
                    non_sale_gp: assignment.non_sale_gp,
                }
            }),
        })
    }

    const seedAdditionalForm = (additional: ModelAttributes<'AgentReportAdditional'>[]) => {
        additionalForm.updateInitialData({
            assignments: additional.map((assignment) => {
                return {
                    id: assignment.id,
                    title: assignment.title,
                    additional_gp: assignment.additional_gp,
                    additional_net: assignment.additional_net,
                    remark: assignment.remark || '',
                    type: assignment.type,
                }
            }),
        })
    }

    const seedSalaryForm = (invoice: ModelAttributes<'AgentReportInvoice'>) => {
        salaryForm.data.base_salary = invoice.base_salary
    }

    const seedBonusForm = (invoice: ModelAttributes<'AgentReportInvoice'>) => {
        bonusForm.data.bonus = invoice.bonus
    }

    const total = computed(() => {
        return calculateSaleInvoiceTotal(salesForm.data.assignments, additionalForm.data.assignments, salaryForm.data.base_salary, bonusForm.data.bonus)
    })

    const totalToPay = computed(() => {
        return calculateLeadManagementInvoiceTotalToPay(total.value, countTotalLeadManagementSaleTpToPay(salesForm.data.assignments))
    })

    watch(() => invoiceRecord.value.sales, () => {
        seedSalesForm(invoiceRecord.value.sales)
    }, { immediate: true, deep: true })

    watch(() => invoiceRecord.value.additional, () => {
        seedAdditionalForm(invoiceRecord.value.additional)
    }, { immediate: true, deep: true })

    watch(() => invoiceRecord.value.base_salary, () => {
        seedSalaryForm(invoiceRecord.value)
    }, { immediate: true })

    watch(() => invoiceRecord.value.bonus, () => {
        seedBonusForm(invoiceRecord.value)
    }, { immediate: true })

    return {
        invoiceRecord,

        salesForm,
        additionalForm,
        salaryForm,
        bonusForm,

        hasAnyChanges,
        tp_count,
        total,
        totalToPay,
    }
}
