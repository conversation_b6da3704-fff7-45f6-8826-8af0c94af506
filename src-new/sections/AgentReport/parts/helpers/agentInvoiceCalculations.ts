import type {
    AdditionalFormItem,
    InternalFormItem,
    SaleFormItem,
    TicketingFormItem,
    TotalResult,
} from '~/sections/AgentReport/parts/helpers/agentInvoice'
import { SaleType } from '~/api/models/Sale/Sale'
import { AgentReportAdditionalType } from '~/api/models/AgentReport/AgentReportAdditional'

export const ensureIsNumber = (value: any) => {
    if (typeof value !== 'number') {
        return 0
    }

    return value
}

export const calculateTpCount = (assignments: SaleFormItem[]) => {
    let count = 0
    for (const assignment of assignments) {
        if (assignment.is_sale_tp_count) {
            count += 1
        }
    }

    return count
}

export const countTotalElement = (array: SaleFormItem[] | AdditionalFormItem[] | TicketingFormItem[] | InternalFormItem[], key: string, abs: boolean = false): number => {
    let count = 0
    for (const item of array) {
        if (item.hasOwnProperty(key)) {
            const value = ensureIsNumber(item[key as keyof typeof item])
            count += abs ? Math.abs(value) : value
        }
    }

    return count
}

export const countTotalSaleTpToPay = (assignments: SaleFormItem[]) => {
    const tp_count = calculateTpCount(assignments)

    const includedAssignments = assignments.filter(assignment => assignment.is_sale_tp_count)

    const non_sale_count_without_tp = assignments.filter(assignment => assignment.sale_type !== SaleType.Sale && !assignment.is_sale_tp_count)
    const tp_conversion = tp_count / (assignments.length - non_sale_count_without_tp.length) * 100

    let commission = 15

    if (tp_conversion < 20) {
        commission = 10
    } else if (tp_conversion >= 30 && tp_count > 5) {
        commission = 25
    }

    return (countTotalElement(includedAssignments, 'sale_tp') / 100) * commission
}

export const countTotalCSSaleTpToPay = (assignments: SaleFormItem[]) => {
    const tp_count = calculateTpCount(assignments)

    const includedAssignments = assignments.filter(assignment => assignment.is_sale_tp_count)

    const tp_conversion = tp_count / assignments.length * 100

    let commission = 15

    if (tp_conversion < 20) {
        commission = 10
    } else if (tp_conversion >= 30 && tp_count > 5) {
        commission = 25
    }

    return (countTotalElement(includedAssignments, 'sale_tp') / 100) * commission
}

export const countTotalLeadManagementSaleTpToPay = (assignments: SaleFormItem[]) => {
    return countTotalCSSaleTpToPay(assignments)
}

export const saleAgentInvoiceLesenka = (value: number, level = 1, is_beginner = true): number => {
    if (level >= 5) {
        const lastLevel = is_beginner ? 28 : 33

        return (value / 100) * lastLevel
    }

    const ost = Math.max(value - 7500, 0)

    value = value - ost

    const psExperiencedOptions = {
        '1': 15,
        '2': 18,
        '3': 21,
        '4': 30,
    }

    const psBeginnerOptions = {
        '1': 10,
        '2': 13,
        '3': 16,
        '4': 25,
    }

    const psOptions = is_beginner ? psBeginnerOptions : psExperiencedOptions
    const ps = psOptions[String(level) as keyof typeof psOptions] === undefined ? 33 : psOptions[String(level) as keyof typeof psOptions]

    return (value / 100) * ps + (ost > 0 ? saleAgentInvoiceLesenka(ost, level + 1, is_beginner) : 0)
}

type BonusLesenkaParams = {
    min: {
        value: number,
        percent: number, // 0.05
    },
    middle: {
        value: number,
        percent: number, // 0.075
    },
    max: {
        percent: number, // 0.1
    }
}
export const calculateBonusLesenka = (value: number, params: BonusLesenkaParams) => {
    if (value < 0) {
        return 0
    }

    if (value <= params.min.value) {
        return value * params.min.percent
    } else if (value <= params.middle.value) {
        return params.min.value * params.min.percent + (value - params.min.value) * params.middle.percent
    } else {
        return params.min.value * params.min.percent + (params.middle.value - params.min.value) * params.middle.percent + (value - params.middle.value) * params.max.percent
    }
}

export const calculateSaleInvoiceTotal = (assignments: SaleFormItem[], additionalAssignments: AdditionalFormItem[], base_salary = 0, bonus = 0): TotalResult => {
    return {
        new: countTotalElement(assignments, 'new') +
            countTotalElement(assignments, 'additional_gp') +
            countTotalElement(additionalAssignments, 'additional_gp'),
        return_referral: countTotalElement(assignments, 'return_referral'),
        non_sale_gp: countTotalElement(assignments, 'non_sale_gp'),
        non_company: countTotalElement(assignments, 'non_company'),
        tips: countTotalElement(assignments, 'tips'),
        sale_tp: countTotalElement(assignments, 'sale_tp'),
        other_tp: countTotalElement(assignments, 'other_tp'),
        additional_gp: countTotalElement(assignments, 'additional_gp') + countTotalElement(additionalAssignments, 'additional_gp'),
        additional_net: countTotalElement(assignments, 'additional_net') + countTotalElement(additionalAssignments, 'additional_net'),
        special_services_fee: 0,
        airline_reimbursement_fee: 0,
        base_salary,
        bonus,
    }
}

export const calculateRevTicketingTotal = (
    price_drops: TicketingFormItem[],
    alt_extras: TicketingFormItem[],
    additionalAssignments: AdditionalFormItem[],
    base_salary = 0,
    bonus = 0,
): TotalResult => {
    return {
        new: countTotalElement(price_drops, 'amount') +
            countTotalElement(alt_extras, 'amount') +
            countTotalElement(additionalAssignments, 'additional_gp'),
        return_referral: 0,
        non_company: 0,
        tips: 0,
        sale_tp: 0,
        other_tp: 0,
        additional_gp: 0,
        special_services_fee: 0,
        airline_reimbursement_fee: 0,
        additional_net: countTotalElement(additionalAssignments, 'additional_net'),
        non_sale_gp: 0,
        base_salary,
        bonus,
    }
}

export const calculateSpecialServicesTotal = (assignments: SaleFormItem[], additionalAssignments: AdditionalFormItem[], base_salary = 0, bonus = 0): TotalResult => {
    return {
        new: countTotalElement(assignments, 'new') +
            countTotalElement(assignments, 'additional_gp') +
            countTotalElement(additionalAssignments, 'additional_gp'),
        return_referral: countTotalElement(assignments, 'return_referral'),
        non_sale_gp: countTotalElement(assignments, 'non_sale_gp'),
        non_company: countTotalElement(assignments, 'non_company'),
        tips: countTotalElement(assignments, 'tips'),
        sale_tp: countTotalElement(assignments, 'sale_tp'),
        other_tp: countTotalElement(assignments, 'other_tp'),
        special_services_fee: countTotalElement(assignments, 'special_services_fee', true),
        airline_reimbursement_fee: 0,
        additional_gp: countTotalElement(assignments, 'additional_gp') + countTotalElement(additionalAssignments, 'additional_gp'),
        additional_net: countTotalElement(assignments, 'additional_net') + countTotalElement(additionalAssignments, 'additional_net'),
        base_salary,
        bonus,
    }
}

export const calculateAwardTicketingTotal = (
    additionalAssignments: AdditionalFormItem[],
    base_salary = 0,
    bonus = 0,
): TotalResult => {
    return {
        new: countTotalElement(additionalAssignments, 'additional_gp'),
        return_referral: 0,
        non_company: 0,
        non_sale_gp: 0,
        tips: 0,
        sale_tp: 0,
        other_tp: 0,
        additional_gp: 0,
        additional_net: countTotalElement(additionalAssignments, 'additional_net'),
        base_salary,
        special_services_fee: 0,
        airline_reimbursement_fee: 0,
        bonus,
    }
}

export const calculateExecutiveTotal = (
    additionalAssignments: AdditionalFormItem[],
    base_salary = 0,
    bonus = 0,
): TotalResult => {
    return {
        new: countTotalElement(additionalAssignments, 'additional_gp'),
        return_referral: 0,
        non_company: 0,
        non_sale_gp: 0,
        tips: 0,
        sale_tp: 0,
        other_tp: 0,
        additional_gp: 0,
        additional_net: countTotalElement(additionalAssignments, 'additional_net'),
        special_services_fee: 0,
        airline_reimbursement_fee: 0,
        base_salary,
        bonus,
    }
}

export const calculateVerificationTotal = (assignments: SaleFormItem[], additionalAssignments: AdditionalFormItem[], internalProfitAssignments: InternalFormItem[], base_salary = 0, bonus = 0): TotalResult => {
    return {
        new: countTotalElement(assignments, 'new') +
            countTotalElement(assignments, 'additional_gp') +
            countTotalElement(additionalAssignments, 'additional_gp'),
        return_referral: countTotalElement(assignments, 'return_referral'),
        non_sale_gp: countTotalElement(assignments, 'non_sale_gp'),
        non_company: countTotalElement(assignments, 'non_company'),
        tips: countTotalElement(assignments, 'tips'),
        sale_tp: countTotalElement(assignments, 'sale_tp'),
        other_tp: countTotalElement(assignments, 'other_tp'),
        additional_gp: countTotalElement(assignments, 'additional_gp') + countTotalElement(additionalAssignments, 'additional_gp') + countTotalElement(internalProfitAssignments, 'amount'),
        additional_net: countTotalElement(assignments, 'additional_net') + countTotalElement(additionalAssignments, 'additional_net'),
        special_services_fee: 0,
        airline_reimbursement_fee: countTotalElement(assignments, 'airline_reimbursement_fee'),
        base_salary,
        bonus,
    }
}

export const calculateTotalGross = (total: TotalResult): number => {
    const excludeTotalFields = ['total_gross', 'total_net', 'additional_gp', 'base_salary', 'bonus']
    let count = 0

    Object.keys(total).forEach(key => {
        if (total.hasOwnProperty(key) && !excludeTotalFields.includes(key)) {
            // @ts-ignore
            count += total[key as keyof typeof total]
        }
    })

    return count
}

export const calculateNonSaleGp = (assignments: SaleFormItem[], is_beginner = true) => {
    let result = 0

    for (const assignment of assignments) {
        if ([SaleType.Exchange, SaleType.InvoiceExchange].includes(assignment.sale_type)) {
            result += assignment.non_sale_gp * 0.15
        } else if ([SaleType.Refund, SaleType.InvoiceRefund].includes(assignment.sale_type)) {
            result += assignment.non_sale_gp * (is_beginner ? 0.10 : 0.15)
        } else {
            result += 0
        }
    }

    return result
}

export const calculateSaleInvoiceTotalToPay = (total: TotalResult, sale_tp: number, is_beginner = true, assignments: SaleFormItem[]) => {
    const result = {
        new: saleAgentInvoiceLesenka(total.new, 1, is_beginner),
        non_sale_gp: calculateNonSaleGp(assignments, is_beginner),
        return_referral: total.return_referral * 0.25,
        non_company: total.non_company * 0.4,
        tips: total.tips * 0.5,
        sale_tp: sale_tp, // calculated in countTotalSaleTpToPay
        additional_gp: total.additional_gp,
        additional_net: total.additional_net,
        special_services_fee: total.special_services_fee,
        airline_reimbursement_fee: 0,
        other_tp: total.other_tp * 0.15,
        base_salary: total.base_salary,
        bonus: total.bonus,
        total_gross: 0,
        total_net: 0,
    }

    result.total_gross = calculateTotalGross(result) + total.bonus

    result.total_net = (result.total_gross) * 0.93

    return result
}

export const calculateCSInvoiceTotalToPay = (total: TotalResult, sale_tp: number) => {
    const bonus = calculateBonusLesenka(
        total.new,
        {
            min: {
                value: 3000,
                percent: 0.05,
            },
            middle: {
                value: 6000,
                percent: 0.075,
            },
            max: {
                percent: 0.1,
            },
        },
    )

    const result = {
        new: bonus,
        non_sale_gp: 0,
        return_referral: 0,
        non_company: 0,
        tips: total.tips * 0.5,
        sale_tp: sale_tp,
        additional_gp: total.additional_gp,
        additional_net: total.additional_net,
        special_services_fee: total.special_services_fee,
        airline_reimbursement_fee: 0,
        other_tp: 0,
        total_gross: 0,
        total_net: 0,
        base_salary: total.base_salary,
        bonus: total.bonus,
    }

    result.total_gross = calculateTotalGross(result) + total.base_salary + total.bonus

    result.total_net = (result.total_gross) * 0.93

    return result as TotalResult
}

export const calculateLeadManagementInvoiceTotalToPay = (total: TotalResult, sale_tp: number) => {
    return calculateCSInvoiceTotalToPay(total, sale_tp)
}

export const calculateSpecialServicesInvoiceTotalToPay = (total: TotalResult, sale_tp: number) => {
    const result = {
        new: total.new * 0.05,
        non_sale_gp: 0,
        return_referral: 0,
        non_company: 0,
        tips: total.tips * 0.5,
        sale_tp: sale_tp,
        additional_gp: total.additional_gp,
        additional_net: total.additional_net,
        special_services_fee: total.special_services_fee * 0.05,
        airline_reimbursement_fee: 0,
        other_tp: 0,
        total_gross: 0,
        total_net: 0,
        base_salary: total.base_salary,
        bonus: total.bonus,
    }

    result.total_gross = calculateTotalGross(result) + total.base_salary + total.bonus

    result.total_net = (result.total_gross) * 0.93

    return result as TotalResult
}

export const calculateRevTicketingTotalToPay = (
    price_drops: TicketingFormItem[],
    alt_extras: TicketingFormItem[],
    additionalAssignments: AdditionalFormItem[],
    base_salary = 0,
    bonus = 0,
) => {
    const priceDrop = calculateBonusLesenka(
        countTotalElement(price_drops, 'amount'),
        {
            min: {
                value: 1000,
                percent: 0.05,
            },
            middle: {
                value: 2000,
                percent: 0.075,
            },
            max: {
                percent: 0.1,
            },
        },
    )

    const altExtra = calculateBonusLesenka(
        countTotalElement(alt_extras, 'amount'),
        {
            min: {
                value: 500,
                percent: 0.016,
            },
            middle: {
                value: 1000,
                percent: 0.024,
            },
            max: {
                percent: 0.036,
            },
        },
    )

    const result = {
        new: priceDrop + altExtra + countTotalElement(additionalAssignments, 'additional_gp'),
        non_sale_gp: 0,
        return_referral: 0,
        non_company: 0,
        tips: 0,
        sale_tp: 0,
        other_tp: 0,
        additional_gp: 0,
        additional_net: countTotalElement(additionalAssignments, 'additional_net'),
        total_gross: 0,
        special_services_fee: 0,
        airline_reimbursement_fee: 0,
        total_net: 0,
        base_salary,
        bonus,
    }

    result.total_gross = calculateTotalGross(result) + base_salary + bonus

    result.total_net = (result.total_gross) * 0.93

    return result as TotalResult
}

export const calculateAwardTicketingTotalToPay = (
    additionalAssignments: AdditionalFormItem[],
    base_salary = 0,
    bonus = 0,
) => {
    const result = {
        new: countTotalElement(additionalAssignments, 'additional_gp'),
        non_sale_gp: 0,
        return_referral: 0,
        non_company: 0,
        tips: 0,
        sale_tp: 0,
        other_tp: 0,
        additional_gp: 0,
        additional_net: countTotalElement(additionalAssignments, 'additional_net'),
        special_services_fee: 0,
        airline_reimbursement_fee: 0,
        total_gross: 0,
        total_net: 0,
        base_salary,
        bonus,
    }

    result.total_gross = calculateTotalGross(result) + base_salary + bonus

    result.total_net = (result.total_gross) * 0.93

    return result as TotalResult
}

export const calculateVerificationTotalToPay = (
    total: TotalResult,
    sale_tp: number,
    internalAssignments: InternalFormItem[],
) => {
    const result = {
        new: total.new * 0.05,
        non_sale_gp: 0,
        return_referral: 0,
        non_company: 0,
        tips: total.tips * 0.5,
        sale_tp: sale_tp,
        additional_gp: total.additional_gp,
        additional_net: total.additional_net,
        other_tp: 0,
        total_gross: 0,
        total_net: 0,
        base_salary: total.base_salary,
        special_services_fee: 0,
        airline_reimbursement_fee: total.airline_reimbursement_fee * 0.05,
        bonus: total.bonus,
    }

    result.total_gross = calculateTotalGross(result) + total.base_salary + total.bonus + countTotalElement(internalAssignments, 'amount')

    result.total_net = (result.total_gross) * 0.93

    return result as TotalResult
}

export const calculateExecutiveAdditionalToPay = (assignment: AdditionalFormItem): number => {
    if (!assignment.additional_gp) {
        return 0
    }

    if (assignment.type === AgentReportAdditionalType.SpecServDepGP) {
        return assignment.additional_gp * 0.02
    }

    if (assignment.type === AgentReportAdditionalType.CsDepGP) {
        return assignment.additional_gp * 0.02
    }

    if (assignment.type === AgentReportAdditionalType.SpecServFeeSales) {
        return assignment.additional_gp * 0.15
    }

    if (assignment.type === AgentReportAdditionalType.VerDepGP) {
        return assignment.additional_gp * 0.05
    }

    if (assignment.type === AgentReportAdditionalType.PriceDrop) {
        return assignment.additional_gp * 0.02
    }

    if (assignment.type === AgentReportAdditionalType.AltExtra) {
        return assignment.additional_gp * 0.02
    }

    if (assignment.type === AgentReportAdditionalType.Other) {
        return assignment.additional_gp
    }

    return 0
}
export const calculateExecutiveTotalToPay = (
    additionalAssignments: AdditionalFormItem[],
    base_salary = 0,
    bonus = 0,
) => {
    const result = {
        new: 0,
        non_sale_gp: 0,
        return_referral: 0,
        non_company: 0,
        tips: 0,
        sale_tp: 0,
        other_tp: 0,
        additional_gp: 0,
        additional_net: countTotalElement(additionalAssignments, 'additional_net'),
        total_gross: 0,
        total_net: 0,
        special_services_fee: 0,
        airline_reimbursement_fee: 0,
        base_salary,
        bonus,
    }

    for (const assignment of additionalAssignments) {
        result.new += calculateExecutiveAdditionalToPay(assignment)
    }

    result.total_gross = calculateTotalGross(result) + base_salary + bonus

    result.total_net = (result.total_gross) * 0.93

    return result as TotalResult
}
