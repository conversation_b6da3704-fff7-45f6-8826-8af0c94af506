<template>
    <tr>
        <td colspan="8" class="p-0">
            <AppTable
                class="mb-1"
                :columns="columns"
                zebra
            >
                <template #body>
                    <PreviewInvoiceAgentSale
                        :invoice="invoice"
                        :department="DepartmentName.CustomerSupport"
                        :show-columns="showColumns"
                    />
                    <PreviewInvoiceAgentAdditional
                        :invoice="invoice"
                        :department="DepartmentName.CustomerSupport"
                        :show-columns="showColumns"
                    />
                    <PreviewInvoiceAgentTotal
                        :invoice="invoice"
                        :department="DepartmentName.CustomerSupport"
                        :show-columns="showColumns"
                        show-salary
                        show-bonus
                    />
                </template>
            </AppTable>
        </td>
    </tr>
</template>

<script setup lang="ts">
import { useNewContext } from '~/composables/useNewContext'
import type { InvoiceRef } from '~/sections/AgentReport/parts/helpers/agentInvoice'
import PreviewInvoiceAgentSale
    from '~/sections/AgentReport/parts/invoices/components/preview/PreviewInvoiceAgentSale.vue'
import { DepartmentName } from '~/api/models/Department/Department'
import PreviewInvoiceAgentAdditional
    from '~/sections/AgentReport/parts/invoices/components/preview/PreviewInvoiceAgentAdditional.vue'
import PreviewInvoiceAgentTotal
    from '~/sections/AgentReport/parts/invoices/components/preview/PreviewInvoiceAgentTotal.vue'
import { showCSColumns } from '~/sections/AgentReport/parts/helpers/agentInvoiceColumns'

defineOptions({
    name: 'LeadManagementDepartmentInvoicePreview',
})

const props = withDefaults(defineProps<{
    invoice: InvoiceRef
}>(), {})

const showColumns = showCSColumns

const { useModel } = await useNewContext('AgentReport', props.invoice.agent_report_pk)
const columns = useTableColumns({
    id: {
        label: '#',
        width: 1,
    },
    sale_pk: {
        label: 'Sale #',
        width: 10,
    },
    sale_date: {
        label: 'Sale date',
        width: 10,
    },
    client_status_pk: {
        label: 'Client Status',
        width: 10,
        enabled: () => showColumns.client_status_pk,
    },
    new: {
        label: 'GP',
        width: 10,
        enabled: () => showColumns.new,
    },
    tips: {
        label: 'Tips',
        width: 10,
        enabled: () => showColumns.tips,
    },
    sale_tp: {
        label: 'TP',
        width: 15,
        enabled: () => showColumns.sale_tp,
    },
    other_tp: {
        label: 'Exch/Ref TP',
        width: 10,
        enabled: () => showColumns.other_tp,
    },
    additional_gp: {
        label: 'Additional GP',
        width: 15,
        enabled: () => showColumns.additional_gp,
    },
    additional_net: {
        label: 'Additional Net',
        width: 15,
        enabled: () => showColumns.additional_net,
    },
    remark: {
        label: 'Remark',
        width: 40,
    },

})
</script>
