<template>
    <InvoiceAgentInfo
        :table-is-visible="tableIsVisible"
        :invoice="invoice"
        @change-table-is-visible="changeTableIsVisible"
    />

    <tr v-if="tableIsVisible">
        <td colspan="10" class="p-0">
            <AppTable
                class="mb-1"
                :columns="columns"
                zebra
            >
                <template #body>
                    <InvoiceAgentSale
                        :form="salesForm"
                        :can-edit="canEdit"
                        :invoice="invoice"
                        :department="DepartmentName.CustomerSupport"
                        :show-columns="showColumns"
                    />

                    <InvoiceAgentAdditional
                        :can-edit="canEdit"
                        :additional-form="additionalForm"
                        :invoice="invoice"
                        :show-columns="showColumns"
                        :department="DepartmentName.CustomerSupport"
                    />

                    <InvoiceAgentTotal
                        can-add-sale
                        :total="total"
                        :tp-count="tp_count"
                        :has-any-changes="hasAnyChanges"
                        :can-edit="canEdit"
                        :invoice="invoice"
                        :total-to-pay="totalToPay"
                        show-salary
                        :salary-form="salaryForm"
                        :department="DepartmentName.CustomerSupport"
                        :show-columns="showColumns"
                        show-bonus
                        :bonus-form="bonusForm"
                    />
                </template>
            </AppTable>
        </td>
    </tr>
</template>

<script setup lang="ts">
import InvoiceAgentInfo from '~/sections/AgentReport/parts/invoices/components/process/InvoiceAgentInfo.vue'
import InvoiceAgentTotal from '~/sections/AgentReport/parts/invoices/components/process/InvoiceAgentTotal.vue'
import InvoiceAgentAdditional from '~/sections/AgentReport/parts/invoices/components/process/InvoiceAgentAdditional.vue'
import { DepartmentName } from '~/api/models/Department/Department'
import InvoiceAgentSale from '~/sections/AgentReport/parts/invoices/components/process/InvoiceAgentSale.vue'
import type { InvoiceRef } from '~/sections/AgentReport/parts/helpers/agentInvoice'
import { showCSColumns } from '~/sections/AgentReport/parts/helpers/agentInvoiceColumns'
import { useLeadManagementInvoice } from '~/sections/AgentReport/parts/composables/useLeadManagementInvoice'

defineOptions({
    name: 'LeadManagementDepartmentInvoice',
})

const props = withDefaults(defineProps<{
    invoice: InvoiceRef
    canEdit?: boolean
    notCompleteForExecutiveCs: string[]
    notCompleteForExecutiveTicketing: string[]
}>(), {
    canEdit: false,
})

const {
    salesForm,
    additionalForm,
    salaryForm,
    bonusForm,
    hasAnyChanges,
    tp_count,
    total,
    totalToPay,
} = useLeadManagementInvoice(computed(() => props.invoice))

const tableIsVisible = ref(false)

const showColumns = showCSColumns

const changeTableIsVisible = (value: boolean) => {
    tableIsVisible.value = value
}

const columns = useTableColumns({
    id: {
        label: '#',
        width: 5,
    },
    sale_pk: {
        label: 'Sale #',
        width: 10,
    },
    sale_date: {
        label: 'Sale date',
        width: 20,
    },

    client_status_pk: {
        label: 'Client Status',
        width: 10,
        enabled: () => showColumns.client_status_pk,
    },

    new: {
        label: 'GP',
        width: 10,
        enabled: () => showColumns.new,
    },
    special_services_fee: {
        label: 'Spec. Serv. fee',
        width: 10,
        enabled: () => showColumns.special_services_fee,
    },

    tips: {
        label: 'Tips',
        width: 10,
        enabled: () => showColumns.tips,
    },
    sale_tp: {
        label: 'TP',
        width: 15,
        enabled: () => showColumns.sale_tp,
    },
    other_tp: {
        label: 'Exch/Ref TP',
        width: 10,
        enabled: () => showColumns.other_tp,
    },
    additional_gp: {
        label: 'Additional GP',
        width: 10,
        enabled: () => showColumns.additional_gp,
    },
    additional_net: {
        label: 'Additional Net',
        width: 10,
        enabled: () => showColumns.additional_net,
    },
    remark: {
        label: 'Remark',
        width: 40,
    },
    actions: {
        label: 'Actions',
        width: 6,
    },
})
</script>
