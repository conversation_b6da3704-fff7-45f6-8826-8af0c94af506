<template>
    <div
        v-tooltip="{content: tooltip}"
        class="flex items-baseline w-[40px] h-[20px]"
    >
        <CheckIconThin
            class="relative left-[6px] w-[14px] h-[14px] "
            :class="offer.is_mail_sent_at ? '!text-[#3B76F6]' : '!text-[#ADB5CC]'"
        />
        <CheckIconThin
            class="w-[14px] h-[14px]"
            :class="offer.is_mail_viewed_at ? '!text-[#3B76F6]' : '!text-[#ADB5CC]'"
        />
        <CheckIconThin
            class="relative left-[-6px] w-[14px] h-[14px]"
            :class="offer.is_mail_clicked_at ? '!text-[#3B76F6]' : '!text-[#ADB5CC]'"
        />
    </div>
</template>

<script setup lang="ts">
import CheckIconThin from '~assets/icons/CheckIconThin.svg?component'
import type { ModelAttributes } from '~types/lib/Model'

defineOptions({
    name: 'LeadOfferClientViewed',
})

const props = defineProps<{
    offer: ModelAttributes<'LeadOffer'>
}>()

const formatter = useService('formatter')
const tooltip = computed(() => {
    const result = []
    const sent = props.offer.is_mail_sent_at ? formatter.datetime(props.offer.is_mail_sent_at, undefined, { full: true }) : undefined
    const viewed = props.offer.is_mail_viewed_at ? formatter.datetime(props.offer.is_mail_viewed_at, undefined, { full: true }) : undefined
    const clicked = props.offer.is_mail_clicked_at ? formatter.datetime(props.offer.is_mail_clicked_at, undefined, { full: true }) : undefined
    const drop_off_point = props.offer.drop_off_point

    if (sent) {
        result.push(`Sent: ${sent}`)
    }

    if (viewed) {
        result.push(`Viewed: ${viewed}`)
    }

    if (clicked) {
        result.push(`Clicked: ${clicked}`)
    }

    if (drop_off_point) {
        result.push(`Drop-off point: ${drop_off_point}`)
    }

    return result.join(', ')
})
</script>

