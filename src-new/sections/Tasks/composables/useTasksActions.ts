import SaleDisclaimerModal from '~/modals/Sale/SaleDisclaimerModal.vue'
import type { ModelAttributes } from '~types/lib/Model'
import { EmailTemplateName } from '~/api/models/Email/EmailTemplate'
import FollowUpIntroConfirmModal from '~/sections/Lead/Info/modals/FollowUpIntroConfirmModal.vue'
import { toastWarning } from '@/lib/core/helper/ToastHelper'
import { useWaitForResourceEvent } from '~/composables/useWaitForResourceEvent'
import DisclaimerRequestModal from '~/sections/Organizer/modals/DisclaimerRequestModal.vue'
import ChangeSaleTypeModal from '~/sections/Sale/modals/ChangeSaleTypeModal.vue'

export const useTasksActions = () => {
    const { useModel, hasPermission } = useContext()

    const tasksActionsHandler = async (task: ModelAttributes<'Task'>) => {
        if (isBookkeepingTask(task)) {
            await handleBookkeepingTask(task)

            return
        }

        if (isDisclaimerTask(task)) {
            await handleDisclaimerTask(task)

            return
        }

        if (task.handler.includes('FollowUp')) {
            await handleFollowUpTask(task)

            return
        }

        if (task.handler.includes('SaleDisclaimer')) {
            await handleSaleDisclaimerTask(task)

            return
        }

        if (task.handler.includes('ChangeSaleType')) {
            await handleChangeSaleType(task)

            return
        }

        await handleTask(task)
    }

    //

    const changeSaleTypeModal = useModal(ChangeSaleTypeModal)
    const handleChangeSaleType = async (task: ModelAttributes<'Task'>) => {
        const sale = await useModel('Sale').useRecord().fetch(String(task.referer_pk))

        await changeSaleTypeModal.open({
            salePk: String(task.referer_pk),
            type: sale.value.type,
            taskPk: usePk(task),
        })
    }

    const followUpModal = useModal(FollowUpIntroConfirmModal)
    const handleFollowUpTask = async (task: ModelAttributes<'Task'>) => {
        const status = !task.completed_at

        if (!task.referer_pk) {
            toastWarning('Action is not possible')

            return
        }
        const lead_pk = String(task.referer_pk)

        const leadRecord = await useModel('Lead').useRecord({
            with: ['email'],
        }).fetch(lead_pk)
        const leadEmail = leadRecord.value.email.value

        if (status) {
            const emailTemplate = (() => {
                if (task.handler.includes('FollowUpIntro')) {
                    return EmailTemplateName.FollowUpIntro
                } else if (task.handler.includes('FollowUp1')) {
                    return EmailTemplateName.FollowUp1
                } else if (task.handler.includes('FollowUp2')) {
                    return EmailTemplateName.FollowUp2
                } else if (task.handler.includes('FollowUp3')) {
                    return EmailTemplateName.FollowUp3
                } else if (task.handler.includes('FollowUpTravelCash')) {
                    return EmailTemplateName.FollowUpTravelCash
                }
            })()

            await followUpModal.open({
                taskPk: usePk(task),
                emailTemplate,
                context: {
                    lead_pk: lead_pk,
                    email: leadEmail,
                },
                leadPk: lead_pk,
            })
        } else {
            toastWarning('Action is not possible')

            return
        }
    }

    //

    const disclaimerRequestModal = useModal(DisclaimerRequestModal)

    const handleDisclaimerTask = async (task: ModelAttributes<'Task'>) => {
        if (task.issue_pk) {
            await openIssue(task.issue_pk)

            return
        }

        if (hasPermission('manage', 'all')) {
            await handleTask(task)

            return
        }

        await disclaimerRequestModal.open({
            relatedModel: {
                model_name: 'Task',
                model_pk: usePk(task),
            },
        })
    }

    const handleBookkeepingTask = async (task: ModelAttributes<'Task'>) => {
        if (task.issue_pk) {
            await openIssue(task.issue_pk)

            return
        }

        if (hasPermission('manage', 'all')) {
            await handleTask(task)

            return
        }
    }

    const issueModal = useGlobalModal('IssueSideModal')

    async function openIssue(issuePk: PrimaryKey) {
        await issueModal.open(issuePk)
    }

    const saleDisclaimerModal = useModal(SaleDisclaimerModal)

    const handleSaleDisclaimerTask = async (task: ModelAttributes<'Task'>) => {
        await saleDisclaimerModal.open({
            taskPk: String(task.id),
        })
    }

    //

    const handleTask = async (task: ModelAttributes<'Task'>) => {
        await useWaitForResourceEvent(
            useModel('Task').actions.changeCompleted({
                pk: usePk(task),
                status: !task.completed_at,
            }),
            'Task',
            'update',
            usePk(task),
        )
    }

    return {
        tasksActionsHandler,
    }
}

//

export const disclaimerHandlers = [
    'DisclaimerSimpleTask',
    'DisclaimerAwardTask',
    'DisclaimerExtraLagTask', // It is "Lag" on backend
    'DisclaimerFakeReturnTask',
    'DisclaimerMixedBaggageTask',
    'DisclaimerMixedCabinTask',
    'DisclaimerUpgradeTask',
] as const

export const isDisclaimerTask = (task: ModelAttributes<'Task'>) => {
    return disclaimerHandlers.some((handler) => {
        return task.handler.includes(handler)
    })
}

export const bookkeepingHandlers = [
    'BookkeepingPriceDropTask',
    'BookkeepingAltExtraTask',
] as const

export const isBookkeepingTask = (task: ModelAttributes<'Task'>) => {
    return bookkeepingHandlers.some((handler) => {
        return task.handler.includes(handler)
    })
}
