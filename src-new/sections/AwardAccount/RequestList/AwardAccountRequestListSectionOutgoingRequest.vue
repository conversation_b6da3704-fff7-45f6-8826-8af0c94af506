<template>
    <tr class="border-b">
        <td class="text-right">
            <span v-if="request.is_refund" class="badge --pending --small --soft --outline">
                Refund
            </span>
            <span v-else class="badge --danger --small --soft --outline">
                Used
            </span>
        </td>
        <td>{{ request.id }}</td>
        <td />
        <td />
        <td
            :class="{
                'line-through': request.refunded_pk
            }"
        >
            {{ $format.money(request.miles_amount, { fraction: 0 }) }}
        </td>
        <td
            :class="{
                'line-through': request.refunded_pk
            }"
        >
            {{ $format.money(request.price || 0, { fraction: 2 }) }}
        </td>
        <td>{{ $format.money((request.price || 0) / request.miles_amount, { fraction: 5 }) }}</td>
        <td v-if="hasPermission('viewRcpm', 'AwardAccount')">
            {{ $format.money((request.rcpm), { fraction: 5 }) }}
        </td>
        <td>
            <EditableText
                class="max-w-[50ch]"
                :model-value="request.remark"
                :on-save="saveRemark"
            />
        </td>
        <td>{{ $format.datetime(request.created_at) }}</td>
        <td class="text-2xs !py-0">
            <div v-if="request.product" class="flex items-center gap-2">
                <ModelInfo :model="{ model_name: 'Sale', model_pk: request.product.sale_pk }" />

                <div class="leading-tight">
                    <div>
                        <span class="text-secondary">PNR:</span> <span class="select-all">{{ request.product.order_number || '---' }}</span>
                    </div>
                    <div>
                        <span class="text-secondary">TKT:</span> <span class="select-all">{{ request.product.external_number || '---' }}</span>
                    </div>
                </div>
            </div>
        </td>
        <td class="text-right !py-0">
            <template v-if="hasPermission('manageAwardDetails', 'Sale')">
                <AppButton
                    v-if="!request.is_blocked"
                    v-tooltip="'Reject request'"
                    class="--danger --ghost --only"
                    @click="deleteRequest"
                >
                    <Trash2Icon />
                </AppButton>
            </template>
        </td>
    </tr>
</template>

<script setup lang="ts">
import type { ModelRef } from '~types/lib/Model'
import { $confirmDelete } from '@/plugins/ConfirmPlugin'
import EditableText from '~/components/Input/Editable/EditableText.vue'
import { useProgress } from '@marcoschulte/vue3-progress'
import ModelInfo from '~/components/Model/ModelInfo.vue'

defineOptions({
    name: 'AwardAccountRequestListSectionOutgoingRequest',
})

const props = defineProps<{
    request: ModelRef<'AwardAccountOutgoingRequest', 'product'>
}>()

//

const { useModel, hasPermission } = useContext()

//

async function deleteRequest() {
    await $confirmDelete({
        text: 'Are you sure you want to delete this request?',
        confirmButton: 'Delete',
    })

    await useProgress().attach(
        useWaitForResourceEvent(
            useModel('AwardAccountOutgoingRequest').actions.delete({
                pk: usePk(props.request),
            }),
            'AwardAccountOutgoingRequest',
            'delete',
            usePk(props.request),
        ),
    )
}

//

async function saveRemark(remark: string) {
    await useModel('AwardAccountOutgoingRequest').actions.editRemark({
        pk: usePk(props.request),
        remark,
    })
}
</script>

