import ValidationRules from '#/src-new/lib/ValidationRule/ValidationRules'
import type { City } from './useLocationOptions'

export type VpnFormData = {
    name: string
    country: string | null
    region: string | null
    city: City | null
    note: string | null
    protocol: string | null
    username: string | null
    password: string | null
    host: string | null
    port: number | null
    screen_masking: string
    screen_resolution_custom?: string | null
    media_devices_masking: string
    video_input_count?: number | null
    audio_input_count?: number | null
    audio_output_count?: number | null
    graphics_noise: string
    graphics_masking: string
    canvas_noise: string
    audio_masking: string
    navigator_masking: string
    user_agent?: string | null
    platform?: string | null
    hardware_concurrency?: number | null
    oscpu?: string | null
    ports_masking: string
    whitelisted_ports?: string | null
    fonts_masking: string
    browser_type: string
}

export function useVpnForm(isCustomProxy: Ref<boolean>) {
    const form = useForm<VpnFormData>({
        name: ``,
        country: 'us',
        region: null,
        city: null,
        isp: null,
        note: '',
        screen_masking: 'mask',

        protocol: null,
        username: null,
        password: null,
        host: null,
        port: null,

        media_devices_masking: 'mask',
        video_input_count: 0,
        audio_input_count: 1,
        audio_output_count: 1,

        graphics_noise: 'mask',
        graphics_masking: 'mask',
        canvas_noise: 'mask',
        audio_masking: 'natural',

        navigator_masking: 'mask',

        ports_masking: 'mask',

        fonts_masking: 'mask',
        browser_type: 'stealthfox',
    }, {
        name: [ValidationRules.Required(), ValidationRules.MaxLength(100)],
        note: [ValidationRules.MaxLength(200)],
        protocol: ValidationRules.RequiredWhen(() => isCustomProxy.value),
        username: ValidationRules.RequiredWhen(() => isCustomProxy.value),
        password: ValidationRules.RequiredWhen(() => isCustomProxy.value),
        host: ValidationRules.RequiredWhen(() => isCustomProxy.value),
        port: ValidationRules.RequiredWhen(() => isCustomProxy.value),
        screen_resolution_custom: [ValidationRules.RequiredWhen(() => form.data.screen_masking === 'custom')],
        video_input_count: (video_input_count) => {
            if (form.data.media_devices_masking === 'custom') {
                if (video_input_count < 0) return 'Video input count must be more or equal than 0'

                if (video_input_count > 1) return 'Video input count must be less or equal than 1'

                if (!video_input_count && video_input_count !== 0) return 'Video input count required'
            }
        },
        audio_input_count: [ValidationRules.RequiredWhen(() => form.data.media_devices_masking === 'custom')],
        audio_output_count: [ValidationRules.RequiredWhen(() => form.data.media_devices_masking === 'custom')],
        user_agent: [ValidationRules.RequiredWhen(() => form.data.navigator_masking === 'custom')],
        platform: [ValidationRules.RequiredWhen(() => form.data.navigator_masking === 'custom')],
        hardware_concurrency: [ValidationRules.RequiredWhen(() => form.data.navigator_masking === 'custom')],
        oscpu: [ValidationRules.RequiredWhen(() => form.data.navigator_masking === 'custom')],
        whitelisted_ports: [ValidationRules.RequiredWhen(() => form.data.ports_masking === 'custom')],
    })

    return { form }
}
