<template>
    <SuspenseManual :state="suspense">
        <div class="w-full card card__body mb-1">
            <InputText
                ref="searchbar"
                v-model.trim="searchInput"
                placeholder="Start typing ..."
                clear-button
            />
        </div>
        <div
            v-if="externalUserAgentList.loaded.value && !externalUserAgentList.records.length"
            class="w-full card card__body flex justify-center items-center align-middle h-[345px]"
        >
            Profiles with unused slot for selected mile price program not found
        </div>
        <div
            v-else-if="!filteredExternalUserAgentList?.length"
            class="w-full card card__body flex justify-center items-center align-middle h-[345px]"
        >
            Profiles not found
        </div>
        <div
            v-else
            class="flex flex-col gap-1.5 fancy-scroll overflow-y-auto  card card__body h-[345px]"
        >
            <div
                v-for="(externalUserAgent, index) in filteredExternalUserAgentList"
                :key="index"
                class="grid grid-cols-7 gap-1 items-center"
            >
                <div class="border dark:border-secondary/60 rounded py-2 px-2.5 flex gap-2.5 col-span-6">
                    <div class="flex-none flex flex-col w-[140px]">
                        <div class="text-2xs text-secondary">
                            Profile Name
                        </div>
                        <div class="max-w-[120px] truncate text-xs">
                            {{ externalUserAgent.name }}
                        </div>
                    </div>
                    <div class="flex-none flex w-[1px] border" />
                    <div>
                        <span class="text-2xs text-secondary">Linked Programs</span>
                        <div class="text-xs flex flex-wrap">
                            <div
                                v-for="(milePriceProgram, milePriceProgramIndex) in externalUserAgent.milePricePrograms"
                                :key="milePriceProgramIndex"
                                class="flex"
                            >
                                {{ milePriceProgram.name }}
                                <span v-if="milePriceProgramIndex + 1 !== externalUserAgent.milePricePrograms.length" class="flex w-[1px] border mx-1" />
                            </div>
                        </div>
                    </div>
                </div>
                <AppButton
                    class="--xs"
                    :class="{
                        '--success': modelValue === usePk(externalUserAgent)
                    }"
                    @click="select(externalUserAgent)"
                >
                    {{ modelValue === usePk(externalUserAgent) ? 'Selected' : 'Select' }}
                </AppButton>
            </div>
        </div>
        <template #fallback>
            <PlaceholderBlock class="w-full h-full" />
        </template>
    </SuspenseManual>
</template>

<script setup lang="ts">
import type { ModelRef } from '~types/lib/Model'
import type { Props } from '~/lib/Input/useInputField'
import { ExternalUserAgentStatus } from '~/api/models/ExternalUserAgent/ExternalUserAgent'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'

const props = defineProps<Omit<Props<PrimaryKey>, 'type'> & {
    milePriceProgramPk: PrimaryKey
}>()

const emit = defineEmits<{
    'update:modelValue': [value: PrimaryKey],
}>()

const { useModel } = useContext()
const externalUserAgentModel = useModel('ExternalUserAgent')

const externalUserAgentPks = (await externalUserAgentModel.actions.getNotUsedExternalUserAgentPks({ mile_price_program_pk: props.milePriceProgramPk })).pks
const externalUserAgentList = externalUserAgentModel.useList({
    pks: externalUserAgentPks,
    with: ['milePricePrograms'],
    where: (and) => and.in('status', [ExternalUserAgentStatus.Pending, ExternalUserAgentStatus.Approved]),
})

const suspense = useSuspensableComponent(async () => {
    await externalUserAgentList.fetch()
})

const select = (externalUserAgent: ModelRef<'ExternalUserAgent'>) => {
    emit('update:modelValue', usePk(externalUserAgent))
}

//

const searchInput = ref('')

const searchbar: Ref<HTMLHtmlElement> = ref({} as HTMLHtmlElement)

const handleKeydown = (event: KeyboardEvent) => {
    if (event.key.length === 1 && event.key !== ' ' && !isSearchFocused()) {
        searchbar.value?.focus()
    }
}

window.addEventListener('keydown', handleKeydown)

function isSearchFocused(): boolean {
    return document.activeElement === searchbar.value
}

const filteredExternalUserAgentList = computed(() => {
    if (searchInput.value) {
        return externalUserAgentList.records.filter(externalUser => filterByKeywords(externalUser))
    }

    return externalUserAgentList.records
})

function filterByKeywords(externalUser: ModelRef<'ExternalUserAgent', 'milePricePrograms'>): boolean {
    const keywords = searchInput.value.toLowerCase()
    const linkedPrograms = externalUser.milePricePrograms.map(milePrice => milePrice.name).join(' ').toLowerCase()

    return externalUser.name.toLowerCase().includes(keywords) || linkedPrograms.includes(keywords)
}
</script>
