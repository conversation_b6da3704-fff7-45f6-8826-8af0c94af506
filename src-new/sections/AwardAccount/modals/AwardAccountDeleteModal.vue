<template>
    <AppModalWrapper :header="`To be deleted account #${accountPk}`" class="!max-w-[400px]">
        <div class="p-4 border-t flex flex-col gap-4">
            <AppTip
                type="info"
                class="items-center"
            >
                Please provide reason for deleting account.
            </AppTip>

            <FormField
                :form="form"
                :field="'remark'"
                label="Remark"
            >
                <InputTextarea
                    v-model="form.data.remark"
                    size="small"
                    rows="6"
                    maxlength="256"
                />
            </FormField>
        </div>

        <template #footer="{ close }">
            <div class="flex items-center gap-4 justify-between">
                <AppButton @click="close">
                    Cancel
                </AppButton>

                <AppButton
                    class="--primary"
                    :loading="form.loading"
                    @click="submit"
                >
                    Submit
                </AppButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import FormField from '~/components/Form/FormField.vue'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'

defineOptions({
    name: 'AwardAccountDeleteModal',
})

const props = defineProps<{
    accountPk: PrimaryKey
}>()

const emit = defineEmits<{
    close: [],
}>()

//

const { useModel } = await useNewContext('AwardAccount', props.accountPk)

//

const form = useForm({
    remark: '',
}, {
    remark: [ValidationRules.Required(), ValidationRules.MaxLength(256, 'Remark should be less than 256 symbols')],
})

//

const submit = form.useSubmit(async () => {
    await useModel('AwardAccount').actions.markAccountToBeDeleted({
        pk: props.accountPk,
        remark: form.data.remark,
    })

    toastSuccess('Account was marked as to be deleted')

    emit('close')
})
</script>
