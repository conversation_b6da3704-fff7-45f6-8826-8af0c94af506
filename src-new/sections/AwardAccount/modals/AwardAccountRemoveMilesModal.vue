<template>
    <AppModalWrapper header="Write off miles" class="!max-w-[400px]">
        <form class="p-4 border-t grid grid-cols-2 gap-4" @submit.prevent="submit">
            <FormField
                :form="form"
                :field="'miles_amount'"
                label="Miles amount"
                class="col-span-2"
            >
                <InputNumber
                    v-model="form.data.miles_amount"
                    step="1"
                />
            </FormField>

            <FormField
                :form="form"
                :field="'remark'"
                label="Remark"
                class="col-span-2"
            >
                <InputTextarea
                    v-model="form.data.remark"
                    :placeholder="'Describe deleting reason'"
                />
            </FormField>
        </form>
        <template #footer>
            <div class="flex justify-end gap-4">
                <AppModalButton class="btn-secondary" @click="$emit('close')">
                    Cancel
                </AppModalButton>
                <AppModalButton class="btn-danger" @click="submit">
                    Submit
                </AppModalButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import FormField from '~/components/Form/FormField.vue'

defineOptions({
    name: 'AwardAccountRemoveMilesModal',
})

const props = defineProps<{
    pk: PrimaryKey,
    balance: number
}>()

const emit = defineEmits<{
    'close': [],
}>()

const { useModel } = await useNewContext('AwardAccountIncomingRequest', props.pk)

const form = useForm({
    miles_amount: 0,
    remark: '',
}, {
    miles_amount: [ValidationRules.Required(), ValidationRules.MinMax(1, props.balance)],
    remark: ValidationRules.Required(),
})

const submit = form.useSubmit(async () => {
    await useModel('AwardAccountIncomingRequest').actions.writeOff({
        pk: props.pk,
        miles_amount: form.data.miles_amount,
        remark: form.data.remark,
    })
    emit('close')
})
</script>

