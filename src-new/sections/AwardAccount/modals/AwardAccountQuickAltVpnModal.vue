<template>
    <AppModalWrapper
        class="!max-w-[720px]"
        close-button
        header="Create quick profile"
    >
        <div class="card">
            <form class="flex gap-5 card card__body justify-between">
                <div class="flex flex-col gap-5 w-full">
                    <FormField
                        :form="form"
                        field="name"
                        label="Profile Name"
                        required
                    >
                        <InputText v-model="form.data.name" />
                    </FormField>

                    <FormField
                        :form="form"
                        field="os_type"
                        label="Operation system"
                    >
                        <InputSelect
                            v-model="form.data.os_type"
                            placeholder="Select operation system"
                            :options="operationSystemOptions"
                            filter
                        />
                    </FormField>

                    <FormField
                        :form="form"
                        field="browser_type"
                        label="Browser"
                    >
                        <InputSelect
                            v-model="form.data.browser_type"
                            placeholder="Select browser type"
                            :options="browserOptions"
                            filter
                        />
                    </FormField>

                    <FormField
                        :form="form"
                        field="count"
                        label="Number of profiles (10 max)"
                    >
                        <InputNumber
                            v-model="form.data.count"
                            min="0"
                            max="10"
                        />
                    </FormField>

                    <FormField
                        :form="form"
                        field="start_url"
                    >
                        <label class="form-label flex gap-2 items-center">
                            Start URL <HelpCircleIcon v-tooltip="'This URL will open in the first tab. <br />Additional URLs will open in other tabs.'" />
                        </label>

                        <div class="flex gap-2">
                            <InputText v-model="form.data.start_url" />
                            <AppButton
                                class="--only --ghost"
                                type="button"
                                @click="form.data.additional_urls.push('')"
                            >
                                <PlusSquareIcon />
                            </AppButton>
                        </div>
                    </FormField>

                    <div v-if="form.data.additional_urls.length">
                        <label class="form-label">
                            Additional URLs
                        </label>
                        <div class="flex flex-col gap-2">
                            <FormField
                                v-for="(additional_url, index) in form.data.additional_urls"
                                :key="index"
                                :form="form"
                                :field="`additional_urls.${index}`"
                                class="flex gap-2"
                            >
                                <template #error>
                                    <span v-show="false" />
                                </template>
                                <InputText v-model="form.data.additional_urls[index]" />
                                <AppButton
                                    class="--only --ghost --danger"
                                    type="button"
                                    @click="form.data.additional_urls.splice(index, 1)"
                                >
                                    <Trash2Icon />
                                </AppButton>
                            </FormField>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col gap-5 w-full">
                    <FormField
                        :form="form"
                        field="proxy"
                        label="Proxy"
                    >
                        <InputSelect
                            v-model="form.data.proxy"
                            :options="proxyOptions"
                            @change="resetOnProxyChange"
                        />
                    </FormField>

                    <template v-if="isCustomProxy">
                        <FormField
                            :form="form"
                            field="country"
                            label="Country"
                        >
                            <InputSelect
                                v-model="form.data.country"
                                placeholder="Select Country"
                                :options="countryOptions"
                                with-empty="Random"
                                filter
                                @change="resetLocationData"
                            />
                        </FormField>

                        <FormField
                            :form="form"
                            field="region"
                            label="Region"
                            class="w-full"
                        >
                            <InputSelect
                                v-model="form.data.region"
                                placeholder="Select Region"
                                :options="regionOptions"
                                filter
                                with-empty
                            />
                        </FormField>

                        <FormField
                            :form="form"
                            field="city"
                            label="City"
                            class="w-full"
                        >
                            <InputSelect
                                v-model="form.data.city"
                                placeholder="Select City"
                                :options="cityOptions"
                                filter
                                with-empty
                                @change="updateRegionFromCity($event)"
                            />
                        </FormField>

                        <FormField
                            :form="form"
                            field="isp"
                            label="ISP"
                        >
                            <InputSelect
                                v-model="form.data.isp"
                                placeholder="Select provider"
                                :options="ispOptions"
                                filter
                                with-empty
                            />
                        </FormField>

                        <FormField
                            :form="form"
                            field="format"
                            label="Format"
                        >
                            <InputSelect v-model="form.data.format" :options="proxyFormatOptions" />
                        </FormField>

                        <FormField
                            :form="form"
                            field="protocol"
                            label="Protocol"
                        >
                            <InputSelect v-model="form.data.protocol" :options="proxyProtocolOptionsCustom" />
                        </FormField>

                        <template v-if="form.data.format === 'single'">
                            <div>
                                <label class="form-label flex gap-2 items-center">
                                    Proxy details <HelpCircleIcon v-tooltip="`Use 'IP:port' or 'IP:port:login:password' format<br> to quickly paste proxy details.`" />
                                </label>
                                <div class="flex gap-2">
                                    <FormField
                                        :form="form"
                                        field="ip_address"
                                    >
                                        <InputText v-model="form.data.ip_address" placeholder="IP address" />
                                    </FormField>
                                    <FormField
                                        :form="form"
                                        field="port"
                                        class="flex-1"
                                    >
                                        <InputNumber v-model="form.data.port" placeholder="Port" />
                                    </FormField>
                                </div>
                                <FormField
                                    :form="form"
                                    field="login"
                                    class="mt-2"
                                >
                                    <InputText v-model="form.data.login" placeholder="Login" />
                                </FormField>
                                <FormField
                                    :form="form"
                                    field="password"
                                    class="mt-2"
                                >
                                    <InputText v-model="form.data.password" placeholder="Password" />
                                </FormField>
                            </div>

                            <div class="grid gap-2">
                                <div class="grid grid-cols-2 gap-4">
                                    <AppButton
                                        class="flex-grow --primary --outline"
                                        :loading="proxyGenerateIsActive"
                                        type="button"
                                        @click="generateProxy()"
                                    >
                                        Generate Proxy
                                    </AppButton>

                                    <AppButton
                                        class="flex-grow --success --outline"
                                        :loading="proxyCheckIsActive"
                                        :disabled="!form.data.ip_address || !form.data.port"
                                        type="button"
                                        @click="checkProxy()"
                                    >
                                        Check Proxy
                                    </AppButton>
                                </div>
                                <div class="grid grid-cols-2">
                                    <AppTip
                                        v-if="isProxyChecked && isProxyValid"
                                        class="tip--small items-center py-0 w-[155px]"
                                        type="success"
                                    >
                                        <span>Proxy is valid</span>
                                    </AppTip>
                                    <AppTip
                                        v-else-if="isProxyChecked && !isProxyValid"
                                        class="tip--small items-center py-0 w-[155px]"
                                        type="danger"
                                    >
                                        <span>Proxy is invalid</span>
                                    </AppTip>
                                </div>
                            </div>
                        </template>
                        <template v-else>
                            <FormField
                                :form="form"
                                field="proxy_list"
                            >
                                <label class="form-label flex gap-2 items-center">
                                    Proxy list (20 max)<HelpCircleIcon v-tooltip="`Use 'IP:port' or<br>'IP:port:login:password' format.`" />
                                </label>
                                <InputTextarea v-model="form.data.proxy_list" rows="5" />
                            </FormField>

                            <FormField
                                :form="form"
                                field="connect_proxies"
                                label="Connect Proxies"
                            >
                                <InputSelect v-model="form.data.connect_proxies" :options="connectProxiesOptions" />
                            </FormField>
                        </template>
                    </template>
                    <template v-else-if="form.data.proxy === 'mlx'">
                        <FormField
                            :form="form"
                            field="country"
                            label="Country"
                        >
                            <InputSelect
                                v-model="form.data.country"
                                placeholder="Select Country"
                                :options="countryOptions"
                                with-empty="Random"
                                filter
                                @change="resetLocationData"
                            />
                        </FormField>
                        <div v-if="form.data.country" class="flex gap-2">
                            <FormField
                                :form="form"
                                field="region"
                                label="Region"
                                class="w-full"
                            >
                                <InputSelect
                                    v-model="form.data.region"
                                    placeholder="Select Region"
                                    :options="regionOptions"
                                    filter
                                    with-empty
                                />
                            </FormField>
                            <FormField
                                :form="form"
                                field="city"
                                label="City"
                                class="w-full"
                            >
                                <InputSelect
                                    v-model="form.data.city"
                                    placeholder="Select City"
                                    :options="cityOptions"
                                    filter
                                    with-empty
                                    @change="updateRegionFromCity($event)"
                                />
                            </FormField>
                        </div>
                        <FormField
                            :form="form"
                            field="protocol"
                            label="Protocol"
                        >
                            <InputSelect v-model="form.data.protocol" :options="proxyProtocolOptionsMlx" />
                        </FormField>
                        <FormField
                            :form="form"
                            field="session_time"
                            label="Sticky session"
                        >
                            <InputSelect v-model="form.data.session_time" :options="sessionTimeOptions" />
                        </FormField>
                        <template v-if="form.data.session_time === 'custom'">
                            <FormField
                                :form="form"
                                field="ipttl"
                            >
                                <InputNumber v-model="form.data.ipttl" :min="60" />
                            </FormField>
                        </template>
                    </template>
                </div>
            </form>
        </div>
        <template #footer>
            <div class="flex justify-between">
                <AppButton @click="$emit('close')">
                    Cancel
                </AppButton>
                <AppButton
                    class="--primary"
                    :loading="form.loading"
                    @click="submit"
                >
                    Confirm
                </AppButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import FormField from '~/components/Form/FormField.vue'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import type { QuickProfileFormData } from '~/service/ExternalUserAgentService'
import { preventDuplication } from '~/lib/Helper/PromiseHelper'
import { ExternalUserAgentProxyType } from '~/api/models/ExternalUserAgent/ExternalUserAgentProxy'
import { useLocationOptions } from '~/sections/AwardAccount/modals/AwardAccountAltVpn/composable/useLocationOptions'

defineOptions({
    name: 'AwardAccountQuickAltVpnModal',
})

const emit = defineEmits(['close'])

const { useModel } = useContext()

const externalUserAgentModel = useModel('ExternalUserAgent')
const externalUserAgentProxy = useModel('ExternalUserAgentProxy')
const multilogin = useService('multilogin')

const form = useForm<QuickProfileFormData>({
    name: '',
    os_type: 'windows',
    browser_type: 'stealthfox',
    count: 1,
    start_url: 'https://pixelscan.net/',
    additional_urls: [],
    proxy: 'none',
    country: 'us',
    region: undefined,
    city: undefined,
    isp: undefined,
    session_type: 'sticky',
    session_time: 'auto',
    ipttl: 60,

    // custom
    format: 'single',
    protocol: 'http',

    proxy_list: '',
    connect_proxies: 'sequentially',

    ip_address: '',
    port: undefined,
    login: '',
    password: '',
}, {
    name: ValidationRules.Required(),
    os_type: ValidationRules.Required(),
    browser_type: ValidationRules.Required(),
    count: [ValidationRules.Required(), ValidationRules.MinMax(0, 10)],
    start_url: [ValidationRules.Required(), ValidationRules.Url(undefined, ' ')],
    additional_urls: ValidationRules.Array(ValidationRules.Url(undefined, ' ')),

    //
    ip_address: ValidationRules.RequiredWhen((): boolean => form.data.format === 'single' && isCustomProxy.value),
    port: ValidationRules.RequiredWhen((): boolean => form.data.format === 'single' && isCustomProxy.value),
    proxy_list: ValidationRules.RequiredWhen((): boolean => form.data.format === 'list' && isCustomProxy.value),
})

const isCustomProxy = computed(() => form.data.proxy === 'custom')

const { countryOptions, regionOptions, cityOptions, ispOptions, resetLocationData, updateRegionFromCity } = await useLocationOptions(form, isCustomProxy)

const submit = form.useSubmit(async (data) => {
    const quickProfileData: QuickProfileFormData = {
        name: data.name,
        os_type: data.os_type,
        browser_type: data.browser_type,
        count: data.count,
        start_url: data.start_url,
        additional_urls: data.additional_urls,
        proxy: data.proxy,

        //
        country: undefined,
        region: undefined,
        city: undefined,
        isp: undefined,
        session_type: undefined,
        session_time: undefined,
        ipttl: undefined,

        //
        format: undefined,
        protocol: undefined,
        proxy_list: undefined,
        connect_proxies: undefined,
        ip_address: undefined,
        port: undefined,
        login: undefined,
        password: undefined,
    }

    if (data.proxy == 'custom') {
        quickProfileData.format = data.format
        quickProfileData.protocol = data.protocol

        if (data.format === 'single') {
            quickProfileData.ip_address = data.ip_address
            quickProfileData.port = data.port
            quickProfileData.login = data.login
            quickProfileData.password = data.password
        } else if (data.format === 'list') {
            quickProfileData.proxy_list = data.proxy_list
            quickProfileData.connect_proxies = data.connect_proxies
        }
    } else if (data.proxy == 'mlx') {
        quickProfileData.country = data.country

        if (quickProfileData.country) {
            quickProfileData.region = data.region
            quickProfileData.city = data.city
        }

        quickProfileData.protocol = data.protocol
        quickProfileData.session_type = data.session_type
        quickProfileData.session_time = data.session_time

        if (data.session_time == 'custom') {
            quickProfileData.ipttl = data.ipttl
        }
    }

    const isOpen = await multilogin.startQuickProfile(quickProfileData)

    if (isOpen) {
        emit('close')
    }
}, { resetOnSuccess: false })

const {
    os_types: operationSystemOptions,
    browser_types: browserOptions,
} = await externalUserAgentModel.actions.getMultiLoginQuickProfileFormOptions()

const proxyOptions = ref([
    { title: 'Alt. VPN', value: 'mlx' },
    { title: 'Custom', value: 'custom' },
    { title: 'None', value: 'none' },
])

const proxyFormatOptions = ref([
    { title: 'One proxy', value: 'single' },
    { title: 'Proxy list', value: 'list' },
])

const proxyProtocolOptionsCustom = ref([
    { title: 'HTTP', value: 'http' },
    { title: 'SOCKS5', value: 'socks5' },
])
const proxyProtocolOptionsMlx = ref([
    { title: 'HTTP', value: 'http' },
    { title: 'SOCKS5', value: 'socks5' },
])

const connectProxiesOptions = ref([
    { title: 'Sequentially', value: 'sequentially' },
    { title: 'Randomly without repetition', value: 'random' },
])

const sessionTimeOptions = ref([
    { title: 'Keep Ip for as long as possible', value: 'auto' },
    { title: 'Custom, sec', value: 'custom' },
])

const resetOnProxyChange = () => {
    form.data.protocol = 'socks5'
}

const proxyCheckIsActive = ref(false)
const proxyGenerateIsActive = ref(false)
const isProxyValid = ref(false)
const isProxyChecked = ref(false)

const checkProxy = async () => {
    await preventDuplication(async () => {
        try {
            await multilogin.checkProxy({
                type: form.data.protocol,
                host: form.data.ip_address,
                port: form.data.port,
                username: form.data.login,
                password: form.data.password,
            })
            isProxyValid.value = true
        } catch (error) {
            isProxyValid.value = false
        } finally {
            isProxyChecked.value = true
        }
    }, proxyCheckIsActive)
}

const generateProxy = async () => {
    await preventDuplication(async () => {
        const proxyParams = {
            type: ExternalUserAgentProxyType.NodeMaven,
            protocol: form.data.protocol ?? null,
            location: form.data.country ? {
                country: form.data.country,
                city: form.data.city?.value ?? null,
                region: form.data.region ?? null,
                isp: form.data.isp ?? null,
            } : null,
        }

        const proxy = await externalUserAgentProxy.actions.generateProxy(proxyParams)

        form.updateData({
            ip_address: proxy.host,
            port: proxy.port,
            login: proxy.username,
            password: proxy.password,
            protocol: proxy.protocol,
        })
    }, proxyGenerateIsActive)
}

watch(() => form.data.region, () => {
    form.data.city = undefined
})

watch(() => form.data.city, () => {
    form.data.isp = undefined
})
</script>
