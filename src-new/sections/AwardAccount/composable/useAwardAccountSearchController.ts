import SearchController from '~/lib/Search/SearchController'
import NumberSearchTag from '~/lib/Search/Tag/NumberSearchTag'
import SelectSearchTag from '~/lib/Search/Tag/SelectSearchTag'
import TextSearchTag from '~/lib/Search/Tag/TextSearchTag'
import BooleanSelectSearchTag from '~/lib/Search/Tag/BooleanSelectSearchTag'
import NumberRangeSearchTag from '~/lib/Search/Tag/NumberRangeSearchTag'
import { AwardAccountBookingTypeField } from '~/api/dictionaries/Static/AwardAccount/AwardAccountBookingTypeDictionary'
import { TableColumnsController } from '~/lib/Table/TableColumnsController'
import SortController from '~/lib/Search/SortController'
import BooleanSearchTag from '~/lib/Search/Tag/BooleanSearchTag'

export function useAwardAccountSearchController() {
    const { useDictionary, hasPermission } = useNewContext('selected')

    const searchController = SearchController.forModel('AwardAccount', {
        id: new NumberSearchTag('ID'),

        mile_price_program_pk: new SelectSearchTag(
            'Mile price program',
            useGeneralDictionary('MilePriceProgram').mapRecords.forSelect(),
        ),

        vpn: new TextSearchTag('VPN'),
        alt_vpn: new BooleanSelectSearchTag('Alt. VPN').hideInSearch(!hasPermission('useAltVpn', 'all')),
        holder_name: new TextSearchTag('Holder'),
        account_number: new TextSearchTag('Account'),

        consolidator_pk: new SelectSearchTag(
            'Consolidator',
            useDictionary('Consolidator').mapAwardRecords.forSelect(),
        ),

        balance: new NumberRangeSearchTag('Miles'),

        status: new SelectSearchTag('Status', useGeneralDictionary('AwardAccountStatus').mapRecords.forSelect()),

        booking_type: new SelectSearchTag(
            'Booking type',
            useGeneralDictionary('AwardAccountBookingType').mapRecords.forSelect(),
            {
                applyCondition: (tag, _, and) => {
                    if ((tag.values ?? []).find(value => value === AwardAccountBookingTypeField.Any)) {
                        and.or((q) => {
                            for (const field of Object.values(AwardAccountBookingTypeField)) {
                                if (field === AwardAccountBookingTypeField.Any) {
                                    continue
                                }

                                q.eq(field, true)
                            }
                        })

                        return
                    }

                    and.or(or => {
                        for (const key of (tag.values ?? []) as AwardAccountBookingTypeField[]) {
                            or.eq(key, true)
                        }
                    })
                },
            },
        ),

        type: new SelectSearchTag('Type', useGeneralDictionary('AwardAccountType').mapRecords.forSelect()),

        //

        is_reserved: new SelectSearchTag('Reserved', [
            {
                title: 'Reserved',
                value: true,
            },
            {
                title: 'Not reserved',
                value: false,
            },
        ], {
            placeholder: 'All',
        }),
        group_account_number: new TextSearchTag('Main account'),
        to_be_deleted: new BooleanSelectSearchTag('To be deleted', [ 'Yes', 'No']),
    }, {
        presets: {
            key: 'award-accounts',
        },
    })

    const columnsController = markRaw(new TableColumnsController(useTableColumns({
        id: {
            label: 'ID',
            width: 5,
            sortable: true,
        },
        mile_price_program_pk: {
            label: 'Pr.',
            width: 5,
            sortable: true,
            tooltip: 'Mile price program',
        },
        vpn: {
            label: 'VPN',
            width: 10,
        },
        alt_vpn: {
            enabled: hasPermission('useAltVpn', 'all'),
            label: 'Alt. VPN',
            width: 19,
        },
        holder_name: {
            label: 'Holder name',
            width: 16,
            sortable: true,
        },
        account_number: {
            label: 'Account №',
            width: 15,
            sortable: true,
        },
        password: {
            label: 'Pass',
            width: 4,
            tooltip: 'Account password',

        },
        consolidator_pk: {
            label: 'Supplier',
            width: 16,
            sortable: true,
        },
        balance: {
            label: 'Miles',
            width: 10,
            sortable: true,
        },
        cpm: {
            label: 'CPM',
            width: 7,
            sortable: true,
        },
        rcpm: {
            label: 'RCPM',
            width: 7,
            sortable: true,
            enabled: hasPermission('viewRcpm', 'AwardAccount'),
        },
        status: {
            label: 'Status',
            width: 10,
            sortable: true,
            tooltip: 'Account status',
        },
        info: {
            label: 'In use till',
            width: 14,
        },
        counts: {
            label: 'Ex/Ret/Tot/TUp',
            tooltip: 'Exchange / Refund / Total bookings / Top-ups',
            width: 12,
        },
        booking_type: {
            label: 'Booking type',
            width: 14,
        },
        remark: {
            label: 'Remark',
        },
        type: {
            label: 'Type',
            width: 10,
            sortable: true,
            tooltip: 'Account type',
        },
        to_be_deleted: {
            label: 'To be deleted',
            width: 18,
        },
        actions: {
            width: 10,
        },
    }), {
        id: { active: true, required: true },
        mile_price_program_pk: { active: true },
        vpn: { active: true },
        alt_vpn: { active: true },
        holder_name: { active: true },
        account_number: { active: true },
        password: { active: true },
        consolidator_pk: { active: true },
        balance: { active: true },
        cpm: { active: true },
        rcpm: { active: true },
        status: { active: true },
        info: { active: true },
        counts: { active: true },
        booking_type: { active: true },
        remark: { active: true },
        type: { active: true },
        to_be_deleted: { active: true },
        actions: { active: true },
    }, 'award-accounts', 'award-accounts'))

    const columns = columnsController.getColumnsDefinition()
    const sortController = SortController.fromColumns(columns, {
        default: { 'id': 'desc' },
    })

    return {
        searchController,
        columnsController,
        columns,
        sortController,
    }
}

export type AwardAccountController = ReturnType<typeof useAwardAccountSearchController>
