<template>
    <AppTable v-bind.prop="$props">
        <template
            v-for="(name) in Object.keys(slots)"
            :key="name"
            #[name]="props"
        >
            <slot :name="name" v-bind="props" />
        </template>
    </AppTable>
</template>

<script setup lang="ts" generic="T extends AnyObject[]">
import type { AppTableProps } from '~/lib/Table/TableColumnsController'

defineProps<AppTableProps<T>>()

defineSlots<TableSlots>()

type TableSlots = {
    id: { item: unknown },
    mile_price_program_pk: { item: unknown },
    vpn: { item: unknown },
    alt_vpn: { item: unknown },
    holder_name: { item: unknown },
    account_number: { item: unknown },
    password: { item: unknown },
    consolidator_pk: { item: unknown },
    balance: { item: unknown },
    cpm: { item: unknown },
    rcpm: { item: unknown },
    status: { item: unknown },
    info: { item: unknown },
    counts: { item: unknown },
    booking_type: { item: unknown },
    remark: { item: unknown },
    type: { item: unknown },
    to_be_deleted: { item: unknown },
    actions: { item: unknown },
}

const slots = useSlots()
</script>

