<template>
    <div>
        <div class="card">
            <div class="p-2 flex items-center gap-4">
                <AwardAccountListSectionFilter :search-controller="awardAccountController.searchController" class="mr-auto" />
                <AppPaginationCompact :pagination="accountList.pagination" />
                <AppPageSize :pagination="accountList.pagination" />
                <TableColumnsConfigurationButton :controller="awardAccountController.columnsController" />
            </div>

            <SuspenseManual :state="suspense">
                <template #default>
                    <div class="overflow-x-auto fancy-scroll-x">
                        <AwardAccountTableWrapper
                            :columns="awardAccountController.columnsController"
                            :items="accounts"
                            :search-tags="awardAccountController.searchController.tags"
                            :sort-controller="awardAccountController.sortController"
                            :zebra="zebra"
                        >
                            <template #id="{ item: account }: { item: Item }">
                                <TableCellLink :to="routeToAwardAccount(account.pk)">
                                    {{ account.id }}
                                </TableCellLink>
                            </template>

                            <template #mile_price_program_pk="{ item: account }: { item: Item }">
                                <td class="text-center">
                                    {{ milePriceProgramName(account) }}
                                </td>
                            </template>

                            <template #vpn="{ item: account }: { item: Item }">
                                <td>{{ account.vpn }}</td>
                            </template>

                            <template #alt_vpn="{ item: account }: { item: Item }">
                                <td v-if="hasPermission('useAltVpn', 'all')" class="items-center">
                                    <template v-if="isAccountStatusAvailable(account) || account.external_user_agent_pk">
                                        <div class="min-w-40">
                                            <div v-if="account.externalUserAgent" class="grid grid-cols-2 gap-2 items-center">
                                                <AppButton
                                                    class="--outline --xs !border"
                                                    :class="[account.externalUserAgent?.status == ExternalUserAgentStatus.Pending ? '--pending' : '--success']"
                                                    :loading="isMultiloginLoading"
                                                    :disabled="isMultiloginLoading"
                                                    @click="openMultilogin(account.externalUserAgent)"
                                                >
                                                    <CheckIcon />
                                                    Online
                                                </AppButton>
                                                <div class="flex justify-center w-full gap-4">
                                                    <EditIcon
                                                        class="cursor-pointer --secondary w-3.5 h-3.5"
                                                        @click="openExternalUserAgentUpdateModal(usePk(account.externalUserAgent))"
                                                    />
                                                    <TrashIcon
                                                        class="cursor-pointer --secondary w-3.5 h-3.5"
                                                        @click="removeExternalUserAgent(usePk(account.externalUserAgent), usePk(account))"
                                                    />
                                                </div>
                                            </div>
                                            <div v-else class="grid grid-cols-2 gap-2 items-center justify-center">
                                                <AppButton
                                                    class="--xs --outline --muted !border dark:text-white"
                                                    @click="openExternalUserAgentCreateModal(usePk(account))"
                                                >
                                                    Create VPN
                                                </AppButton>
                                                <AppButton
                                                    class="--xs --outline --muted !border dark:text-white"
                                                    @click="openExternalUserAgentLinkModal(account)"
                                                >
                                                    Link VPN
                                                </AppButton>
                                            </div>
                                        </div>
                                    </template>
                                </td>
                            </template>

                            <template #holder_name="{ item: account }: { item: Item }">
                                <td>
                                    <div class="flex items-center">
                                        <template v-if="account.groupAccount">
                                            <UserIcon
                                                v-if="account.parent_award_account_pk"
                                                class="mr-1 icon colored-text --danger --xs flex-none cursor-pointer"
                                                @click="filterByAccountGroup(account.groupAccount)"
                                            />
                                            <UsersIcon
                                                v-else
                                                class="mr-1 icon colored-text --danger --xs flex-none cursor-pointer"
                                                @click="filterByAccountGroup(account.groupAccount)"
                                            />
                                        </template>
                                        <div class="truncate">
                                            {{ getFormalFullName(account.holder) }}
                                        </div>
                                    </div>
                                </td>
                            </template>
                            <template #account_number="{ item: account }: { item: Item }">
                                <td v-copy class="cursor-[copy] select-all">
                                    <div class="flex items-center">
                                        <AlertTriangleIcon
                                            v-if="account.warning_flag"
                                            v-tooltip="getWarningFlagTooltip(account.warning_flag)"
                                            class="mr-1 icon colored-text --danger --xs flex-none"
                                        />
                                        <LockIcon
                                            v-if="account.is_reserved"
                                            v-tooltip="'Reserved'"
                                            class="mr-1 icon --xs flex-none text-secondary"
                                        />
                                        <div class="truncate">
                                            {{ account.account_number }}
                                        </div>
                                    </div>
                                </td>
                            </template>
                            <template #password="{ item: account }: { item: Item }">
                                <td class="text-center">
                                    <AppCopy v-copy="account.credentials.password" />
                                </td>
                            </template>
                            <template #consolidator_pk="{ item: account }: { item: Item }">
                                <td>
                                    {{ areaName(account) || consolidatorName(account) }}
                                </td>
                            </template>
                            <template #balance="{ item: account }: { item: Item }">
                                <td>{{ $format.money(account.balance, {fraction: 0}) }}</td>
                            </template>
                            <template #cpm="{ item: account }: { item: Item }">
                                <td>{{ $format.money(account.cpm, {fraction: 4}) }}</td>
                            </template>
                            <template #rcpm="{ item: account }: { item: Item }">
                                <td>{{ $format.money(account.rcpm, {fraction: 4}) }}</td>
                            </template>
                            <template #status="{ item: account }: { item: Item }">
                                <td>
                                    <div class="flex items-center gap-2 colored-text min-w-28" :class="`--${accountStatusInfo(account).style}`">
                                        <Component :is="accountStatusInfo(account).icon" />
                                        {{ accountStatusInfo(account).title }}
                                    </div>
                                </td>
                            </template>
                            <template #info="{ item: account }: { item: Item }">
                                <td
                                    class="!py-0"
                                    :class="{
                                        '!py-2': !!account.audit_sale_pk
                                    }"
                                >
                                    <RouterLink
                                        v-if="account.last_sale_pk"
                                        class="link"
                                        :to="routeToSale(account.last_sale_pk)"
                                    >
                                        Sale #{{ account.last_sale_pk }}
                                    </RouterLink>
                                    <div>
                                        {{ $format.date(account.in_use_till_date) }}
                                        {{ account.active_sale_count ? `(${account.active_sale_count})` : '' }}
                                    </div>
                                    <RouterLink
                                        v-if="account.audit_sale_pk"
                                        class="link"
                                        :to="routeToSale(account.audit_sale_pk)"
                                    >
                                        Audit sale #{{ account.audit_sale_pk }}
                                    </RouterLink>
                                </td>
                            </template>
                            <template #counts="{ item: account }: { item: Item }">
                                <td class="font-mono">
                                    <span
                                        :class="account.exchange_sale_count ? 'text-info' : 'text-secondary-300'"
                                        v-html="String(account.exchange_sale_count || '--').padStart(2, '&nbsp;')"
                                    />|<span
                                        :class="account.refund_sale_count ? 'text-pending' : 'text-secondary-300'"
                                        v-html="String(account.refund_sale_count || '--').padStart(2, '&nbsp;')"
                                    />|<span
                                        :class="account.total_sale_count ? 'text-secondary' : 'text-secondary-300'"
                                        v-html="String(account.total_sale_count || '--').padStart(2, '&nbsp;')"
                                    />|<span
                                        :class="account.top_up_count ? 'text-success' : 'text-secondary-300'"
                                        v-html="String(account.top_up_count || '--').padStart(2, '&nbsp;')"
                                    />
                                </td>
                            </template>
                            <template #booking_type="{ item: account }: { item: Item }">
                                <td>
                                    {{ bookingTypeFormatted(account) }}
                                </td>
                            </template>
                            <template #remark="{ item: account }: { item: Item }">
                                <td>
                                    {{ account.remark }}
                                </td>
                            </template>
                            <template #type="{ item: account }: { item: Item }">
                                <td>
                                    {{ accountTypeInfo(account).title }}
                                </td>
                            </template>
                            <template #to_be_deleted="{ item: account }: { item: Item }">
                                <td>
                                    <AppButton
                                        v-if="hasPermission('manage', 'AwardAccount') && account.is_to_be_deleted"
                                        v-tooltip="{
                                            content: `Delete reason: ${account.to_be_deleted_remark}`,
                                            html: false
                                        }"
                                        class="--outline --danger --xs"
                                        @click="deleteAccount(account)"
                                    >
                                        <Trash2Icon />
                                        Delete
                                    </AppButton>

                                    <AppButton
                                        v-else
                                        class="--outline --xs"
                                        :class="{
                                            '--warning': !account.is_to_be_deleted
                                        }"
                                        @click="markAccountToDelete(account)"
                                    >
                                        <CheckIcon v-if="account.is_to_be_deleted" />
                                        To be deleted
                                    </AppButton>
                                </td>
                            </template>
                            <template #actions="{ item: account }: { item: Item }">
                                <td>
                                    <div class="flex -my-1 gap-2 justify-end">
                                        <AppButton
                                            v-tooltip="'Account info'"
                                            class="--sm --only"
                                            @click="openAccount(account)"
                                        >
                                            <InfoIcon />
                                        </AppButton>
                                        <Dropdown v-if="hasPermission('manageAwardDetails', 'Sale')" teleport>
                                            <template #toggle="{ toggle }">
                                                <AppButton
                                                    v-tooltip="'Account actions'"
                                                    class="--sm --only"
                                                    @click="toggle"
                                                >
                                                    <MoreVerticalIcon />
                                                </AppButton>
                                            </template>
                                            <template #content>
                                                <ContextMenu :options="menuOptions(account)" />
                                            </template>
                                        </Dropdown>
                                    </div>
                                </td>
                            </template>
                        </AwardAccountTableWrapper>
                    </div>
                </template>
                <template #fallback>
                    <PlaceholderBlock class="w-full h-[300px]" />
                </template>
            </SuspenseManual>
        </div>

        <AppTablePagination class="mt-6" :pagination="accountList.pagination" />
    </div>
</template>

<script setup lang="ts">
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import AwardAccountListSectionFilter from '~/sections/AwardAccount/List/AwardAccountListSectionFilter.vue'
import { AwardAccountBookingTypeField } from '~/api/dictionaries/Static/AwardAccount/AwardAccountBookingTypeDictionary'
import { AwardAccountStatus, AwardAccountWarningFlag } from '~/api/models/AwardAccount/AwardAccount'
import AwardAccountTableWrapper from '~/sections/AwardAccount/List/AwardAccountTableWrapper.vue'
import { routeToAwardAccount } from '@/lib/core/helper/RouteNavigationHelper'
import { ExternalUserAgentStatus } from '~/api/models/ExternalUserAgent/ExternalUserAgent'
import { usePk } from '~/composables/usePk'
import { getFormalFullName } from '~/lib/Helper/PersonHelper'
import TableCellLink from '~/components/Table/TableCellLink.vue'
import ContextMenu from '@/components/ContextMenu/ContextMenu.vue'
import AwardAccountCreateModal from '~/sections/AwardAccount/modals/AwardAccountCreateModal.vue'
import { Edit3Icon, PlusIcon } from '@zhuowenli/vue-feather-icons'
import type { ContextMenuOption } from '@/plugins/ContextMenuPlugin'
import AwardAccountInfoSideModal from '~/sections/AwardAccount/modals/AwardAccountInfoSideModal.vue'
import type { ModelAttributes, ModelIdentification } from '~types/lib/Model'
import { $confirm, $confirmDelete } from '@/plugins/ConfirmPlugin'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import AwardAccountAltVpnModal from '~/sections/AwardAccount/modals/AwardAccountAltVpn/AwardAccountAltVpnModal.vue'
import AwardAccountLinkWithAltVpnModal
    from '~/sections/AwardAccount/modals/AwardAccountLinkWithAltVpn/AwardAccountLinkWithAltVpnModal.vue'
import type { AwardAccountController } from '~/sections/AwardAccount/composable/useAwardAccountSearchController'
import SaleSelectModal from '~/modals/Sale/SaleSelectModal.vue'
import { SaleCategoryType } from '~/api/models/Sale/Sale'
import AwardAccountDeleteModal from '~/sections/AwardAccount/modals/AwardAccountDeleteModal.vue'

defineOptions({
    name: 'AwardAccountListSection',
})

//

const props = defineProps<{
    awardAccountController: AwardAccountController,
}>()

const {
    useModel,
    hasPermission,
} = useContext()

//

const suspense = useSuspensableComponent(async () => {
    await accountList.fetch()
    await props.awardAccountController.columnsController.syncColumns()
})

//

const accountList = useModel('AwardAccount').useList({
    where: (and) => props.awardAccountController.searchController.applyCondition(and),
    with: ['holder', 'credentials', 'parent', 'groupAccount', 'externalUserAgent'],
    sort: props.awardAccountController.sortController,
    pageSize: 14,
})

type Item = typeof accountList.records[0]

// Search Controller
props.awardAccountController.searchController.useList(accountList)
props.awardAccountController.searchController.tags.balance.setLimits(await getMilesMaxValue())

//

const accounts = computed(() => accountList.records)

const zebra = useZebraClasses(accounts, (account) => {
    switch (account.warning_flag) {
        case AwardAccountWarningFlag.DoNotUse:
        case AwardAccountWarningFlag.UnderRisk:
        case AwardAccountWarningFlag.Chargeback:
            return 'table-tr--highlighted-danger'
        case AwardAccountWarningFlag.CantUse:
            return 'table-tr--highlighted-warning'
    }
})

//

const eventService = useService('event')

onMounted(() => {
    eventService.on('awardAccountCreated', accountList.fetch.bind(accountList))
})
onUnmounted(() => {
    eventService.off('awardAccountCreated', accountList.fetch.bind(accountList))
})

let offAwardAccountDeleted: (() => void) | undefined = undefined

onMounted(() => {
    offAwardAccountDeleted = eventService.on('awardAccountDeleted', accountList.fetch.bind(accountList))
})
onUnmounted(() => {
    offAwardAccountDeleted?.()
})

// from row

const multilogin = useService('multilogin')

const addModal = useModal(AwardAccountCreateModal)
const externalUserAgentModel = useModel('ExternalUserAgent')

const isMultiloginLoading = ref(false)

const isAccountStatusAvailable = (account: ModelAttributes<'AwardAccount'>) => {
    const availableStatuses = [
        AwardAccountStatus.Active,
        AwardAccountStatus.InUse,
        AwardAccountStatus.Used,
        AwardAccountStatus.Leftover,
        AwardAccountStatus.Pending,
        AwardAccountStatus.Blocked,
    ]

    return availableStatuses.includes(account.status)
}

//

const menuOptions = (account: ModelAttributes<'AwardAccount'>) => {
    const statusActions = accountStatusDictionary.records.filter(status => {
        return account.status !== status.value
    }).map(status => {
        return {
            text: 'Move to: ' + status.title,
            onClick: () => changeStatus(account, status.value),
            icon: status.icon,
            class: `colored-text --${status.style}`,
        }
    })

    return [
        {
            text: 'Edit',
            icon: Edit3Icon,
            onClick: () => editModal(account).open(),
        },
        account.parent_award_account_pk ? undefined : {
            text: 'Create sub-account',
            icon: PlusIcon,
            onClick: () => addModal.open({ parent: account }),
        },
        ...statusActions,
    ].filter(Boolean) as ContextMenuOption[]
}

//

const milePriceProgramName = (account: ModelAttributes<'AwardAccount'>) => {
    return useGeneralDictionary('MilePriceProgram').find(account.mile_price_program_pk)?.name
}

const accountStatusDictionary = useGeneralDictionary('AwardAccountStatus')
const accountStatusInfo = (account: ModelAttributes<'AwardAccount'>) => accountStatusDictionary.find(account.status)

const accountTypeDictionary = useGeneralDictionary('AwardAccountType')
const accountTypeInfo = (account: ModelAttributes<'AwardAccount'>) => accountTypeDictionary.find(account.type)

const consolidatorDictionary = useGeneralDictionary('Consolidator')
const consolidatorAreaDictionary = useGeneralDictionary('ConsolidatorArea')

const consolidatorName = (account: ModelAttributes<'AwardAccount'>) => {
    if (!account.consolidator_pk) {
        return
    }

    return consolidatorDictionary.find(account.consolidator_pk)?.name
}

const areaName = (account: ModelAttributes<'AwardAccount'>) => {
    if (!account.consolidator_area_pk) {
        return
    }

    return consolidatorAreaDictionary.find(account.consolidator_area_pk)?.name
}

const bookingTypeDictionary = useGeneralDictionary('AwardAccountBookingType')

const bookingTypeFormatted = (account: ModelAttributes<'AwardAccount'>) => {
    const values = Object.values(AwardAccountBookingTypeField).map(field => {
        const value = field in account ? account[field as keyof typeof account] : undefined

        if (!value) {
            return
        }

        const bookingType = bookingTypeDictionary.find(field)

        if (!bookingType) {
            return
        }

        return bookingType.title
    }).filter(Boolean)

    if (values.length === Object.keys(AwardAccountBookingTypeField).length - 1) {
        return 'Any'
    }

    return values.join(', ')
}

//

const warningFlagDictionary = useGeneralDictionary('AwardAccountWarningFlag')

const getWarningFlagTooltip = (warningFlag: AwardAccountWarningFlag) => {
    const warningFlagInfo = warningFlagDictionary.find(warningFlag)

    if (!warningFlagInfo) {
        return
    }

    return warningFlagInfo.title
}

//

const editModal = (account: ModelAttributes<'AwardAccount'>) => useModal(AwardAccountCreateModal, { account: account })

//
const deleteAccountModal = useModal(AwardAccountDeleteModal)
const accountModal = useModal(AwardAccountInfoSideModal)

function openAccount(account: ModelIdentification) {
    // @todo Handle side modals via hash
    accountModal.open({ pk: usePk(account) })
}

function filterByAccountGroup(mainAccount: ModelAttributes<'AwardAccount'>) {
    props.awardAccountController.searchController.tags.group_account_number.setValues([mainAccount.account_number])
}

//
function markAccountToDelete(account: ModelAttributes<'AwardAccount'>) {
    deleteAccountModal.open({ accountPk: usePk(account) })
}

async function deleteAccount(account: ModelAttributes<'AwardAccount'>) {
    await $confirm({
        text: 'Are you sure you want to delete this account?',
        description: 'This action is irreversible. All associated data will be permanently removed.',
        confirmButton: 'Delete Permanently',
        cancelButton: 'Cancel',
    })

    await useModel('AwardAccount').actions.delete({
        pk: usePk(account),
    })

    toastSuccess('Account deleted')
    useService('event').emit('awardAccountDeleted')
}

const selectSaleModal = useModal(SaleSelectModal)
async function changeStatus(account: ModelAttributes<'AwardAccount'>, status: AwardAccountStatus) {
    await $confirm({
        text: 'Are you sure you want to change account status?',
        confirmButton: 'Change status',
    })

    if (status === AwardAccountStatus.BlockedAudit) {
        const selectedSale = await selectSaleModal.open({
            applyCondition: and => {
                and.or((or) => {
                    or.eq('sale_category', SaleCategoryType.Award)
                    or.eq('sale_category', SaleCategoryType.Mix)
                })
            },
        })

        if (selectedSale) {
            await useModel('AwardAccount').actions.markAsBlockedAudit({
                pk: usePk(account),
                sale_pk: usePk(selectedSale),
            })
        }
    } else {
        await useModel('AwardAccount').actions.changeStatus({
            pk: usePk(account),
            status,
        })
    }

    toastSuccess('Account status changed')
}

const openExternalUserAgentCreateModal = (awardAccountPk: PrimaryKey) => {
    useModal(AwardAccountAltVpnModal).open({
        awardAccountPk,
    })
}

const openExternalUserAgentUpdateModal = (externalUserAgentPk: PrimaryKey) => {
    useModal(AwardAccountAltVpnModal).open({
        externalUserAgentPk,
    })
}

const openExternalUserAgentLinkModal = async (account: ModelAttributes<'AwardAccount'>) => {
    const externalUserAgentPk = await useModal(AwardAccountLinkWithAltVpnModal).open({
        milePriceProgramPk: account.mile_price_program_pk,
    })

    if (externalUserAgentPk) {
        await externalUserAgentModel.actions.linkWithAwardAccount({
            external_user_agent_pk: externalUserAgentPk,
            award_account_pk: usePk(account),
        })
    }
}

const removeExternalUserAgent = async (externalUserAgentPk: PrimaryKey, awardAccountPk: PrimaryKey) => {
    const resp = await $confirmDelete({
        text: 'Are you sure you want to delete it?',
    })

    if (resp) {
        await externalUserAgentModel.actions.remove({ pk: externalUserAgentPk, award_account_pk: awardAccountPk })
    }
}

const openMultilogin = async (externalMultilogin: ModelAttributes<'ExternalUserAgent'>) => {
    isMultiloginLoading.value = true
    await multilogin.login(externalMultilogin)
    isMultiloginLoading.value = false
}

async function getMilesMaxValue(): Promise<[number, number]> {
    const maxValue = await useModel('AwardAccount').actions.getMilesMaxValue({})

    return [0, maxValue]
}
</script>
