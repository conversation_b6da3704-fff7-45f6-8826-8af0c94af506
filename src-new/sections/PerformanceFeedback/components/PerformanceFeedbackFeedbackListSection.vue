<template>
    <SuspenseManual :state="suspense">
        <template #default>
            <teleport to="#performance-feedback-lists-pagination">
                <div class="flex items-center gap-x-4 pb-2.5">
                    <AppPaginationCompact :pagination="pagination" />
                    <AppPageSize :pagination="pagination" />
                </div>
            </teleport>

            <teleport to="#performance-feedback-lists-pagination-under-list">
                <AppTablePagination class="my-4 px-6" :pagination="pagination" />
            </teleport>

            <AppTable
                :columns="columns"
                :items="performanceFeedbackRecords"
                :sort-controller="sortController"
                :search-tags="searchController.tags"
                class="--fixed-header"
                zebra
            >
                <template #body>
                    <tr v-for="(feedback, index) in performanceFeedbackRecords" :key="index">
                        <td>
                            <div class="flex gap-2 items-center">
                                <AppAvatar
                                    :model="feedback.agent"
                                    class="w-6 h-6"
                                />
                                <div>
                                    <div class="text-[10px] text-secondary-400">
                                        {{ getAgentPosition(feedback.agent) }}
                                    </div>
                                    <div class="text-xs">
                                        {{ getFullName(feedback.agent) }}
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td>
                            {{ $format.datetime(feedback.created_at, 'UTC' ,{ full: true }) }}
                        </td>
                        <td>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-x-1">
                                    <div
                                        v-for="star in 5"
                                        :key="star"
                                        class="text-secondary-300 cursor-pointer"
                                        :class="{
                                            'feedback-selected-rate--primary': star <= feedback.rating,
                                            'feedback-selected-rate--danger': star <= feedback.rating && feedback.rating <= 3
                                        }"
                                    >
                                        <StarIcon class="!h-3.5 !w-3.5 !stroke-0.5 " />
                                    </div>
                                </div>
                                <div>
                                    <span
                                        class="inline-block text-primary dark:text-primary-400 text-center min-w-[8px]"
                                        :class="{
                                            '!text-danger-500': feedback.rating <= 3
                                        }"
                                    >{{ feedback.rating }}</span><span class="text-secondary-400"> of 5</span>
                                </div>
                            </div>
                        </td>
                        <td class="max-w-[1260px] whitespace-normal">
                            {{ feedback.remark }}
                        </td>
                    </tr>
                </template>
            </AppTable>
        </template>
        <template #fallback>
            <PlaceholderBlock class="w-full h-[500px]" />
        </template>
    </SuspenseManual>
</template>

<script setup lang="ts">
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import SortController from '~/lib/Search/SortController'
import { getFullName } from '~/lib/Helper/PersonHelper'
import type { ModelAttributes } from '~types/lib/Model'

const { useModel, useDictionary } = useContext()

const suspense = useSuspensableComponent(async () => {
    await performanceFeedbackList.fetch()
})

const performanceFeedbackModel = useModel('PerformanceFeedback')

//

const positionDictionary = useDictionary('Position')

const getAgentPosition = (agent: ModelAttributes<'Agent'>) => {
    return positionDictionary.find(agent.position_pk)?.name
}

const searchController = inject('feedbackSearchController')

const columns = useTableColumns({
    agent_pk: {
        sortable: true,
        label: 'Employee name',
        width: 25,
    },
    created_at: {
        sortable: true,
        label: 'Created at',
        width: 20,
    },
    rating: {
        sortable: true,
        label: 'Rating',
        width: 24,
    },
    remark: {
        sortable: true,
        label: 'Comment',
    },
})
const sortController = SortController.fromColumns(columns, { })

const performanceFeedbackList = performanceFeedbackModel.useList({
    with: ['agent'],
    where: (and) => {
        searchController.applyCondition(and)
    },
    sort: sortController,
    pageSize: 20,
})

searchController.useList(performanceFeedbackList)

const performanceFeedbackRecords = computed(() => {
    return performanceFeedbackList.records
})

const pagination = computed(() => {
    return performanceFeedbackList.pagination
})

//

const eventService = useService('event')

onMounted(() => {
    eventService.on('performanceFeedbackCreated', suspense.fetch)
})
onUnmounted(() => {
    eventService.off('performanceFeedbackCreated', suspense.fetch)
})
</script>

<style scoped>
.feedback-selected-rate--primary {
    svg {
        fill: #3B76F6;
    }
}

.feedback-selected-rate--danger {
    svg {
        fill: #F04438;
    }
}
</style>
