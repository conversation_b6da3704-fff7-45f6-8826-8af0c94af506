<template>
    <div class="w-full pt-1 pb-1 relative max-w-[1430px] mx-auto">
        <div class="w-full h-[250px] relative ">
            <ChartComponent
                :key="chartKey"
                :chart-data="chartData"
                :options="chartOptions"
                class="w-full h-full z-[9]"
            />
            <div ref="tooltipPortal" class="tooltip-portal absolute top-0 left-0 w-full h-full z-50 pointer-events-none" />
        </div>
        <div class="absolute left-[-20px] bottom-[2px] h-full pt-3">
            <div class="flex flex-col justify-between font-medium text-2xs text-secondary h-full">
                <div
                    v-for="i in 6"
                    :key="i"
                    class="w-[30px] text-center"
                >
                    {{ 6 - i }}
                </div>
            </div>
        </div>
        <div
            class="absolute left-[8px] bottom-[-16px] w-full"
        >
            <div class="flex justify-between font-medium text-2xs text-secondary">
                <div
                    v-for="(label, index) in chartData.labels"
                    :key="index"
                    class="relative flex justify-center w-[20px] group "
                >
                    <div class="inline-flex items-center gap-x-1 hover:bg-primary-50 rounded px-1  pl-2 py-0.5" @click="openCreateEventModal(label, index)">
                        <span v-if="label === '12 AM'" class="cursor-pointer text-center whitespace-nowrap">
                            12 <span class="text-primary-600">AM</span>
                        </span>
                        <span v-else-if="label === '12 PM'" class="cursor-pointer text-center whitespace-nowrap">
                            12 <span class="text-primary-600">PM</span>
                        </span>
                        <span v-else class="cursor-pointer text-center whitespace-nowrap">
                            {{ label }}
                        </span>
                        <AppButton class="!w-[16px] !h-[16px] text-white rounded opacity-0 scale-90 transition-all duration-200 ease-out group-hover:opacity-100 group-hover:scale-100 flex items-center justify-center --only --primary">
                            <PlusIcon class="h-[8px] w-[8px]" />
                        </AppButton>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { ChartDataset } from 'chart.js'
import {
    Chart as ChartJS,
    LineElement,
    LineController,
    PointElement,
    LinearScale,
    CategoryScale,
    Tooltip,
    Filler,
} from 'chart.js'
import { defineChartComponent } from 'vue-chart-3'
import { h, render } from 'vue'
import PerformanceFeedbackStatisticTooltip
    from '~/sections/PerformanceFeedback/components/Tooltip/PerformanceFeedbackStatisticTooltip.vue'
import PerformanceFeedbackCreateEventModal from '~/sections/PerformanceFeedback/modals/PerformanceFeedbackCreateEventModal.vue'
import type { RangeType } from '~/sections/PerformanceFeedback/composable/useChartAggregation'
import { useChartAggregation } from '~/sections/PerformanceFeedback/composable/useChartAggregation'
import { type DateRange, getRangeThisMonth, getRangeThisWeek, getRangeToday } from '@/lib/core/helper/DateHelper'
import type { ModelAttributes } from '~types/lib/Model'

const props = defineProps<{
    chartSelectedRange: RangeType,
    selectedCustomRangeValue: DateRange | undefined,
    feedbackRecords: ModelAttributes<'PerformanceFeedback'>[],
    eventRecords: ModelAttributes<'PerformanceFeedbackEvent'>[],
    chartDataIsUpdated: boolean,
    // eslint-disable-next-line
    changeChartDataUpdatedStatus?: () => void
}>()

const performanceFeedbackRecords = computed(() => props.feedbackRecords)
const performanceFeedbackEventRecords = computed(() => props.eventRecords)

const getLabels = () => {
    if (selectedRange.value === 'custom' && props.selectedCustomRangeValue) {
        return generateLabels(getRangeForLabelFromCustomRange())
    }

    return generateLabels(selectedRange.value)
}

//

const format = useService('formatter')

// chart range

const selectedRange = computed(() => {
    return props.chartSelectedRange
})

//

const { aggregate, generateLabels, getDateFromLabel } = useChartAggregation()

// Register chart.js components

const verticalHoverLinePlugin = {
    id: 'verticalHoverLine',
    afterDraw(chart) {
        const tooltip = chart.tooltip

        if (!tooltip?.dataPoints?.length || tooltip.opacity === 0) return

        const ctx = chart.ctx
        const { x, y } = tooltip.dataPoints[0].element
        const bottomY = chart.chartArea.bottom

        ctx.save()
        ctx.beginPath()
        ctx.setLineDash([3, 3])
        ctx.moveTo(x, y)
        ctx.lineTo(x, bottomY)
        ctx.lineWidth = 1
        ctx.strokeStyle = isDark.value ? '#E2E8F0' : '#94A3B8'
        ctx.stroke()
        ctx.restore()
    },
}

ChartJS.register(
    LineElement,
    LineController,
    PointElement,
    LinearScale,
    CategoryScale,
    Tooltip,
    Filler,
    verticalHoverLinePlugin,
)

const ChartComponent = defineChartComponent('LineChartSelf', 'line')

// chart tooltip

const tooltipEl = ref<HTMLElement | null>(null)
const tooltipPortal = ref<HTMLElement | null>(null)
let hideTimeout: ReturnType<typeof setTimeout> | null = null

function renderCustomTooltip({ chart, tooltip }) {
    if (!tooltipPortal.value) return

    if (!tooltip || tooltip.opacity === 0) {
        if (tooltipEl.value) {
            if (hideTimeout) clearTimeout(hideTimeout)
            hideTimeout = setTimeout(() => {
                tooltipEl.value!.style.display = 'none'
            }, 100)
        }

        return
    }

    const tooltipData = tooltip.dataPoints?.[0]

    if (!tooltipData) return

    const index = tooltipData.dataIndex
    const events = eventByIndex.value?.[index] ?? []
    const averageRating = chart.data.datasets[0]?.data?.[index] ?? null

    if (!tooltipEl.value) {
        tooltipEl.value = document.createElement('div')
        tooltipEl.value.className = 'custom-chart-tooltip'
        tooltipEl.value.style.position = 'absolute'
        tooltipEl.value.style.pointerEvents = 'auto'
        tooltipPortal.value?.appendChild(tooltipEl.value)

        tooltipEl.value.onmouseenter = () => {
            if (hideTimeout) clearTimeout(hideTimeout)
        }

        tooltipEl.value.onmouseleave = () => {
            hideTimeout = setTimeout(() => {
                tooltipEl.value!.style.display = 'none'
            }, 100)
        }
    }

    const vnode = h(PerformanceFeedbackStatisticTooltip, {
        events,
        format,
        rating: averageRating,
    })
    render(vnode, tooltipEl.value)

    nextTick(() => {
        const canvasRect = chart.canvas.getBoundingClientRect()
        const tooltipRect = tooltipEl.value!.getBoundingClientRect()

        const pointX = tooltip.caretX
        const pointY = tooltip.dataPoints?.[0]?.element?.y ?? tooltip.caretY

        const left = canvasRect.left + window.pageXOffset + pointX + 6
        const top = window.pageYOffset + pointY - tooltipRect.height / 2

        tooltipEl.value!.style.display = 'block'
        tooltipEl.value!.style.left = `${left}px`
        tooltipEl.value!.style.top = `${top}px`

        tooltipEl.value!.classList.add('right-arrow')
        tooltipEl.value!.classList.remove('left-arrow')
    })
}

//

//

const todayRange = getRangeToday()
const thisWeekRange = getRangeThisWeek()
const thisMonthRange = getRangeThisMonth()

function getRangeForLabelFromCustomRange() {
    const selectedRangeTimestamp = props.selectedCustomRangeValue ? props.selectedCustomRangeValue.start.unixTimestamp() : undefined

    //todo refactor with usage of DateHelper
    if (selectedRangeTimestamp < thisMonthRange.start.unixTimestamp()) {
        return 'year'
    } else if (selectedRangeTimestamp < thisWeekRange.start.unixTimestamp()) {
        return 'month'
    } else if (selectedRangeTimestamp < todayRange.start.unixTimestamp()) {
        return 'week'
    } else if (selectedRangeTimestamp > todayRange.start.unixTimestamp()) {
        return 'day'
    } else {
        return 'week'
    }
}

//

const THRESHOLD = 3
const { isDark } = useDarkMode()

const chartOptions = ref({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: { display: false },
        tooltip: {
            enabled: false,
            external: renderCustomTooltip,
        },
    },
    animation: false,
    scales: {
        y: {
            min: 0,
            max: 5.2,
            ticks: {
                display: false,
                stepSize: 1,
                precision: 0,
                callback: (value: number) => {
                    return value === 5.2 ? '5' : value.toString()
                },
            },
            grid: {
                drawBorder: false,
                drawTicks: false,
                color: isDark.value ? '#373F55' : '#E5E7EB',
            },
        },
        x: {
            ticks: {
                display: false,
            },
            grid: {
                display: false,
                drawBorder: false,
                drawTicks: false,
            },
        },
    },
})

// chart data

const eventByIndex = ref<Record<number, any[]>>({})

const chartData = ref({
    labels: [],
    datasets: [],
})

function createFeedbackDataset(data: number[]): ChartDataset<'line'> {
    return {
        label: 'Feedback Ratings',
        data,
        borderColor: '#4285f4',
        borderWidth: 1.5,
        fill: true,
        tension: 0,
        backgroundColor: isDark.value ? 'rgba(0, 0, 0, 0.2)' : 'rgba(235, 237, 243, 0.3)',
        segment: {
            borderColor: ctx => {
                const { p0, p1, chart } = ctx
                const y0 = p0.parsed.y
                const y1 = p1.parsed.y

                const blueLineColor = isDark.value ? '#6090FA' : '#3B76F6'
                const redLineColor = isDark.value ? '#B42318' : '#FDA29B'

                if ((y0 >= THRESHOLD && y1 >= THRESHOLD) || (y0 < THRESHOLD && y1 < THRESHOLD)) {
                    return y0 >= THRESHOLD ? blueLineColor : redLineColor
                }

                const gradient = chart.ctx.createLinearGradient(p0.x, p0.y, p1.x, p1.y)
                const ratio = Math.abs((THRESHOLD - y0) / (y1 - y0))

                if (y0 > y1) {
                    gradient.addColorStop(0, blueLineColor)
                    gradient.addColorStop(Math.max(0, ratio), blueLineColor)
                    gradient.addColorStop(Math.min(1, ratio), redLineColor)
                    gradient.addColorStop(1, redLineColor)
                } else {
                    gradient.addColorStop(0, redLineColor)
                    gradient.addColorStop(Math.max(0, ratio), redLineColor)
                    gradient.addColorStop(Math.min(1, ratio), blueLineColor)
                    gradient.addColorStop(1, blueLineColor)
                }

                return gradient
            },
            borderWidth: () => 1.5,
        },
        pointRadius: 0,
        pointHoverRadius: 5,
        order: 1,
    }
}

const resolvedRange = computed(() => {
    return selectedRange.value === 'custom' && props.selectedCustomRangeValue
        ? getRangeForLabelFromCustomRange()
        : selectedRange.value
})

function getEventDataset(data: (number | null)[], colorMap: Record<number, string>, dark: boolean): ChartDataset<'line'> {
    const resolveColor = (index: number): string => {
        return colorMap.hasOwnProperty(index)
            ? colorMap[index]
            : (dark ? '#D3D7E4' : '#293145')
    }

    return {
        id: `events-${Date.now()}`,
        label: `Events-${Date.now()}`,
        data,
        type: 'line',
        pointRadius: 4,
        borderWidth: 0,
        showLine: false,
        fill: false,
        order: 0,
        pointStyle: 'circle',
        pointBackgroundColor: (ctx) => {
            const index = ctx.dataIndex ?? 0

            return resolveColor(index)
        },
        pointHoverBackgroundColor: (ctx) => {
            const index = ctx.dataIndex ?? 0

            return resolveColor(index)
        },
        hitRadius: 6,
        hoverRadius: 6,
    }
}

function getPointOnlyDataset(data: (number | null)[]): ChartDataset<'line'> {
    return {
        id: 'point-only',
        label: 'Black points on hover',
        data,
        type: 'line',
        pointRadius: 0,
        pointHoverRadius: 5,
        pointBackgroundColor: '#000000',
        pointHoverBackgroundColor: '#000000',
        borderWidth: 0,
        showLine: false,
        fill: false,
        order: 0,
        pointStyle: 'circle',
        hitRadius: 8,
    }
}

const chartKey = ref(0)

async function updateChartData() {
    const labels = getLabels()
    const bucketCount = labels.length
    const feedbackData = structuredClone(
        aggregate(performanceFeedbackRecords.value, resolvedRange.value, bucketCount),
    )
    const result: (number | null)[] = structuredClone(Array(bucketCount).fill(null))
    const map: Record<number, any[]> = {}
    const colorMap: Record<number, string> = {}

    for (const event of performanceFeedbackEventRecords.value) {
        const date = new Date(event.event_date * 1000)
        const index = getBucketIndexForDate(resolvedRange.value, date)

        if (index !== null && index >= 0 && index < bucketCount) {
            result[index] = feedbackData[index] ?? null
            map[index] ??= []
            map[index].push(event)
        }
    }

    eventByIndex.value = map

    for (const [indexStr, events] of Object.entries(map)) {
        const index = Number(indexStr)
        colorMap[index] = events[0]?.marker_hex ?? (isDark.value ? '#D3D7E4' : '#293145')
    }

    const pointOnlyData: (number | null)[] = feedbackData.map((value, index) =>
        map[index] ? null : value,
    )

    chartData.value = {
        labels: [...labels],
        datasets: [
            createFeedbackDataset([...feedbackData]),
            getEventDataset([...result], { ...colorMap }, isDark.value),
            getPointOnlyDataset([...pointOnlyData]),
        ],
    }

    await nextTick()
    chartKey.value++
}

function getBucketIndexForDate(range: RangeType, date: Date): number | null {
    if (range === 'year') return date.getMonth()

    if (range === 'month') return date.getDate() - 1

    if (range === 'week') return (date.getDay() + 6) % 7

    if (range === 'day') return date.getHours()

    return null
}

// create event

const createEventModal = useModal(PerformanceFeedbackCreateEventModal)

function openCreateEventModal(label?: string | number, index?: number) {
    let date

    if (label !== undefined && index !== undefined) {
        date = getDateFromLabel(selectedRange.value, label, index)
    }
    createEventModal.open({
        eventDate: date ? date : undefined,
    })
}

//

watchEffect(() => {
    if (!props.chartDataIsUpdated) {
        updateChartData()
        props.changeChartDataUpdatedStatus?.()
    }
})

watch(() => props.feedbackRecords, () => {
    updateChartData()
}, {
    deep: true,
})

//

onMounted(() => {
    tooltipPortal.value = document.querySelector('.tooltip-portal')
    updateChartData()
})
</script>

<style scoped>
.custom-chart-tooltip {
    z-index: 1000;
    pointer-events: none;
    position: absolute;
}

.custom-chart-tooltip::before {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    border: 6px solid transparent;
}

.custom-chart-tooltip.right-arrow::before {
    left: -12px;
    border-right-color: white;
}
</style>
