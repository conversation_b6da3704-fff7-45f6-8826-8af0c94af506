<template>
    <SuspenseManual :state="suspense">
        <template #default>
            <teleport to="#performance-feedback-lists-pagination">
                <div class="flex items-center gap-x-4 pb-2.5">
                    <AppPaginationCompact :pagination="pagination" />
                    <AppPageSize :pagination="pagination" />
                </div>
            </teleport>
            <teleport to="#performance-feedback-lists-pagination-under-list">
                <AppTablePagination class="my-4 px-6" :pagination="pagination" />
            </teleport>
            <AppTable
                :columns="columns"
                :items="performanceFeedbackEventRecords"
                :sort-controller="sortController"
                :search-tags="searchController.tags"
                class="--fixed-header"
                zebra
            >
                <template #body>
                    <tr v-for="(event, index) in performanceFeedbackEventRecords" :key="index">
                        <td>
                            {{ $format.datetime(event.event_date, 'UTC' ,{ full: true }) }}
                        </td>
                        <td>
                            {{ event.title }}
                        </td>
                        <td>
                            <div class="flex items-center justify-between">
                                <div
                                    class="rounded-full h-[10px] w-[10px]"
                                    :style="{ backgroundColor: event.marker_hex }"
                                />
                                <div class="text-secondary dark:text-secondary-300">
                                    {{ colorHexMap[event.marker_hex] }}
                                </div>
                            </div>
                        </td>
                        <td class="whitespace-normal">
                            {{ event.description }}
                        </td>
                        <td>
                            <div class="flex gap-x-2">
                                <AppButton class="--only" @click="editEvent(usePk(event))">
                                    <EditIcon />
                                </AppButton>
                                <AppButton class="--only" @click="deleteEvent(usePk(event))">
                                    <TrashIcon />
                                </AppButton>
                            </div>
                        </td>
                    </tr>
                </template>
            </AppTable>
        </template>
        <template #fallback>
            <PlaceholderBlock class="w-full h-[500px]" />
        </template>
    </SuspenseManual>
</template>

<script lang="ts">
declare global {
    interface ApplicationEvents {
        performanceEventUpdated(): void
    }
}

export const colorHexMap = {
    '#3B76F6': 'Blue',
    '#17B26A': 'Green',
    '#F04438': 'Red',
    '#ED9814': 'Yellow',
    '#F97316': 'Orange',
    '#5DA8E8': 'Light blue',
    '#616F96': 'Gray',
}
</script>

<script setup lang="ts">
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import SortController from '~/lib/Search/SortController'
import PerformanceFeedbackCreateEventModal
    from '~/sections/PerformanceFeedback/modals/PerformanceFeedbackCreateEventModal.vue'
import { $confirm } from '@/plugins/ConfirmPlugin'

const { useModel } = useContext()

const suspense = useSuspensableComponent(async () => {
    await performanceFeedbackEventList.fetch()
})

const performanceFeedbackEventModel = useModel('PerformanceFeedbackEvent')

//

const columns = useTableColumns({
    event_date: {
        sortable: true,
        label: 'Event date',
        width: 30,
    },
    title: {
        sortable: true,
        label: 'Event name',
        width: 35,
    },
    marker_hex: {
        sortable: true,
        label: 'Color marker',
        width: 18,
    },
    description: {
        sortable: false,
        label: 'Description',
    },
    actions: {
        sortable: false,
        label: '',
        width: 5,

    },
})

const sortController = SortController.fromColumns(columns, { })

const searchController = inject('eventsSearchController')

const performanceFeedbackEventList = performanceFeedbackEventModel.useList({
    where: (and) => {
        searchController.applyCondition(and)
    },
    sort: sortController,
    pageSize: 20,
})

searchController.useList(performanceFeedbackEventList)

const performanceFeedbackEventRecords = computed(() => {
    return performanceFeedbackEventList.records
})

const pagination = computed(() => {
    return performanceFeedbackEventList.pagination
})

//

const performanceFeedbackEventModal = useModal(PerformanceFeedbackCreateEventModal)

function editEvent(pk: PrimaryKey) {
    performanceFeedbackEventModal.open({
        eventPk: pk,
    })
}

async function deleteEvent(pk: PrimaryKey) {
    await $confirm('Are you sure you want to delete this event?')

    await performanceFeedbackEventModel.actions.deleteEvent({
        pk: pk,
    })

    useService('event').emit('performanceEventUpdated')
}

//

const eventService = useService('event')

onMounted(() => {
    eventService.on('performanceEventUpdated', suspense.fetch)
})
onUnmounted(() => {
    eventService.off('performanceEventUpdated', suspense.fetch)
})
</script>
