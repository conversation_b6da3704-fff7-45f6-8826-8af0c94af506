import SearchController from '~/lib/Search/SearchController'
import SelectSearchTag from '~/lib/Search/Tag/SelectSearchTag'
import DateRangeSearchTag from '~/lib/Search/Tag/DateRangeSearchTag'
import NumberRangeSearchTag from '~/lib/Search/Tag/NumberRangeSearchTag'
import TextSearchTag from '~/lib/Search/Tag/TextSearchTag'
import { colorHexMap } from '~/sections/PerformanceFeedback/components/PerformanceFeedbackEventsListSection.vue'
import type { DateRange } from '@/lib/core/helper/DateHelper'
import { predefinedRanges } from '@/lib/core/helper/DateHelper'

export function usePerformanceSearchStore() {
    const {  useDictionary } = useContext()

    const agentDictionary = useDictionary('Agent')

    const agentForSelectOptions = computed(() => {
        return agentDictionary.mapAllRecords.forSelect()
    })

    const selectedRange = ref<'year' | 'month' | 'week' | 'day' | 'custom'>('month')
    const selectedCustomRangeValue = ref<DateRange>()

    let ignoreListSearch = true

    const rangeDateSearchMap = {
        'thisMonth': 'month',
        'thisYear': 'year',
        'thisWeek': 'week',
        'today': 'day',
    }

    const chartSearchController = SearchController.forModel('PerformanceFeedback', {
        agent_pk: new SelectSearchTag('Agent', []),
        created_at: new DateRangeSearchTag('Created at').setDefaultValue(['thisMonth']),
        rating: new NumberRangeSearchTag('Rating', [1, 5]),
        remark: new TextSearchTag('Comment'),
    }, {
        syncWithQuery: false,
    })

    const feedbackSearchController = SearchController.forModel('PerformanceFeedback', {
        agent_pk: new SelectSearchTag('Employee name', agentForSelectOptions),
        created_at: new DateRangeSearchTag('Created at').setDefaultValue(['thisMonth']),
        rating: new NumberRangeSearchTag('Rating', [1, 5]),
        remark: new TextSearchTag('Comment'),
    }, {
        syncWithQuery: false,
    })

    const eventsSearchController = SearchController.forModel('PerformanceFeedbackEvent', {
        event_date: new DateRangeSearchTag('Event date').setDefaultValue(['thisMonth']),
        marker_hex: new SelectSearchTag('Marker', Object.entries(colorHexMap).map(([value, title]) => ({ value, title }))),
        title: new TextSearchTag('Event name'),
        description: new TextSearchTag('Description'),
    }, {
        syncWithQuery: false,
    })

    const syncControllers = (dateRange: DateRange) => {
        chartSearchController.tags.created_at.setValue(dateRange)
        eventsSearchController.tags.event_date.setValue(dateRange)
        feedbackSearchController.tags.created_at.setValue(dateRange)

        ignoreListSearch = true
    }

    feedbackSearchController.useChangeHandler(() => {
        const tags = feedbackSearchController.tags

        chartSearchController.tags.agent_pk.setValues(tags.agent_pk.values ?? [])
        chartSearchController.tags.rating.setValues(tags.rating.values ?? [])
        chartSearchController.tags.remark.setValues(tags.remark.values ?? [])

        if (ignoreListSearch) {
            ignoreListSearch = false

            return
        }

        if (tags.created_at.values) {
            const dateRange = tags.created_at.values[0]
            chartSearchController.tags.created_at.setValue(dateRange)
            eventsSearchController.tags.event_date.setValue(dateRange)

            selectedCustomRangeValue.value = predefinedRanges[dateRange].resolver('') ?? dateRange
            selectedRange.value = rangeDateSearchMap[dateRange] ?? 'custom'
        }
    })

    return {
        chartSearchController,
        feedbackSearchController,
        eventsSearchController,
        syncControllers,
        selectedRange,
        selectedCustomRangeValue,
    }
}
