<template>
    <AppModalWrapper class="!w-[420px] !rounded-xl border border-secondary-100">
        <template #default>
            <div class="flex flex-col gap-y-2.5 p-4 pb-2.5">
                <div class="">
                    <div class="flex flex-col items-center gap-y-4">
                        <div class="tip --primary p-0 border-none bg-white dark:bg-dark-3">
                            <AppTipIcon type="primary" />
                        </div>
                        <div class="flex flex-col items-center gap-y-1">
                            <h4 class="text-base m-0">
                                Rate your experience
                            </h4>
                            <p class="text-sm text-secondary m-0">
                                How satisfied are you with the system’s performance?
                            </p>
                        </div>
                        <FormField
                            :form="form"
                            field="rating"
                            class="flex flex-col items-center"
                        >
                            <div class="flex items-center gap-x-1">
                                <div
                                    v-for="star in 5"
                                    :key="star"
                                    class="text-secondary-300 cursor-pointer"
                                    :class="{
                                        'feedback-selected-rate--primary': hoverRating ? hoverRating > 3 && star <= hoverRating : star <= form.data.rating && (form.data.rating > 3 && form.data.rating > 0),
                                        'feedback-selected-rate--danger': hoverRating ? hoverRating <= 3 && star <= hoverRating : star <= form.data.rating && (form.data.rating <= 3 && form.data.rating > 0),
                                    }"
                                    @click="setRating(star)"
                                    @mouseover="hoverRating = star"
                                    @mouseleave="hoverRating = 0"
                                >
                                    <StarIcon class="h-8 w-8 !stroke-0.5 " />
                                </div>
                            </div>
                        </FormField>
                    </div>
                </div>
                <hr class="w-full">
                <div class="flex flex-col gap-y-2">
                    <div class="flex items-center justify-between cursor-pointer" @click="toggleReviewBlock">
                        <p class="m-0 font-medium text-xs">
                            Write your review <span class="text-secondary">({{ reviewLabel }})</span>
                        </p>
                        <AppButton
                            class="--only --small dark:bg-dark-2 dark:text-secondary-100"
                            :class="{
                                'transform rotate-180': reviewIsOpened,
                            }"
                        >
                            <ChevronUpIcon />
                        </AppButton>
                    </div>
                    <div v-if="reviewIsOpened" class="flex flex-col gap-y-2">
                        <FormField
                            :form="form"
                            field="remark"
                        >
                            <InputTextarea
                                v-model="form.data.remark"
                                placeholder="Remark"
                                :maxlength="250"
                            />
                        </FormField>
                        <span class="text-secondary dark:text-secondary-300 text-xs">Your feedback is important to help us improve the system.</span>
                    </div>
                </div>
            </div>
            <hr class="w-full">
            <div class="px-4 py-3 dark:border-t dark:border-dark-1">
                <div class="w-full flex items-center justify-between">
                    <FormField
                        :form="form"
                        field="dont_show_again"
                    >
                        <label class="text-xs flex items-center gap-x-2">
                            <InputCheckbox v-model="form.data.dont_show_again" class="dark:border-secondary-300" />
                            <span class="cursor-pointer select-none text-xs text-secondary dark:text-secondary-300">Don't show again today</span>
                        </label>
                    </FormField>
                    <div class="flex gap-x-3">
                        <AppButton @click="close">
                            Cancel
                        </AppButton>
                        <AppButton
                            class="--primary"
                            :loading="formLoading"
                            @click="submit"
                        >
                            Confirm
                        </AppButton>
                    </div>
                </div>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script lang="ts">
declare global {
    interface ApplicationEvents {
        performanceFeedbackCreated(): void
    }
}
</script>

<script setup lang="ts">
import FormField from '~/components/Form/FormField.vue'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'

defineOptions({
    name: 'PerformanceFeedbackReportModal',
    modal: {
        position: 'right-bottom',
        overlay: false,
        promise: true,
    },
})

const props = defineProps<{
    // eslint-disable-next-line
    onSubmit?: () => void
    // eslint-disable-next-line
    onManualClose?: () => void
    // eslint-disable-next-line
    onDontShowAgain?: () => void
}>()

const emit = defineEmits<{
    close: [],
}>()

const { useModel } =  await useNewContext('selected')

const reviewIsOpened = ref(false)
const hoverRating = ref(0)

const form = useForm<{
    dont_show_again: boolean,
    rating: number,
    remark: string,
}>({
    dont_show_again: false,
    rating: 0,
    remark: '',
}, {
    rating: [
        ValidationRules.RequiredWhen((): boolean => !form.data.dont_show_again),
        () => {
            if (!form.data.dont_show_again && form.data.rating <= 3 && form.data.remark.trim().length < 6) {
                return 'Help us improve – please leave a short comment.'
            }
        },
    ],
    remark: [
        ValidationRules.RequiredWhen((): boolean => !form.data.dont_show_again && form.data.rating <= 3),
        () => {
            if (!form.data.dont_show_again && form.data.rating <= 3 && form.data.remark && form.data.remark.trim().length < 6) {
                return 'Remark should be more than 6 symbols.'
            }
        },
    ],
})

function close() {
    if (form.data.dont_show_again) {
        props.onDontShowAgain?.()
    } else {
        props.onManualClose?.()
    }
}

function setRating(rating: number) {
    form.data.rating = rating
}

function toggleReviewBlock() {
    reviewIsOpened.value = !reviewIsOpened.value
}

const performanceFeedbackModel = useModel('PerformanceFeedback')

const submit = form.useSubmit(async (data) => {
    if (data.dont_show_again) {
        props.onDontShowAgain?.()

        return
    }
    await performanceFeedbackModel.actions.leaveFeedback({
        rating: data.rating,
        remark: data.remark,
    })
    useService('event').emit('performanceFeedbackCreated')

    props.onSubmit?.()
})

const formLoading = computed(() => {
    return form.loading.value
})

watch(() => form.data.rating, (value) => {
    if (value <= 3) {
        reviewIsOpened.value = true
        form.errors.add({ rating: ['Help us improve – please leave a short comment.']})
    } else {
        reviewIsOpened.value = false
        form.errors.clear('rating')
    }
})

const reviewLabel = computed(() => {
    if (form.data.rating && form.data.rating <= 3) {
        return 'required'
    }

    return 'optional'
})
</script>

<style scoped>
.feedback-selected-rate--primary {
    svg {
        fill: #3B76F6;
    }
}

.feedback-selected-rate--danger {
    svg {
        fill: #F04438;
    }
}
</style>
