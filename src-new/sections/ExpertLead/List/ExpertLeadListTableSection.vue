<template>
    <div>
        <div class="card">
            <div class="flex items-center justify-end gap-4 w-full">
                <SearchPresetsList class="mr-auto" :search-controller="searchController" />

                <div class="flex items-center gap-4 p-2 flex-none">
                    <AppPaginationInfo :pagination="leadsList.pagination" />
                    <AppPaginationCompact :pagination="leadsList.pagination" />
                    <AppButton
                        v-if="hasPermission('viewExpertProcessingQueue','Lead')"
                        class="--small"
                        @click="openExpertsQueueProcessingModal"
                    >
                        <ListIcon />
                        Processing queue
                    </AppButton>
                    <AppPageSize :pagination="leadsList.pagination" :options="[10, 19, 40, 100]" />
                </div>
            </div>
            <div class="list-table-v2 table-fixed relative">
                <SuspenseManual :state="suspense">
                    <AppTable
                        :items="items"
                        :columns="columns"
                        :sort-controller="sortController"
                        :search-tags="searchController.tags"
                        zebra
                    >
                        <template #body>
                            <tr
                                v-for="leadRecord in items"
                                :key="usePk(leadRecord)"
                                :class="{
                                    'table-tr--highlighted-danger' : leadRecord?.expertLeadInfo.is_duplicate,
                                    'border border-danger': leadRecord.is_test
                                }"
                            >
                                <TableCellLink :to="linkToExpertLead(usePk(leadRecord))">
                                    {{ usePk(leadRecord) }}

                                    <div
                                        v-if="leadRecord.is_test"
                                        class="absolute top-0.5 right-0.5 bg-danger rounded text-3xs w-[fit-content] text-white font-semibold px-1.5 leading-normal self-center ml-2"
                                    >
                                        Test
                                    </div>
                                </TableCellLink>

                                <td>
                                    {{ leadRecord.from_iata_code }}
                                </td>
                                <td>
                                    {{ leadRecord.to_iata_code }}
                                </td>
                                <td>
                                    {{ $format.date(leadRecord.departure_date, 'UTC', { full:true }) }}
                                </td>
                                <td>
                                    {{ $format.date(leadRecord.return_date, 'UTC', { full:true }) || 'oneWay' }}
                                </td>
                                <td>
                                    <span v-if="leadRecord.expertLeadInfo.requestedBy" class="text-blue-500">
                                        {{ getFullName(leadRecord.expertLeadInfo.requestedBy) }}
                                    </span>
                                </td>
                                <td v-if="workspaces.length > 1" class="text-center">
                                    <WorkspaceLabelComponent short :project-pk="leadRecord.project_pk" />
                                </td>
                                <td>
                                    {{ $format.datetime(leadRecord.created_at) }}
                                </td>
                                <td>
                                    {{ $format.datetime(leadRecord.expertLeadInfo.expert_request_start_at) }}
                                </td>
                                <TableCellLink :to="linkToClient(leadRecord.client_pk)">
                                    {{ getFullName(leadRecord.clientPreview) }}
                                </TableCellLink>
                                <PQsDetails
                                    :pk="String(leadRecord.id)"
                                    :project-pk="Number(leadRecord.project_pk)"
                                    :price-quote-count="leadRecord.listInformation.count"
                                    :price-quote-last-time="leadRecord.listInformation.price_quote_last_time"
                                    :is-possible-to-add-pq="leadRecord.is_possible_to_add_pq"
                                />
                                <PQsDetails
                                    :pk="String(leadRecord.id)"
                                    :project-pk="Number(leadRecord.project_pk)"
                                    :price-quote-count="leadRecord.listInformation.sent_count"
                                    :price-quote-last-time="leadRecord.listInformation.price_quote_last_time"
                                    :is-possible-to-add-pq="leadRecord.is_possible_to_add_pq"
                                    only-sent-pq
                                />

                                <td>
                                    <div
                                        v-tooltip="{ content:leadRecord.remark }"
                                        :content="leadRecord.remark"
                                        class="truncate max-w-[460px] cursor-pointer"
                                        @click.stop="copyToClipboard(leadRecord.remark ?? '')"
                                    >
                                        {{ leadRecord.remark }}
                                    </div>
                                </td>
                                <td>
                                    {{ leadRecord.expertLeadInfo?.external_price }}
                                </td>
                                <td>
                                    <div class="flex justify-center space-x-1.5 w-16">
                                        <div
                                            v-if="leadRecord.status?.name"
                                            v-tooltip="{ content: getLeadStatusTooltip(leadRecord), html: false }"
                                        >
                                            <LeadStatusBadge
                                                :status-pk="leadRecord.status_pk"
                                                class="cursor-pointer"
                                                small
                                            />
                                        </div>
                                        <div v-if="leadRecord.is_bonus" v-tooltip="{ content: 'Bonus' }">
                                            <LeadBonusBadge class="cursor-pointer" small />
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="flex justify-center space-x-1.5">
                                        <span
                                            v-if="leadRecord.expertLeadInfo?.expert_request_is_done"
                                            class="text-green-500"
                                        >Done</span>
                                        <span
                                            v-else-if="leadRecord.expertLeadInfo.expert_pk === currentUserPk && !!leadRecord.expertLeadInfo?.expert_request_start_at"
                                            class="text-blue-500 cursor-pointer hover:text-blue-700"
                                            @click="endWork(leadRecord)"
                                        >Done</span>
                                        <span
                                            v-else-if="leadRecord.expertLeadInfo.expert_pk === currentUserPk && !leadRecord.expertLeadInfo?.expert_request_start_at"
                                            class="text-blue-500 cursor-pointer hover:text-blue-700"
                                            @click="handleExpertAction(leadRecord)"
                                        >Start</span>
                                        <span
                                            v-else-if="leadRecord.expertLeadInfo.expert_pk !== currentUserPk && !leadRecord.expertLeadInfo?.expert_request_start_at"
                                            class="text-yellow-500 cursor-pointer hover:text-yellow-700"
                                            @click="handleExpertAction(leadRecord)"
                                        >Start</span>
                                        <span
                                            v-else-if="leadRecord.expertLeadInfo.expert_pk !== currentUserPk && !!leadRecord.expertLeadInfo?.expert_request_start_at"
                                            class="text-gray-500"
                                        >Process</span>
                                    </div>
                                </td>
                                <td v-if="isUserExpertManager">
                                    <div class="flex">
                                        <div
                                            v-if="leadRecord.expertLeadInfo?.expert_request_at && !leadRecord.expertLeadInfo?.expert_request_start_at"
                                            class="inline-flex gap-1 cursor-pointer"
                                            @click="sendLeadBackToQueue(leadRecord)"
                                        >
                                            <CornerUpLeftIcon class="w-4 h-4 text-blue-500 hover:text-blue-700" />
                                            <div class="text-secondary self-end">
                                                Send back
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </template>
                    </AppTable>
                    <template #fallback>
                        <div class="p-4 w-full">
                            <PlaceholderBlock class="w-full h-64" />
                        </div>
                    </template>
                </SuspenseManual>
            </div>
        </div>
        <AppTablePagination class="mt-6 px-6" :pagination="leadsList.pagination" />
    </div>
</template>

<script setup lang="ts">
import AppTable from '~/components/Table/AppTable.vue'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import WorkspaceLabelComponent from '@/components/Workspace/WorkspaceLabelComponent.vue'
import TableCellLink from '~/components/Table/TableCellLink.vue'
import PQsDetails from '~/sections/Lead/List/components/PQsDetails.vue'
import type {
    ExpertLeadColumns,
    ExpertLeadSearchController,
    ExpertLeadSortController,
} from '~/sections/ExpertLead/composable/useExpertLeadSearchController'
import { getFullName } from '~/lib/Helper/PersonHelper'
import { copyToClipboard } from '@/lib/core/helper/ClipboardHelper'
import { linkToClient, linkToExpertLead } from '@/lib/core/helper/RouteNavigationHelper'
import SearchPresetsList from '~/components/Search/SearchPresetsList.vue'
import type { ModelAttributes, ModelRef } from '#/types/lib/Model'
import ExpertLeadProcessingQueueModal from '~/sections/ExpertLead/modals/ExpertLeadProcessingQueueModal.vue'
import LeadStatusBadge from '~/components/Page/Lead/LeadStatusBadge.vue'
import LeadBonusBadge from '~/components/Page/Lead/LeadBonusBadge.vue'
import { LeadStatusName } from '~/api/models/Lead/LeadStatus'
import { ClosingReasons } from '~/api/models/Lead/Lead'
import { DepartmentName } from '~/api/models/Department/Department'
import { PositionName } from '~/api/models/Position/Position'
import { useExpertAssignment } from '~/sections/ExpertLead/composable/useExpertAssignment'
import { handleUnprocessedExpertLeadsError } from '~/sections/ExpertLead/helpers/handleUnprocessedExpertLeadsError'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import PromptModal from '~/modals/PromptModal.vue'
import { toastError } from '@/lib/core/helper/ToastHelper'
import { $confirm } from '@/plugins/ConfirmPlugin'
import { MessageType } from '@/types/enums/MessageType'

const props = defineProps<{
    searchController: ExpertLeadSearchController,
    sortController: ExpertLeadSortController,
    tableColumns: ExpertLeadColumns,
}>()

const { useModel, useCurrentUser, currentUserPk, hasPermission } = useContext()

const leadModel = useModel('Lead')

const workspaces = useGeneralDictionary('Workspace').records
const { assignExpert } = useExpertAssignment()
const currentUser = useCurrentUser()
const promptModal = useModal(PromptModal)

const suspense = useSuspensableComponent(async () => {
    await leadsList.fetch()
})

//

const sortController = props.sortController
const columns = props.tableColumns

const leadsList = leadModel.useList({
    with: ['clientPreview', 'executor', 'email', 'phone', 'listInformation', 'status', 'createdBy', 'expertLeadInfo', 'expertLeadInfo.requestedBy'],
    sort: sortController,
    where: (and) => {
        searchController.applyCondition(and)
    },
    pageSize: 19,
})

const searchController = props.searchController.useList(leadsList)

const items = computed(() => sortController.sort(leadsList.records))

const isUserExpertManager = computed(() => {
    return isUserInDepartment(currentUser, DepartmentName.Experts) && isUserHasPosition(currentUser, PositionName.Manager)
})

const handleExpertAction = async (lead: ModelAttributes<'Lead'>) => {
    const isCurrentUserAssigned = lead.expert_pk === currentUserPk
    const currentExpert = useDictionary('Agent', { workspace: getWorkspaceFromObject(lead) })
        .records.find(agent => agent.pk === lead.expert_pk)

    if (isUserExpertManager.value && !isCurrentUserAssigned) {
        if (currentExpert) {
            return toastError(`Lead is already assigned to expert ${getFullName(currentExpert)}`)
        }
        await assignExpert(usePk(lead))

        return
    }

    if (!isCurrentUserAssigned && currentExpert && !lead.expertLeadInfo.expert_request_is_done && lead.expertLeadInfo.expert_request_start_at) {
        const confirmed = await $confirm({
            type: MessageType.Warning,
            text: `This lead is already being worked on by expert ${getFullName(currentExpert)}. Are you sure you want to take over this lead?`,
            confirmButton: 'Yes',
        })

        if (!confirmed) return
    }
    await startWork(lead)
}

const startWork = async (lead: ModelAttributes<'Lead'>) => {
    try {
        await useModel('LeadAdditionalExpertInformation', {
            http: { workspace: getWorkspaceFromObject(lead) },
        }).actions.startWork({
            lead_pk: usePk(lead),
        })
    } catch (e) {
        handleUnprocessedExpertLeadsError(e)
    }
}

const endWork = async (lead: ModelAttributes<'Lead'>) => useModel('LeadAdditionalExpertInformation', { http: { workspace: getWorkspaceFromObject(lead) } }).actions.endWork({ lead_pk: usePk(lead) })

const sendLeadBackToQueue = async (lead: ModelAttributes<'Lead'>) => {
    const reason = await promptModal.open({
        title: 'Send lead back to queue',
        placeholder: 'Please enter the reason',
        required: true,
        inputAttrs: { maxLength: 300, minLength: 6 },
        validationRules: [
            ValidationRules.MinLength(6, 'Reason should be more than {value} symbols'),
        ],
    })

    if (reason) {
        await useModel('LeadAdditionalExpertInformation', {
            http: { workspace: getWorkspaceFromObject(lead) },
        }).actions.sendBack({
            lead_pk: usePk(lead),
            reason: reason,
        })
    }
}

// ExpertLeadProcessingQueueModal

const expertLeadProcessingQueueModal = useModal(ExpertLeadProcessingQueueModal)
function openExpertsQueueProcessingModal() {
    expertLeadProcessingQueueModal.open()
}

const closingReasonDictionary = useGeneralDictionary('ClosingReason')

const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text

    return text.substring(0, maxLength) + '...'
}

const getLeadStatusTooltip = (leadRecord: ModelRef<'Lead', 'status'>) => {
    const closingReason = leadRecord.closing_reason
    const closingReasonRemark = leadRecord.closing_reason_remark ?? ''

    if ([LeadStatusName.Lost, LeadStatusName.Closed, LeadStatusName.Fraud].includes(leadRecord.status.system_name) && closingReason) {
        const baseResult = leadRecord.status.name + ' (reason: '

        if (closingReason === ClosingReasons.Other) {
            return baseResult + truncateText(closingReasonRemark, 150) + ')'
        }

        const reasonTitle = closingReasonDictionary.find(closingReason).title

        return baseResult + reasonTitle + ')'
    }

    return leadRecord.status.name
}
</script>
