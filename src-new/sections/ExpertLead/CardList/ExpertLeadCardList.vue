<template>
    <SuspenseManual :state="suspense">
        <template #default>
            <div class="relative max-h-full !w-72 group">
                <VirtualList
                    ref="listRef"
                    :item-height="(index) => leadsListWithSelector[index]?.itemHeight"
                    :items="leadsListWithSelector"
                    :infinite-scroll-options="{ list: leadsList, distance: 300 }"
                    class="fancy-scroll pr-2 h-full min-w-[290px] !overflow-y-scroll "
                    @load="onLoad"
                >
                    <template #items="{ list }">
                        <div v-if="isLeadsListOutdated" class="hidden absolute bottom-[35px] w-full z-10 px-2 pr-3 h-[35px] mb-2 group-hover:block">
                            <div class="h-full flex button-group shadow">
                                <AppButton
                                    v-tooltip="{
                                        content: `Refresh list`
                                    }"
                                    :loading="suspense.loading.value"
                                    class="rounded-none rounded-t w-full h-full dark:bg-dark-1 dark:text-secondary-200"
                                    @click="refreshLeadList"
                                >
                                    <Loader v-if="suspense.loading.value" />
                                    Refresh list
                                </AppButton>
                            </div>
                        </div>
                        <div class="hidden absolute bottom-0 z-10 w-full px-2 pr-3 h-[35px] group-hover:block">
                            <div class="w-full h-full button-group grid grid-cols-2 shadow">
                                <AppButton
                                    v-tooltip="{
                                        content: `Press Alt+J (or Arrow Left) to navigate to the previous lead.`
                                    }"
                                    class="rounded-none rounded-t h-full dark:bg-dark-1 dark:text-secondary-200"
                                    @click="goToPreviousLead"
                                >
                                    <ChevronLeftIcon />
                                    Prev
                                </AppButton>
                                <AppButton
                                    v-tooltip="{
                                        content: `Press Alt+K (or Arrow Right) to navigate to the next lead.`
                                    }"
                                    class="rounded-none rounded-t h-full dark:bg-dark-1 dark:text-secondary-200"
                                    @click="goToNextLead"
                                >
                                    Next
                                    <ChevronRightIcon />
                                </AppButton>
                            </div>
                        </div>
                        <div class="p-2 pr-0">
                            <div class="sticky top-0 z-10 mb-2">
                                <div class="flex items-center">
                                    <div
                                        class="card relative flex flex-grow items-center justify-between text-xs pl-2.5 font-semibold text-center border dark:border-gray-700 text-gray-700 dark:text-gray-400"
                                    >
                                        {{ leadsTitle }} ({{ leadsAmount }})
                                        <div
                                            v-if="isFiltered"
                                            class="absolute right-6 border-none p-1.5 --no-hover outline-none"
                                            @click="resetFilters"
                                        >
                                            <XIcon class="cursor-pointer !stroke-2" />
                                        </div>
                                        <button />
                                        <Dropdown>
                                            <template #toggle="{ toggle }">
                                                <div class="cursor-pointer h-full py-2 pr-2.5" @click="toggle">
                                                    <MenuIcon class="dark:text-white w-[14px] h-[14px]" />
                                                </div>
                                            </template>
                                            <template #content="{ close }">
                                                <div @click="close">
                                                    <div class="mt-2 mr-2 card min-w-[150px] shadow-sm border-2 border-secondary-100">
                                                        <div class="py-4 px-4 border-b flex justify-start">
                                                            <span class="leading-none font-semibold">Sort by</span>
                                                        </div>
                                                        <div class="p-2 flex flex-col">
                                                            <a
                                                                v-for="(item, index) in orderByLeadSortOptions"
                                                                :key="item.direction"
                                                                class="dropdown-menu__item"
                                                                :class="{
                                                                    'text-primary': item.direction !== LeadSortOptions.Default
                                                                }"
                                                                @click.stop="() => {
                                                                    leadsSortHandler(item.field, index)
                                                                }"
                                                            >
                                                                {{ item.label }}
                                                                <ArrowUpIcon
                                                                    v-if="item.direction === LeadSortOptions.Ascending"
                                                                    class="w-3 h-3 ml-auto"
                                                                />
                                                                <ArrowDownIcon
                                                                    v-if="item.direction === LeadSortOptions.Default"
                                                                    class="w-3 h-3 ml-auto"
                                                                />
                                                                <ArrowDownIcon
                                                                    v-if="item.direction === LeadSortOptions.Descending"
                                                                    class="w-3 h-3 ml-auto"
                                                                />

                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </template>
                                        </Dropdown>
                                    </div>
                                </div>
                                <div v-if="activeLeadRecord?.lead" class="bg-white dark:bg-dark-3">
                                    <div class="flex pr-2 pt-1">
                                        <div class="flex h-[1px] w-full items-center justify-center border-t border-secondary-100 dark:border-dark-6 my-3">
                                            <span class="text-xs px-2 bg-white font-semibold text-secondary dark:bg-dark-3 dark:text-secondary-300">{{ CardType.Selected }}</span>
                                        </div>
                                    </div>
                                    <LeadCard
                                        class="!mt-1"
                                        :lead="activeLeadRecord.lead"
                                        is-active
                                        :is-last-opened-active="false"
                                        @update:remove-lead-from-last-opened-block="()=>{}"
                                        @click="activeLeadRecord.lead?.isDeleted ? showIsForbiddenMessage() : setActiveLead(usePk(activeLeadRecord.lead))"
                                    />
                                </div>
                            </div>

                            <template
                                v-for="({ data, index }) in makeTypedList(list)"
                                :key="index"
                            >
                                <div v-if="data.showDivider" class="flex">
                                    <div class="flex h-[1px] w-full items-center justify-center border-t mt-5 mb-2 dark:border-dark-6">
                                        <span class="text-secondary px-2 bg-white text-xs font-semibold dark:bg-dark-3 dark:text-secondary-300">{{ $format.date(data.lead.created_at) }}</span>
                                    </div>
                                </div>
                                <div class="py-1.5 px-0.5">
                                    <LeadCard
                                        :lead="data.lead"
                                        :is-active="activeLeadPk === usePk(data.lead)"
                                        :is-pinned="false"
                                        :is-last-opened="false"
                                        @update:remove-lead-from-last-opened-block="()=>{}"
                                        @click="data.lead?.isDeleted ? showIsForbiddenMessage() : setActiveLead(usePk(data.lead))"
                                    />
                                </div>
                            </template>
                        </div>
                    </template>
                </VirtualList>
            </div>
        </template>
        <template #fallback>
            <div class="p-4 min-h-[670px] min-w-[290px]">
                <PlaceholderBlock class="w-full h-full " />
            </div>
        </template>
    </SuspenseManual>
</template>

<script lang="ts">
enum LeadSortOptions {
    Default = '',
    Ascending = 'asc',
    Descending = 'desc',
}
</script>

<script setup lang="ts">
import VirtualList from '~/components/VirtualList.vue'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import LeadCard from '~/sections/Lead/CardList/components/LeadCard.vue'
import type { ModelAttributes } from '~types/lib/Model'
import { goTo, goToExpertLead, goToLeadManagement, routeToExpertLead } from '@/lib/core/helper/RouteNavigationHelper'
import type { LeadSearchController, LeadSortController } from '~/sections/Lead/composable/useLeadSearchController'
import { unique } from '~/lib/Helper/ArrayHelper'
import type { ApiListResponse } from '~types/lib/Api'
import { useApiRoute } from '~/composables/_core/useApiRoute'
import ArrayConditionHelper from '~/lib/Helper/ArrayCondition/ArrayConditionHelper'
import { useLeadHotkeys } from '~/sections/Lead/composable/useLeadHotkeys'
import { toastError } from '@/lib/core/helper/ToastHelper'
import { CardType } from '~/sections/Lead/CardList/LeadCardList.vue'

defineOptions({
    name: 'ExpertLeadCardList',
})

const props = defineProps<{
    leadPk?: PrimaryKey,
    searchController: LeadSearchController,
    sortController: LeadSortController,
}>()

const CardHeight = {
    withCardDivider: 158,
    withoutCardDivider: 132,
    cardDivider: 29,
    hiddenCard: 0,
}

const { useModel, hasPermission } = useContext()

const leadModel = useModel('Lead')

//

const canManageAll = computed(() => hasPermission('manage', 'all'))

const showDividersBySelector = {
    selected: true,
}

const activeLeadPk = computed(() => {
    return props.leadPk
})

const suspense = useSuspensableComponent(async () => {
    await Promise.all([
        leadsList.fetch(),
        userSelectedLeads.fetch(),
    ])
})

const refreshLeadList = async () => leadsList.fetch()
//

const sortController = props.sortController

const orderByLeadSortOptions = computed(() => {
    if (sortController.state?.created_at) {
        return [
            {
                label: 'Created date',
                field: 'created_at',
                direction: sortController.state.created_at,
            },
            {
                label: 'Taken date',
                field: 'taken_date',
                direction: LeadSortOptions.Default,
            },
        ]
    }

    if (sortController.state?.taken_date) {
        return [
            {
                label: 'Created date',
                field: 'created_at',
                direction: LeadSortOptions.Default,
            },
            {
                label: 'Taken date',
                field: 'taken_date',
                direction: sortController.state.taken_date,
            },
        ]
    }

    return [
        {
            label: 'Created date',
            field: 'created_at',
            direction: LeadSortOptions.Default,
        },
        {
            label: 'Taken date',
            field: 'taken_date',
            direction: LeadSortOptions.Default,
        },
    ]
})

//

const leadsList = leadModel.useList({
    with: ['clientPreview', 'executor', 'email', 'phone', 'utm', 'listInformation', 'status', 'agentLeadInfo', 'expert', 'chat', 'chat.info', 'createdBy'],
    withOptional: {
        prediction: () =>   canManageAll.value,
    },
    where: (and) => {
        searchController.applyCondition(and)
    },
    sort: sortController,
    pageSize: 10,
    pageFromPk: activeLeadPk.value,
    preload: {
        direction: 'both',
        pagesAmount: 1,
    },
})

const searchController = props.searchController.useList(leadsList)

//

const isLeadsListOutdated = computed(() => leadsList.records?.some((lead) => lead.isDeleted))

const showIsForbiddenMessage = () => {
    toastError('You can no longer access this lead')
}

function makeTypedList(virtualList: unknown) {
    return virtualList as {
        data: {
            lead: typeof leadsList['records'][number],
            type: CardType,
            showDivider: boolean,
            itemHeight: number,
        },
        index: number,
    }[]
}

const computedLeadPks = computed(() => {
    return [activeLeadPk.value].filter(Boolean)
})

const useLeadPks = ref<PrimaryKey[]>([])

watch(computedLeadPks, (pks) => {
    if (!pks.length) {
        return
    }

    pks = unique(pks.filter(Boolean))

    useHttp(leadModel.resourceContextOptions.http).get<ApiListResponse<PrimaryKey[]>>(useApiRoute().search('Lead'), {
        query: {
            limit: 100,
            where: (new ArrayConditionHelper()).in('id', pks).getParams(),
        },
    }).then((response) => {
        useLeadPks.value = computedLeadPks.value.filter((pk) => {
            return response.result.includes(pk)
        })
    })
}, { immediate: true })

const userSelectedLeads = leadModel.useResourceList(useLeadPks, {
    with: ['clientPreview', 'executor', 'email', 'phone', 'utm', 'listInformation', 'status', 'agentLeadInfo', 'expert', 'chat', 'chat.info', 'createdBy'],
    withOptional: {
        prediction: () =>   canManageAll.value,
    },
})

//

const combinedLeadsSelectedByUserComputed = computed(() => {
    return [...userSelectedLeads.records, ...leadsList.records]
})

const activeLeadRecord = ref<typeof leadsListWithSelector['value'][number]>()

const leadsListWithSelector = computed(() => {
    if (combinedLeadsSelectedByUserComputed.value.length === 0) {
        return []
    }

    const leadsResultList = combinedLeadsSelectedByUserComputed.value.map((lead: ModelAttributes<'Lead'>, leadIndex: number) => {
        const leadType = leadTypeConditions(lead, leadIndex)

        const shouldShowDivider = leadDividerConditions(leadType, leadIndex)
        const itemHeight = shouldShowDivider ? CardHeight.withCardDivider : CardHeight.withoutCardDivider

        return {
            lead: lead,
            type: leadType,
            showDivider: shouldShowDivider,
            itemHeight: itemHeight,
        }
    })

    if (tryUsePk(leadsResultList[0]?.lead) === activeLeadPk.value) {
        activeLeadRecord.value = leadsResultList.shift()
    }

    return leadsResultList
})

//

function leadTypeConditions(lead: ModelAttributes<'Lead'>, leadIndex: number) {
    if (usePk(lead) === activeLeadPk.value && leadIndex === 0) {
        return CardType.Selected
    }

    return CardType.Day
}

function leadDividerConditions(leadType: CardType, leadIndex: number) {
    if (leadIndex === 0) {
        showDividersBySelector.selected = true
    }

    if (showDividersBySelector.selected && leadType === CardType.Selected) {
        showDividersBySelector.selected = false

        return true
    }

    if (leadType === CardType.Day) {
        if (leadIndex === 0 || leadTypeConditions(combinedLeadsSelectedByUserComputed.value[leadIndex - 1], leadIndex - 1) !== CardType.Day) {
            return true
        }
        const previousLeadDate = Date.fromUnixTimestamp(combinedLeadsSelectedByUserComputed.value[leadIndex - 1].created_at).toFormatUTC('dd/MM/yyyy')
        const currentLeadDate = Date.fromUnixTimestamp(combinedLeadsSelectedByUserComputed.value[leadIndex].created_at).toFormatUTC('dd/MM/yyyy')

        return previousLeadDate !== currentLeadDate
    }

    return false
}

//

const leadsAmount = computed(() => {
    return leadsList.pagination.value?.items.count
})

const loadMoreDebounced = useDebounceFn(() => {
    return leadsList.pagination.value?.loadNextPage()
}, 100)

const onLoad = () => {
    if (leadsList.loading.value || leadsList.loadingMore.value) {
        return
    }

    return loadMoreDebounced()
}

//

const loadPreviousRecords = useDebounceFn(() => {
    return leadsList.pagination.value?.loadPreviousPage()
}, 100)

const onLoadPrevious = () => {
    if (leadsList.loading.value || leadsList.loadingMore.value) {
        return
    }

    return loadPreviousRecords()
}

//

function setActiveLead(pk: PrimaryKey) {
    goToExpertLead(pk, true)
}

//

const isFiltered = computed(() => {
    return sortController.state?.created_at || sortController.state?.taken_date
})

function leadsSortHandler(field: string, index: number) {
    if (index === 0 && orderByLeadSortOptions.value[1].direction !== LeadSortOptions.Default) {
        orderByLeadSortOptions.value[1].direction = LeadSortOptions.Default
    } else if (index === 1 && orderByLeadSortOptions.value[0].direction !== LeadSortOptions.Default) {
        orderByLeadSortOptions.value[0].direction = LeadSortOptions.Default
    }

    if (orderByLeadSortOptions.value[index].direction === LeadSortOptions.Descending) {
        orderByLeadSortOptions.value[index].direction = LeadSortOptions.Ascending
        sortController.set(orderByLeadSortOptions.value[index].field, LeadSortOptions.Ascending)
    } else if (orderByLeadSortOptions.value[index].direction === LeadSortOptions.Ascending) {
        orderByLeadSortOptions.value[index].direction = LeadSortOptions.Default
        sortController.set(orderByLeadSortOptions.value[index].field, undefined)
    } else {
        orderByLeadSortOptions.value[index].direction = LeadSortOptions.Descending
        sortController.set(orderByLeadSortOptions.value[index].field, LeadSortOptions.Descending)
    }
}

function resetFilters() {
    sortController.reset()
}

//

const leadStatusDictionary = useGeneralDictionary('LeadStatus')

const leadsTitle = computed(() => {
    const activeTagsLabels = searchController.activeTags.map(activeTag => {
        return activeTag.queryLabel
    })

    if (searchController.activeTags.length === 1 && activeTagsLabels.includes('Bonus')) {
        const bonusSearchValue = (searchController.activeTags.find(activeTag => {
            return activeTag.queryLabel === 'Bonus'
        }))?.values

        if (bonusSearchValue && bonusSearchValue[0] === '0') {
            return 'Not bonus leads'
        }

        if (bonusSearchValue && bonusSearchValue[0] === '1') {
            return 'Bonus leads'
        }
    }

    if (searchController.activeTags.length === 1 && activeTagsLabels.includes('Status')) {
        const statusSearchValue = (searchController.activeTags.find(activeTag => {
            return activeTag.queryLabel === 'Status'
        }))?.values

        if (statusSearchValue) {
            const leadStatusTitle = (leadStatusDictionary.find(statusSearchValue[0] as PrimaryKey))?.name

            return `${leadStatusTitle} leads`
        }
    }

    if (searchController.hasActiveTags) {
        return 'Filtered leads'
    }

    return 'Leads'
})

//

const listRef = ref<{
    // eslint-disable-next-line
    scrollTo: (index: number) => void
}>()

watch(listRef, () => {
    if (props.leadPk) {
        nextTick(() => {
            const activeLeadPk = props.leadPk

            const firstDayIndex = leadsListWithSelector.value.findIndex((item) => {
                return item.type === CardType.Day
            })

            const leadRecords =  leadsListWithSelector.value.slice(firstDayIndex).map((item) => usePk(item.lead))

            const activeLeadPkIndex = leadRecords.findIndex(pk => pk === activeLeadPk) + computedLeadPks.value.length

            if (activeLeadPkIndex !== -1) {
                listRef.value?.scrollTo(activeLeadPkIndex)
            }
        })
    }
})

const getRealIndex = (index: number) => {
    const firstDayIndex = leadsListWithSelector.value.findIndex((item) => {
        return item.type === CardType.Day
    })

    return firstDayIndex + index
}

const {
    goToPreviousLead,
    goToNextLead,
} = useLeadHotkeys({
    activeLeadPk: activeLeadPk,
    pkList: computed(() => {
        const firstDayIndex = leadsListWithSelector.value.findIndex((item) => {
            return item.type === CardType.Day
        })

        return leadsListWithSelector.value.slice(firstDayIndex).map((item) => usePk(item.lead))
    }),
    onNavigate: async (index, leadPk) => {
        const realIndex = getRealIndex(index)

        // @todo Should be called after navigation, but there is a bug with computed list in parent component that makes it impossible
        // When parent list will be updated correctly, this should be moved after goTo call
        listRef.value?.scrollTo(realIndex)

        const route = routeToExpertLead(leadPk)

        await goTo(route, true)
    },
    onBeforeNavigate: async (activeIndex, direction) => {
        if (direction === 'next' && leadsList.pagination.value) {
            const realIndex = getRealIndex(activeIndex)

            if (realIndex + 1 + 1 >= leadsListWithSelector.value.length) {
                await onLoad()
            }
        }

        if (direction === 'prev' && leadsList.pagination.value) {
            if (activeIndex - 1 <= 0) {
                await onLoadPrevious()
            }
        }
    },
})
</script>
