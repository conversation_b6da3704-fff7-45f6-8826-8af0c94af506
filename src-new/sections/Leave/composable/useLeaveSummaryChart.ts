import type { Coords } from '~/sections/Leave/composable/useLeaveSummaryChartDataHelper'

export function useLeaveSummaryChart() {
    const { isDark } = useDarkMode()

    const colors = computed(() => ({
        primary: isDark.value ? '#6090FA' : '#3B76F6',
        danger: isDark.value ? '#B42318' : '#fd6e63',
        grid: isDark.value ? '#242B3A' : '#EBEDF3',
        inactiveGradient: isDark.value ? ['#474747',  '#232a3b'] : ['#e8e8e8', 'rgba(232,232,232,0.26)'],
        averageLine: isDark.value ? '#404b63' : '#d9dae3',
        hoverPoint: isDark.value ? '#FFFFFF' : '#293145',
        hoverLine: isDark.value ? '#313A4D' : '#D3D7E4',
    }))

    const drawLine = (ctx: CanvasRenderingContext2D,  points: Coords[], color: string, accentColor: string, averageLine: number = 0) => {
        ctx.beginPath()

        if (points.length == 1) {
            ctx.strokeStyle = (color)
            ctx.beginPath()
            ctx.moveTo(0, ctx.canvas.height)
            ctx.lineTo(ctx.canvas.width / 2, points[0].y)
            ctx.lineTo(ctx.canvas.width, ctx.canvas.height)
            ctx.stroke()
        }

        for (let i = 0; i < points.length - 1; i++) {
            const y1 = points[i].y, y2 = points[i + 1].y
            const x1 = points[i].x, x2 = points[i + 1].x

            if ((y1 - averageLine) * (y2 - averageLine) < 0) {
                const t = (averageLine - y1) / (y2 - y1)
                const crossX = x1 + t * (x2 - x1)

                ctx.strokeStyle = (y1 > averageLine ? color : accentColor)
                ctx.beginPath()
                ctx.moveTo(x1, y1)
                ctx.lineTo(crossX, averageLine)
                ctx.stroke()

                ctx.strokeStyle = (y2 > averageLine ? color : accentColor)
                ctx.beginPath()
                ctx.moveTo(crossX, averageLine)
                ctx.lineTo(x2, y2)
                ctx.stroke()
            } else {
                ctx.strokeStyle = (y1 >= averageLine ? color : accentColor)
                ctx.beginPath()
                ctx.moveTo(x1, y1)
                ctx.lineTo(x2, y2)
                ctx.stroke()
            }
        }

        ctx.closePath()
    }

    const drawGradient = (canvas: HTMLCanvasElement, points: Coords[], colors: string[]) => {
        const ctx = canvas.getContext('2d') as CanvasRenderingContext2D

        ctx.beginPath()
        ctx.moveTo(points[0].x, canvas.height)

        const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height)

        gradient.addColorStop(0, colors[0])
        gradient.addColorStop(1, colors[1])

        ctx.fillStyle =  gradient

        for (let i = 0; i < points.length - 1; i++) {
            ctx.lineTo(points[i].x, points[i].y)
            ctx.lineTo(points[i + 1].x, points[i + 1].y)
        }

        ctx.lineTo(points[points.length - 1].x + 3, points[points.length - 1].y)
        ctx.lineTo(points[points.length - 1].x + 3, canvas.height)
        ctx.closePath()
        ctx.fill()
    }

    const drawAverageLine = (canvas: HTMLCanvasElement, positionY: number, color: string) => {
        const ctx = canvas.getContext('2d') as CanvasRenderingContext2D

        ctx.lineWidth = 2
        ctx.strokeStyle = color

        ctx.beginPath()
        ctx.moveTo(0, positionY)
        ctx.lineTo(canvas.width, positionY)
        ctx.stroke()
    }

    const drawGrid = (canvas: HTMLCanvasElement, gridColor: string) => {
        const ctx = canvas.getContext('2d') as CanvasRenderingContext2D

        ctx.lineWidth = 1

        ctx.strokeStyle = gridColor

        const stepY = Math.round(canvas.height / 5)

        for (let i = 0; i <= 5; i++) {
            const y = stepY * i

            ctx.beginPath()
            ctx.moveTo(0, y)
            ctx.lineTo(canvas.width, y)
            ctx.stroke()
        }
    }

    const drawGridForSection = (canvas: HTMLCanvasElement, points: number[][], gridColor: string, section: number) => {
        const ctx = canvas.getContext('2d') as CanvasRenderingContext2D

        ctx.lineWidth = 1
        ctx.strokeStyle = gridColor

        const sectionsLength = points.map(section => section.length)

        const offsetLength = sectionsLength.slice(0, section).reduce((sum, d) => sum + d, 0)

        const offsets = [offsetLength, offsetLength + sectionsLength[section]]

        const step = canvas.width / points.flat().length
        offsets.forEach(sectionOffset => {
            const x = Math.round(sectionOffset * step)
            ctx.beginPath()
            ctx.moveTo(x, 0)
            ctx.lineTo(x, canvas.height)
            ctx.stroke()
        })
    }

    const drawHoverEffects = (canvas: HTMLCanvasElement, colorPoint: string, colorLine: string, x: number, y: number) => {
        const ctx = canvas.getContext('2d') as CanvasRenderingContext2D

        ctx.clearRect(0, 0, canvas.width, canvas.height)
        ctx.strokeStyle = colorLine
        ctx.lineWidth = 1

        ctx.setLineDash([3, 3])
        ctx.beginPath()
        ctx.moveTo(x, y)
        ctx.lineTo(x, canvas.height)
        ctx.stroke()
        ctx.setLineDash([])

        ctx.fillStyle = colorPoint
        ctx.beginPath()
        ctx.arc(x, y, 5, 0, Math.PI * 2)
        ctx.fill()
    }

    return {
        colors,
        drawLine,
        drawGradient,
        drawAverageLine,
        drawGrid,
        drawGridForSection,
        drawHoverEffects,
    }
}
