import type { MaybeRefOrGetter } from 'vue'

export type Coords = {
    x: number,
    y: number
}

export function useLeaveSummaryChartDataHelper(dataPoints: MaybeRefOrGetter<number[][]>) {
    const points = computed(() => toValue(dataPoints).flat())

    const minValue = computed(() => Math.min(...points.value))
    const maxValue = computed(() => Math.max(...points.value))

    const averageValue = computed(() => {
        return Math.round(points.value.reduce((a, b) => a + b, 0) / points.value.filter(d => d !== 0).length)
    })

    const stepValue = computed(() => {
        const approxStep = Math.ceil(maxValue.value / 5)

        const stepValues = [1, 2, 5, 10, 20, 25, 50, 100, 200, 500]

        return stepValues.find(value => approxStep < value) ?? 0
    })

    const yAxisLabels = computed(() => {
        const yAxisValues = []

        for (let i = 5; i >= 0; i--) {
            yAxisValues.push(i * stepValue.value)
        }

        return yAxisValues
    })

    const yAxisMax = computed(() => stepValue.value  * 5)

    const getAverageLineY =  (canvasHeight: number) => {
        return canvasHeight - ((averageValue.value) / (yAxisMax.value)) * canvasHeight
    }

    const getPoints = (canvas: HTMLCanvasElement, data: number[], offset = 0) => {
        return data.map((point, index) => ({
            x: Math.round((canvas.width / (points.value.length - 1)) * (index + offset)),
            y: Math.round(canvas.height - ((point) / (yAxisMax.value)) * canvas.height),
        }))
    }

    return {
        minValue,
        maxValue,
        averageValue,
        yAxisLabels,
        yAxisMax,
        getPoints,
        getAverageLineY,
    }
}
