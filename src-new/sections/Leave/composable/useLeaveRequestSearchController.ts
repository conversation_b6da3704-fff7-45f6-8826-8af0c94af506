import SearchController from '~/lib/Search/SearchController'
import TeamSearchTag from '~/lib/Search/Tag/Preset/TeamSearchTag'
import DepartmentSearchTag from '~/lib/Search/Tag/Preset/DepartmentSearchTag'
import DateRangeSearchTag from '~/lib/Search/Tag/DateRangeSearchTag'
import TextSearchTag from '~/lib/Search/Tag/TextSearchTag'
import SelectSearchTag from '~/lib/Search/Tag/SelectSearchTag'
import SortController from '~/lib/Search/SortController'

export function useLeaveRequestSearchController() {
    const { useDictionary, workspace } = useContext()

    const leaveRequestStatusDictionary = useDictionary('LeaveRequestStatus')
    const leaveRequestTypeDictionary = useDictionary('LeaveRequestType')
    const agentDictionary = useDictionary('Agent')

    const searchController = SearchController.forModel('LeaveRequest', {
        beneficiary_pk: new SelectSearchTag('Agent', agentDictionary.mapRecords.forSelect()),
        beneficiary_team_pk: new TeamSearchTag(workspace),
        beneficiary_department_pk: new DepartmentSearchTag(),
        dates: new DateRangeSearchTag('Leave dates', { timezone: 'UTC' }),
        comment: new TextSearchTag('Comment'),
        status: new SelectSearchTag('Status', leaveRequestStatusDictionary.mapRecords.forSelect()),
        request_type: new SelectSearchTag('Type', leaveRequestTypeDictionary.mapRecords.forSelect()),
    }, {
        syncWithQuery: false,
    })

    const columns = useTableColumns({
        beneficiary_pk: {
            label: 'Employee name',
            sortable: true,
        },
        beneficiary_team_pk: {
            label: 'Team',
            sortable: true,
        },
        beneficiary_department_pk: {
            label: 'Department',
            sortable: true,
        },
        dates: {
            label: 'Leave dates',
            sortable: true,
        },
        days_count: {
            label: 'Days',
            sortable: true,
            width: 'min',
        },
        comment: {
            label: 'Comment',
            sortable: true,
            width: 'max',
        },
        request_type: {
            label: 'Type',
            sortable: true,
            width: 'min',
        },
        status: {
            label: 'Status',
            sortable: true,
            width: 'min',
        },
        actions: {
            label: 'Actions',
            width: 'min',
            center: true,
        },
    })

    const sortController = SortController.fromColumns(columns, {
        syncWithQuery: true,
        autoHydrate: true,
    })

    return {
        searchController,
        sortController,
        columns,
    }
}

export type LeaveRequestSearchController = ReturnType<typeof useLeaveRequestSearchController>['searchController']
export type LeaveRequestSortController = ReturnType<typeof useLeaveRequestSearchController>['sortController']
export type LeaveRequestColumns = ReturnType<typeof useLeaveRequestSearchController>['columns']
