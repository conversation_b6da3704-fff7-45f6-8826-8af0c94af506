<template>
    <Teleport v-if="filterContainer" :to="filterContainer">
        <SearchFilter class="w-[800px]" :controller="searchController" />
    </Teleport>

    <LeaveSummary
        ref="leaveSummarySection"
        :search-controller="searchController"
        @select-date-range="changeSelectedDateRange($event)"
    />

    <div class="card card--bordered flex flex-col h-full">
        <div class="flex items-center justify-between px-2.5 py-2">
            <SearchPresetsList :search-controller="searchController" />
            <div class="flex items-center gap-2">
                <AppPaginationInfo :pagination="pagination" />
                <AppPaginationCompact :pagination="pagination" />
                <AppPageSize :pagination="pagination" :options="[10, 20, 40, 100]" />
                <AppButton class="--primary" @click="openCreateLeaveRequestModal()">
                    <CalendarIcon />Leave request
                </AppButton>
            </div>
        </div>
        <div class="overflow-auto fancy-scroll fancy-scroll-x">
            <SuspenseManual :state="suspense">
                <AppTable
                    :columns="columns"
                    :items="pagination.records"
                    zebra
                    class="--fixed-header"
                >
                    <template #body>
                        <template v-for="(agent, $i) in pagination.records" :key="$i">
                            <tr>
                                <td>{{ getFullName(agent) }}</td>
                                <td>{{ agent.team?.name }}</td>
                                <td>{{ agent.department.name }}</td>
                                <td
                                    v-for="(index) in selectedMonth?.days"
                                    :key="index"
                                    :class="{
                                        'bg-danger-100/50': highlightedDays.has(index),
                                    }"
                                    @click="() => {
                                        if (isBusy(agent.pk, index)) {
                                            openViewRequestModal(usePk(getRequest(agent.pk, index)))
                                        } else {
                                            openCreateLeaveRequestModal(undefined, {
                                                beneficiary_pk: agent.pk,
                                                dateRange: normalizeDateRange({
                                                    start: Date.fromUnixTimestamp(selectedMonth.startUnixTimestamp + (index -
                                                        1) * dayMs / 1000),
                                                    end: Date.fromUnixTimestamp(selectedMonth.startUnixTimestamp + (index -
                                                        1) * dayMs / 1000),
                                                }),
                                            })
                                        }
                                    }"
                                >
                                    <div class="flex justify-center">
                                        <span
                                            v-if="isSickDay(agent.pk, index)"
                                            class="--pending --xs --soft --outline badge --only"
                                        >
                                            <ActivityIcon />
                                        </span>
                                        <span
                                            v-else-if="isBusy(agent.pk, index)"
                                            class="--danger --xs --soft --outline badge --only"
                                        >
                                            <XIcon />
                                        </span>
                                        <span v-else class="--muted --xs --outline badge --only" />
                                    </div>
                                </td>
                            </tr>
                        </template>
                    </template>
                </AppTable>
            </SuspenseManual>
        </div>
    </div>
    <AppTablePagination class="my-4 px-6" :pagination="pagination" />
</template>

<script setup lang="ts">
import SearchFilter from '~/components/Search/SearchFilter.vue'
import SearchController from '~/lib/Search/SearchController'
import TeamSearchTag from '~/lib/Search/Tag/Preset/TeamSearchTag'
import DepartmentSearchTag from '~/lib/Search/Tag/Preset/DepartmentSearchTag'
import LeaveSummary from '~/sections/Leave/components/LeaveSummary.vue'
import { getFullName } from '~/lib/Helper/PersonHelper'
import type { TableColumn } from '~/composables/useTableColumns'
import { LeaveRequestStatus, LeaveRequestType } from '~/api/models/LeaveManagement/LeaveRequest'
import { groupBy } from '~/lib/Helper/ArrayHelper'
import { FrontendPagination } from '~/lib/Model/FrontendPagination'
import SelectSearchTag from '~/lib/Search/Tag/SelectSearchTag'
import LeaveRequestCreateModal from '~/modals/Leave/LeaveRequestCreateModal.vue'
import { type DateRange, getDaysInRange, normalizeDateRange } from '@/lib/core/helper/DateHelper'
import LeaveRequestReviewModal from '~/modals/Leave/LeaveRequestReviewModal.vue'
import SearchPresetsList from '~/components/Search/SearchPresetsList.vue'

defineProps<{
    filterContainer: HTMLElement | undefined
}>()

const { useModel, useDictionary, workspace } = useContext()
const agentDictionary = useDictionary('Agent')

const selectedMonth = ref<{
    days: number,
    startUnixTimestamp: number,
    endUnixTimestamp: number,
}>(getMonthInfo(new Date().getMonth()))

const leaveSummarySection = ref<LeaveSummary>()

const weekMode = computed(() => {
    const week = new Date(selectedMonth.value.startUnixTimestamp * 1000)

    return selectedMonth.value.days == 7 && week.getDay() == 1
})

function getDateRangeInfo(dateRange: DateRange) {
    return {
        days: getDaysInRange(dateRange) + 1,
        startUnixTimestamp: dateRange.start.unixTimestamp(),
        endUnixTimestamp: dateRange.end.unixTimestamp(),
    }
}

function getMonthInfo(month: number, year = new Date().getFullYear()) {
    const startDate = new Date(Date.UTC(year, month, 1, 0, 0, 0, 0))
    const endDate = new Date(Date.UTC(year, month + 1, 0, 23, 59, 59, 999))

    return {
        days: getDaysInRange({ start: startDate, end: endDate }) + 1,
        startUnixTimestamp: startDate.unixTimestamp(),
        endUnixTimestamp: endDate.unixTimestamp(),
    }
}

const changeSelectedDateRange = (dateRange: DateRange) => {
    selectedMonth.value = getDateRangeInfo(dateRange)
}

watch(selectedMonth, () => {
    suspense.fetch()
})

const searchController = new SearchController({
    id: new SelectSearchTag('Agent', agentDictionary.mapRecords.forSelect()),
    team_pk: new TeamSearchTag(workspace),
    department_pk: new DepartmentSearchTag(),
}, {
    syncWithQuery: false,
    presets: {
        key: 'leave-management-summary',
    },
})

const requestsList = useModel('LeaveRequest').useList({
    with: ['agent', 'agent.team', 'agent.department'],
    where: (and) => {
        and.eq('status', LeaveRequestStatus.Approved)

        if (availableAgents.value.length) {
            and.inCompact('beneficiary_pk', availableAgents.value.map(usePk))
        }

        if (selectedMonth.value) {
            and.gte('dates', Math.floor(selectedMonth.value.startUnixTimestamp))
            and.lte('dates', Math.floor(selectedMonth.value.endUnixTimestamp))
        }
    },
    limit: false,
})

//

const agentList = useModel('Agent').useList({
    with: ['team', 'department'],
    limit: false,
    where: (and) => {
        searchController.applyCondition(and)
    },
})

const availableAgents = computed(() => {
    const agents = agentList.records

    if (!agents.length) return []

    const periodStart = selectedMonth.value.startUnixTimestamp
    const periodEnd = selectedMonth.value.endUnixTimestamp

    return agents.filter(agent => {
        const agentStart = agent.hired_at ?? agent.created_at
        const agentEnd = agent.dismissed_at ?? Infinity

        return agentStart <= periodEnd && agentEnd >= periodStart
    })
})

searchController.useList(agentList)
searchController.useList(requestsList)

const suspense = useSuspensableComponent(async () => {
    if (selectedMonth.value) {
        await agentList.fetch()
        await requestsList.fetch()
    }
})

const columns = computed(() => {
    const { groups, columns } = getDateColumns()

    return useTableColumns({
        beneficiary_pk: {
            label: 'Employee name',
            width: 'min',
        },
        beneficiary_team_pk: {
            label: 'Team',
            width: 'min',
        },
        beneficiary_department_pk: {
            label: 'Department',
            width: 'min',
        },
        ...columns,
    }, {
        ...groups,
    })
})

const groupedRequests = computed(() => {
    return groupBy(requestsList.records, 'beneficiary_pk')
})

const pagination = new FrontendPagination(() => {
    return availableAgents.value
}, {
    page: 1,
    pageSize: 20,
})

const dayMs = Timespan.days(1).inMilliseconds

const busyMap = computed(() => {
    const map = new Map<PrimaryKey, Set<number>>()

    for (const [agentPk, requests] of Object.entries(groupedRequests.value)) {
        const set = new Set<number>()

        for (const { dates: { leave_start, leave_end } } of requests) {
            let ts = getUTCMidnight(leave_start)
            const end = getUTCMidnight(leave_end)

            while (ts <= end) {
                set.add(ts)
                ts += dayMs
            }
        }

        map.set(agentPk, set)
    }

    return map
})

const sickDayMap = computed(() => {
    const map = new Map<PrimaryKey, Set<number>>()

    for (const [agentPk, requests] of Object.entries(groupedRequests.value)) {
        const set = new Set<number>()

        for (const { dates: { leave_start, leave_end }, request_type } of requests) {
            let ts = getUTCMidnight(leave_start)
            const end = getUTCMidnight(leave_end)

            while (ts <= end) {
                if (request_type === LeaveRequestType.SickDay) {
                    set.add(ts)
                }
                ts += dayMs
            }
        }

        if (set.size) {
            map.set(agentPk, set)
        }
    }

    return map
})

function getRequest(agentPk: PrimaryKey, day: number) {
    // grab this agent’s requests (or empty array if none)
    const requests = groupedRequests.value[agentPk] ?? []

    // build a Date for the 1st of the selected month (or fallback to today)
    const base = selectedMonth.value?.startUnixTimestamp
        ? Date.fromUnixTimestamp(selectedMonth.value.startUnixTimestamp)
        : new Date()

    // compute the UTC‐midnight timestamp for the “day” of that month
    const offsetDay = base.getDate() + day - 1
    const ts = Date.UTC(base.getUTCFullYear(), base.getUTCMonth(), offsetDay)

    // find the first request whose leave_start…leave_end spans ts
    return requests.find(({ dates: { leave_start, leave_end } }) => {
        const start = getUTCMidnight(leave_start)
        const end   = getUTCMidnight(leave_end)

        return ts >= start && ts <= end
    })
}

const getUTCMidnight = (timestamp: number): number => {
    const date = Date.fromUnixTimestamp(timestamp)

    return Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate())
}

const isBusy = (agentPk: PrimaryKey, day: number): boolean => {
    const base = selectedMonth.value?.startUnixTimestamp ? Date.fromUnixTimestamp(selectedMonth.value.startUnixTimestamp) : new Date()

    const offsetDay = base.getDate() + day - 1
    const ts = Date.UTC(base.getUTCFullYear(), base.getUTCMonth(), offsetDay)

    return busyMap.value.get(agentPk)?.has(ts) ?? false
}

const isSickDay = (agentPk: PrimaryKey, day: number): boolean => {
    const base = selectedMonth.value?.startUnixTimestamp ? Date.fromUnixTimestamp(selectedMonth.value.startUnixTimestamp) : new Date()

    const offsetDay = base.getDate() + day - 1
    const ts = Date.UTC(base.getUTCFullYear(), base.getUTCMonth(), offsetDay)

    return sickDayMap.value.get(agentPk)?.has(ts) ?? false
}

const getDateColumns = () => {
    const columns: Record<string, TableColumn> = {}
    const groups = {}

    if (selectedMonth.value) {
        const baseDate = new Date(selectedMonth.value.startUnixTimestamp * 1000)

        for (let i = 0; i < selectedMonth.value.days; i++) {
            const dayIndex = i + 1

            const currentDate = new Date(baseDate)
            currentDate.setDate(currentDate.getDate() + i)

            const key = currentDate.getParts().MMMM

            if (!groups[key]) {
                groups[key] = {
                    label: `${key} ${currentDate.getFullYear()}`,
                    columns: [],
                    between: true,
                    hideDelimiter: true,
                }
            }

            groups[key].columns.push({
                column: `day_${dayIndex}`,
                label: dayIndex,
            })

            columns[`day_${dayIndex}`] = {
                icon: () =>
                    h('div', { class: highlightedDays.value.has(dayIndex) ? 'text-danger' : '' },
                      `${weekMode.value ? currentDate.getParts().EEE : currentDate.getDate()}`,
                    ),
                width: 'auto',
                center: true,
            }
        }
    }

    return {
        columns,
        groups,
    }
}

//

const createRequestModal = useModal(LeaveRequestCreateModal)

const openCreateLeaveRequestModal = async (pk?: PrimaryKey, formData?: {
    beneficiary_pk?: PrimaryKey,
    dateRange?: DateRange,
}) => {
    await createRequestModal.open({ pk, formData })

    await suspense.fetch()

    if (leaveSummarySection.value) {
        await leaveSummarySection.value.fetchData()
    }
}

//

const viewRequestModal = useModal(LeaveRequestReviewModal)

const openViewRequestModal = async (pk: PrimaryKey) => {
    await viewRequestModal.open({ pk, edit: true })

    await suspense.fetch()

    if (leaveSummarySection.value) {
        await leaveSummarySection.value.fetchData()
    }
}

//
const highlightedDays = computed(() => {
    const result = new Set<number>()

    const agents = agentList.records
    const totalAgents = agents.length

    if (!selectedMonth.value || totalAgents === 0) return result

    const base = Date.fromUnixTimestamp(selectedMonth.value.startUnixTimestamp)

    for (let i = 1; i <= selectedMonth.value.days; i++) {
        const offsetDay = base.getDate() + i - 1
        const ts = Date.UTC(base.getUTCFullYear(), base.getUTCMonth(), offsetDay)
        let busyCount = 0

        for (const agent of agents) {
            const busyDays = busyMap.value.get(agent.pk)

            if (busyDays?.has(ts)) {
                busyCount++
            }
        }

        if ((busyCount / totalAgents) > 0.3) {
            result.add(i)
        }
    }

    return result
})
</script>
