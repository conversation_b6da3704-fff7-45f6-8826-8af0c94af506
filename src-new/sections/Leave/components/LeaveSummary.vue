<template>
    <div class="flex gap-2">
        <div class="card card--bordered card__body flex flex-col flex-[7] p-5">
            <div class="mb-6 pb-4 flex items-center justify-between relative">
                <div>
                    <div class="text-lg font-semibold">
                        Summary of leave
                    </div>
                    <div class="text-sm ">
                        <span class="text-secondary-400 dark:text-secondary-200">Selected period:</span>
                        <span>{{ displaySelectedPeriod }}</span>
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <div class="button-group">
                        <AppButton
                            v-for="(rangeType,i) in RangeType"
                            :key="i"
                            class="dark:text-secondary-50 capitalize"
                            :class="[selectedRangeType === rangeType ? '--primary' : 'dark:bg-dark-2']"
                            @click="setSelectedRangeType(rangeType)"
                        >
                            {{ rangeType }}
                        </AppButton>
                    </div>
                    <InputDateRange
                        v-model="selectedCustomRange"
                        class="max-w-[200px] dark:rounded-md dark:border dark:border-secondary-600"
                        timezone="UTC"
                        :disabled="selectedRangeType !== RangeType.Custom"
                        @update:model-value="updateSelectedRange"
                    />
                </div>
                <hr class="border-secondary-100 absolute bottom-1 -left-5 w-[102.8%]">
            </div>
            <div class="pl-7 h-[242px]">
                <div v-if="loading" class="flex items-center justify-center h-full gap-2">
                    <Loader /> Loading summary chart..
                </div>
                <div v-else ref="chartElement">
                    <LineChart
                        :key="`${getSelectedPeriod}-${selectedRangeType}`"
                        :data-points="dataPoints"
                        :highlighted-area="getSelectedPeriod"
                        :start-date="selectedDateRange?.start"
                    />
                    <div v-if="selectedRangeType == RangeType.Year" class="button-group justify-between mt-2.5">
                        <AppButton
                            v-for="(period, index) of monthTabs"
                            :key="index"
                            :width="buttonWidths[index]"
                            class="dark:bg-dark-3 dark:border-dark-5 dark:hover:bg-dark-5 dark:text-gray-400 dark:hover:text-gray-400 m-0"
                            :class="{
                                '--primary dark:bg-primary-2': selectedPeriod.month === period.value.month && selectedPeriod.year === period.value.year
                            }"
                            @click="changePeriod(period.value)"
                        >
                            {{ period.title }}
                        </AppButton>
                    </div>
                    <div v-else-if="selectedRangeType == RangeType.Month" class="flex justify-between mt-3 text-2xs text-secondary dark:text-secondary-50">
                        <span v-for="day in monthTabs[currentMonth].value.days" :key="day">{{ day }}</span>
                    </div>

                    <div v-else-if="selectedRangeType == RangeType.Week" class="flex justify-between mt-3 text-2xs text-secondary dark:text-secondary-50">
                        <span v-for="day in weekDays" :key="day">{{ day }}</span>
                    </div>
                    <div v-else-if="selectedRangeType == RangeType.Custom" class="flex justify-between mt-3 text-2xs text-secondary dark:text-secondary-50">
                        <span v-for="customDate in customDates" :key="customDate">{{ customDate }}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="flex flex-col flex-[2] gap-2">
            <div class="card card--bordered card__body px-5 flex-grow">
                <div class="flex flex-col justify-between h-full">
                    <div class="self-start border dark:border-secondary rounded-lg p-2">
                        <CalendarIcon class="!w-5 !h-5" />
                    </div>
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-lg font-semibold">
                                {{ busiestMonth }}
                            </div>
                            <div class="text-xs text-secondary-400 dark:text-secondary-200 font-medium mt-1">
                                Busiest period
                            </div>
                        </div>
                        <ChartMonthLine fill="#DC2626" stroke="#F04438" />
                    </div>
                </div>
            </div>
            <div class="card card--bordered card__body flex items-start gap-5 px-5 py-4">
                <div class="border dark:border-secondary rounded-lg p-2">
                    <UserIcon class="!w-5 !h-5" />
                </div>
                <div class="flex flex-col gap-1">
                    <div class="text-lg font-semibold">
                        {{ displayPeakAbsences }}
                    </div>
                    <div class="text-xs text-secondary-400 dark:text-secondary-200 font-medium leading-5">
                        Employees peak absences
                    </div>
                </div>
            </div>
            <div class="card card--bordered card__body flex items-start gap-5 px-5 py-4">
                <div class="border dark:border-secondary rounded-lg p-2">
                    <BarChart2Icon class="!w-5 !h-5" />
                </div>
                <div class="flex flex-col gap-1">
                    <div class="text-lg font-semibold">
                        {{ averageEmployeeAbsence }}
                    </div>
                    <div class="text-xs text-secondary-400 dark:text-secondary-200 font-medium leading-5">
                        Average number of absences for period
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import LineChart from '~/sections/Leave/components/LineChart.vue'
import ChartMonthLine from '~/sections/Leave/components/ChartMonthLine.vue'
import { preventDuplication } from '~/lib/Helper/PromiseHelper'
import type { DateRange } from '@/lib/core/helper/DateHelper'
import { getDaysInRange } from '@/lib/core/helper/DateHelper'
import { getRangeThisMonth, getRangeThisWeek, getRangeThisYear } from '@/lib/core/helper/DateHelper'
import type SearchController from '~/lib/Search/SearchController'
import ArrayConditionHelper from '~/lib/Helper/ArrayCondition/ArrayConditionHelper'
import { toastError } from '@/lib/core/helper/ToastHelper'

type ChartDate = {
    month: number,
    year: number,
    days: number,
}

const props = defineProps<{
    searchController: SearchController
}>()

const emit = defineEmits<{
    selectDateRange: [DateRange]
}>()

enum RangeType {
    Year = 'year',
    Month = 'month',
    Week = 'week',
    Custom = 'custom',
}

const { useModel } = useContext()

const formatter = useService('formatter')

const leaveRequestModel = useModel('LeaveRequest')

const summaryData = ref<number[][]>([[]])
const loading = ref(false)

const chartElement = ref<HTMLElement>()

let lastSelectedRangeType: RangeType | undefined = undefined

const selectedRangeType = ref<RangeType>()

const selectedCustomRange = ref<DateRange>()

const dateNow = new Date()
const currentYear = dateNow.getFullYear()
const currentMonth = dateNow.getMonth()

const selectedDateRange = ref<DateRange>()

const monthTabs = (() => {
    const result: {
        title: string,
        value: ChartDate
    }[] = []

    for (let i = 0; i < 12; i++) {
        const date = new Date(currentYear, i, 1)
        const dateParts = date.getParts()

        result.push({
            title: `${dateParts.MMMM}`,
            value: {
                month: i,
                year: Number(dateParts.yyyy),
                days: new Date(currentYear, i + 1, 0).getDate(),
            },
        })
    }

    return result
})()

const selectedPeriod = shallowRef<ChartDate>(monthTabs[dateNow.getMonth()].value)

const weekDays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']

useSuspensableComponent(async () => {
    setSelectedRangeType(RangeType.Year)
    await fetchData()
})

// computed

const dataPoints = computed(() => {
    if (selectedRangeType.value == RangeType.Month) {
        return [summaryData.value[currentMonth]]
    }

    if (selectedRangeType.value == RangeType.Week) {
        return getWeekData(summaryData.value)
    }

    return summaryData.value
})

const averageEmployeeAbsence = computed(() => {
    const data = getSelectedPeriod.value !== undefined
        ? dataPoints.value[getSelectedPeriod.value]
        : dataPoints.value.flat()

    if (!data) return 0

    const validData = data.flat().filter(d => d !== 0)
    const sum = validData.reduce((p, c) => p + c, 0)
    const avg = Math.round(sum / validData.length)

    return Number.isNaN(avg) ? 0 : avg
})

const peakEmployeeAbsence = computed((): { label: string, count: number } => {
    const data = dataPoints.value
    const start = selectedDateRange.value?.start
    const end = selectedDateRange.value?.end

    const peakData = {
        label: 'No peak data',
        count: 0,
    }

    if (!data || !start || !end) return peakData

    const info = getPeakAbsenceInfo(dataPoints.value, start, end, getSelectedPeriod.value)

    if (!info) return peakData

    const parts = info.date.getParts()

    peakData.label = selectedRangeType.value === RangeType.Week ? parts.EEEE : `${parts.MMM} ${parts.d}, ${parts.yyyy}`
    peakData.count = info.max

    return peakData
})

const displayPeakAbsences = computed(() => {
    const peakData = peakEmployeeAbsence.value

    if (peakData.label == 'No peak data') return peakData.label

    return `${peakData.label} (${peakData.count} employees)`
})

const busiestMonth = computed((): string => {
    const data = dataPoints.value

    if (selectedRangeType.value == RangeType.Year) {
        let peakMonthIndex = -1
        let peakMonthValue = -Infinity

        data.forEach((monthData, idx) => {
            const avg = monthData.reduce((a, b) => a + b, 0) / monthData.length

            if (avg > peakMonthValue) {
                peakMonthValue = avg
                peakMonthIndex = idx
            }
        })

        return `${new Date(currentYear, peakMonthIndex).getParts().MMMM}`
    }

    return `${peakEmployeeAbsence.value.label}`
})

const getSelectedPeriod = computed(() => {
    return selectedRangeType.value == RangeType.Year ?  monthTabs.findIndex(item => selectedPeriod.value.month == item.value.month) : undefined
})

const buttonWidths = computed(() => {
    const totalDays = monthTabs.reduce((a, b) => a + b.value.days, 0)

    return monthTabs.map(tab => `${((tab.value.days / totalDays) * 100).toFixed(4)}%`)
})

const customDates = computed(() => {
    return displaySelectedPeriod.value.split(' - ')
})

const displaySelectedPeriod = computed(() => {
    return formatter.dateRange({ start: selectedDateRange.value.start.unixTimestamp(), end: selectedDateRange.value.end.unixTimestamp() }, 'UTC', { full: true })
})

// functions

const updateSelectedRange = (dateRange: DateRange) => {
    if (selectedRangeType.value == RangeType.Custom)
    {
        if (getDaysInRange(dateRange) > 366) {
            toastError('The date range must not exceed 366 days')

            return
        }

        selectedDateRange.value = dateRange
    }
}

const setSelectedRangeType = (range: RangeType) => {
    if (selectedRangeType.value === range) return

    lastSelectedRangeType = selectedRangeType.value

    switch (range) {
        case RangeType.Custom:
            selectedDateRange.value = getRangeThisMonth('UTC')
            selectedCustomRange.value = selectedDateRange.value
            break

        case RangeType.Year:
            selectedDateRange.value = getRangeThisYear('UTC')
            break

        case RangeType.Month:
            selectedDateRange.value = getRangeThisMonth('UTC')
            break

        case RangeType.Week:
            selectedDateRange.value = getRangeThisWeek('UTC')
            break
    }

    if (range != RangeType.Custom) {
        selectedCustomRange.value = undefined
    }

    selectedRangeType.value = range
}

function getPeakAbsenceInfo(data: number[][], start: Date, end: Date, period?: number) {
    if (!data.length || !start || !end) return null

    const sliced = period != null && data[period]
        ? [data[period]]
        : data

    if (!sliced[0]) return null

    const [rangeStart, rangeEnd] = getDateRangeByPeriod(start, end, period)

    let max = -Infinity, idx = -1, flatIndex = 0

    for (const row of sliced) {
        for (const value of row) {
            if (value > max) {
                max = value
                idx = flatIndex
            }
            flatIndex++
        }
    }

    if (idx < 0) return null

    const totalPoints = sliced.reduce((sum, row) => sum + row.length, 0)
    const fraction = (idx + 0.5) / totalPoints
    const timestamp = rangeStart.getTime() + fraction * (rangeEnd.getTime() - rangeStart.getTime())

    return { max, date: new Date(timestamp) }
}

const getDateRangeByPeriod = (start: Date, end: Date, period?: number): [Date, Date] => {
    if (period == null) return [start, end]

    const year = start.getUTCFullYear()
    const baseMonth = start.getUTCMonth()
    const month = baseMonth + period

    const rangeStart = new Date(Date.UTC(year, month, 1))
    let rangeEnd = new Date(Date.UTC(year, month + 1, 1))

    if (rangeEnd.getTime() > end.getTime()) {
        rangeEnd = end
    }

    return [rangeStart, rangeEnd]
}

const fetchData = async () => {
    await preventDuplication(async () => {
        let dates = null

        if (selectedRangeType.value == RangeType.Custom && selectedDateRange.value) {
            dates = {
                from: selectedDateRange.value.start.unixTimestamp(),
                to: selectedDateRange.value.end.unixTimestamp(),
            }
        }

        const where = new ArrayConditionHelper()
        props.searchController.applyCondition(where)

        const params = where.getParams()

        summaryData.value = (await leaveRequestModel.actions.getSummaryChartData({
            search_params: params,
            dates,
        })).data
    }, loading)
}

const changePeriod = (date: ChartDate) => {
    if (selectedPeriod.value.month != date.month) {
        const dateRange: DateRange = {
            start: new Date(Date.UTC(currentYear, date.month, 1, 0, 0, 0, 0)),
            end: new Date(Date.UTC(currentYear,  date.month + 1, 0, 23, 59, 59, 999)),
        }
        emit('selectDateRange', dateRange)
    }
    selectedPeriod.value = date
}

const getWeekData = (yearData: number[][]): number[][] => {
    const week = Array(7).fill(0)

    const startOfWeek = new Date(dateNow)
    const day = startOfWeek.getDay() || 7
    startOfWeek.setDate(dateNow.getDate() - day + 1)

    for (let i = 0; i < 7; i++) {
        const current = new Date(startOfWeek)
        current.setDate(startOfWeek.getDate() + i)

        const m = current.getMonth()
        const d = current.getDate() - 1
        week[i] = yearData[m]?.[d] ?? 0
    }

    return [week]
}

//

const stopWatch = props.searchController.useChangeHandler(
    async () => {
        await fetchData()
    },
)

onUnmounted(() => {
    stopWatch?.()
})

watch(() => selectedDateRange.value, async (range: DateRange) => {
    if (selectedRangeType.value === RangeType.Year) {
        const selectedMonthRange: DateRange = {
            start: new Date(Date.UTC(currentYear, selectedPeriod.value.month, 1, 0, 0, 0, 0)),
            end: new Date(Date.UTC(currentYear, selectedPeriod.value.month + 1, 0, 23, 59, 59, 999)),
        }
        emit('selectDateRange', selectedMonthRange)
    } else {
        emit('selectDateRange', range)
    }

    const current = selectedRangeType.value

    if (current === RangeType.Custom || (lastSelectedRangeType === RangeType.Custom && current !== RangeType.Custom)) {
        await fetchData()
    }
})

defineExpose({
    fetchData,
})
</script>
