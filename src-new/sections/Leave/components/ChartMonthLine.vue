<template>
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="98"
        height="50"
        viewBox="0 0 98 50"
        fill="none"
    >
        <path
            d="M97 1C81.4031 2.29974 80.4372 33.4164 65 37C52.2702 39.9551 45.8241 23.1804 33 25C19.0903 26.9737 14.6391 44.4536 1 49H97V1Z"
            :fill="fill"
            fill-opacity="0.1"
        />
        <path
            d="M1 49C14.6391 44.4536 19.0903 26.9737 33 25C45.8241 23.1804 52.2702 39.9551 65 37C80.4372 33.4164 81.4031 2.29974 97 1"
            :stroke="stroke"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
    </svg>
</template>

<script setup lang="ts">
const props = defineProps<{
    fill: string,
    stroke: string
}>()
</script>

