<template>
    <div class="relative">
        <div class="flex flex-col justify-end gap-6 h-[210px] mr-2 absolute -left-7 -top-1">
            <div
                v-for="(value, index) in yAxisLabels"
                :key="index"
                class="text-center text-secondary dark:text-secondary-200 text-xs"
            >
                {{ value }}
            </div>
        </div>
        <div ref="chartContainer" class="bg-white dark:bg-[#1A202E] h-[200px] relative">
            <canvas
                ref="chartCanvas"
                class="canvas"
            />
            <canvas
                ref="hoverCanvas"
                class="canvas"
                @mousemove="handleMouseMove"
                @mouseleave="hideTooltip"
            />
            <div
                v-if="tooltip.show"
                :style="tooltip.style"
                class="tooltip"
            >
                <div>{{ tooltip.text }}</div>
                <div>{{ tooltip.date }}</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useLeaveSummaryChart } from '~/sections/Leave/composable/useLeaveSummaryChart'
import { useLeaveSummaryChartDataHelper } from '~/sections/Leave/composable/useLeaveSummaryChartDataHelper'
import { useResizeObserver } from '@vueuse/core'

const props = defineProps<{
    dataPoints: number[][]
    highlightedArea?: number
    startDate?: Date
}>()

const { isDark } = useDarkMode()

const dataPoints = computed(() => props.dataPoints.flat())

const { colors, drawLine, drawGradient, drawAverageLine, drawGrid, drawGridForSection, drawHoverEffects } = useLeaveSummaryChart()

const { getAverageLineY, yAxisMax, yAxisLabels, getPoints } = useLeaveSummaryChartDataHelper(props.dataPoints)

const chartCanvas = ref<HTMLCanvasElement>()

const hoverCanvas = ref<HTMLCanvasElement>()

const chartContainer = ref(null)

const tooltip = ref<{
    show: boolean
    text: string
    date: string
    style: {
        top: string
        left: string
    }
}>({ show: false, text: '', date: '', style: { top: '0px', left: '0px' } })

const hoverPoint = ref<{
    x: number, y: number
} | null>(null)

useResizeObserver(chartCanvas, (entries) => {
    const el = entries[0].contentRect

    resizeCanvas(el.width, el.height)
    drawChart()
})

const resizeCanvas = (width: number, height: number) => {
    if (chartCanvas.value) {
        chartCanvas.value.width =  width
        chartCanvas.value.height = height
    }

    if (hoverCanvas.value) {
        hoverCanvas.value.width = width
        hoverCanvas.value.height = height
    }
}

const getHighlightedAreaPoints = () => {
    const points = props.dataPoints

    const data = points[props.highlightedArea]

    const offsetX = points.filter((val, index) => index < props.highlightedArea).flat().length

    return getPoints(chartCanvas.value!, data, offsetX)
}

const drawChart = () => {
    if (!chartCanvas.value) {
        return
    }

    const canvas = chartCanvas.value

    const ctx = canvas.getContext('2d') as CanvasRenderingContext2D

    ctx.clearRect(0, 0, canvas.width, canvas.height)

    ctx.lineWidth = 1
    const allPoints = getPoints(canvas, dataPoints.value)

    const averageY = getAverageLineY(canvas.height)

    if (props.highlightedArea) {
        drawGridForSection(canvas, props.dataPoints, colors.value.grid, props.highlightedArea)
        const highlightedCoords = getHighlightedAreaPoints()
        drawGradient(canvas, highlightedCoords, colors.value.inactiveGradient)
    }

    drawGrid(canvas, colors.value.grid)

    if (dataPoints.value.length > 1) {
        drawAverageLine(canvas, averageY, colors.value.averageLine)
    }

    ctx.lineWidth = 1

    drawLine(ctx, allPoints,  colors.value.primary, colors.value.danger, averageY)
}

const handleMouseMove = (event: MouseEvent) => {
    const canvas = hoverCanvas.value as HTMLCanvasElement

    const { offsetX } = event
    const index = Math.round((offsetX / canvas.width) * (dataPoints.value.length - 1))

    const pointValue = dataPoints.value[index]

    if (pointValue  != undefined) {
        const x = dataPoints.value.length == 1 ? canvas.width / 2 : (canvas.width / (dataPoints.value.length - 1)) * index
        const y = canvas.height - ((pointValue) / (yAxisMax.value)) * canvas.height

        hoverPoint.value = { x, y }

        if (hoverPoint.value) {
            if (pointValue == 0) {
                hideTooltip()

                return
            } else {
                drawHoverEffects(canvas, colors.value.hoverPoint, colors.value.hoverLine, hoverPoint.value.x, hoverPoint.value.y)
            }
        }

        const newDate = new Date(props.startDate.getTime())
        newDate.setDate(newDate.getDate() + index)

        const tooltipParts = newDate.getParts()

        tooltip.value.style = { top: `${y - 20}px`, left: `${x}px` }
        tooltip.value.text = `${pointValue} employees on leave`
        tooltip.value.date = `${tooltipParts.MMM} ${tooltipParts.d}, ${tooltipParts.yyyy}`
        tooltip.value.show = true
    }
}

const hideTooltip = () => {
    tooltip.value.show = false
    hoverPoint.value = null

    const canvas = hoverCanvas.value as HTMLCanvasElement
    const ctx = canvas.getContext('2d') as CanvasRenderingContext2D

    ctx.clearRect(0, 0, canvas.width, canvas.height)
}

watch(() => isDark.value, () => {
    drawChart()
})
</script>

<style scoped>
.tooltip {
    transform: translate(-50%, -100%);
    @apply absolute !w-32 bg-secondary-900 dark:bg-[#242B3A] text-white px-2 py-1.5 text-2xs rounded pointer-events-none z-[100];
}
.canvas {
    @apply absolute z-1 w-full h-full;
}
</style>
