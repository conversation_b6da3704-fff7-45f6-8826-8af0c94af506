<template>
    <div class="card card--bordered">
        <div class="card__header">
            <div class="card__title">
                Requests
            </div>
            <div class="flex gap-4">
                <AppPaginationCompact class="flex-none" :pagination="leaveRequestList.pagination" />
                <AppPageSize class="flex-none" :pagination="leaveRequestList.pagination" />
                <AppButton class="--primary" @click="openCreateLeaveRequestModal()">
                    <CalendarIcon />Leave request
                </AppButton>
            </div>
        </div>
        <div class="card__body h-flex overflow-auto fancy-scroll">
            <AppTable
                :items="requestList"
                :columns="columns"
                :sort-controller="sortController"
                zebra
                class="--rounded --bordered"
            >
                <template #body>
                    <tr v-for="(leaveRequest, index) of requestList" :key="index">
                        <td>
                            {{
                                formatter.dateRange({start: leaveRequest.dates.leave_start, end: leaveRequest.dates.leave_end}, 'UTC')
                            }}
                        </td>
                        <td class="text-center">
                            {{ leaveRequest.days_count }}
                        </td>
                        <td class="whitespace-normal">
                            {{ leaveRequest.comment }}
                        </td>
                        <td class="text-center">
                            {{ leaveRequestTypeDictionary.find(leaveRequest.request_type).title }}
                        </td>
                        <td>
                            <div class="flex gap-2">
                                <div class="flex items-start">
                                    <span class="rounded-full p-1 mt-1" :class="leaveRequestStatusDictionary.find(leaveRequest.status).bgStyleClass" />
                                </div>
                                {{ leaveRequestStatusDictionary.find(leaveRequest.status).title }}
                            </div>
                        </td>
                        <td class="justify-items-center">
                            <div class="flex gap-0.5">
                                <AppButton
                                    v-if="leaveRequest.status === LeaveRequestStatus.Pending"
                                    class="--only --danger --ghost"
                                    @click="deleteRequest(usePk(leaveRequest))"
                                >
                                    <TrashIcon />
                                </AppButton>
                                <AppButton
                                    v-if="leaveRequest.status === LeaveRequestStatus.Pending"
                                    class="--only --secondary --ghost"
                                    @click="openCreateLeaveRequestModal(usePk(leaveRequest))"
                                >
                                    <EditIcon />
                                </AppButton>
                                <AppButton class="--only --secondary --ghost" @click="openViewModal(usePk(leaveRequest))">
                                    <EyeIcon />
                                </AppButton>
                            </div>
                        </td>
                    </tr>
                </template>
            </AppTable>
        </div>
    </div>
</template>

<script setup lang="ts">
import AppTable from '~/components/Table/AppTable.vue'
import { LeaveRequestStatus } from '~/api/models/LeaveManagement/LeaveRequest'
import LeaveRequestCreateModal from '~/modals/Leave/LeaveRequestCreateModal.vue'
import LeaveRequestReviewModal from '~/modals/Leave/LeaveRequestReviewModal.vue'
import { $confirmDelete } from '@/plugins/ConfirmPlugin'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import type {
    LeaveRequestCardListColumns,
    LeaveRequestCardListSearchController,
    LeaveRequestCardListSortController,
} from '~/sections/Leave/PersonalLeave/composable/useLeaveRequestCardListSearchController'

const props = defineProps<{
    agentPk: PrimaryKey
    searchController: LeaveRequestCardListSearchController,
    sortController: LeaveRequestCardListSortController,
    columns: LeaveRequestCardListColumns,
}>()

const { useModel, useDictionary } = useContext()

const leaveRequestModel = useModel('LeaveRequest')
const leaveRequestStatusDictionary = useDictionary('LeaveRequestStatus')
const leaveRequestTypeDictionary = useDictionary('LeaveRequestType')

const formatter = useService('formatter')

const createRequestModal = useModal(LeaveRequestCreateModal)

const viewRequestModal = useModal(LeaveRequestReviewModal)

const leaveRequestList = leaveRequestModel.useList({
    where: (and) => {
        props.searchController.applyCondition(and)
        and.eq('beneficiary_pk', props.agentPk)
    },
    pageSize: 40,
})

props.searchController.useList(leaveRequestList)

const suspense = useSuspensableComponent(async () => {
    await leaveRequestList.fetch()
})

watch(() => props.agentPk, async () => {
    await suspense.fetch()
})

const requestList = computed(() => {
    // eslint-disable-next-line vue/no-mutating-props
    return props.sortController.sort(leaveRequestList.records)
})

const deleteRequest = async (pk: PrimaryKey) => {
    await $confirmDelete('Are you sure you want to delete this request?')

    await useModel('LeaveRequest').actions.delete({ pk })

    toastSuccess('Leave request was deleted successfully')
    await suspense.fetch()
}

const openCreateLeaveRequestModal = async (pk?: PrimaryKey) => {
    await createRequestModal.open({
        pk,
    })

    await suspense.fetch()
}

const openViewModal = (pk: PrimaryKey) => {
    viewRequestModal.open({
        pk,
    })
}
</script>
