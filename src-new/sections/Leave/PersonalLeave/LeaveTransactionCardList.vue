<template>
    <div class="card card--bordered">
        <div class="card__header card__title">
            Available days history
        </div>
        <div class="card__body h-flex overflow-auto fancy-scroll">
            <AppTable
                :items="leaveTransactionList.records"
                :columns="columns"
                zebra
                class="--rounded --bordered"
            >
                <template #body>
                    <tr v-for="(leaveTransaction, index) of leaveTransactionList.records" :key="index">
                        <td>
                            {{ formatter.date(leaveTransaction.created_at, undefined, { full: true }) }},
                            <span class="text-secondary">{{ formatter.time(leaveTransaction.created_at) }}</span>
                        </td>
                        <td :class="leaveTransaction.amount < 0 ? 'text-secondary' : 'text-success'">
                            {{ leaveTransaction.amount < 0 ? '' : '+' }}{{ leaveTransaction.amount }} {{ Math.abs(leaveTransaction.amount) > 1 ? 'days' : 'day' }}
                        </td>
                    </tr>
                </template>
            </AppTable>
        </div>
    </div>
</template>

<script setup lang="ts">
import AppTable from '~/components/Table/AppTable.vue'

const props = defineProps<{
    agentPk: PrimaryKey
}>()

const { useModel } = useContext()
const leaveTransactionModel = useModel('LeaveTransaction')
const leaveTransactionList = leaveTransactionModel.useList({
    where: and => and.eq('beneficiary_pk', props.agentPk),
})
const formatter = useService('formatter')

const columns = useTableColumns({
    leave_date: {
        label: 'Date',
        width: 'min',
    },
    days_count: {
        label: 'Count',
        width: 'min',
    },
})

const suspense = useSuspensableComponent(async () => {
    await leaveTransactionList.fetch()
})

watch(() => props.agentPk, async () => {
    await suspense.fetch()
})
</script>
