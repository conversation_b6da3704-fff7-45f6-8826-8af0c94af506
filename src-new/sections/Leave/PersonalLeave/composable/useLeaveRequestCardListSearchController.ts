import SortController from '~/lib/Search/SortController'
import SearchController from '~/lib/Search/SearchController'
import SelectSearchTag from '~/lib/Search/Tag/SelectSearchTag'
import DateRangeSearchTag from '~/lib/Search/Tag/DateRangeSearchTag'
import TextSearchTag from '~/lib/Search/Tag/TextSearchTag'

export function useLeaveRequestCardListSearchController() {
    const { useDictionary } = useContext()

    const leaveRequestStatusDictionary = useDictionary('LeaveRequestStatus')
    const leaveRequestTypeDictionary = useDictionary('LeaveRequestType')

    const searchController = SearchController.forModel('LeaveRequest', {
        dates: new DateRangeSearchTag('Leave dates', { timezone: 'UTC' }),
        comment: new TextSearchTag('Comment'),
        status: new SelectSearchTag('Status', leaveRequestStatusDictionary.mapRecords.forSelect()),
        request_type: new SelectSearchTag('Type', leaveRequestTypeDictionary.mapRecords.forSelect()),
    }, {
        syncWithQuery: false,
    })

    const columns = useTableColumns({
        dates: {
            label: 'Leave Dates',
            sortable: true,
            width: 'min',
        },
        days_count: {
            label: 'Days',
            sortable: true,
            width: 'min',
        },
        comment: {
            label: 'Comment',
            sortable: true,
        },
        request_type: {
            label: 'Type',
            sortable: true,
            width: 'min',
        },
        status: {
            label: 'Status',
            sortable: true,
            width: 'min',
        },
        action: {
            label: 'Actions',
            width: 'min',
        },
    })

    const sortController = SortController.fromColumns(columns, {})

    return {
        columns,
        sortController,
        searchController,
    }
}

export type LeaveRequestCardListSearchController = ReturnType<typeof useLeaveRequestCardListSearchController>['searchController']
export type LeaveRequestCardListSortController = ReturnType<typeof useLeaveRequestCardListSearchController>['sortController']
export type LeaveRequestCardListColumns = ReturnType<typeof useLeaveRequestCardListSearchController>['columns']
