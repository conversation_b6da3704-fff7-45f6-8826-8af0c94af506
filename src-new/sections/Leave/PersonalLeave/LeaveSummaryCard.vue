<template>
    <div class="card card--bordered">
        <div class="card__header card__title">
            <div class="flex items-center gap-1">
                <div v-if="agent" class="flex items-center gap-1">
                    <AppAvatar
                        :image="agent.avatar"
                        class="w-6 h-6"
                    />
                    <span>{{ getFullName(agent) }}.</span>
                </div>
                <span>Summary of leave</span>
            </div>
        </div>
        <div class="card__body flex gap-4">
            <div class="card card--bordered card__body flex flex-col gap-5 w-full">
                <div class="flex justify-between items-center">
                    <span class="--success --xl --soft --outline badge --only">
                        <CheckIcon class="!w-5 !h-5" />
                    </span>

                    <SuspenseManual :state="suspense">
                        <template #default>
                            <span class="font-semibold text-3xl">{{ leaveCounter.available_days }}</span>
                        </template>
                        <template #fallback>
                            <PlaceholderText class="font-semibold text-3xl" :chars="2" />
                        </template>
                    </SuspenseManual>
                </div>

                <div class="flex flex-col gap-1">
                    <div class="font-semibold text-base">
                        Days available
                    </div>
                    <div class="font-medium text-sm text-secondary-400">
                        To book time off
                    </div>
                </div>
            </div>

            <div class="card card--bordered card__body flex flex-col gap-5 w-full">
                <div class="flex justify-between items-center">
                    <span class="--secondary --xl --outline badge --only">
                        <CalendarClockIcon class="!w-5 !h-5" />
                    </span>

                    <SuspenseManual :state="suspense">
                        <template #default>
                            <span class="font-semibold text-3xl">{{ leaveCounter.pending_requests }}</span>
                        </template>
                        <template #fallback>
                            <PlaceholderText class="font-semibold text-3xl" :chars="2" />
                        </template>
                    </SuspenseManual>
                </div>

                <div class="flex flex-col gap-1">
                    <div class="font-semibold text-base">
                        Pending requests
                    </div>
                    <div class="font-medium text-sm text-secondary-400">
                        Awaiting approval
                    </div>
                </div>
            </div>

            <div class="card card--bordered card__body flex flex-col gap-5 w-full">
                <div class="flex justify-between items-center">
                    <span class="--secondary --xl --outline badge --only">
                        <CalendarPlusIcon class="!w-5 !h-5" />
                    </span>

                    <SuspenseManual :state="suspense">
                        <template #default>
                            <span class="font-semibold text-3xl">{{ leaveCounter.approved_future_days }}</span>
                        </template>
                        <template #fallback>
                            <PlaceholderText class="font-semibold text-3xl" :chars="2" />
                        </template>
                    </SuspenseManual>
                </div>

                <div class="flex flex-col gap-1">
                    <div class="font-semibold text-base">
                        Days upcoming
                    </div>
                    <div class="font-medium text-sm text-secondary-400">
                        Approved days
                    </div>
                </div>
            </div>
            <div class="card card--bordered card__body flex flex-col gap-5 w-full">
                <div class="flex justify-between items-center">
                    <span class="--secondary --xl --outline badge --only">
                        <AlertCircleIcon class="!w-5 !h-5" />
                    </span>

                    <SuspenseManual :state="suspense">
                        <template #default>
                            <span class="font-semibold text-3xl">{{ leaveCounter.sick_days }}</span>
                        </template>
                        <template #fallback>
                            <PlaceholderText class="font-semibold text-3xl" :chars="2" />
                        </template>
                    </SuspenseManual>
                </div>

                <div class="flex flex-col gap-1">
                    <div class="font-semibold text-base">
                        Sick days
                    </div>
                    <div class="font-medium text-sm text-secondary-400">
                        Taken sick days
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import CalendarClockIcon from '~assets/icons/CalendarClockIcon.svg?component'
import CalendarPlusIcon from '~assets/icons/CalendarPlusIcon.svg?component'
import PlaceholderText from '@/components/Placeholder/PlaceholderText.vue'
import { getFullName } from '~/lib/Helper/PersonHelper'

const props = defineProps<{
    agentPk: PrimaryKey
}>()

const { useModel } = useContext()
const leaveCounterRecord = useModel('LeaveCounter').useRecord({ with: ['agent']})
const leaveCounter = await leaveCounterRecord.fetch(props.agentPk)

const agent = computed(() => {
    return leaveCounter.value.agent
})

const suspense = useSuspensableComponent(async () => {
    await leaveCounterRecord.fetch(props.agentPk)
})

watch(() => props.agentPk, async () => {
    await suspense.fetch()
})
</script>
