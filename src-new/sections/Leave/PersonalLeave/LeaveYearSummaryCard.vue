<template>
    <div class="card card--bordered">
        <div class="card__header card__title">
            Yearly leave<br>
        </div>
        <div class="card__body">
            <div class="flex relative pt-2">
                <svg
                    width="240"
                    height="132"
                    viewBox="0 0 240 132"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M226.167 118C226.167 103.817 223.371 89.7734 217.939 76.6702C212.508 63.567 204.546 51.6612 194.51 41.6325C184.473 31.6037 172.558 23.6485 159.445 18.221C146.332 12.7935 132.277 10 118.083 10C103.89 9.99999 89.835 12.7935 76.7217 18.221C63.6084 23.6485 51.6934 31.6037 41.6569 41.6324C31.6204 51.6611 23.6591 63.567 18.2274 76.6701C12.7957 89.7733 10 103.817 10 118"
                        stroke="#EBEDF3"
                        style="stroke:#EBEDF3;stroke:color(display-p3 0.9216 0.9294 0.9529);stroke-opacity:1;"
                        stroke-width="20"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                    />
                    <path
                        ref="progressPath"
                        d="M226.167 118C226.167 103.817 223.371 89.7734 217.939 76.6702C212.508 63.567 204.546 51.6612 194.51 41.6325C184.473 31.6037 172.558 23.6485 159.445 18.221C146.332 12.7935 132.277 10 118.083 10C103.89 9.99999 89.835 12.7935 76.7217 18.221C63.6084 23.6485 51.6934 31.6037 41.6569 41.6324C31.6204 51.6611 23.6591 63.567 18.2274 76.6701C12.7957 89.7733 10 103.817 10 118"
                        stroke="#3B76F6"
                        :stroke-dasharray="pathLength"
                        :stroke-dashoffset="dashOffset"
                        style="stroke:#3B76F6;stroke:color(display-p3 0.2314 0.4627 0.9647);stroke-opacity:1;"
                        stroke-width="20"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                    />
                </svg>
                <div class="h-[132px] w-[240px] flex items-end justify-center absolute bottom-3">
                    <div class="flex flex-col items-center">
                        <span class="font-medium text-xs text-secondary">Days per year</span>
                        <span class="font-semibold text-2xl">{{ maxDayOffs }}</span>
                    </div>
                </div>
                <div
                    class="--rounded  --soft --outline badge absolute top-0 right-0"
                    :class="{
                        '--success': percentage >= 75,
                        '--warning': percentage >= 50 && percentage < 75 ,
                        '--pending': percentage >= 25 && percentage < 50,
                        '--danger': percentage >= 0 && percentage < 25,
                    }"
                >
                    <template v-if="percentage >= 50">
                        <TrendingUpIcon /> {{ Math.round(percentage) }}% left
                    </template>
                    <template v-else>
                        <TrendingDownIcon /> {{ Math.round(percentage) }}% left
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
const props = defineProps<{
    agentPk: PrimaryKey
}>()

const { useModel } = useContext()
const leaveCounterRecord = await useModel('LeaveCounter').useRecord()
const leaveCounter = await leaveCounterRecord.fetch(props.agentPk)

const usedDaysInCurrentYear = computed(() => {
    if (leaveCounter.value.used_days_in_current_year) {
        return leaveCounter.value.used_days_in_current_year
    }

    return 0
})

const pathLength = ref(0)
const progressPath = ref(null)

const dashOffset = computed(() => {
    const offset = pathLength.value * ((maxDayOffs - usedDaysInCurrentYear.value) / maxDayOffs) * -1

    return offset > 0 ? 0 : offset
})

const maxDayOffs = 20

const percentage = computed(() => Math.ceil(100 - (usedDaysInCurrentYear.value / maxDayOffs) * 100))

onMounted(() => {
    if (progressPath.value) {
        pathLength.value = progressPath.value?.getTotalLength()
    }
})
watch(() => props.agentPk, async (newPk) => {
    await leaveCounterRecord.fetch(newPk)
}, { immediate: true },
)
</script>

