<template>
    <div class=" flex flex-col h-full gap-2">
        <Teleport v-if="filterContainer" :to="filterContainer">
            <SearchFilter class="w-[800px]" :controller="searchController" />
        </Teleport>

        <div v-if="hasPermission('viewPersonalBalance', 'LeaveRequest')" class="flex w-full gap-2">
            <LeaveSummaryCard :agent-pk="agentPk" class="flex-[7] h-full" />

            <LeaveYearSummaryCard :agent-pk="agentPk" class="flex-[2] h-full" />
        </div>

        <div class="flex w-full gap-2 h-flex">
            <LeaveRequestCardList
                class="flex-[7] h-full flex flex-col"
                :agent-pk="agentPk"
                :sort-controller="sortController"
                :search-controller="searchController"
                :columns="columns"
            />

            <LeaveTransactionCardList
                v-if="hasPermission('viewPersonalBalance', 'LeaveRequest')"
                class="flex-[2] h-full flex flex-col"
                :agent-pk="agentPk"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import LeaveRequestCardList from '~/sections/Leave/PersonalLeave/LeaveRequestCardList.vue'
import LeaveTransactionCardList from '~/sections/Leave/PersonalLeave/LeaveTransactionCardList.vue'
import LeaveSummaryCard from '~/sections/Leave/PersonalLeave/LeaveSummaryCard.vue'
import LeaveYearSummaryCard from '~/sections/Leave/PersonalLeave/LeaveYearSummaryCard.vue'
import SearchFilter from '~/components/Search/SearchFilter.vue'
import {
    useLeaveRequestCardListSearchController,
} from '~/sections/Leave/PersonalLeave/composable/useLeaveRequestCardListSearchController'

defineOptions({
    name: 'LeaveManagementSection',
})

defineProps<{
    filterContainer: HTMLElement | undefined
    agentPk: PrimaryKey
}>()

const { sortController, searchController, columns } = useLeaveRequestCardListSearchController()

const { hasPermission } = useContext()
</script>

