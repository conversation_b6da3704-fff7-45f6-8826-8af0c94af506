<template>
    <div>
        <div class="card card--bordered flex flex-col">
            <Teleport v-if="filterContainer" :to="filterContainer">
                <SearchFilter class="w-[800px]" :controller="searchController" />
            </Teleport>

            <div class="flex items-center justify-end gap-2 px-2.5 py-2">
                <AppPaginationInfo :pagination="list.pagination" />
                <AppPaginationCompact class="flex-none" :pagination="list.pagination" />
                <AppPageSize class="flex-none" :pagination="list.pagination" />
                <AppButton class="--primary" @click="openCreateEditModal()">
                    <CalendarIcon /> Leave request
                </AppButton>
            </div>

            <div class="overflow-auto fancy-scroll">
                <SuspenseManual :state="suspense">
                    <template #default>
                        <AppTable
                            :columns="columns"
                            :items="list.records"
                            :search-tags="searchController.tags"
                            :sort-controller="sortController"
                            zebra
                            class="--fixed-header"
                        >
                            <template #body>
                                <tr
                                    v-for="request in list.records"
                                    :key="request.pk"
                                >
                                    <td>
                                        <div class="flex items-center gap-2">
                                            <AppAvatar :model="request.agent" class="sales-agent-avatar" />
                                            <span>{{ getFullName(request.agent) }}</span>
                                        </div>
                                    </td>
                                    <td>{{ request.agent.team?.name }}</td>
                                    <td>{{ request.agent.department.name }}</td>
                                    <td>{{ formatter.dateRange({start:request.dates.leave_start, end: request.dates.leave_end}, 'UTC') }}</td>
                                    <td>
                                        <div class="flex justify-center">
                                            {{ request.days_count }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="whitespace-normal">
                                            {{ request.comment }}
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        {{ leaveRequestTypeDictionary.find(request.request_type).title }}
                                    </td>
                                    <td>
                                        <div class="flex gap-2">
                                            <div class="flex items-start">
                                                <span class="rounded-full p-1 mt-1" :class="leaveRequestStatusDictionary.find(request.status).bgStyleClass" />
                                            </div>
                                            {{ leaveRequestStatusDictionary.find(request.status).title }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="flex justify-center gap-0.5">
                                            <AppButton
                                                v-if="canEditRequest(request)"
                                                class="--only --danger --ghost"
                                                @click="deleteRequest(usePk(request))"
                                            >
                                                <TrashIcon />
                                            </AppButton>
                                            <AppButton
                                                v-if="canEditRequest(request)"
                                                class="--only --secondary --ghost"
                                                @click="openCreateEditModal(usePk(request))"
                                            >
                                                <EditIcon />
                                            </AppButton>
                                            <AppButton
                                                v-if="canReviewRequest(request)"
                                                class="--primary --ghost"
                                                @click="openReviewModal(usePk(request), true)"
                                            >
                                                <ClipboardIcon />
                                                <span class="text-primary">Review request</span>
                                            </AppButton>
                                            <AppButton
                                                v-else
                                                class="--only --secondary --ghost"
                                                @click="openReviewModal(usePk(request), true)"
                                            >
                                                <EyeIcon />
                                            </AppButton>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </AppTable>
                    </template>
                    <template #fallback>
                        <PlaceholderBlock class="w-full h-full" />
                    </template>
                </SuspenseManual>
            </div>
        </div>
        <AppTablePagination class="my-4 px-6" :pagination="list.pagination" />
    </div>
</template>

<script setup lang="ts">
import { LeaveRequestStatus } from '~/api/models/LeaveManagement/LeaveRequest'
import { getFullName } from '~/lib/Helper/PersonHelper'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import { $confirmDelete } from '@/plugins/ConfirmPlugin'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import LeaveRequestCreateModal from '~/modals/Leave/LeaveRequestCreateModal.vue'
import LeaveRequestReviewModal from '~/modals/Leave/LeaveRequestReviewModal.vue'
import { useLeaveRequestSearchController } from '~/sections/Leave/composable/useLeaveRequestSearchController'
import { PositionName } from '~/api/models/Position/Position'
import SearchFilter from '~/components/Search/SearchFilter.vue'
import type { ModelRef } from '~types/lib/Model'
defineProps<{
    filterContainer: HTMLElement | undefined
}>()

const { sortController, searchController, columns } = useLeaveRequestSearchController()

const { useCurrentUser, useModel, useDictionary } = useContext()

const currentUser = useCurrentUser()

const formatter = useService('formatter')

const createRequestModal = useModal(LeaveRequestCreateModal)

const reviewRequestModal = useModal(LeaveRequestReviewModal)

const leaveRequestStatusDictionary = useDictionary('LeaveRequestStatus')
const leaveRequestTypeDictionary = useDictionary('LeaveRequestType')

const isUserHasPositionAgent = isUserHasPosition(currentUser, PositionName.Agent)

const list = useModel('LeaveRequest').useList({
    with: ['agent', 'agent.team', 'agent.department'],
    sort: sortController,
    where: (and) => {
        searchController.applyCondition(and)
    },
    page: 1,
    pageSize: 20,
})

searchController.useList(list)

const suspense = useSuspensableComponent(async () => {
    await list.fetch()
})

const canEditRequest = (request: ModelRef<'LeaveRequest', 'agent'>) => {
    return request.status === LeaveRequestStatus.Pending && usePk(request.agent) === usePk(currentUser)
}

const canReviewRequest = (request: ModelRef<'LeaveRequest', 'agent'>) => {
    return request.status === LeaveRequestStatus.Pending && !isUserHasPositionAgent && usePk(request.agent) !== usePk(currentUser)
}

const deleteRequest = async (pk: PrimaryKey) => {
    await $confirmDelete('Are you sure you want to delete this request?')
    await useModel('LeaveRequest').actions.delete({ pk })

    toastSuccess('Leave request was deleted successfully')
    await suspense.fetch()
}

const openCreateEditModal = async (pk?: PrimaryKey) => {
    await createRequestModal.open({
        pk,
    })

    await suspense.fetch()
}

const openReviewModal = (pk: PrimaryKey, edit: boolean = false) => {
    reviewRequestModal.open({
        pk,
        edit,
    })
}
</script>
