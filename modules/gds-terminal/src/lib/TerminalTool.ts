import SabreGDS from '~modules/gds-terminal/src/lib/gds/SabreGDS'
import type { GDS } from '~modules/gds-terminal/src/lib/GDS'
import type { MatrixSize } from '~modules/gds-terminal/src/store/SessionStore'
import { SessionStore } from '~modules/gds-terminal/src/store/SessionStore'
import { reactive } from 'vue'
import EventBus from '~/lib/EventBus'
import type { WebSocketEventServiceInterface } from '~/service/WebSocketEventService/WebSocketEventServiceInterface'
import type { ComponentInternalInstance } from 'vue'
import type { TerminalColorSchemesMap } from '~modules/gds-terminal/src/helpers/ThemeTerminalHelper'

export enum TerminalEnvironment {
    Stage = 'stage',
    Production = 'prod',
    Mock = 'mock',
}

export type TerminalToolOptions = {
    /**
     * Unique key for the terminal tool instance
     * Terminals with the same key will share output and input
     */
    key: string,

    /**
     * Unique key for terminal storage
     * Terminals with the same storage key will share the same view settings
     * Can be used like: 'full-page', 'sidebar', 'popup' etc.
     */
    storageKey: string,

    /**
     * Default grid size for the terminal layout configuration
     */
    defaultLayoutGrid?: MatrixSize

    /**
     * Highlight if production environment is enabled
     */
    highlightProductionEnvironment?: boolean

    /**
     * Default environment for terminal backend
     */
    defaultEnvironment: TerminalEnvironment
}

export default class TerminalTool {
    public constructor(
        public readonly options: TerminalToolOptions,
        public wsService: WebSocketEventServiceInterface,
    ) {
        this.state.environment = options.defaultEnvironment
    }

    protected state = reactive({
        input: '',
        isInitialized: false,
        environment: TerminalEnvironment.Stage,
    })

    public get input() {
        return this.state.input
    }

    public set input(value: string) {
        this.state.input = value.toUpperCase()
    }

    public clearInput() {
        this.input = ''
    }

    public confirmInput() {
        const input = this.state.input.trim()
        this.getActiveSessionStore().getActiveTerminal().confirmInput(input)

        this.eventBus.emit('confirmInput', input)
        this.clearInput()
    }

    public getEnvironment() {
        return this.state.environment
    }

    public terminalThemeSchemesMap = shallowRef<TerminalColorSchemesMap[]>([])

    public setTerminalThemeSchemesMap(value: TerminalColorSchemesMap[]) {
        this.terminalThemeSchemesMap.value = value
    }

    public async setEnvironment(environment: TerminalEnvironment) {
        if (this.state.environment === environment) {
            useLogger('terminal').info('Terminal environment is already set to:', environment)

            return
        }

        useLogger('terminal').info('Switching terminal environment to:', environment)

        this.state.environment = environment

        if (!this.activeGds.value) {
            return
        }

        //

        const sessionStores = this.sessions[this.activeGds.value.name] ?? []

        const promises: Promise<void>[] = []

        for (const sessionStore of Object.values(sessionStores)) {
            if (!sessionStore.terminalConnector.isReady) {
                continue
            }

            sessionStore.clearOutput()

            promises.push(
                sessionStore.resubscribe(),
            )
        }

        await Promise.all(promises)
    }

    public get isInitialized() {
        return this.state.isInitialized
    }

    protected set isInitialized(value: boolean) {
        this.state.isInitialized = value
    }

    protected get key() {
        return this.options.key
    }

    //

    public eventBus = new EventBus<{
        beforeInitialize(): Promise<void> | void,
        unmount(instance: ComponentInternalInstance): void,
        focus(): void,
        confirmInput(input: string): void
    }>()

    public async initialize() {
        if (this.isInitialized) {
            return
        }

        await this.eventBus.emitAwaitable('beforeInitialize')

        this.isInitialized = true
    }

    protected disposables: (() => Promise<void> | void)[] = []

    public async dispose() {
        for (const disposable of this.disposables) {
            await disposable()
        }

        for (const sessionStores of Object.values(this.sessions)) {
            for (const sessionStore of sessionStores) {
                await sessionStore.dispose()
            }
        }
    }

    public unmount(instance: ComponentInternalInstance) {
        this.eventBus.emit('unmount', instance)
    }

    //

    protected gdsSystems = [
        new SabreGDS(),
    ]

    public getDefaultGDSSystem(): GDS {
        return this.gdsSystems[0]
    }

    protected activeGds = shallowRef<GDS>()
    protected activeSessionStore = shallowRef<SessionStore>()
    protected sessions = shallowReactive<Record<string, SessionStore[]>>({})

    //

    // Session handling
    // ================

    public getGDSSessionStores(gds: GDS): SessionStore[] {
        const gdsSessions = this.sessions[gds.name]

        if (!gdsSessions) {
            throw new Error(`GDS "${gds.name}" was not initialized`)
        }

        return gdsSessions
    }

    public getActiveSessionStore(): SessionStore {
        if (!this.activeSessionStore.value) {
            throw new Error('Active session store is not set')
        }

        return this.activeSessionStore.value
    }

    public setActiveGds(gds: GDS): void {
        const gdsSessions = gds.createGDSSessions()
        const sessionStores: SessionStore[] = []

        for (const gdsSession of gdsSessions) {
            const sessionStore = markRaw(new SessionStore(gds, gdsSession, this))
            sessionStores.push(sessionStore)
        }

        this.sessions[gds.name] = sessionStores
        this.activeGds.value = gds
    }

    public getActiveGds(): GDS {
        if (!this.activeGds.value) {
            throw new Error('Active GDS is not set')
        }

        return this.activeGds.value
    }

    public async setActiveSessionStore(session: SessionStore) {
        const focusedTerminalWindowNumber = this.activeSessionStore.value?.activeWindowIndex.value ?? 0

        await session.activate()

        this.activeSessionStore.value = session

        await nextTick(() => {
            this.activeSessionStore.value?.focusTerminal(focusedTerminalWindowNumber)
        })
    }

    public async tryToSetActiveSessionStoreByGdsSessionName(gdsSessionName: string) {
        const gds = this.getActiveGds()

        const sessionStoreToSwitch = this.getGDSSessionStores(gds).find((sessionStore) => {
            return sessionStore.gdsSession.name === gdsSessionName
        })

        if (!sessionStoreToSwitch) {
            throw new Error(`Session store with name "${gdsSessionName}" not found`)
        }

        await this.setActiveSessionStore(sessionStoreToSwitch)
    }

    public hasUnsavedData() {
        return this.getActiveSessionStore()
            .terminalWindows.value
            .some((terminalWindow) => terminalWindow.terminal.processes.length)
    }
}

export enum TerminalFontSize {
    ExtraSmall = 10,
    Small = 12,
    Medium = 14,
    Large = 16,
    XLarge = 18,
}
