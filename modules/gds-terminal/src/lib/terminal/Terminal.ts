import { TerminalProcess, TerminalProcessStatus } from './TerminalProcess'
import { createUniqueIdentifier } from '~modules/gds-terminal/src/helpers/IdentificationHelper'
import TextDelta from '~modules/gds-terminal/src/lib/delta/Text/TextDelta'
import type Command from '~modules/gds-terminal/src/lib/commands/Command'
import EventBus from '~/lib/EventBus'
import ClearScreenCommand from '~modules/gds-terminal/src/lib/commands/ClearScreenCommand'
import HelpCommand from '~modules/gds-terminal/src/lib/commands/HelpCommand'
import ChangeSessionCommand from '~modules/gds-terminal/src/lib/commands/ChangeSessionCommand'
import type { TerminalConnector } from '~modules/gds-terminal/src/lib/terminal/TerminalConnector'
import type { SettingWorker } from '~modules/gds-terminal/src/lib/SettingWorker'
import type LocalCommand from '~modules/gds-terminal/src/lib/commands/LocalCommand'
import RemoteCommand from '~modules/gds-terminal/src/lib/commands/RemoteCommand'
import EmptyCommand from '~modules/gds-terminal/src/lib/commands/EmptyCommand'
import { CommandExecutionProcess } from '~modules/gds-terminal/src/lib/CommandExecutionProcess'
import { CommandExecutionProcessStatus } from '~modules/gds-terminal/src/lib/CommandExecutionProcess'
import ErrorDelta from '~modules/gds-terminal/src/lib/delta/Error/ErrorDelta'
import type { OutputDelta } from '~modules/gds-terminal/src/lib/OutputDelta'
import WPNCAllPriceCommand from '~modules/gds-terminal/src/lib/commands/Custom/WPNCAllPriceCommand'
import type TerminalTool from '~modules/gds-terminal/src/lib/TerminalTool'
import ReplicateCommand from '~modules/gds-terminal/src/lib/commands/Custom/ReplicateCommand'

export class Terminal {
    public readonly id = createUniqueIdentifier()

    public constructor(
        public terminalTool: TerminalTool,
        public processor: TerminalConnector,
        public settingsWorkerDriver: SettingWorker,
        public terminalNumber: number,
    ) {
        //
    }

    public async initialize() {
        await this.restoreState()
    }

    private async restoreState() {
        useLogger('gds-terminal').info('State restoring was skipped')

        return
        // try {
        //     const rawProcesses = await this.settingsWorkerDriver.getAllProcessByTerminalNumber(this.terminalNumber)
        //
        //     if (isDevelopment) {
        //        useLogger('gds-terminal').info('Hydrating terminal', rawProcesses)
        //     }
        //
        //     for (const rawProcess of rawProcesses) {
        //         const output = JSON.parse(rawProcess.output)
        //
        //         if (isDevelopment) {
        //             useLogger('gds-terminal').log('Hydrating process output', output)
        //         }
        //
        //         const process = TerminalProcess.hydrate(output, this)
        //
        //         if (process) {
        //             this.attachProcess(process)
        //         }
        //     }
        // } catch (e) {
        //     useLogger('gds-terminal').error('Failed to hydrate terminal')
        //     useLogger('gds-terminal').error(e)
        // }
    }

    public processes: TerminalProcess[] = shallowReactive([])

    //

    public async dispose() {
        for (let i = this.processes.length - 1; i >= 0; i--) {
            const process = this.processes[i]

            await process.cancel(this)
        }
    }

    //

    public attachProcess(process: TerminalProcess): void {
        if (process.state.status === TerminalProcessStatus.Pending || process.state.status === TerminalProcessStatus.InProgress) {
            process.eventBus.on('update', (process) => {
                // noinspection JSIgnoredPromiseFromCall
                this.settingsWorkerDriver.addOrUpdateProcess(process, this.terminalNumber)
            })
        }

        this.processes.push(process)
    }

    public get canAcceptInput() {
        return this.processor.isReady
    }

    public get isExecuting() {
        if (!this.processor.isReady) {
            return true
        }

        if (this.activeExecutionProcess.value) {
            return true
        }

        for (let i = this.processes.length - 1; i >= Math.max(0, this.processes.length - 100); i--) {
            if (this.processes[i].state.status === TerminalProcessStatus.InProgress) {
                return true
            }
        }

        return false
    }

    public localCommands: Array<{
        makeFromInput(input: string): LocalCommand | undefined
        describe(): OutputDelta[]
    }> = [
        EmptyCommand,
        HelpCommand,
        ClearScreenCommand,
        ChangeSessionCommand,
        WPNCAllPriceCommand,
        ReplicateCommand,
    ]

    public confirmInput(input: string) {
        if (!this.canAcceptInput) {
            return
        }

        const command = this.detectCommandFromInput(input)

        if (this.activeExecutionProcess.value) {
            if (this.activeExecutionProcess.value.canBeExtended()) {
                this.activeExecutionProcess.value.enqueue(command)
            } else {
                return
            }
        } else {
            this.activeExecutionProcess.value = new CommandExecutionProcess([command], { extendable: true })

            // noinspection JSIgnoredPromiseFromCall
            this.startCommandExecutionProcess(this.activeExecutionProcess.value)
        }
    }

    public detectCommandFromInput(input: string) {
        input = input.trim().toUpperCase()

        const localCommand = (() => {
            for (const command of this.localCommands) {
                const localCommand = command.makeFromInput(input)

                if (localCommand) {
                    return localCommand
                }
            }
        })()

        const command: Command = localCommand ?? new RemoteCommand(input)

        return command
    }

    public async executeCommand(command: Command) {
        useLogger('gds-terminal').info('Executing command', command)

        const process = markRaw(command.createProcess())

        this.attachProcess(process)
        this.eventBus.emit('output', process, true)

        this.scrollToProcess(process)

        if (process.state.status === TerminalProcessStatus.Pending) {
            await command.execute(this, process)
        }

        if (command.shouldScrollToCommandStart) {
            this.scrollToProcess(process)
        }
    }

    public eventBus = new EventBus<{
        output(process: TerminalProcess, isInput: boolean): void,
    }>()

    public internalEvents = new EventBus<{
        scrollTo(line: number): void
    }>()

    public scrollToLine(line: number) {
        this.internalEvents.emit('scrollTo', line)
    }

    public scrollToProcess(process: TerminalProcess) {
        let line = 0

        for (const p of this.processes) {
            if (p === process) {
                break
            }

            line += p.getNumberOfLines()
        }

        // Find number of lines before the process then add 1 to scroll to the first line of the process
        this.scrollToLine(line + 1)
    }

    public clearScreen() {
        this.terminalTool.clearInput()

        this.scrollToLine(this.getNumberOfLines() + 1)
    }

    public getNumberOfLines() {
        let lines = 0

        for (const process of this.processes) {
            lines += process.getNumberOfLines()
        }

        return lines
    }

    //

    protected activeExecutionProcess = shallowRef<CommandExecutionProcess>()

    public async startCommandExecutionProcess(commandExecutionProcess: CommandExecutionProcess) {
        this.activeExecutionProcess.value = commandExecutionProcess

        commandExecutionProcess.start()

        let caughtError: unknown

        while (commandExecutionProcess.status === CommandExecutionProcessStatus.Running) {
            useLogger('gds-terminal').log('Processing next command')

            try {
                await commandExecutionProcess.processNext(this)
            } catch (e: unknown) {
                caughtError = e

                break
            }
        }

        const isLastCommand = commandExecutionProcess.isLastCommandExecution()

        if (commandExecutionProcess.status === CommandExecutionProcessStatus.Failed) {
            useLogger('gds-terminal').error('Command execution process failed')

            if (!isLastCommand) {
                this.processes.at(-1)?.applyDeltas([
                    ErrorDelta.make('Command execution process failed'),
                ])
            }
        } else if (commandExecutionProcess.status === CommandExecutionProcessStatus.Stopped) {
            useLogger('gds-terminal').log('Command execution process stopped')

            if (!isLastCommand) {
                this.processes.at(-1)?.applyDeltas([
                    TextDelta.make('Command execution process stopped\n', { color: 'gray' }),
                ])
            }
        } else {
            useLogger('gds-terminal').log('Command execution process completed')
        }

        this.activeExecutionProcess.value = undefined

        if (caughtError) {
            useLogger('gds-terminal').error(caughtError)

            throw caughtError
        }
    }

    public stopCommandExecutionProcess() {
        if (!this.activeExecutionProcess.value) {
            return
        }

        this.activeExecutionProcess.value.stop()
    }

    public printOutput(outputDeltas: OutputDelta[]) {
        const terminalProcess = new TerminalProcess(new EmptyCommand, outputDeltas)
        terminalProcess.finish(this)
        this.attachProcess(terminalProcess)
    }

    public clearOutput() {
        for (const process of this.processes) {
            // noinspection JSIgnoredPromiseFromCall
            process.cancel(this)
        }

        this.processes.splice(0, this.processes.length)

        // @todo Update storage
    }

    public pasteOutputsFromTerminal(terminal: Terminal) {
        for (const process of terminal.processes) {
            this.printOutput(process.getOutput().slice())
        }
    }
}
