import type Command from '~modules/gds-terminal/src/lib/commands/Command'
import type { OutputDelta } from './../OutputDelta'
import { reactive } from 'vue'
import useControllablePromise, { type ControllablePromise } from '~/composables/useControllablePromise'
import { type Delta, hydrateDelta } from '~modules/gds-terminal/src/helpers/DeltaHelper'
import { createUniqueIdentifier } from '~modules/gds-terminal/src/helpers/IdentificationHelper'
import LoaderDelta from '~modules/gds-terminal/src/lib/delta/Loader/LoaderDelta'
import type { Terminal } from '~modules/gds-terminal/src/lib/terminal/Terminal'
import TextDelta from '~modules/gds-terminal/src/lib/delta/Text/TextDelta'
import EventBus from '~/lib/EventBus'
import ErrorDelta from '~modules/gds-terminal/src/lib/delta/Error/ErrorDelta'
import DefaultProcessRenderer from '~modules/gds-terminal/src/lib/processRenderer/DefaultProcessRenderer.vue'
import WPNCAllDelta from '~modules/gds-terminal/src/lib/delta/WPNCAllDelta/WPNCAllDelta'
import WPNCAllProcessRenderer from '~modules/gds-terminal/src/lib/processRenderer/WPNCAllProcessRenderer.vue'
import { z } from 'zod'
import WPNIAllDelta from '~modules/gds-terminal/src/lib/delta/WPNIAllDelta/WPNIAllRowDelta'
import WPNIAllProcessRenderer from '~modules/gds-terminal/src/lib/processRenderer/WPNIAllProcessRenderer.vue'
import PromptDelta from '~modules/gds-terminal/src/lib/delta/Prompt/PromptDelta'
import WPNIAllStateDelta from '~modules/gds-terminal/src/lib/delta/WPNIAllDelta/WPNIAllStateDelta'
import AvailabilityDelta from '~modules/gds-terminal/src/lib/delta/AvailabilityDelta/AvailabilityDelta'
import AvailabilityProcessRenderer from '~modules/gds-terminal/src/lib/processRenderer/AvailabilityProcessRenderer.vue'
import FlightHackStateDelta from '~modules/gds-terminal/src/lib/delta/FlightHacksDelta/FlightHackStateDelta'
import FlightHackRowDelta from '~modules/gds-terminal/src/lib/delta/FlightHacksDelta/FlightHackRowDelta'
import FlightHackProcessRenderer from '~modules/gds-terminal/src/lib/processRenderer/FlightHackProcessRenderer.vue'
import WPNCProcessRenderer from '~modules/gds-terminal/src/lib/delta/WPNC/WPNCProcessRenderer.vue'

export enum TerminalProcessStatus {
    Pending = 'pending',
    InProgress = 'in-progress',
    Finished = 'finished',
    Error = 'error',
    Cancelled = 'cancelled',
}

const processUnsubscribeTimeout = Timespan.minutes(5).inMilliseconds
const firstProcessTimeoutMs = Timespan.seconds(30).inMilliseconds
const nextProcessTimeoutMs = Timespan.minutes(2).inMilliseconds

export class TerminalProcess {
    public eventBus = new EventBus<{
        update(process: TerminalProcess): void,
    }>()

    declare private output: OutputDelta[]

    public constructor(
        public command: Command,
        output: OutputDelta[] = [],
    ) {
        this.output = reactive(output) as OutputDelta[]

        if (this.output.length) {
            this.setRenderer(this.resolveRenderer())
        }
    }

    public renderer = shallowRef<Component>(markRaw(DefaultProcessRenderer))

    public setRenderer(value: Component) {
        this.renderer.value = value
    }

    public getOutput() {
        return this.output
    }

    public emitUpdateOutput() {
        this.eventBus.emit('update', this)
    }

    public id = createUniqueIdentifier()

    public state = reactive({
        status: TerminalProcessStatus.Pending,
        subscribed: false,
    })

    protected controllablePromise: ControllablePromise<Promise<void>> | undefined

    public async cancel(terminal: Terminal): Promise<void> {
        if (this.state.status !== TerminalProcessStatus.InProgress) {
            return
        }

        this.state.status = TerminalProcessStatus.Cancelled

        terminal.processor.eventBus.emit('error', {
            type: 'error',
            processId: this.id,
            deltas: [
                {
                    type: 'text',
                    raw: 'Cancelled\n',
                    payload: { color: 'gray' },
                },
            ],
        })
    }

    public finish(terminal: Terminal) {
        this.state.status = TerminalProcessStatus.Finished

        terminal.processor.eventBus.emit('output', {
            type: 'output',
            processId: this.id,
            final: true,
            unsubscribe: true,
            deltas: [],
        })
    }

    public unsubscribe() {
        this.state.subscribed = false
        this.disposables.forEach((dispose) => dispose())
    }

    public isSubscribed() {
        return this.state.subscribed
    }

    public async execute(terminal: Terminal): Promise<void> {
        if (this.state.status !== TerminalProcessStatus.Pending) {
            throw new Error('Cannot execute process that is not pending')
        }

        this.controllablePromise = useControllablePromise()

        this.registerWsListeners(terminal)

        this.state.status = TerminalProcessStatus.InProgress

        try {
            await terminal.processor.execute(this)
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error'

            useLogger('gds-terminal').error(error)

            this.output.push(ErrorDelta.make(errorMessage))
            this.emitUpdateOutput()

            this.state.status = TerminalProcessStatus.Error

            return
        }

        return this.controllablePromise.promise
    }

    protected addLoader() {
        return this.output.push(LoaderDelta.make(true))
    }

    protected removeLoader() {
        const index = this.output.findLastIndex((delta) => delta instanceof LoaderDelta)

        if (index === -1) {
            return
        }

        this.output.splice(index, 1)
    }

    protected disposables: (() => void)[] = []

    protected registerWsListeners(terminal: Terminal) {
        let timeout: NodeJS.Timeout | undefined

        this.disposables.push(
            watch(() => this.state.status, (status) => {
                if (status === TerminalProcessStatus.InProgress) {
                    this.addLoader()
                } else {
                    this.removeLoader()
                }
                this.emitUpdateOutput()
            }, { flush: 'sync' }),
        )

        this.disposables.push(
            terminal.processor.eventBus.on('output', (message) => {
                if (message.processId !== this.id) {
                    return
                }

                this.applyDeltas(this.hydrateDeltas(message.deltas))

                if (message.final) {
                    this.state.status = TerminalProcessStatus.Finished

                    this.controllablePromise?.resolve()

                    if (timeout) {
                        clearTimeout(timeout)
                    }
                } else {
                    extendTimeout(nextProcessTimeoutMs)
                }

                if (message.unsubscribe) {
                    // noinspection JSIgnoredPromiseFromCall
                    nextTick(() => {
                        this.unsubscribe()
                    })
                }
            }),
        )

        const extendTimeout = (extendTimeMs: number) => {
            if (timeout) {
                clearTimeout(timeout)
            }

            timeout = setTimeout(() => {
                this.state.status = TerminalProcessStatus.Error

                this.output.push(TextDelta.make('Timeout'))
                this.emitUpdateOutput()

                this.controllablePromise?.reject('Timeout')
            }, extendTimeMs)
        }

        let unsubscribeTimeout: NodeJS.Timeout | undefined

        this.disposables.push(() => {
            if (timeout) {
                clearTimeout(timeout)
            }

            if (unsubscribeTimeout) {
                clearTimeout(unsubscribeTimeout)
            }
        })

        const createUnsubscribeTimeout = () => {
            if (unsubscribeTimeout) {
                clearTimeout(unsubscribeTimeout)
            }

            unsubscribeTimeout = setTimeout(() => {
                this.unsubscribe()
            }, processUnsubscribeTimeout)
        }

        createUnsubscribeTimeout()
        extendTimeout(firstProcessTimeoutMs)

        this.disposables.push(
            terminal.processor.eventBus.on('error', (error) => {
                if (error.processId !== this.id) {
                    return
                }

                this.applyDeltas(this.hydrateDeltas(error.deltas))

                this.state.status = TerminalProcessStatus.Error

                this.controllablePromise?.reject('Error received')

                this.unsubscribe()
            }),
        )

        this.state.subscribed = true
    }

    protected hydrateDeltas(rawDeltas: Delta[]) {
        return rawDeltas.map((rawDelta) => {
            try {
                return hydrateDelta(rawDelta)
            } catch (error: unknown) {
                useLogger('gds-terminal').error('Received invalid delta', rawDelta)
                useLogger('gds-terminal').error(error)
            }
        }).filter(Boolean)
    }

    public applyDeltas(deltas: OutputDelta[]) {
        for (const delta of deltas) {
            if (delta.id) {
                const index = this.output.findIndex((d) => d.id === delta.id)

                if (index === -1) {
                    this.output.push(delta)
                } else {
                    this.output[index] = delta
                }
            } else {
                this.output.push(delta)
            }
        }

        if (this.renderer.value === DefaultProcessRenderer) {
            this.setRenderer(this.resolveRenderer())
        }

        this.emitUpdateOutput()
    }

    public getNumberOfLines() {
        let lines = 0

        let hasWPNCAllDelta = false
        let hasAvailabilityDelta = false

        if (this.output.length === 1 && (this.output[0] instanceof PromptDelta)) {
            return 1 // @todo Fix this. Process should have minimum 1 line
        }

        for (const delta of this.output) {
            lines += delta.getNumberOfLines()

            // @todo Rewrite using groups of something
            if (delta instanceof WPNCAllDelta) {
                hasWPNCAllDelta = true
            }

            if (delta instanceof AvailabilityDelta) {
                hasAvailabilityDelta = true
            }
        }

        if (hasWPNCAllDelta) {
            lines++ // 1 additional line for header
        }

        if (hasAvailabilityDelta) {
            lines++ // 1 additional line for table scroll padding
        }

        return lines
    }

    public hasCorrectOutput() {
        // If it is a process with no output, it is correct
        if (this.output.length === 1 && (this.output[0] instanceof PromptDelta)) {
            return true
        }

        for (let i = this.output.length - 1; i >= 0; i--) {
            const delta = this.output[i]

            const raw = delta.getRaw()

            if (!raw.length) {
                continue
            }

            return raw.at(-1) === '\n'
        }

        return false
    }

    protected resolveRenderer() {
        if (this.output.some((delta) => delta instanceof WPNCAllDelta)) {
            return markRaw(WPNCAllProcessRenderer)
        } else if (this.output.some((delta) => delta instanceof WPNIAllStateDelta || delta instanceof WPNIAllDelta)) {
            return markRaw(WPNIAllProcessRenderer)
        } else if (this.output.some((delta) => delta instanceof AvailabilityDelta)) {
            return markRaw(AvailabilityProcessRenderer)
        } else if (this.output.some((delta) => ['WPNC', 'WPNCH\n'].includes(delta.getRaw()))) {
            return markRaw(WPNCProcessRenderer)
        } else if (this.output.some((delta) => delta instanceof FlightHackStateDelta || delta instanceof FlightHackRowDelta)) {
            return markRaw(FlightHackProcessRenderer)
        }

        return markRaw(DefaultProcessRenderer)
    }

    public static hydrate(rawProcess: unknown, terminal: Terminal): TerminalProcess | undefined {
        const parsed = TerminalProcessSchema.parse(rawProcess)

        const command = terminal.detectCommandFromInput(parsed.command.raw)
        const output: OutputDelta[] = parsed.output.map(hydrateDelta)

        const process = new TerminalProcess(command, output)

        process.setRenderer(process.resolveRenderer())

        process.state.status = parsed.status
        process.id = parsed.id

        return process
    }

    public toJSON() {
        return {
            id: this.id,
            status: this.state.status,
            output: this.output.map((delta) => delta.toJSON()),
            command: this.command.toJSON(),
        } satisfies z.infer<typeof TerminalProcessSchema>
    }
}

const TerminalProcessSchema = z.object({
    id: z.string(),
    status: z.nativeEnum(TerminalProcessStatus),
    command: z.object({
        raw: z.string(),
    }),
    output: z.array(z.any()),
})
