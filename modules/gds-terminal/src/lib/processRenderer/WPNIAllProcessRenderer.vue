<template>
    <template v-for="(outputDelta, $i) of promptOutputs" :key="$i">
        <Component :is="outputDelta.component" v-bind.prop="{ delta: outputDelta }" />
    </template>
    <template v-for="(record, index) in state" :key="index">
        <span class="font-bold cursor-pointer terminal-delta__text--yellow uppercase hover:underline" @click="openWpniAllModal(record.data.source)">{{ record.data.source }}</span><span>{{ ''.padEnd(14 - record.data.source.length) }}</span><span class="font-bold uppercase">Options found: </span><span v-if="record.data.loading && isSubscribed"><Loader class="!inline-block" />{{ ''.padEnd(7) }}</span><span v-else class="terminal-delta__text--green">{{
            `${groupedDataBySource[record.data.source]?.length ?? 0}`.padEnd(9)
        }}</span><span class="font-bold uppercase">Best Price: </span><span v-if="record.data.loading && isSubscribed"><Loader class="!inline-block" />{{ ''.padEnd(10) }}</span><span v-else :class="groupedDataBySource[record.data.source] ? 'terminal-delta__text--green' : 'terminal-delta__text--gray'">{{
            `${groupedDataBySource[record.data.source] ? formatMoney({ amount: findBestPrice(groupedDataBySource[record.data.source]), currency: 'USD' }) : '-'}`.padEnd(12)
        }}</span><span
            v-if="index === 0"
            class="font-bold cursor-pointer terminal-delta__text--yellow uppercase hover:underline"
            @click="openWpniAllModal()"
        >View options</span><br>
    </template>
    <FlightHackProcessRenderer :terminal-process="terminalProcess" />
</template>

<script setup lang="ts">
import type { TerminalProcess } from '~modules/gds-terminal/src/lib/terminal/TerminalProcess'
import type { OutputDelta } from '~modules/gds-terminal/src/lib/OutputDelta'
import WPNIAllDelta from '~modules/gds-terminal/src/lib/delta/WPNIAllDelta/WPNIAllRowDelta'
import WpniAllModal from '~modules/gds-terminal/src/modals/WpniAllModal.vue'
import { groupBy } from '~/lib/Helper/ArrayHelper'
import WPNIAllStateDelta from '~modules/gds-terminal/src/lib/delta/WPNIAllDelta/WPNIAllStateDelta'
import type TerminalTool from '~modules/gds-terminal/src/lib/TerminalTool'
import { useTerminalContext } from '~modules/gds-terminal/src/composables/terminal/useTerminalContext'
import FlightHackProcessRenderer from '~modules/gds-terminal/src/lib/processRenderer/FlightHackProcessRenderer.vue'
import FlightHackRowDelta from '~modules/gds-terminal/src/lib/delta/FlightHacksDelta/FlightHackRowDelta'

defineOptions({
    name: 'WPNIAllProcessRenderer',
})

//

const props = defineProps<{
    terminalProcess: TerminalProcess
}>()

const wpniAllState = computed(() => props.terminalProcess.getOutput().filter((output) => output instanceof WPNIAllStateDelta) as WPNIAllStateDelta[])

const state = computed(() => wpniAllState.value)

const toolStore = inject<TerminalTool>('terminalTool')!
const terminalContext = useTerminalContext()

const wpniAllModal = useModal(WpniAllModal)

const promptOutputs = computed(() => {
    const deltas: OutputDelta[] = []

    for (const outputDelta of props.terminalProcess.getOutput()) {
        if (outputDelta instanceof WPNIAllDelta || outputDelta instanceof WPNIAllStateDelta) {
            break
        }

        deltas.push(outputDelta)
    }

    return deltas
})

const wpniAllData = computed(() => {
    return (props.terminalProcess.getOutput().filter((outputDelta) => outputDelta instanceof WPNIAllDelta) as WPNIAllDelta[]).map((wpniAllDelta) => wpniAllDelta.data)
})

const flightHacksData = computed(() => {
    return (props.terminalProcess.getOutput().filter((outputDelta) => outputDelta instanceof FlightHackRowDelta) as FlightHackRowDelta[]).map((flightHack) => flightHack.data)
})

const groupedDataBySource = computed(() => {
    return groupBy(wpniAllData.value, 'source')
})

const findBestPrice = (records) => {
    return records.length ? records.reduce((min, option) => (option.price?.amount < min ? option.price?.amount : min), Infinity) : 0
}

const openWpniAllModal = (source?: string) => {
    wpniAllModal.open({
        command: 'WPNI ALL',
        records: [...wpniAllData.value, ...flightHacksData.value],
        pcc: source,
        terminalTool: markRaw(toolStore),
        leadPk: terminalContext.getLeadContext()?.lead_pk,
    })
}

const formatter = useService('formatter')

function formatMoney(money?: { amount: number, currency: string }) {
    if (!money) {
        return ''
    }

    return formatter.money(money.amount, { withCurrency: money.currency })
}

const isSubscribed = computed(() => props.terminalProcess.isSubscribed())
</script>
