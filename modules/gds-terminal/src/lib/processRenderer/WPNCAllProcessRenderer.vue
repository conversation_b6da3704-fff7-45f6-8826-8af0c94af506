<template>
    <template v-for="(outputDelta, $i) of promptOutputs" :key="$i">
        <Component :is="outputDelta.component" v-bind.prop="{ delta: outputDelta }" />
    </template>
    <span class="font-bold">{{ 'GDS'.padEnd(9) }}{{ 'PCC'.padEnd(9) }}{{ 'PTC'.padEnd(9) }}{{ 'FT'.padEnd(9) }}{{
        'BG'.padEnd(9) }}<span class="cursor-pointer" @click="toggleOrder('priceAdt')">{{ adultPriceHeader.padEnd(12) }}</span>{{ 'CHD'.padEnd(12) }}{{ 'INF'.padEnd(12) }}</span>
    <Component
        :is="outputDelta.component"
        v-for="(outputDelta) of wpncOutputs"
        :key="outputDelta.id"
        v-bind.prop="{ delta: outputDelta, canAct }"
    />
    <FlightHackProcessRenderer :terminal-process="terminalProcess" />
</template>

<script setup lang="ts">
import type { TerminalProcess } from '~modules/gds-terminal/src/lib/terminal/TerminalProcess'
import WPNCAllDelta from '~modules/gds-terminal/src/lib/delta/WPNCAllDelta/WPNCAllDelta'
import type { OutputDelta } from '~modules/gds-terminal/src/lib/OutputDelta'
import EmptyCommand from '~modules/gds-terminal/src/lib/commands/EmptyCommand'
import { sortByValueFn } from '~/lib/Helper/ArrayHelper'
import type { SortDirection } from '~/lib/Search/SortController'
import FlightHackProcessRenderer from '~modules/gds-terminal/src/lib/processRenderer/FlightHackProcessRenderer.vue'

defineOptions({
    name: 'WPNCAllProcessRenderer',
})

//

const props = defineProps<{
    terminalProcess: TerminalProcess
}>()

//

const promptOutputs = computed(() => {
    const deltas: OutputDelta[] = []

    for (const outputDelta of props.terminalProcess.getOutput()) {
        if (outputDelta instanceof WPNCAllDelta) {
            break
        }

        deltas.push(outputDelta)
    }

    return deltas
})

function isDeltaSortable(delta: WPNCAllDelta) {
    return !delta.data.error && delta.data.priceAdt?.amount
}

type SortableFields = 'priceAdt'

const sortBy = ref<{
    field: SortableFields
    direction: SortDirection
}>({
    field: 'priceAdt',
    direction: 'asc',
})

const wpncOutputs = computed(() => {
    const deltas = props.terminalProcess.getOutput().filter((outputDelta) => outputDelta instanceof WPNCAllDelta)

    const sortable = deltas.filter((delta) => isDeltaSortable(delta as WPNCAllDelta))
    const notSortable = deltas.filter((delta) => !isDeltaSortable(delta as WPNCAllDelta))

    return [
        ...sortable.sort(sortByValueFn<WPNCAllDelta>((delta) => delta.data[sortBy.value.field]?.amount, sortBy.value.direction)),
        ...notSortable,
    ]
})

const canAct = computed(() => {
    return !(props.terminalProcess.command instanceof EmptyCommand)
})

//

const adultPriceHeader = computed(() => {
    let header = 'ADT'

    if (sortBy.value.field === 'priceAdt') {
        header += sortBy.value.direction === 'desc' ? '▾' : '▴'
    }

    return header.padEnd(12)
})

function toggleOrder(field: SortableFields) {
    if (sortBy.value.field === field) {
        sortBy.value.direction = sortBy.value.direction === 'asc' ? 'desc' : 'asc'
    } else {
        sortBy.value.field = field
        sortBy.value.direction = 'asc'
    }
}
</script>
