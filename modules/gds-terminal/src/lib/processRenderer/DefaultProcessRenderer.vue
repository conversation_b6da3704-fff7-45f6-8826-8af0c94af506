<template>
    <template v-for="(outputDelta, $i) of terminalProcess.getOutput()" :key="$i">
        <Component :is="outputDelta.component" v-bind.prop="{ delta: outputDelta }" />
    </template><span v-if="isDebug && showDebugTrailingCharacter" class="terminal__debug-trailing-character" />
</template>

<script setup lang="ts">
import type { TerminalProcess } from '~modules/gds-terminal/src/lib/terminal/TerminalProcess'

defineOptions({
    name: 'DefaultProcessRenderer',
})

//

const props = defineProps<{
    terminalProcess: TerminalProcess
}>()

const isDebug = config.debug
const showDebugTrailingCharacter = computed(() => !props.terminalProcess.hasCorrectOutput())
</script>
