<template>
    <template v-if="flightHacksState.length">
        <span class="underline font-bold terminal-delta__text--red">Flight Hacks<br></span>
        <template
            v-for="(record, index) in flightHacksState"
            :key="index"
        >
            <span
                class="font-bold cursor-pointer terminal-delta__text--yellow uppercase hover:underline"
                @click="openFlightHacksModal(record.data.source)"
            >
                {{ record.data?.source }}
            </span>
            <span>{{ ''.padEnd(14 - record.data?.source.length) }}</span>
            <span class="font-bold uppercase">Options found: </span>
            <span v-if="record.data.loading && isSubscribed">
                <Loader class="!inline-block" />{{ ''.padEnd(7) }}
            </span>
            <span v-else class="terminal-delta__text--green">
                {{ `${groupedDataBySourceFlightHacksData[record.data.source]?.length ?? 0}`.padEnd(9) }}
            </span>
            <span class="font-bold uppercase">Best Price: </span>
            <span v-if="record.data.loading && isSubscribed">
                <Loader class="!inline-block" />{{ ''.padEnd(10) }}
            </span>
            <span v-else :class="groupedDataBySourceFlightHacksData[record.data.source] ? 'terminal-delta__text--green' : 'terminal-delta__text--gray'">
                {{ `${groupedDataBySourceFlightHacksData[record.data.source] ? formatMoney({ amount: findBestPrice(groupedDataBySourceFlightHacksData[record.data.source]), currency: 'USD' }) : '-'}`.padEnd(12) }}
            </span>
            <span
                v-if="index === 0"
                class="font-bold cursor-pointer terminal-delta__text--yellow uppercase hover:underline"
                @click="openFlightHacksModal()"
            >View options</span>
            <br>
        </template>
    </template>
</template>

<script setup lang="ts">
import { groupBy } from '~/lib/Helper/ArrayHelper'
import WpniAllModal from '~modules/gds-terminal/src/modals/WpniAllModal.vue'
import type TerminalTool from '~modules/gds-terminal/src/lib/TerminalTool'
import { useTerminalContext } from '~modules/gds-terminal/src/composables/terminal/useTerminalContext'
import FlightHackRowDelta from '~modules/gds-terminal/src/lib/delta/FlightHacksDelta/FlightHackRowDelta'
import FlightHackStateDelta from '~modules/gds-terminal/src/lib/delta/FlightHacksDelta/FlightHackStateDelta'
import type { TerminalProcess } from '~modules/gds-terminal/src/lib/terminal/TerminalProcess'
import WPNIAllDelta from '~modules/gds-terminal/src/lib/delta/WPNIAllDelta/WPNIAllRowDelta'

const props = defineProps<{
    terminalProcess: TerminalProcess
}>()

const toolStore = inject<TerminalTool>('terminalTool')!

const terminalContext = useTerminalContext()

const formatter = useService('formatter')

const wpniAllModal = useModal(WpniAllModal)

const flightHacksState = computed(() => {
    return props.terminalProcess.getOutput().filter((outputDelta) => outputDelta instanceof FlightHackStateDelta) as FlightHackStateDelta[]
})

const flightHacksData = computed(() => {
    return (props.terminalProcess.getOutput().filter((outputDelta) => outputDelta instanceof FlightHackRowDelta) as FlightHackRowDelta[]).map((flightHack) => flightHack.data)
})

const wpniAllData = computed(() => {
    return (props.terminalProcess.getOutput().filter((outputDelta) => outputDelta instanceof WPNIAllDelta) as WPNIAllDelta[]).map((wpniAllDelta) => wpniAllDelta.data)
})

const openFlightHacksModal = (source?: string) => {
    wpniAllModal.open({
        command: props.terminalProcess.command.raw,
        records: [...wpniAllData.value, ...flightHacksData.value],
        pcc: source,
        onlyFlightHacks: true,
        terminalTool: markRaw(toolStore),
        leadPk: terminalContext.getLeadContext()?.lead_pk,
    })
}

const groupedDataBySourceFlightHacksData = computed(() => {
    return groupBy(flightHacksData.value, 'source')
})

function formatMoney(money?: { amount: number, currency: string }) {
    if (!money) {
        return ''
    }

    return formatter.money(money.amount, { withCurrency: money.currency })
}

const findBestPrice = (records) => {
    return records.length ? records.reduce((min, option) => (option.price?.amount < min ? option.price?.amount : min), Infinity) : 0
}

const isSubscribed = computed(() => props.terminalProcess.isSubscribed())
</script>
