<template>
    <span
        class="terminal-delta__text--bold terminal-delta__text--yellow uppercase hover:underline"
        :class="{
            'cursor-pointer': !isLoading,
            'cursor-not-allowed': isLoading,
        }"
        @click="getPqForSession"
    >Create PQ</span> <Loader v-if="isLoading" class="!inline-block" />
    <br>
</template>

<script setup lang="ts">
import type CreatePqButtonDelta from '~modules/gds-terminal/src/lib/delta/CreatePqButtonDelta/CreatePqButtonDelta'
import type TerminalTool from '~modules/gds-terminal/src/lib/TerminalTool'
import { useTerminalContext } from '~modules/gds-terminal/src/composables/terminal/useTerminalContext'
import { preventDuplication } from '~/lib/Helper/PromiseHelper'
import LeadSelectModal from '~modules/gds-terminal/src/modals/LeadSelectModal.vue'

const props = defineProps<{
    delta: CreatePqButtonDelta,
}>()

const toolStore = inject<TerminalTool>('terminalTool')!
const terminalContext = useTerminalContext()

const data = computed(() => props.delta.data)
const isLoading = ref(false)

const leadSelectModal = useModal(LeadSelectModal)

const getPqForSession = async () => {
    await preventDuplication(async () => {
        let lead_pk = terminalContext.getLeadContext()?.lead_pk

        if (!lead_pk) {
            lead_pk = await leadSelectModal.open()
        }

        const { segments } = await toolStore.getActiveSessionStore().terminalConnector.fetchSessionContext()

        if (!segments.format) {
            return
        }

        const modal = useModal((await import('@/components/Modals/priceQuote/PriceQuoteModal.vue')).default)

        await modal.open({
            terminalIdentifier: toolStore.getActiveSessionStore().terminalConnector.getSessionChannel(),
            lead_id: Number(lead_pk),
            prepopulateFrom: {
                option: segments.format,
                consolidator_area_name: toolStore.getActiveSessionStore().activePcc.value,
                carrier_code: data.value.validatingCarrier,
                net_price: data.value.totalPrice.amount,
                fare_amount: data.value.baseFarePrice?.amount,
                commission: data.value.commissionValue?.amount,
            },
        })
    }, isLoading)
}
</script>
