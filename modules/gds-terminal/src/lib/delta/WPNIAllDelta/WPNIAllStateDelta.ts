import type { Delta, WPNIAllState } from '~modules/gds-terminal/src/helpers/DeltaHelper'
import { DeltaSchema } from '~modules/gds-terminal/src/helpers/DeltaSchemeHelper'
import { z } from 'zod'
import { OutputDelta } from '~modules/gds-terminal/src/lib/OutputDelta'

export default class WPNIAllStateDelta extends OutputDelta<Delta<z.infer<typeof WPNIAllStateDeltaPayloadSchema>>> {
    public static hydrate(json: unknown): WPNIAllStateDelta {
        const rawDelta = DeltaSchema.parse(json)
        WPNIAllStateDeltaPayloadSchema.parse(rawDelta.payload)

        return new WPNIAllStateDelta(rawDelta)
    }

    public get data(): WPNIAllState {
        return this.rawDelta.payload!
    }

    public getNumberOfLines(): number {
        return 1
    }
}

export enum WPNIAllTableRowType {
    Gds = 'gds',
    Award = 'award',
}

export const WPNIAllStateDeltaPayloadSchema = z.object({
    source: z.string(),
    gds: z.string(),
    loading: z.boolean(),
    type: z.enum(WPNIAllTableRowType),
}).describe('WPNIAllStateDeltaPayloadSchema')
