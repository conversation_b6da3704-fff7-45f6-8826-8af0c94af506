import type { Delta, WPNIAllTableRow } from '~modules/gds-terminal/src/helpers/DeltaHelper'
import { DeltaSchema } from '~modules/gds-terminal/src/helpers/DeltaSchemeHelper'
import { z } from 'zod'
import { OutputDelta } from '~modules/gds-terminal/src/lib/OutputDelta'
import { MoneySchema } from '~modules/gds-terminal/src/helpers/DeltaSchemeHelper'
import { WPNIAllTableRowType } from '~modules/gds-terminal/src/lib/delta/WPNIAllDelta/WPNIAllStateDelta'

export default class WPNIAllRowDelta extends OutputDelta<Delta<z.infer<typeof WPNIAllRowDeltaPayloadSchema>>> {
    public static hydrate(json: unknown): WPNIAllRowDelta {
        const rawDelta = DeltaSchema.parse(json)
        WPNIAllRowDeltaPayloadSchema.parse(rawDelta.payload)

        return new WPNIAllRowDelta(rawDelta)
    }

    public get data(): WPNIAllTableRow {
        return this.rawDelta.payload!
    }

    public getNumberOfLines(): number {
        return 0
    }
}

export const WPNIAllRowDeltaPayloadSchema = z.object({
    source: z.string(),
    optionNumber: z.number(),
    flownMiles: z.number(),
    segments: z.array(z.array(z.string())),
    validatingCarrier: z.string(),
    stopsCount: z.number(),
    flightDuration: z.object({
        flightTime: z.number(),
        waitingTime: z.number(),
        totalTime: z.number(),
    }),
    priceAward: MoneySchema.optional(),
    price: MoneySchema.optional(),
    type: z.enum(WPNIAllTableRowType),
}).describe('WPNIAllRowDeltaPayload')
