import LoaderDeltaComponent from '~modules/gds-terminal/src/lib/delta/Loader/LoaderDeltaComponent.vue'
import { OutputDelta } from '~modules/gds-terminal/src/lib/OutputDelta'
import { DeltaSchema } from '~modules/gds-terminal/src/helpers/DeltaSchemeHelper'

export default class LoaderDelta extends OutputDelta {
    public component = markRaw(LoaderDeltaComponent)

    public static make(newLine = false): LoaderDelta {
        return new LoaderDelta({
            type: 'loader',
            raw: 'Loading ...' + (newLine ? '\n' : ''),
        })
    }

    public static hydrate(json: unknown): LoaderDelta {
        const rawDelta = DeltaSchema.parse(json)
        LoaderDeltaPayloadSchema.parse(rawDelta.payload)

        return new LoaderDelta(rawDelta)
    }
}

export const LoaderDeltaPayloadSchema = z.undefined().describe('LoaderDeltaPayload')
