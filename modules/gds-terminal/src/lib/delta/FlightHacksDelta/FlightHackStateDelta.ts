import type { Delta, FlightHackState } from '~modules/gds-terminal/src/helpers/DeltaHelper'
import { DeltaSchema } from '~modules/gds-terminal/src/helpers/DeltaSchemeHelper'
import { z } from 'zod'
import { OutputDelta } from '~modules/gds-terminal/src/lib/OutputDelta'

export default class FlightHackStateDelta extends OutputDelta<Delta<z.infer<typeof FlightHackStateDeltaPayloadSchema>>> {
    public static hydrate(json: unknown): FlightHackStateDelta {
        const rawDelta = DeltaSchema.parse(json)
        FlightHackStateDeltaPayloadSchema.parse(rawDelta.payload)

        return new FlightHackStateDelta(rawDelta)
    }

    public get data(): FlightHackState {
        return this.rawDelta.payload!
    }

    public getNumberOfLines(): number {
        return 1
    }
}

export const FlightHackStateDeltaPayloadSchema = z.object({
    source: z.string(),
    gds: z.string(),
    loading: z.boolean(),
}).describe('FlightHackStateDeltaPayloadSchema')
