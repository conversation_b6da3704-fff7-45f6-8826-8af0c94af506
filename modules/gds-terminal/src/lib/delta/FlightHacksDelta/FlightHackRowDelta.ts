import type { Delta, FlightHackTableRow } from '~modules/gds-terminal/src/helpers/DeltaHelper'
import { DeltaSchema } from '~modules/gds-terminal/src/helpers/DeltaSchemeHelper'
import { z } from 'zod'
import { OutputDelta } from '~modules/gds-terminal/src/lib/OutputDelta'
import { MoneySchema } from '~modules/gds-terminal/src/helpers/DeltaSchemeHelper'

export default class FlightHackRowDelta extends OutputDelta<Delta<z.infer<typeof FlightHackPayloadSchema>>> {
    public static hydrate(json: unknown): FlightHackRowDelta {
        const rawDelta = DeltaSchema.parse(json)
        FlightHackPayloadSchema.parse(rawDelta.payload)

        return new FlightHackRowDelta(rawDelta)
    }

    public get data(): FlightHackTableRow {
        return this.rawDelta.payload!
    }

    public getNumberOfLines(): number {
        return 0
    }
}

export enum FlightHackType {
    ELR = 'ELR',
    FRT = 'FRT',
    ORIGIN_OPEN_JAW = 'ORIGIN_OPEN_JAW'
}

export const FlightHackPayloadSchema = z.object({
    source: z.string(),
    optionNumber: z.number(),
    flownMiles: z.number(),
    segments: z.array(z.array(z.string())),
    validatingCarrier: z.string(),
    stopsCount: z.number(),
    flightDuration: z.object({
        flightTime: z.number(),
        waitingTime: z.number(),
        totalTime: z.number(),
    }),
    priceAward: MoneySchema.optional(),
    price: MoneySchema.optional(),
    flightHackType: z.enum(FlightHackType),
}).describe('FlightHackRowDeltaPayload')
