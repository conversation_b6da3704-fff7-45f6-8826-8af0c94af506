<template>
    <template v-for="(outputDelta, $i) of promptOutputs" :key="$i">
        <Component :is="outputDelta.component" v-bind.prop="{ delta: outputDelta }" />
    </template>

    <FlightHackProcessRenderer :terminal-process="terminalProcess" />
</template>

<script setup lang="ts">
import type { TerminalProcess } from '~modules/gds-terminal/src/lib/terminal/TerminalProcess'
import FlightHackProcessRenderer from '~modules/gds-terminal/src/lib/processRenderer/FlightHackProcessRenderer.vue'

defineOptions({
    name: 'WPNCProcessRenderer',
})

//

const props = defineProps<{
    terminalProcess: TerminalProcess
}>()

const promptOutputs = computed(() => {
    return props.terminalProcess.getOutput()
})
</script>
