import { ofetch } from 'ofetch'
import { useAuth } from '~/stores/useAuth'

export function useTerminalHttpClient() {
    const http = ofetch.create({
        baseURL: config.gds.httpBase,
        onRequest(context) {
            const authStore = useAuth()
            const token = authStore.token

            const headers = new Headers(context.options.headers)
            headers.set('X-Auth-Token', token)

            context.options.headers = headers
        },
    })

    return {
        http,
    }
}
