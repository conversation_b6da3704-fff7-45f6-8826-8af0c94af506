import { useResizeObserver } from '@vueuse/core'
import type { Terminal } from '~modules/gds-terminal/src/lib/terminal/Terminal'

export function useTerminalViewport(terminal: Terminal, viewportRef: Ref<HTMLElement | undefined>, scrollableRef: Ref<HTMLElement | undefined>) {
    const charTestRef = ref<HTMLElement>()

    //

    const charHeight = ref(0)
    const charWidth = ref(0)

    function recalculateCharSize() {
        const charTest = charTestRef.value

        if (!charTest) {
            return
        }

        const rect = charTest.getBoundingClientRect()

        charWidth.value = rect.width
        charHeight.value = rect.height
    }

    //

    /**
     * @todo Make lines depend on line height instead of char height
     */
    function getViewportSize() {
        const viewport = viewportRef.value

        if (!viewport) {
            return {
                lines: 0,
                cols: 0,
                height: 0,
                width: 0,
            }
        }

        const rect = viewport.getBoundingClientRect()

        const height = rect.height
        const width = rect.width
        const lines = Math.floor(height / charHeight.value)
        const cols = Math.floor(width / charWidth.value)

        return {
            lines,
            cols,
            height,
            width,
        }
    }

    //

    const viewportSpace = ref(0) // If viewport size is not divisible by char height, this will keep the remaining space

    function recalculateViewportSpace() {
        const viewport = viewportRef.value

        if (!viewport) {
            return
        }

        const { lines, height } = getViewportSize()

        viewportSpace.value = height % charHeight.value + (lines - 1) * charHeight.value
    }

    useResizeObserver(viewportRef, recalculateViewportSpace)

    //

    function scrollTo(line: number) {
        const viewport = scrollableRef.value

        if (!viewport) {
            return
        }

        viewport.scrollTop = (line - 1) * charHeight.value
        console.log('Scrolling to line', line, 'with char height', charHeight.value, 'and scroll top', viewport.scrollTop)
    }

    //

    const disposables: any[] = []

    onMounted(() => {
        disposables.push(
            terminal.internalEvents.on('scrollTo', (line) => {
                // noinspection JSIgnoredPromiseFromCall
                nextTick(() => {
                    // noinspection JSIgnoredPromiseFromCall
                    nextTick(() => {
                        scrollTo(line)
                    })
                })
            }),
        )

        // Disable editing in scrollable area
        if (viewportRef.value) {
            viewportRef.value.addEventListener('beforeinput', e => e.preventDefault())
            viewportRef.value.addEventListener('paste', e => e.preventDefault())
            viewportRef.value.addEventListener('drop', e => e.preventDefault())
        }
    })

    onUnmounted(() => {
        for (const disposable of disposables) {
            disposable()
        }
    })

    //

    onMounted(() => {
        recalculateCharSize()
        recalculateViewportSpace()
    })

    return {
        charTestRef,
        viewportRef,
        charHeight,
        charWidth,
        viewportSpace,
        getViewportSize,
        recalculateViewportSpace,
        recalculateCharSize,
        scrollTo,
    }
}
