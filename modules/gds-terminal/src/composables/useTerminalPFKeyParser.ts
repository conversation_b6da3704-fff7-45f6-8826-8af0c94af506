import { downloadBlob } from '@/lib/core/helper/File/FileHelpers'

export type RawPfKeyEntry = {
    index: number
    label: string
    desc: string
    command: string
    global: boolean
}

type TerminalPfKeyEntry = {
    keys: string[]
    title: string
    description: string
    command: string
    isGlobal: boolean
}

type PfKeyField = keyof Pick<RawPfKeyEntry, 'label' | 'desc' | 'command'>

export const sabreDecodeMap: Record<string, string> = {
    '\u0081': '¥',
    '\u0083': '^',
    '\u0099': '‡',
    '\u00A4': '¤',
    '\u00A7': '§',
    '\u00B6': '¶',
}

export const sabreEncodeMap: Record<string, string> = {
    '¥': '\\u0081',
    '^': '\\u0083',
    '‡': '\\u0099',
    '¤': '\\u00A4',
    '§': '\\u00A7',
    '¶': '\\u00B6',
}

export function useTerminalPFKeysParser() {
    const parsePfFile = (content: string): TerminalPfKeyEntry[] => {
        const lines = content.split(/\r?\n/)
        const map = new Map<string, Partial<RawPfKeyEntry>>()

        let currentKey: { indexStr: string; field: string } | null = null

        for (const line of lines) {
            if (!line || line.startsWith('#')) continue

            const match = line.match(/^pfkey\.(\d+)\.(label|desc|command|global)=(.*)$/)

            if (match) {
                const [, indexStr, field, valueRaw] = match
                const value = decodeSabreUnicode(valueRaw)
                const index = parseInt(indexStr, 10)

                const entry = map.get(indexStr) ?? { index }
                entry[field as PfKeyField] = value
                map.set(indexStr, entry)

                currentKey = { indexStr, field: field as PfKeyField }
            } else if (currentKey) {
                const entry = map.get(currentKey.indexStr)

                if (!entry) break

                const field = currentKey.field as PfKeyField
                const previousValue = entry[field] ?? ''
                entry[field] = previousValue + decodeSabreUnicode(line)
            }
        }

        const clearedData = Array.from(map.values()).filter(entry => !!entry.command)

        return clearedData.map(entry => ({
            keys: [`F${entry.index! + 1}`],
            title: entry.label || '',
            description: entry.desc || '',
            command: entry.command!,
            isGlobal: !!entry.global,
        }))
    }

    const generatePfFileContent = (pfKeys: RawPfKeyEntry[]): string => {
        const now = new Date()

        const header = `#PF Key File\n#${now.toDateString()} ${now.toTimeString().split(' ')[0]} ${Intl.DateTimeFormat().resolvedOptions().timeZone}\n`

        const lines = pfKeys.filter(pfkey => !!pfkey.command).flatMap((entry => [
                `pfkey.${entry.index}.label=${entry.label}`,
                `pfkey.${entry.index}.desc=${entry.desc || ''}`,
                `pfkey.${entry.index}.command=${encodeToSabreUnicode(entry.command)}`,
                entry.global ?  `pfkey.${entry.index}.global=1\n` : '\n',
            ]))

        return [header, ...lines].join('\n')
    }

    const createPFKeysFile = (content: string) => {
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })

        const now = new Date().getParts()
        const fileName = `exported-${now.yyyy}${now.MM}${now.d}${now.HH}${now.mm}.pfkey`

        downloadBlob(blob, fileName)
    }

    function decodeSabreUnicode(input: string): string {
        const decoded = input.replace(/\\u([0-9a-fA-F]{4})/g, (_, code) =>
            String.fromCharCode(parseInt(code, 16)),
        )

        const regex = new RegExp(`[${Object.values(sabreEncodeMap).join('')}]`, 'g')

        return decoded.replace(regex, (char: string) => {
            return sabreDecodeMap[char] || char
        })
    }

    function encodeToSabreUnicode(input: string): string {
        const regex = new RegExp(`[${Object.keys(sabreEncodeMap).join('')}]`, 'g')

        return input.replace(regex, (char) => {
            return sabreEncodeMap[char] || char
        })
    }

    return {
        parsePfFile,
        generatePfFileContent,
        createPFKeysFile,
        encodeToSabreUnicode,
        decodeSabreUnicode,
    }
}

