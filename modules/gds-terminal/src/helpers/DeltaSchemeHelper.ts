import { z } from 'zod'

export const DeltaSchema = z.object({
    id: z.string().optional(),
    raw: z.string(),
    type: z.enum(['text', 'error', 'loader', 'prompt', 'wpncAllTableRow', 'wpniAllTableRow', 'wpniAllState', 'flightHackState', 'flightHackTableRow', 'availabilityTableRow', 'createPqButton']),
    payload: z.any(),
}).describe('Delta')

export const MoneySchema = z.object({
    amount: z.number(),
    currency: z.string(),
})
