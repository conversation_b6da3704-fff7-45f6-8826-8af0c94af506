import { useLogger } from '~/composables/useLogger'
import LoaderDelta from '~modules/gds-terminal/src/lib/delta/Loader/LoaderDelta'
import type { TextDeltaColor } from '~modules/gds-terminal/src/lib/delta/Text/TextDelta'
import TextDelta from '~modules/gds-terminal/src/lib/delta/Text/TextDelta'
import type { OutputDelta } from '~modules/gds-terminal/src/lib/OutputDelta'
import ErrorDelta from '~modules/gds-terminal/src/lib/delta/Error/ErrorDelta'
import WPNCAllDelta from '~modules/gds-terminal/src/lib/delta/WPNCAllDelta/WPNCAllDelta'
import PromptDelta from '~modules/gds-terminal/src/lib/delta/Prompt/PromptDelta'
import type { FlightHackType } from '~modules/gds-terminal/src/lib/delta/FlightHacksDelta/FlightHackRowDelta'
import FlightHackRowDelta from '~modules/gds-terminal/src/lib/delta/FlightHacksDelta/FlightHackRowDelta'
import WPNIAllRowDelta from '~modules/gds-terminal/src/lib/delta/WPNIAllDelta/WPNIAllRowDelta'
import type { WPNIAllTableRowType } from '~modules/gds-terminal/src/lib/delta/WPNIAllDelta/WPNIAllStateDelta'
import WPNIAllStateDelta from '~modules/gds-terminal/src/lib/delta/WPNIAllDelta/WPNIAllStateDelta'
import AvailabilityDelta from '~modules/gds-terminal/src/lib/delta/AvailabilityDelta/AvailabilityDelta'
import CreatePqButtonDelta from '~modules/gds-terminal/src/lib/delta/CreatePqButtonDelta/CreatePqButtonDelta'
import FlightHackStateDelta from '~modules/gds-terminal/src/lib/delta/FlightHacksDelta/FlightHackStateDelta'

export interface DeltaMap {
    text: {
        payload: {
            color?: TextDeltaColor
        }
    }
    error: {
        payload: never
    },
    loader: {
        payload: never
    },
    prompt: {
        payload: never
    },
    wpncAllTableRow: {
        payload: WPNCAllTableRow,
    },
    wpniAllTableRow: {
        payload: WPNIAllTableRow,
    }
    wpniAllState: {
        payload: WPNIAllState
    }
    flightHackTableRow: {
        payload: FlightHackTableRow
    },
    flightHackState: {
        payload: FlightHackState
    },
    availabilityTableRow: {
        payload: AvailabilityTableRow
    },
    createPqButton: {
        payload: CreatePqButton
    },
}

export type CreatePqButton = {
    totalPrice: {
        amount: number,
        currency: string,
    },
    commissionValue?: {
        amount: number,
        currency: string,
    },
    baseFarePrice?: {
        amount: number,
        currency: string,
    },
    validatingCarrier: string | null,
}

export type WPNCAllTableRow = {
    gds: string,
    pcc: string,
    ptc?: string,
    fareType?: string,
    baggage?: string,
    priceAdt?: {
        amount: number,
        currency: string,
    },
    priceChd?: {
        amount: number,
        currency: string,
    },
    priceInf?: {
        amount: number,
        currency: string,
    },
    error?: string,
}

export type WPNIAllTableRow = {
    source: string,
    segments: string[][],
    optionNumber: number,
    flownMiles: number,
    validatingCarrier: string,
    stopsCount: number,
    flightDuration: {
        flightTime: number,
        waitingTime: number,
        totalTime: number,
    },
    price?: {
        amount: number,
        currency: string,
    },
    priceAward?: {
        amount: number,
        currency: string,
    },
    type: WPNIAllTableRowType
    flightHackType?: FlightHackType
}

export type WPNIAllState = {
    source: string,
    gds: string,
    loading: boolean,
    type: WPNIAllTableRowType,
}

export type FlightHackTableRow = {
    source: string,
    segments: string[][],
    optionNumber: number,
    flownMiles: number,
    validatingCarrier: string,
    stopsCount: number,
    flightDuration: {
        flightTime: number,
        waitingTime: number,
        totalTime: number,
    },
    price?: {
        amount: number,
        currency: string,
    },
    priceAward?: {
        amount: number,
        currency: string,
    },
    flightHackType: FlightHackType
}

export type FlightHackState = {
    source: string,
    gds: string,
    loading: boolean,
}

export type AvailabilityTableRow = {
    dl: number;
    flightNumber: number;
    bookingCodes: string[];
    departureAirport: string;
    arrivalAirport: string;
    departureTime: string;
    arrivalTime: string;
    aircraftType: string;
    flightDuration: string;
    carrier: string,
    departureDateAdjustment: string,
    arrivalDateAdjustment: string,
    isConnection: boolean,
    hideDepartureAirport: boolean,
}

//

export type DeltaType = keyof DeltaMap

export type Delta<TPayload = unknown> = {
    type: DeltaType,
    raw: string,
    payload?: TPayload,
    id?: string,
}

export type TypedDeltaPayload<T extends DeltaType> = Required<Delta<DeltaMap[T]['payload']>>

//

export function hydrateDelta(rawDelta: Delta): OutputDelta {
    let delta: OutputDelta

    if (rawDelta.type === 'text') {
        delta = TextDelta.hydrate(rawDelta)
    } else if (rawDelta.type === 'error') {
        delta = ErrorDelta.hydrate(rawDelta)
    } else if (rawDelta.type === 'loader') {
        delta = LoaderDelta.hydrate(rawDelta)
    } else if (rawDelta.type === 'prompt') {
        delta = PromptDelta.hydrate(rawDelta)
    } else if (rawDelta.type === 'wpncAllTableRow') {
        delta = WPNCAllDelta.hydrate(rawDelta)
    } else if (rawDelta.type === 'wpniAllTableRow') {
        delta = WPNIAllRowDelta.hydrate(rawDelta)
    } else if (rawDelta.type === 'wpniAllState') {
        delta = WPNIAllStateDelta.hydrate(rawDelta)
    } else if (rawDelta.type === 'flightHackTableRow') {
        delta = FlightHackRowDelta.hydrate(rawDelta)
    } else if (rawDelta.type === 'flightHackState') {
        delta = FlightHackStateDelta.hydrate(rawDelta)
    } else if (rawDelta.type === 'availabilityTableRow') {
        delta = AvailabilityDelta.hydrate(rawDelta)
    } else if (rawDelta.type === 'createPqButton') {
        delta = CreatePqButtonDelta.hydrate(rawDelta)
    } else {
        useLogger('gds-terminal').error(`Unknown delta type: ${rawDelta.type}`, rawDelta)

        delta = TextDelta.make(rawDelta.raw)

        if (rawDelta.id) {
            delta.setId(rawDelta.id)
        }
    }

    return delta
}

export function formatSabreSegment(_segments: [
    version: string,
    originLocationCode: string,
    departureDateTime: string,
    destinationLocationCode: string,
    arrivalDateTime: string,
    flightNumber: string,
    numberInParty: string,
    resBookDesigCode: string,
    equipment: string,
    marketingAirlineCode: string,
    marketingAirlineFlightNumber: string,
    operatingAirlineCode: string,
    cabin: string
][]) {
    const segments = _segments.map(([_, originLocationCode, departureDateTime, destinationLocationCode, arrivalDateTime, flightNumber, numberInParty, resBookDesigCode, equipment, marketingAirlineCode, marketingAirlineFlightNumber, operatingAirlineCode, cabin]) => ({ _, originLocationCode, departureDateTime, destinationLocationCode, arrivalDateTime, flightNumber, numberInParty, resBookDesigCode, equipment, marketingAirlineCode, marketingAirlineFlightNumber, operatingAirlineCode, cabin }))
    const dayMapping = ['U', 'M', 'T', 'W', 'Q', 'F', 'A']

    function formatSabreDate(dateTimeStr: string) {
        const dt = new Date(dateTimeStr)

        return `${dt.getDate().toString().padStart(2, '0')}${dt.toLocaleString('en', { month: 'short' }).toUpperCase()}`
    }

    function getSabreDayLetter(dateTimeStr: string) {
        const dt = new Date(dateTimeStr)

        return dayMapping[dt.getDay()]
    }

    function formatSabreTime(dateTimeStr: string) {
        const dt = new Date(dateTimeStr)
        const hours = dt.getHours() % 12 || 12 // 12-часовой формат
        const minutes = dt.getMinutes().toString().padStart(2, '0')
        const meridiem = dt.getHours() >= 12 ? 'P' : 'A'

        return `${hours}${minutes}${meridiem}`
    }

    const result = []

    for (const [index, segment] of segments.entries()) {
        const depDate = new Date(segment.departureDateTime)
        const arrDate = new Date(segment.arrivalDateTime)
        const arrivalNextDay = depDate.toDateString() !== arrDate.toDateString() // True если дни отличаются

        result.push(`${index + 1} ${segment.marketingAirlineCode}${segment.marketingAirlineFlightNumber}${segment.resBookDesigCode} ${formatSabreDate(segment.departureDateTime)} ${getSabreDayLetter(segment.departureDateTime)} ${segment.originLocationCode}${segment.destinationLocationCode} SS${segment.numberInParty} ${formatSabreTime(segment.departureDateTime)} ${formatSabreTime(segment.arrivalDateTime)}${arrivalNextDay ? ' +1' : ''} /${segment.operatingAirlineCode} /E`)
    }

    return result
}
