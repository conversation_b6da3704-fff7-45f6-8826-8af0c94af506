<template>
    <div
        class="card border border-1 rounded p-2 relative group w-fit pr-6"
        :class="cardClass"
    >
        <AppButton
            class="p-1.5 --only --xs absolute top-0 right-0 rounded-br-none rounded-tl-none hidden group-hover:flex"
            @click="copyToClipboard(segments.join('\n'))"
        >
            <CopyIcon />
        </AppButton>
        <ul class="list-disc list-outside flex flex-col gap-1">
            <li
                v-for="segment in segments"
                :key="segment"
                class="whitespace-normal text-2xs ml-4"
            >
                {{ segment }}
            </li>
        </ul>
    </div>
</template>

<script setup lang="ts">
import { copyToClipboard } from '@/lib/core/helper/ClipboardHelper'

const props = defineProps<{
    segments: string[]
    isAward: boolean
    isFlightHack: boolean
}>()

const cardClass = computed(() => {
    if (props.isAward) {
        return '!border-danger-100 !bg-danger-50 dark:!bg-danger-950'
    }

    if (props.isFlightHack) {
        return '!border-success-100 !bg-success-50 dark:!bg-success-950'
    }

    return ''
})
</script>
