<template>
    <div
        class="terminal"
        :style="{
            '--terminal-char-width': `${charWidth}px`,
            '--terminal-char-height': `${charHeight}px`,
        }"
        :class="[
            terminalFontSizeClass,
            {'terminal--focused': isActive}
        ]"
        @mouseup="handleMouseUp"
    >
        <div v-if="!terminal.processor.isReady" class="terminal__loading">
            <Loader>
                Connecting
            </Loader>
        </div>

        <div v-if="isActive && activePcc" class="terminal__badge badge --primary --sm">
            {{ activePcc }}
        </div>

        <div ref="viewportRef" class="terminal__viewport">
            <div
                class="terminal__viewport__scrollable"
                contenteditable="true"
                spellcheck="false"
                v-bind="containerProps"
            >
                <div v-if="showLineNumbers" class="terminal__line-numbers">
                    <div
                        v-for="line in terminal.getNumberOfLines()"
                        :key="line"
                        class="terminal__line-numbers__item"
                    >
                        {{ line }}
                    </div>
                </div>
                <div class="terminal__output" v-bind="wrapperProps">
                    <pre v-for="{ data: terminalProcess } of processesToRender" :key="terminalProcess.id"><Component :is="terminalProcess.renderer.value" :terminal-process="terminalProcess" /></pre>
                </div>
            </div>
        </div>

        <span ref="charTestRef" class="terminal__char-test">S</span>
    </div>
</template>

<script setup lang="ts">
import '~modules/gds-terminal/src/assets/styles/terminal.pcss'

import type { Terminal } from '~modules/gds-terminal/src/lib/terminal/Terminal'
import { computedWithControl } from '@vueuse/core'
import { useTerminalViewport } from '~modules/gds-terminal/src/composables/terminal/useTerminalViewport'
import { useTerminalFontSize } from '~modules/gds-terminal/src/composables/useTerminalFontSize'
import type TerminalTool from '~modules/gds-terminal/src/lib/TerminalTool'
import { useVirtualList } from '~/composables/useVirtualList'

defineOptions({
    name: 'TerminalGUI',
})

//

const props = defineProps<{
    terminal: Terminal,
    isActive?: boolean,
}>()

const emit = defineEmits<{
    focus: [],
}>()

//

function handleMouseUp() {
    if (window.getSelection()?.toString()) {
        return
    }

    emit('focus')
}

//

const viewportRef = ref<HTMLElement>()
const outputRef = ref<HTMLElement>()
const scrollableRef = computed(() => {
    return containerProps.ref.value as HTMLElement | undefined
})

//

const { charTestRef, charWidth, charHeight, recalculateCharSize } = useTerminalViewport(props.terminal, viewportRef, scrollableRef)

//

const { terminalFontSizeClass } = useTerminalFontSize()

watch(terminalFontSizeClass, recalculateCharSize, { flush: 'post' })

onUnmounted(() => {
    props.terminal.dispose()
})

onMounted(() => {
    props.terminal.clearScreen()
})

//

const terminalTool = inject<TerminalTool>('terminalTool')!

const activePcc = computed(() => {
    return terminalTool.getActiveSessionStore().activePcc.value
})

//

const processes = computedWithControl(
    () => props.terminal.processes.length,
    () => props.terminal.processes,
)

const { list: processesToRender, containerProps, wrapperProps } = useVirtualList(
    processes,
    {
        itemHeight: (index) => {
            const process = props.terminal.processes[index]!

            return process.getNumberOfLines() * charHeight.value
        },
        overscan: 20,
    },
)

//

// For debugging
const showLineNumbers = false
</script>
