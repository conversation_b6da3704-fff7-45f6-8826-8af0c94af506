<template>
    <AppModalWrapper
        close-button
        outer-button
        stick-to-side
        class="!max-w-[850px] !w-[850px] app-modal__container--flex"
    >
        <AppModalHeader>
            <div class="py-6">
                <div class="font-semibold text-base">
                    {{ command }}
                </div>
            </div>
        </AppModalHeader>

        <div class="card__body card__body--partholder p-2 h-flex">
            <div class="card card__body">
                <div class="flex justify-between">
                    <div class="max-w-[450px] w-[450px]">
                        <SearchFilter :controller="searchController" />
                    </div>

                    <div class="flex gap-4 items-center h-9">
                        <span class="block text-xs font-semibold">Sort by:</span>
                        <div class="button-group">
                            <AppButton
                                v-for="(customSortField, index) in customSortFields"
                                :key="index"
                                :class="{
                                    '--primary': sortController.state && Object.keys(sortController.state).includes(customSortField.name) && sortController.direction === customSortField.direction,
                                }"
                                @click="sortController.set(customSortField.name, customSortField.direction)"
                            >
                                {{ customSortField.title }}
                            </AppButton>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card card__body h-flex overflow-y-auto fancy-scroll">
                <AppTable
                    :sort-controller="sortController"
                    :search-tags="searchController.tags"
                    :columns="columns"
                >
                    <template #body>
                        <tr
                            v-for="(item, index) in pagination.records"
                            :key="index"
                            :class="{
                                'table-tr--highlighted-danger': item.type === WPNIAllTableRowType.Award,
                                'table-tr--highlighted-success': !!item.flightHackType
                            }"
                        >
                            <td>
                                <SegmentsCard
                                    :is-award="item.type === WPNIAllTableRowType.Award"
                                    :is-flight-hack="!!item.flightHackType"
                                    :segments="formatSabreSegment(item.segments)"
                                />
                            </td>
                            <td>
                                <div class="flex gap-2">
                                    {{ formatMoney(item.price) }}
                                    <HelpCircleIcon v-if="item.priceAward" v-tooltip="{ content: formatMoney(item.priceAward) }" />
                                </div>
                            </td>
                            <td>
                                {{ item.validatingCarrier }}
                            </td>
                            <td>
                                {{ formatFlightDuration(item.flightDuration.totalTime) }}
                            </td>
                            <td :class="{'text-danger': item.type === WPNIAllTableRowType.Award, 'text-success': !!item.flightHackType}">
                                {{ item.source }}
                            </td>
                            <td>
                                <template v-if="item.flightHackType">
                                    {{ flightHackTypeLabels[item.flightHackType] }}
                                </template>
                            </td>
                            <td>
                                <AppButton
                                    class="--xs"
                                    :loading="optionsPrepare"
                                    @click="openPqPrepareModal(item)"
                                >
                                    Create PQ
                                </AppButton>
                            </td>
                        </tr>
                    </template>
                </AppTable>
            </div>

            <div class="flex items-center justify-between mt-3">
                <AppPaginationInfo :pagination="pagination" />
                <AppPagination :pagination="pagination" />
            </div>
        </div>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import type { SortDirection } from '~/lib/Search/SortController'
import SortController from '~/lib/Search/SortController'
import SearchController from '~/lib/Search/SearchController'
import AppTable from '~/components/Table/AppTable.vue'
import NumberRangeSearchTag from '~/lib/Search/Tag/NumberRangeSearchTag'
import TextSearchTag from '~/lib/Search/Tag/TextSearchTag'
import type { WPNIAllTableRow, FlightHackTableRow } from '~modules/gds-terminal/src/helpers/DeltaHelper'
import { formatSabreSegment } from '~modules/gds-terminal/src/helpers/DeltaHelper'
import SegmentsCard from '~modules/gds-terminal/src/components/SegmentsCard.vue'
import SearchFilter from '~/components/Search/SearchFilter.vue'
import PqPrepareModal from '~modules/gds-terminal/src/modals/PqPrepareModal.vue'
import { preventDuplication } from '~/lib/Helper/PromiseHelper'
import { FrontendPagination } from '~/lib/Model/FrontendPagination'
import type TerminalTool from '~modules/gds-terminal/src/lib/TerminalTool'
import type { MaybeRefOrGetter } from 'vue'
import BooleanSearchTag from '~/lib/Search/Tag/BooleanSearchTag'
import SelectSearchTag from '~/lib/Search/Tag/SelectSearchTag'
import { WPNIAllTableRowType } from '~modules/gds-terminal/src/lib/delta/WPNIAllDelta/WPNIAllStateDelta'
import  { FlightHackType }  from '#/modules/gds-terminal/src/lib/delta/FlightHacksDelta/FlightHackRowDelta'

defineOptions({
    name: 'WpniAllModal',
    modal: {
        position: 'right',
    },
})

const props = defineProps<{
    command: string
    terminalTool: TerminalTool
    leadPk?: PrimaryKey
    records: MaybeRefOrGetter<Array<WPNIAllTableRow | FlightHackTableRow>>
    pcc?: string
    onlyFlightHacks?: boolean
}>()

const PRICE_WEIGHT_MODIFIER = 1.75
const STOPS_WEIGHT_MODIFIER = 0.75
const DURATION_WEIGHT_MODIFIER = 1

const _records = computed(() => toValue(props.records))

const sessionChannel = props.terminalTool.getActiveSessionStore().terminalConnector.getSessionChannel()

const flightHackTypeLabels = {
    [FlightHackType.ELR]: 'ELR',
    [FlightHackType.FRT]: 'FRT',
    [FlightHackType.ORIGIN_OPEN_JAW]: 'ORIGIN OPEN JAW',
}

const columns = useTableColumns({
    segments: {
        label: 'Segments',
        width: 'max',
    },
    price: {
        label: 'Price',
        sortable: true,
        width: 'min',
    },
    validatingCarrier: {
        label: 'V.Carrier',
        sortable: true,
        width: 'min',
    },
    flightDuration: {
        label: 'Duration',
        sortable: true,
        width: 'min',
    },
    source: {
        label: 'PCC',
        sortable: true,
        width: 'min',
    },
    flightHackType: {
        label: 'Hack Type',
        width: 'min',
    },
    action: {
        width: 'min',
    },
})

const sortController = SortController.fromColumns(columns, {
    default: {
        price: 'asc',
    },
})

const customSortFields: {name: string, title: string, direction: SortDirection}[] = [
    {
        name: 'flightDuration',
        title: 'Best Time',
        direction: 'desc',
    },
    {
        name: 'price',
        title: 'Best Price',
        direction: 'asc',
    },
    {
        name: 'bestDealWeight',
        title: 'Best Deal',
        direction: 'desc',
    },
]

const scoringBoundaries = computed<{ maxStops: number; maxPrice: number; maxDuration: number }>(() => ({
    maxStops: Math.max(..._records.value.map((record) => record.stopsCount)),
    maxPrice: Math.max(..._records.value.map((record) => record.price?.amount ?? 0)),
    maxDuration: Math.max(..._records.value.map((record) => record.flightDuration.totalTime)),
}))

const coefficients = computed(() => ({
    price: PRICE_WEIGHT_MODIFIER,
    duration: scoringBoundaries.value.maxDuration ? (scoringBoundaries.value.maxPrice / scoringBoundaries.value.maxDuration * DURATION_WEIGHT_MODIFIER) : 0,
    stops: scoringBoundaries.value.maxStops ? (scoringBoundaries.value.maxPrice / scoringBoundaries.value.maxStops * STOPS_WEIGHT_MODIFIER) : 0,
}))

const items = computed(() => {
    return sortController.sort(
        searchController.filter(_records.value, {
            price: (record) => {
                return record.price.amount
            },
            flightDuration: (record) => {
                return record.flightDuration.totalTime
            },
            isFlightHack: (record) => {
                return !!record?.flightHackType ? 1 : 0
            },
        }),
        {
            price: (price) => price.amount,
            flightDuration: (flightDuration) => flightDuration.totalTime,
            bestDealWeight: (_, item) => Math.sqrt(
                Math.pow(coefficients.value.price * item.price?.amount ?? 0, 2) +
                    Math.pow(coefficients.value.duration * item.flightDuration.totalTime, 2) +
                    Math.pow(coefficients.value.stops * item.stopsCount, 2),
            ),
        },
    )
})

const limitsForPrice = computed<[min: number, max: number]>(() => {
    return _records.value.reduce(
        (acc, record) => {
            const amount = record.price?.amount

            if (amount !== undefined) {
                return [Math.min(acc[0], amount), Math.max(acc[1], amount)]
            }

            return acc
        },
        [Infinity, -Infinity],
    )
})

const searchController = new SearchController({
    price: new NumberRangeSearchTag('Price', limitsForPrice),
    validatingCarrier: new TextSearchTag('Validating carrier'),
    source: new TextSearchTag('PCC'),
    flightHackType: new SelectSearchTag('Flight hack type', [
        { title: flightHackTypeLabels[FlightHackType.ELR], value: FlightHackType.ELR },
        { title: flightHackTypeLabels[FlightHackType.FRT], value: FlightHackType.FRT },
        { title: flightHackTypeLabels[FlightHackType.ORIGIN_OPEN_JAW], value: FlightHackType.ORIGIN_OPEN_JAW },
    ]),
    isFlightHack: new BooleanSearchTag('Flight hack'),
}, {
    syncWithQuery: false,
})

const pagination = new FrontendPagination(items, {
    page: 1,
    pageSize: 20,
})

if (props.pcc) {
    searchController.tags.source.setValue(props.pcc)
}

if (props.onlyFlightHacks) {
    searchController.tags.isFlightHack.setDefaultValue([1]).initDefaultValues()
}

const formatter = useService('formatter')

const formatMoney = (money?: { amount: number, currency: string }) => {
    if (!money) {
        return ''
    }

    return formatter.money(money.amount, { withCurrency: money.currency })
}

const optionsPrepare = ref(false)

const openPqPrepareModal = async (item: WPNIAllTableRow | FlightHackTableRow) => {
    await preventDuplication(async () => {
        const { option } = await props.terminalTool.getActiveSessionStore().terminalConnector.http<{option: string}>('/command/format-option', {
            method: 'POST',
            body: {
                sessionChannelName: sessionChannel,
                ...item,
            },
        })

        optionsPrepare.value = false

        const pqPrepareModal = useModal(PqPrepareModal)

        await pqPrepareModal.open({
            terminalTool: props.terminalTool,
            formData: {
                pq: option,
                pcc: item.source,
                pq_currency: item.price?.currency ?? 'USD',
                lead_pk: props.leadPk,
            },
            close: pqPrepareModal.close,
        }, { position: 'center' })
    }, optionsPrepare)
}

const formatFlightDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)

    return [
        hours ? `${hours}h` : '',
        minutes ? `${minutes}m` : '',
    ].filter(Boolean).join(' ')
}
</script>
