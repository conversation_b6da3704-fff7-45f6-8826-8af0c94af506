<template>
    <AppModalWrapper
        close-button
        header="Usage statistics"
        class="!min-w-[1520px]"
    >
        <div class="card card__body flex flex-col gap-4">
            <ChartSection
                :search-tags="{
                    commandsSelectSearchTag: commandStatisticsController.commandsSelectSearchTag,
                    agentOrTeamSearchTag: commandStatisticsController.agentOrTeamSearchTag,
                    dateRangeSearchTag: commandStatisticsController.dateRangeSearchTag,
                }"
                :get-statistics-per-day="commandStatisticsController.getStatisticsPerDay"
            />
            <CommandStatisticsList
                :search-tags="{
                    agentOrTeamSearchTag: commandStatisticsController.agentOrTeamSearchTag,
                    dateRangeSearchTag: commandStatisticsController.dateRangeSearchTag,
                }"
                :get-statistics-per-user="commandStatisticsController.getStatisticsPerUser"
            />
        </div>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import ChartSection from '~modules/gds-terminal/src/sections/CommandStatistics/ChartSection.vue'
import CommandStatisticsList from '~modules/gds-terminal/src/sections/CommandStatistics/CommandStatisticsList.vue'
import {
    useCommandStatisticsController,
} from '~modules/gds-terminal/src/sections/CommandStatistics/composable/useCommandStatisticsController'

defineOptions({
    name: 'CommandStatisticsModal',
    modal: {
        position: 'top',
    },
})
const commandStatisticsController = useCommandStatisticsController()
</script>
