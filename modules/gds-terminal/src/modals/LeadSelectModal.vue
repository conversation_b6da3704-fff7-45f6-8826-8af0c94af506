<template>
    <AppModalWrapper class="!w-fit">
        <form class="card border w-[432px]" @submit.prevent="submit">
            <div class="card__header card__header--small">
                <span class="card__title">
                    Select Lead
                </span>
                <AppButton
                    class="--ghost --square -mr-2.5"
                    type="button"
                    @click="emit('close')"
                >
                    <CloseIcon />
                </AppButton>
            </div>
            <div class="card__body grid grid-cols-2 gap-4">
                <FormField
                    :form="form"
                    field="lead_pk"
                    label="Lead ID"
                    required
                    class="col-span-2 text-xs font-medium"
                >
                    <InputSelect
                        v-model="form.data.lead_pk"
                        :options="leadOptions"
                        with-empty
                        search
                        size="small"
                        @search="handleLeadSearch"
                    />
                </FormField>
            </div>
            <div class="card__footer card__footer--small py-2">
                <AppButton
                    class="--small flex-1"
                    type="button"
                    @click="emit('close')"
                >
                    Cancel
                </AppButton>
                <AppButton class="--primary --small flex-1" @click="submit">
                    Confirm
                </AppButton>
            </div>
        </form>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import CloseIcon from '@/assets/icons/CloseIcon.svg?component'
import FormField from '~/components/Form/FormField.vue'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import { getFullName } from '~/lib/Helper/PersonHelper'
import type ArrayConditionAndOr from '~/lib/Helper/ArrayCondition/Conditions/ArrayConditionAndOr'

defineOptions({
    name: 'LeadSelectModal',
    modal: {
        position: 'top',
        promise: true,
    },
})

const props = withDefaults(defineProps<{
    applyCondition?(and: ArrayConditionAndOr<any>): void
}>(), {
    applyCondition: undefined,
})

const emit = defineEmits<{
    resolve: [],
    reject: [],
    close: [],
}>()

const { useModel } = useContext()
const leadModel = useModel('Lead')
const leadList = leadModel.useList({ with: ['clientPreview']})

const form = useForm<{
    lead_pk: PrimaryKey | undefined,
}>({
    lead_pk: undefined,
}, {
    lead_pk: ValidationRules.Required(),
})

const submit = form.useSubmit(async (data) => {
    emit('resolve', data.lead_pk)
})

const leadOptions = computed(() => {
    return leadList.records.map((lead) => ({
        title: `#${lead.pk} | ${getFullName(lead.clientPreview)}`,
        subtitle: `${lead.from_iata_code} -> ${lead.to_iata_code}`,
        value: lead.pk,
    }))
})

const handleLeadSearch = (query: string) => {
    query = query.trim()

    if (query.length < 1) {
        return
    }

    leadList.fetch({
        where: (and) => {
            if (props.applyCondition) {
                props.applyCondition(and)
            } else {
                and.eq('can_add_pq', true)
            }
            and.search(query)
        },
    })
}
</script>
