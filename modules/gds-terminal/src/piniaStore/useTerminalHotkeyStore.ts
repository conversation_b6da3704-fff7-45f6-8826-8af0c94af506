import type { MaybeRefOrGetter } from 'vue'

export const useTerminalHotkeyStore = defineStore('terminalHotkey', () => {
    const { useModel, currentUserPk } = useContext()
    const isTerminalHotkeyBarOpened = ref(false)
    const terminalHotkeyModel = useModel('TerminalHotkey')
    const terminalHotkeyResourceList = terminalHotkeyModel.useResourceList({
        resource: {
            name: 'TerminalHotkeyList',
            pk: currentUserPk,
        },
    })

    const getHotkeysByPcc = (pcc: MaybeRefOrGetter<string | undefined>, withGlobal: boolean = true) => {
        return terminalHotkeyResourceList.records.filter((terminalHotkey) => {
            const name = terminalHotkey.consolidator_system_name

            return name === toValue(pcc) || (withGlobal && name === null)
        })
    }

    const getHotkeysAll = () => {
        return terminalHotkeyResourceList.records
    }

    const fetch = async () => {
        if (terminalHotkeyResourceList.loaded.value) {
            return
        }

        await terminalHotkeyResourceList.fetch()
    }

    const toggleHotkeyBar = (value: boolean) => {
        isTerminalHotkeyBarOpened.value = value
    }

    return {
        fetch,
        getHotkeysByPcc,
        getHotkeysAll,
        isTerminalHotkeyBarOpened,
        toggleHotkeyBar,
    }
})

export const useConsoleHistoryStore = defineStore('consoleHistory', () => {
    const localStorageCommands = useLocalStorage<string[]>('state:console-history', [])
    const commandsHistory = ref<string[]>(localStorageCommands.value ?? [])

    const limit = 100

    const push = (command: string) => {
        commandsHistory.value.unshift(command)

        if (commandsHistory.value.length > limit) {
            commandsHistory.value = commandsHistory.value.slice(0, limit)
        }

        localStorageCommands.value = commandsHistory.value
    }

    return {
        commandsHistory,
        push,
    }
})
