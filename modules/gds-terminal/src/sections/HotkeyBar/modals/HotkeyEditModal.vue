<template>
    <AppModalWrapper
        class="!max-w-[970px]"
        header="Edit PF Keys"
        close-button
    >
        <div class="card h-full">
            <div class="card__body card__body--partholder p-2 flex-row ">
                <div class="card !w-1/2 min-h-[424px] flex flex-col">
                    <div class="card__header">
                        <div class="card__title">
                            Pick a PF key to configure
                        </div>
                        <label v-tooltip="tooltip" class="flex items-center gap-2">
                            {{ globalMode ? 'Global' : 'PCC' }}
                            <InputSwitch
                                v-model="globalMode"
                                size="xs"
                                @update:model-value="resetHotkeySelection"
                            />
                        </label>
                    </div>
                    <div class="card__body  grid grid-cols-6 gap-1  px-4 pt-4 ">
                        <AppButton
                            v-for="(hotkey, index) in hotkeys"
                            :key="index"
                            class="relative"
                            :class="getHotkeyClasses(hotkey)"
                            :disabled="hotkeyAvailable(hotkey)"
                            @click="selectHotkey(hotkey)"
                        >
                            {{ hotkey.map(toUpperCase).join(' + ') }}
                            <span
                                v-if="hotkeyExists(hotkey) && !hotkeyExists(hotkey)?.consolidator_system_name"
                                v-tooltip="'Global hotkey'"
                                class="absolute -right-1 -top-1 badge --primary --xs w-1 h-1 text-3xs"
                            >G</span>
                        </AppButton>
                    </div>

                    <div class="mt-auto p-4 flex justify-center gap-3">
                        <AppButton class="flex-grow" @click="openImportModal">
                            <DownloadIcon />Import PF Keys
                        </AppButton>
                        <AppButton class="flex-grow" @click="openExportModal">
                            <UploadIcon />Export PF Keys
                        </AppButton>
                    </div>
                </div>

                <div class="card !w-1/2 min-h-[424px]">
                    <div class="card__header">
                        <div class="card__title">
                            Edit Key Details
                        </div>
                        <span v-if="!globalMode" class="badge --primary">
                            PCC: {{ pcc }}
                        </span>
                    </div>
                    <form
                        v-if="selectedTerminalHotkey.length"
                        class="card__body"
                        @submit.prevent="submit"
                    >
                        <div class="flex flex-col gap-4">
                            <FormField
                                :form="form"
                                field="title"
                                label="PF Key Label"
                                required
                            >
                                <InputText v-model="form.data.title" placeholder="Short name for this macro" />
                            </FormField>

                            <!--                        <FormField-->
                            <!--                            :form="form"-->
                            <!--                            field="autorun"-->
                            <!--                            label=" "-->
                            <!--                        >-->
                            <!--                            <label class="flex gap-2 items-center mt-2">-->
                            <!--                                <InputCheckbox v-model="form.data.autorun" />-->
                            <!--                                <span class="block select-none">Autorun</span>-->
                            <!--                            </label>-->
                            <!--                        </FormField>-->

                            <FormField
                                :form="form"
                                field="command"
                                label="Command"
                                required
                                :help="`Use ${commandSplitCharacter} to split commands and execute them one by one. Alt + Enter to insert ${commandSplitCharacter}. <br>You can use new lines for better readability.`"
                            >
                                <InputTextarea
                                    ref="commandInput"
                                    v-model="command"
                                    placeholder="List of commands"
                                    rows="4"
                                    @keydown="updateHotKeyCommand($event)"
                                />
                            </FormField>
                            <FormField
                                :form="form"
                                field="description"
                                label="Description"
                                help="The description will appear as a tooltip"
                            >
                                <InputTextarea v-model="form.data.description" placeholder="Description" />
                            </FormField>
                        </div>
                    </form>
                    <div v-else class="flex justify-center items-center mt-36 text-secondary">
                        Select key to edit
                    </div>
                </div>
            </div>
        </div>

        <template #footer="{close}">
            <div class="flex justify-between gap-4">
                <AppButton
                    class="mr-auto"
                    :disabled="form.loading.value"
                    @click="close"
                >
                    Close
                </AppButton>

                <AppButton
                    v-if="form.data.pk"
                    class="--danger-lite"
                    @click="removeBinding"
                >
                    Remove binding
                </AppButton>

                <AppButton
                    class="--primary"
                    :loading="form.loading.value"
                    @click="submit"
                >
                    Apply changes
                </AppButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import FormField from '~/components/Form/FormField.vue'
import { toUpperCase } from 'uri-js/dist/esnext/util'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import { arraysAreEqual } from '~/lib/Helper/ArrayHelper'
import { replaceEventKey, tryIdentifyExceptionKey } from '~modules/gds-terminal/src/helpers/InputHelper'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import { useTerminalHotkeyStore } from '~modules/gds-terminal/src/piniaStore/useTerminalHotkeyStore'
import { commandSplitCharacter } from '~modules/gds-terminal/src/composables/terminal/useTerminalHotKeys'
import HotkeyExportModal from '~modules/gds-terminal/src/sections/HotkeyBar/modals/HotkeyExportModal.vue'
import HotkeyImportModal from '~modules/gds-terminal/src/sections/HotkeyBar/modals/HotkeyImportModal.vue'

const props = defineProps<{
    pcc: string
}>()

const hotkeys = [['F1'], ['F2'], ['F3'], ['F4'], ['F5'], ['F6'], ['F7'], ['F8'], ['F9'], ['F10'],
                 ['F11'], ['F12'], ['F13'], ['F14'], ['F15'], ['F16'], ['F17'], ['F18'], ['F19'], ['F20']]

const { useModel, currentUserPk } = useContext()

const terminalHotkeyModel = useModel('TerminalHotkey')
const terminalHotkeyStore = useTerminalHotkeyStore()

const terminalHotkeyList = computed(() => terminalHotkeyStore.getHotkeysByPcc(props.pcc))

const terminalHotkeyListAll = computed(() => terminalHotkeyStore.getHotkeysAll())

const selectedTerminalHotkey = ref<string[]>([])

const globalMode = ref(true)

const tooltip = computed(() => {
    return globalMode.value ? 'Visible across the entire GDS Terminal' : 'Visible only within the current PCC'
})

const exportModal = useModal(HotkeyExportModal)
const importModal = useModal(HotkeyImportModal)

const form = useForm<{
    pk: PrimaryKey | undefined,
    title: string,
    autorun: boolean,
    command: string,
    description: string | null,
    pcc: string | null,
}>({
    pk: undefined,
    title: '',
    autorun: false,
    command: '',
    description: '',
    pcc: globalMode.value ? null : props.pcc,
}, {
    title: ValidationRules.Required(),
    command: ValidationRules.Required(),
})

const submit = form.useSubmit(async (data) => {
    if (data.pk) {
        await terminalHotkeyModel.actions.createOrUpdate({
            data: [{
                pk: data.pk,
                title: data.title,
                description: data.description,
                command: data.command,
                autorun: data.autorun,
                keys: selectedTerminalHotkey.value,
                consolidator_system_name: data.pcc,
            }],
        })

        toastSuccess('PF key updated successfully!')
    } else {
        await terminalHotkeyModel.actions.createOrUpdate({
            data: [{
                pk: null,
                title: data.title,
                description: data.description,
                command: data.command,
                autorun: data.autorun,
                keys: selectedTerminalHotkey.value,
                consolidator_system_name: globalMode.value ? null : props.pcc,
            }],
        })

        await waitForResourceEvent('TerminalHotkeyList', 'insert', currentUserPk)

        form.updateData({
            pk: hotkeyExists(selectedTerminalHotkey.value)?.pk,
        })

        toastSuccess('PF key created successfully!')
    }
}, {
    resetOnSuccess: false,
})

const hotkeyExists = (hotkey: string[]) => {
    return terminalHotkeyList.value.find((terminalHotkey) => arraysAreEqual(terminalHotkey.keys, hotkey))
}

const hotkeyExistsInAnotherPCC = (hotkey: string[]) => {
    return terminalHotkeyListAll.value.find((terminalHotkey) =>
        arraysAreEqual(terminalHotkey.keys, hotkey) && terminalHotkey.consolidator_system_name && terminalHotkey.consolidator_system_name != props.pcc)
}

const hotkeyAvailable = (hotkey: string[]) => {
    let hk

    if (globalMode.value) {
        hk = hotkeyExistsInAnotherPCC(hotkey) || hotkeyExists(hotkey)
    } else {
        hk = hotkeyExists(hotkey)
    }

    return !!hk && (!!hk.consolidator_system_name === globalMode.value)
}

const selectHotkey = (hotkey: string[]) => {
    selectedTerminalHotkey.value = hotkey

    const alreadyExisted = terminalHotkeyList.value.find((terminalHotkey) => arraysAreEqual(terminalHotkey.keys, hotkey))

    if (alreadyExisted) {
        form.updateInitialData({
            pk: alreadyExisted.pk,
            title: alreadyExisted.title,
            autorun: alreadyExisted.autorun,
            command: alreadyExisted.command,
            description: alreadyExisted.description,
            pcc: alreadyExisted.consolidator_system_name,
        })
    } else {
        form.updateInitialData({
            pk: undefined,
            title: '',
            autorun: false,
            command: '',
            description: '',
            pcc: globalMode.value ? null : props.pcc,
        })
    }
}

const updateHotKeyCommand = (event: KeyboardEvent) => {
    const exceptionKeyToReplace = tryIdentifyExceptionKey(event)

    if (exceptionKeyToReplace) {
        event.preventDefault()
        replaceEventKey(event, exceptionKeyToReplace)

        return
    }

    if (event.key === 'Enter' && event.altKey) {
        event.preventDefault()
        form.data.command += '^\n'
    }
}

const resetHotkeySelection = () => {
    selectedTerminalHotkey.value = []
}

const commandInput = ref()

const command = computed({
    get() {
        return form.data.command
    },
    set(value: string) {
        const cursorPosition = commandInput.value?.input.selectionStart
        form.data.command = value.toUpperCase()

        nextTick(() => {
            if (cursorPosition) {
                commandInput.value?.input.setSelectionRange(cursorPosition, cursorPosition)
            }
        })
    },
})

async function removeBinding() {
    const pk = form.data.pk

    if (!pk) {
        return
    }

    await terminalHotkeyModel.actions.delete({ pk })

    await waitForResourceEvent('TerminalHotkeyList', 'delete', currentUserPk)

    toastSuccess('PF key removed successfully!')

    form.updateInitialData({
        pk: undefined,
        title: '',
        autorun: false,
        command: '',
        description: '',
        pcc: globalMode.value ? null : props.pcc,
    })
}

const getHotkeyClasses = (hotkey: string[]) => {
    const isSelected = arraysAreEqual(hotkey, selectedTerminalHotkey.value)
    const exists = hotkeyExists(hotkey)
    const base = '!border-transparent --outline '

    if (isSelected) {
        return '--primary'
    }

    if (!exists && !isSelected) {
        return base + '--neutral !bg-neutral-50 dark:!bg-dark-5'
    }

    if (!isSelected) {
        return base + '--primary !bg-primary-100 dark:!bg-dark-4'
    }

    return ''
}

const openExportModal = () => {
    exportModal.open({
        pcc: props.pcc,
    })
}
const openImportModal = () => {
    importModal.open({
        pcc: props.pcc,
        isGlobalMode: globalMode.value,
    })
}
</script>
