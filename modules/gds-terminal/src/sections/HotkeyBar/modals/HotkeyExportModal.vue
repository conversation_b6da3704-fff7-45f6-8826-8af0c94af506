<template>
    <AppModalWrapper
        class="!max-w-[472px]"
        header="Export PF Keys"
        close-button
    >
        <div class="card h-full">
            <div class="card__body">
                <div class="flex justify-between">
                    Choose one or more keys

                    <label class="flex gap-2 items-center"><InputCheckbox v-model="allSelected" @change="selectAll" />Select all</label>
                </div>
                <hr class="mt-3 mb-4 dark:border-secondary-600">

                <div class="grid grid-cols-6 gap-1  px-4 pt-4 ">
                    <AppButton
                        v-for="(hotkey, index) in hotkeysData"
                        :key="index"
                        class="relative"
                        :class="getHotkeyClasses(hotkey)"
                        :disabled="!hotkey.exist"
                        @click="()=>{
                            validateSelected(hotkey.selected)
                            hotkey.selected = !hotkey.selected
                        }"
                    >
                        <InputCheckbox
                            v-model="hotkey.selected"
                            :disabled="!hotkey.exist"
                            @click.stop="validateSelected(hotkey.selected)"
                        />{{ hotkey.key.join(' + ') }}
                        <span
                            v-if="hotkey.isGlobal"
                            v-tooltip="'Global hotkey'"
                            class="absolute -right-1 -top-1 badge --primary --xs w-1 h-1 text-3xs"
                        >G</span>
                    </AppButton>
                </div>
            </div>
        </div>
        <template #footer="{close}">
            <div class="flex justify-between gap-4">
                <AppButton
                    class="mr-auto"
                    @click="close"
                >
                    Close
                </AppButton>
                <AppButton
                    class="--primary"
                    :disabled="!isAnySelected"
                    @click="exportPfKeys"
                >
                    Export PF Keys
                </AppButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import { arraysAreEqual } from '~/lib/Helper/ArrayHelper'
import { useTerminalHotkeyStore } from '~modules/gds-terminal/src/piniaStore/useTerminalHotkeyStore'
import type { RawPfKeyEntry } from '~modules/gds-terminal/src/composables/useTerminalPFKeyParser'
import { useTerminalPFKeysParser } from '~modules/gds-terminal/src/composables/useTerminalPFKeyParser'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'

const props = defineProps<{
    pcc: string,
}>()

const { generatePfFileContent, createPFKeysFile } = useTerminalPFKeysParser()

const terminalHotkeyStore = useTerminalHotkeyStore()

const hotkeysData = ref<{
    key: string[],
    selected: boolean,
    exist: boolean,
    isGlobal: boolean,
}[]>([])

const allSelected = ref<boolean>(false)

//

const isAnySelected = computed(() => {
    return !!hotkeysData.value.find((key) => key.selected)
})

const terminalHotkeyList = computed(() => terminalHotkeyStore.getHotkeysByPcc(props.pcc))

//

const initKeys = () => {
    hotkeysData.value = Array.from({ length: 20 }, (_, pfKey) => {
        const key = [`F${pfKey + 1}`]
        const hotkey = hotkeyExists(key)

        return {
            key: key,
            selected: false,
            exist: !!hotkey,
            isGlobal: !!hotkey && !hotkey?.consolidator_system_name,
        }
    })
}

const hotkeyExists = (hotkey: string[]) => {
    return terminalHotkeyList.value.find((terminalHotkey) => arraysAreEqual(terminalHotkey.keys, hotkey))
}

initKeys()

const selectAll = () => {
    hotkeysData.value.filter((key) => key.exist).forEach((key) => { key.selected = allSelected.value })
}

const validateSelected = (event) => {
    if (event) {
        allSelected.value = false
    }
}

const getHotkeyData = (hotkey: string[]) => {
    return terminalHotkeyList.value.find((terminalHotkey) => arraysAreEqual(terminalHotkey.keys, hotkey))
}

const exportPfKeys = () => {
    const dataToExport: RawPfKeyEntry[] = hotkeysData.value.filter((key) => key.selected).map((key) => {
        const hotkeyData = getHotkeyData(key.key)

        return {
            index: Number(key.key[0].slice(1))  - 1,
            command: hotkeyData?.command ?? '',
            desc: hotkeyData?.description ?? '',
            label: hotkeyData?.title ?? '',
            global: key.isGlobal,
        }
    })

    const pfKeysConverted = generatePfFileContent(dataToExport)

    createPFKeysFile(pfKeysConverted)

    toastSuccess('PF keys exported successfully!')
}

const getHotkeyClasses = (hotkey) => {
    const base = '!border-transparent --outline '

    return base + (!hotkey.exist && !hotkey.selected ?  '--neutral !bg-neutral-50 dark:!bg-dark-5' : '--primary !bg-primary-50 dark:!bg-dark-4')
}
</script>

