<template>
    <AppModalWrapper
        class="!max-w-[970px]"
        header="Import PF Keys"
        close-button
    >
        <div class="card min-h-[440px]">
            <div class="card__body">
                <div v-if="!hotkeysData.length" class="flex justify-center items-center mt-36">
                    <UploadArea
                        compact
                        accept=".pfkey"
                        :max-size="5"
                        subtitle="File should be in .pfkey format"
                        @load="handleFileUpload"
                    />
                </div>

                <template v-else>
                    <div class="flex items-center justify-between">
                        <label class="flex items-center gap-2"> <InputCheckbox v-model="allSelected" @change="selectAll" /> Select all </label>

                        <div v-if="hasInvalidPfKeys" class="badge --danger --soft --normal">
                            You can upload up to 20 PF keys only. Please reduce your file content and try again.
                        </div>

                        <AppButton @click="hotkeysData = []">
                            Reset File
                        </AppButton>
                    </div>
                    <hr class="my-4 dark:border-secondary-600">
                    <div class="flex flex-col gap-4 max-h-[400px] overflow-auto fancy-scroll ">
                        <div
                            v-for="(hotkey, index) in hotkeysData"
                            :key="index"
                        >
                            <div class="flex items-center justify-between relative">
                                <label class="flex items-center gap-2">
                                    <InputCheckbox
                                        v-model="hotkey.selected"
                                        :disabled="hotkey.conflicted"
                                        @click.stop="validateSelected(hotkey.selected)"
                                    />
                                    <span class="badge --primary --soft dark:text-primary-9">{{ hotkey.keys.join(' + ') }}</span>
                                    <span class="font-medium">{{ hotkey.title }}</span>
                                </label>

                                <div class="mr-4 flex gap-1">
                                    <span
                                        v-if="!hotkey.consolidator_system_name"
                                        v-tooltip="'Visible across the entire GDS Terminal'"
                                        class=" badge --primary"
                                    >Global hotkey</span>

                                    <span
                                        v-if="hotkey.conflicted"
                                        v-tooltip="getConflictTooltip(hotkey)"
                                        class="badge --warning"
                                    >Hotkey conflicted</span>
                                </div>
                            </div>
                            <div class="break-all ml-5 mr-4 p-2 border dark:border-secondary-600 rounded-md mt-2">
                                {{ hotkey.command }}
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
        <template #footer="{close}">
            <div class="flex justify-between gap-4">
                <AppButton
                    class="mr-auto"
                    @click="close"
                >
                    Close
                </AppButton>
                <AppButton
                    :disabled="!isAnySelected"
                    class="--primary"
                    @click="submit"
                >
                    Apply PF Keys
                </AppButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import { arraysAreEqual } from '~/lib/Helper/ArrayHelper'
import { useTerminalHotkeyStore } from '~modules/gds-terminal/src/piniaStore/useTerminalHotkeyStore'
import { useTerminalPFKeysParser } from '~modules/gds-terminal/src/composables/useTerminalPFKeyParser'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import UploadArea from '@/components/Upload/UploadArea.vue'
import FileData from '@/lib/core/helper/File/FileData'

const props = defineProps<{
    pcc: string,
    isGlobalMode: boolean,
}>()

const emit = defineEmits<{
    close: [],
}>()

const { parsePfFile } = useTerminalPFKeysParser()

const { useModel } = useContext()

const terminalHotkeyModel = useModel('TerminalHotkey')
const hotkeysData = ref<Partial<{
    pk: PrimaryKey | null,
    keys: string[],
    title: string,
    description: string,
    command: string,
    autorun: boolean,
    consolidator_system_name: string | null,
    selected: boolean,
    conflicted: boolean,
}[]>>([])

const allSelected = ref(true)

const hasInvalidPfKeys = ref(false)

const selectAll = () => {
    hotkeysData.value.forEach((key) => {
        if (!key.conflicted) {
            key.selected = allSelected.value
        }
    })
}

const terminalHotkeyStore = useTerminalHotkeyStore()

const terminalHotkeyList = computed(() => terminalHotkeyStore.getHotkeysByPcc(props.pcc))
const terminalHotkeyListAll = computed(() => terminalHotkeyStore.getHotkeysAll())

const isAnySelected = computed(() => {
    return !!hotkeysData.value.find((key) => key.selected)
})

const getHotkeyData = (hotkey: string[]) => {
    return terminalHotkeyList.value.find((terminalHotkey) => arraysAreEqual(terminalHotkey.keys, hotkey))
}

const hotkeyExistsInAnotherPCC = (hotkey: string[]) => {
    return terminalHotkeyListAll.value.find((terminalHotkey) =>
        arraysAreEqual(terminalHotkey.keys, hotkey) && terminalHotkey.consolidator_system_name && terminalHotkey.consolidator_system_name != props.pcc)
}

const validateSelected = (event) => {
    if (event) {
        allSelected.value = false
    }
}

function handleFileUpload(event: Event) {
    const file = FileData.fromFile(event[0])?._file

    if (!file) return

    const reader = new FileReader()

    reader.onload = () => {
        const content = reader.result as string
        const parsedContent = parsePfFile(content)

        hasInvalidPfKeys.value = parsedContent.some(hotkey => !isValidPfKey(hotkey.keys))

        hotkeysData.value = parsedContent.filter(hotkey => isValidPfKey(hotkey.keys)).map((hotkey) => {
            let existingHotkey

            if (props.isGlobalMode || hotkey.isGlobal) {
                existingHotkey = hotkeyExistsInAnotherPCC(hotkey.keys) || getHotkeyData(hotkey.keys)
            } else {
                existingHotkey = getHotkeyData(hotkey.keys)
            }

            const hasPcc = Boolean(existingHotkey?.consolidator_system_name)

            const conflicted = existingHotkey ?
                (!hasPcc && !hotkey.isGlobal && !props.isGlobalMode) || (hasPcc && (hotkey.isGlobal || props.isGlobalMode))
                : false

            return {
                pk: existingHotkey?.pk ?? null,
                ...hotkey,
                autorun: false,
                consolidator_system_name: props.isGlobalMode || hotkey.isGlobal ? null : props.pcc,
                selected: !conflicted,
                conflicted: conflicted,
            }
        }).slice(0, 20)

        allSelected.value = !hotkeysData.value.find(hotkey => hotkey.conflicted)
    }

    reader.readAsText(file, 'utf-8')
}

const submit = async () => {
    const data = hotkeysData.value.filter((key) => key.selected).map(({ selected, isGlobal, ...data }) => data)

    await terminalHotkeyModel.actions.createOrUpdate({
        data: data,
    })

    toastSuccess('PF keys imported successfully!')
    emit('close')
}

const getConflictTooltip = (hotkey) => {
    const isGlobal = hotkey.consolidator_system_name

    return `This hotkey will overwrite ${isGlobal ? 'global' : 'PCC specific'} hotkey with ${isGlobal ? 'PCC specific' : 'global'}`
}

const isValidPfKey = (hotkey) => {
    return Number(hotkey[0].slice(1)) <= 20
}
</script>

