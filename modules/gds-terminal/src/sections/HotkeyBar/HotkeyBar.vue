<template>
    <div class="card items-start p-0.5 flex justify-between">
        <div
            ref="scrollContainerRef"
            class="flex gap-2 pl-0.5 items-center flex-wrap flex-1"
        >
            <HotkeyButton
                v-for="(hotkey, index) in terminalHotkeyList"
                :key="index"
                :hotkey="hotkey"
                @click="applyHotkey(hotkey)"
            />
        </div>

        <div class="flex gap-2 items-center">
            <AppButton class="--only --2xs h-7 w-7" @click="openEditHotkeys">
                <Edit3Icon />
            </AppButton>

            <AppButton
                class="focus:ring-0 focus:outline-none flex justify-center gap-0.5 text-2xs leading-[14px] px-2 h-7 !text-secondary pl-0"
                @click="terminalHotkeyStore.toggleHotkeyBar(false)"
            >
                <ChevronsRightIcon />
                <span class="block">HIDE PF KEYS</span>
            </AppButton>
        </div>
    </div>
</template>

<script setup lang="ts">
import HotkeyButton from '~modules/gds-terminal/src/sections/HotkeyBar/components/HotkeyButton.vue'
import HotkeyEditModal from '~modules/gds-terminal/src/sections/HotkeyBar/modals/HotkeyEditModal.vue'
import { useTerminalHotkeyStore } from '~modules/gds-terminal/src/piniaStore/useTerminalHotkeyStore'
import type { ModelRef } from '~types/lib/Model'
import { CommandExecutionProcess } from '~modules/gds-terminal/src/lib/CommandExecutionProcess'
import { commandSplitCharacter } from '~modules/gds-terminal/src/composables/terminal/useTerminalHotKeys'
import type TerminalTool from '~modules/gds-terminal/src/lib/TerminalTool'

const toolStore = inject<TerminalTool>('terminalTool')!
const terminalHotkeyStore = useTerminalHotkeyStore()

const scrollContainerRef = ref(null)

const activePcc = computed(() => {
    return toolStore.getActiveSessionStore().activePcc.value
})

const terminalHotkeyList = computed(() => terminalHotkeyStore.getHotkeysByPcc(activePcc))

const hotkeyEditModal = useModal(HotkeyEditModal)

const openEditHotkeys = () => {
    const pcc = activePcc.value

    if (!pcc) {
        return
    }

    hotkeyEditModal.open({ pcc })
}

function applyHotkey(hotkey: ModelRef<'TerminalHotkey'>) {
    const terminal = toolStore.getActiveSessionStore().getActiveTerminal()

    if (!terminal) {
        return
    }

    // noinspection JSIgnoredPromiseFromCall
    terminal.startCommandExecutionProcess(
        new CommandExecutionProcess(
            hotkey.command.split(commandSplitCharacter).map((input) => terminal.detectCommandFromInput(input)),
        ),
    )
}
</script>
