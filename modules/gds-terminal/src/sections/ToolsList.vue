<template>
    <div ref="main" class="card card__body px-3 flex flex-col border">
        <div class="title">
            Essentials
        </div>
        <Dropdown
            :trigger="main"
            placement="left-start"
        >
            <template #toggle="{ toggle, isActive }">
                <div
                    class="my-0.5 flex gap-2 py-2 px-3 items-center hover:bg-secondary-50 dark:hover:bg-secondary-50/10 cursor-pointer rounded-md select-none font-medium"
                    :class="{
                        'text-primary-600 bg-secondary-50': isActive
                    }"
                    @click="toggle"
                >
                    <Settings2Icon class="icon --large" />Default PCC
                </div>
            </template>

            <template #content="{ close }">
                <DefaultPccsModal :close="close" />
            </template>
        </Dropdown>

        <Dropdown
            :trigger="main"
            placement="left-start"
        >
            <template #toggle="{ toggle, isActive }">
                <div
                    class="my-0.5 flex gap-2 py-2 px-3 items-center hover:bg-secondary-50 dark:hover:bg-secondary-50/10 cursor-pointer rounded-md select-none font-medium"
                    :class="{
                        'text-primary-600 bg-secondary-50': isActive
                    }"
                    @click="toggle"
                >
                    <CreditCardIcon class="icon --large" />PQ
                </div>
            </template>

            <template #content="{ close }">
                <PqPrepareModal
                    :close="close"
                    :form-data="{lead_pk: lead_pk}"
                    :terminal-tool="toolStore"
                />
            </template>
        </Dropdown>

        <Dropdown
            :trigger="main"
            placement="left-start"
        >
            <template #toggle="{ toggle, isActive }">
                <div
                    class="my-0.5 flex gap-2 py-2 px-3 items-center hover:bg-secondary-50 dark:hover:bg-secondary-50/10 cursor-pointer rounded-md select-none font-medium"
                    :class="{
                        'text-primary-600 bg-secondary-50': isActive
                    }"
                    @click="toggle"
                >
                    <TerminalIcon class="--large" />Input bar position
                </div>
            </template>
            <template #content>
                <SelectInputPosition />
            </template>
        </Dropdown>

        <div
            v-if="hasPermission('manage', 'all')"
            class="my-0.5 flex gap-2 py-2 px-3 items-center hover:bg-secondary-50 dark:hover:bg-secondary-50/10 cursor-pointer rounded-md select-none font-medium"
            @click="openCommandStatistics"
        >
            <ActivityIcon class="--large" />View Usage Stats
        </div>

        <hr class="my-2">

        <div class="title">
            Styles
        </div>

        <Dropdown
            :trigger="main"
            placement="left-start"
        >
            <template #toggle="{ toggle, isActive }">
                <div
                    class="my-0.5 flex gap-2 py-2 px-3 items-center hover:bg-secondary-50 dark:hover:bg-secondary-50/10 cursor-pointer rounded-md select-none font-medium"
                    :class="{
                        'text-primary-600 bg-secondary-50': isActive
                    }"
                    @click="toggle"
                >
                    <TrendingUpIcon class="--large" />Font Size
                </div>
            </template>
            <template #content>
                <SelectFontSize />
            </template>
        </Dropdown>

        <Dropdown
            :trigger="main"
            placement="left-start"
        >
            <template #toggle="{ toggle, isActive }">
                <div
                    class="my-0.5 flex gap-2 py-2 px-3 items-center hover:bg-secondary-50 dark:hover:bg-secondary-50/10 cursor-pointer rounded-md select-none font-medium"
                    :class="{
                        'text-primary-600 bg-secondary-50': isActive
                    }"
                    @click="toggle"
                >
                    <BrushIcon class="--large" />Color Scheme
                </div>
            </template>
            <template #content="{ close }">
                <ColorSchemeEditModal
                    :close="close"
                />
            </template>
        </Dropdown>

        <div v-if="!isEmpty(helpUrl)">
            <hr class="my-2">
            <a
                :href="helpUrl"
                target="_blank"
                rel="noopener noreferrer"
            >
                <div class="my-0.5 flex gap-2 py-2 px-3 items-center hover:bg-secondary-50 dark:hover:bg-secondary-50/10 cursor-pointer rounded-md select-none font-medium">
                    <HelpCircleIcon class="icon --large" />Guide
                </div>
            </a>
        </div>
    </div>
</template>

<script setup lang="ts">
import Settings2Icon from '../assets/icons/Settings2Icon.svg?component'
import BrushIcon from '../assets/icons/BrushIcon.svg?component'
import DefaultPccsModal from '~modules/gds-terminal/src/modals/DefaultPccsModal.vue'
import PqPrepareModal from '~modules/gds-terminal/src/modals/PqPrepareModal.vue'
import SelectFontSize from '~modules/gds-terminal/src/sections/SelectFontSize.vue'
import { useTerminalContext } from '~modules/gds-terminal/src/composables/terminal/useTerminalContext'
import ColorSchemeEditModal from '~modules/gds-terminal/src/sections/ColorScheme/ColorSchemeEditModal.vue'
import { isEmpty } from '~/utils/asserts'
import SelectInputPosition from '~modules/gds-terminal/src/sections/SelectInputPosition.vue'
import CommandStatisticsModal from '~modules/gds-terminal/src/modals/CommandStatisticsModal.vue'
import type TerminalTool from '~modules/gds-terminal/src/lib/TerminalTool'

const props = defineProps<{
    // eslint-disable-next-line
    close: () => void
}>()

const { hasPermission } = useContext()

const commandStatisticsModal = useModal(CommandStatisticsModal)

const toolStore = inject<TerminalTool>('terminalTool')!

const main = ref()

const lead_pk = useTerminalContext().getLeadContext()?.lead_pk
const helpUrl = config.terminal.helpUrl

const openCommandStatistics = async () => {
    await commandStatisticsModal.open()
    props.close()
}
</script>

<style scoped lang="postcss">
.title {
    @apply uppercase text-secondary-400 font-semibold text-xs pl-3 pb-1
}
</style>
