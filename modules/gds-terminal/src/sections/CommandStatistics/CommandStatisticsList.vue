<template>
    <div class="card--bordered rounded-lg">
        <SuspenseManual :loading="suspense.silentLoading">
            <template #default>
                <div class="card__header justify-end">
                    <AppPaginationCompact :pagination="pagination" />
                    <AppPageSize :pagination="pagination" :options="[10, 40, 100, 500]" />
                </div>
                <div class="card__body p-0 h-[326px]">
                    <div class="overflow-auto fancy-scroll h-full">
                        <AppTable
                            class="--rounded-bottom --fixed-header"
                            :items="pagination.records"
                            :columns="columns"
                            :search-tags="searchController.tags"
                            :sort-controller="sortController"
                        >
                            <template #body>
                                <tr v-for="(item, index) in pagination.records" :key="index">
                                    <td>{{ item.agent_name }}</td>
                                    <td>{{ item.command_count }}</td>
                                    <td>{{ item.custom_command_count }}</td>
                                    <td>{{ item.wpniall_count }}</td>
                                    <td>{{ item.wpniallx_count }}</td>
                                    <td>{{ item.wpniallh_count }}</td>
                                    <td>{{ item.wpncall_count }}</td>
                                    <td>{{ item.wpncallh_count }}</td>
                                    <td>{{ item.wpnch_count }}</td>
                                    <td>{{ item.pnr_count }}</td>
                                    <td>{{ item.pq_count }}</td>
                                </tr>
                            </template>
                        </AppTable>
                    </div>
                </div>
                <div class="card__footer justify-between">
                    <AppPaginationInfo :pagination="pagination" />
                    <AppPagination :pagination="pagination" />
                </div>
            </template>
            <template #fallback>
                <PlaceholderBlock class="w-full h-[375px]" />
            </template>
        </SuspenseManual>
    </div>
</template>

<script setup async lang="ts">
import { FrontendPagination } from '~/lib/Model/FrontendPagination'
import { useTableColumns } from '~/composables/useTableColumns'
import SortController from '~/lib/Search/SortController'
import SearchController from '~/lib/Search/SearchController'
import NumberRangeSearchTag from '~/lib/Search/Tag/NumberRangeSearchTag'
import TextSearchTag from '~/lib/Search/Tag/TextSearchTag'
import type {
    fetchStatisticsPerUser,
} from '~modules/gds-terminal/src/sections/CommandStatistics/composable/useCommandStatisticsController'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import type {
    TerminalStatisticsUserData,
} from '~modules/gds-terminal/src/sections/CommandStatistics/composable/useStatisticsRequests'
import { getFullName } from '~/lib/Helper/PersonHelper'
import type AgentOrTeamSearchTag from '~/lib/Search/Tag/AgentOrTeamSearchTag'
import type DateRangeSearchTag from '~/lib/Search/Tag/DateRangeSearchTag'

const props = defineProps<{
    getStatisticsPerUser: fetchStatisticsPerUser,
    searchTags: {
        agentOrTeamSearchTag: AgentOrTeamSearchTag
        dateRangeSearchTag: DateRangeSearchTag
    }
}>()

const { useDictionary } = useContext()
const agentDictionary = useDictionary('Agent')

const list = ref<TerminalStatisticsUserData[]>([])

const items = computed(() => {
    return sortController.sort(
        searchController.filter(
            agentDictionary.businessAgents.map((item) => {
                const info = list.value.find((responseItem) => responseItem.agent_pk === usePk(item))

                return {
                    agent_name: getFullNameByPk(usePk(item)),
                    command_count: info?.command_count ?? 0,
                    custom_command_count: info?.custom_command_count ?? 0,
                    wpniall_count: info?.wpniall_count ?? 0,
                    wpniallx_count: info?.wpniallx_count ?? 0,
                    wpniallh_count: info?.wpniallh_count ?? 0,
                    wpncall_count: info?.wpncall_count ?? 0,
                    wpncallh_count: info?.wpncallh_count ?? 0,
                    wpnch_count: info?.wpnch_count ?? 0,
                    pnr_count: info?.pnr_count ?? 0,
                    pq_count: info?.pq_count ?? 0,
                }
            }),
        ),
    )
})

const pagination = new FrontendPagination(items, {
    page: 1,
    pageSize: 20,
})

const columns = useTableColumns({
    agent_name: {
        label: 'Employee name',
        sortable: true,
    },
    command_count: {
        label: 'Number of comm.',
        sortable: true,
    },
    custom_command_count: {
        label: 'Number of custom comm.',
        sortable: true,
    },
    wpniall_count: {
        label: 'WPNIALL',
        sortable: true,
    },
    wpniallx_count: {
        label: 'WPNIALLX',
        sortable: true,
    },
    wpniallh_count: {
        label: 'WPNIALLH',
        sortable: true,
    },
    wpncall_count: {
        label: 'WPNCALL',
        sortable: true,
    },
    wpncallh_count: {
        label: 'WPNCALLH',
        sortable: true,
    },
    wpnch_count: {
        label: 'WPNCH',
        sortable: true,
    },
    pnr_count: {
        label: 'PNR Creation',
        sortable: true,
    },
    pq_count: {
        label: `PQ's added`,
        sortable: true,
    },
})

const sortController = SortController.fromColumns(columns, {
    default: { command_count: 'desc' },
})

const searchController = new SearchController({
    agent_name: new TextSearchTag('Employee name'),
    command_count: new NumberRangeSearchTag('Number of comm.'),
    custom_command_count: new NumberRangeSearchTag('Number of custom comm.'),
    wpniall_count: new NumberRangeSearchTag('WPNIALL'),
    wpniallx_count: new NumberRangeSearchTag('WPNIALLX'),
    wpniallh_count: new NumberRangeSearchTag('WPNIALLH'),
    wpncall_count: new NumberRangeSearchTag('WPNCALL'),
    wpncallh_count: new NumberRangeSearchTag('WPNCALLH'),
    wpnch_count: new NumberRangeSearchTag('WPNCH'),
    pnr_count: new NumberRangeSearchTag('PNR Creation'),
    pq_count: new NumberRangeSearchTag(`PQ's added`),
})

const suspense = useSuspensableComponent(async () => {
    list.value = await props.getStatisticsPerUser()
})

watch([() => props.searchTags.agentOrTeamSearchTag.values, () => props.searchTags.dateRangeSearchTag.values], async () => {
    await suspense.fetch()
})

const getFullNameByPk = (pk: PrimaryKey) => {
    try {
        return getFullName(agentDictionary.findOrFail(pk))
    } catch (error) {
        return getFullName({})
    }
}
</script>
