<template>
    <Line
        :data="chartData"
        :options="chartOptions"
    />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Line } from 'vue-chartjs'
import type { ChartOptions } from 'chart.js'
import {
    Chart as ChartJS,
    Title,
    Tooltip,
    Legend,
    LineElement,
    PointElement,
    LinearScale,
    TimeScale,
    Filler,
    CategoryScale,
} from 'chart.js'
import 'chartjs-adapter-date-fns'

const props = defineProps<{
    points: PointData[]
}>()

const { isDark } = useDarkMode()

const time = computed<{unit: 'day'} | {unit: 'hour', displayFormats: {hour: string}}>(() => {
    const diffMs = props.points[1].day_at.getTime() - props.points[0].day_at.getTime()
    const oneDayMs = 1000 * 60 * 60 * 24

    if (diffMs >= oneDayMs) {
        return {
            unit: 'day',
        }
    } else {
        return {
            unit: 'hour',
            displayFormats: {
                hour: 'HH:mm',
            },
        }
    }
})

const verticalLinePlugin = {
    id: 'verticalLine',
    beforeDraw(chart: any) {
        if (chart.tooltip._active && chart.tooltip._active.length) {
            const ctx = chart.ctx
            const activePoints = chart.tooltip._active

            const newActivePoint = activePoints.reduce((prev, current) =>
                current.value > prev.value ?  prev : current,
            )

            const x = newActivePoint.element.x
            const y = newActivePoint.element.y
            const bottomY = chart.scales.y.bottom

            ctx.save()
            ctx.beginPath()
            ctx.setLineDash([5, 5])
            ctx.moveTo(x, y)
            ctx.lineTo(x, bottomY)
            ctx.lineWidth = 1
            ctx.strokeStyle = isDark.value ? '#E2E8F0' : '#94A3B8'
            ctx.stroke()
            ctx.restore()
        }
    },
}

ChartJS.register(
    Title,
    Tooltip,
    Legend,
    LineElement,
    PointElement,
    LinearScale,
    TimeScale,
    Filler,
    CategoryScale,
    verticalLinePlugin,
)

onUnmounted(() => {
    ChartJS.unregister(verticalLinePlugin)
})

type PointData = {
    day_at: Date,
    total_count: number,
    tooltip: string[]
}

const chartData = computed(() => ({
    labels: props.points.map(p => p.day_at),
    datasets: [
        {
            label: 'Commands',
            data: props.points.map(p => p.total_count),
            fill: true,
            borderColor: 'rgba(59, 118, 246, 1)',
            tension: 0.3,
            pointBackgroundColor: 'rgba(0,0,0,0)',
            pointBorderColor: 'rgba(0,0,0,0)',
            pointHoverBackgroundColor: isDark.value ? 'rgb(255, 255, 255)' : 'rgba(41, 49, 69, 1)',
            borderWidth: 1.5,

            backgroundColor: (ctx: any) => {
                const { chart } = ctx
                const { ctx: canvasCtx, chartArea } = chart

                if (!chartArea) return

                const gradient = canvasCtx.createLinearGradient(0, chartArea.top, 0, chartArea.bottom)
                gradient.addColorStop(0, 'rgba(59, 118, 246, 0.4)')
                gradient.addColorStop(1, 'rgba(59, 118, 246, 0)')

                return gradient
            },
        },
    ],
}))

const chartOptions: ChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
        mode: 'index' as const,
        intersect: false,
    },
    normalized: true,
    scales: {
        x: {
            grid: {
                display: false,
            },
            ticks: {
                color: isDark.value ? '#FFFFFF' : '#616F96',
                source: 'data',
                autoSkipPadding: 5,
            },

            type: 'time' as const,
            time: time.value,

            title: {
                display: true,
            },
        },
        y: {
            ticks: {
                color: isDark.value ? '#FFFFFF' : '#616F96',
                maxTicksLimit: 6,
                stepSize: 1,
                autoSkip: false,
            },
            beginAtZero: true,
        },
    },
    plugins: {
        legend: {
            display: false,
            labels: {
                color: '#FFFFF',
            },
        },
        tooltip: {
            displayColors: false,
            callbacks: {
                title(tooltipItems): string | string[] | void {
                    return ''
                },

                label: function(context: any) {
                    const index = context.dataIndex
                    const point = props.points[index]

                    return [
                        `Commands: ${point.total_count}`,
                    ]
                },
            },
        },
    },
}
</script>
