import type {
CustomCommandStatistics,
} from '~modules/gds-terminal/src/sections/CommandStatistics/composable/useStatisticsRequests'
import {
    useStatisticsRequests,
} from '~modules/gds-terminal/src/sections/CommandStatistics/composable/useStatisticsRequests'
import AgentOrTeamSearchTag from '~/lib/Search/Tag/AgentOrTeamSearchTag'
import SelectSearchTag from '~/lib/Search/Tag/SelectSearchTag'
import DateRangeSearchTag from '~/lib/Search/Tag/DateRangeSearchTag'
import { getUTCOffset } from '@/lib/core/helper/DateHelper'

export function useCommandStatisticsController() {
    const statisticsRequests = useStatisticsRequests()

    const commandsOptions = ref([
        { title: `Regular commands`, value: 'regular_commands' },
        { title: `WPNIALL`, value: 'wpniall', group: 'Special commands' },
        { title: `WPNIALLX`, value: 'wpniallx', group: 'Special commands' },
        { title: `WPNIALLH`, value: 'wpniallh', group: 'Special commands' },
        { title: `WPNCALL`, value: 'wpncall', group: 'Special commands' },
        { title: `WPNCALLH`, value: 'wpncallh', group: 'Special commands' },
        { title: `WPNCH`, value: 'wpnch', group: 'Special commands' },
        { title: `PNR Creation`, value: 'pnr', group: 'Special commands' },
        { title: `PQ's added`, value: 'pq_creation', group: 'Special commands' },
    ])

    const agentOrTeamSearchTag = new AgentOrTeamSearchTag('Members', { multiple: true, placeholder: 'All members' })
    const commandsSelectSearchTag = new SelectSearchTag<CustomCommandStatistics>('Commands', commandsOptions, {
        selectableGroup: true,
        placeholder: 'All commands',
    })

    // @ts-ignore
    commandsSelectSearchTag.load()

    const dateRangeSearchTag = new DateRangeSearchTag('Date Range', { multiple: false })

    const getStatisticsPerDay = async () => {
        const dateRange = dateRangeSearchTag.values?.[0]

        if (!dateRange) {
            return []
        }

        const newDateRange = dateRangeSearchTag.getDateRangeValueWithoutMoveTimeZone(dateRange)

        const utc_offset = getUTCOffset(useAppTimezone().value)

        return statisticsRequests.getStatisticsPerDay({
            commands: commandsSelectSearchTag.values,
            agent_pks: agentOrTeamSearchTag.getAgentPks(),
            dateRange: dateRangeSearchTag.getDateRangeValue(dateRange),
            group_per_hours: (newDateRange.end.unixTimestamp() - newDateRange.start.unixTimestamp()) < Timespan.days(1).inSeconds,
            utc_offset,
        })
    }

    const getStatisticsPerUser = async  () => {
        const dateRange = dateRangeSearchTag.values?.[0]

        if (!dateRange) {
            return []
        }

        return statisticsRequests.getStatisticsPerUser({
            agent_pks: agentOrTeamSearchTag.getAgentPks(),
            dateRange: dateRangeSearchTag.getDateRangeValue(dateRange),
        })
    }

    return {
        agentOrTeamSearchTag,
        commandsSelectSearchTag,
        dateRangeSearchTag,
        getStatisticsPerDay,
        getStatisticsPerUser,
    }
}

export type CommandStatisticsController = ReturnType<typeof useCommandStatisticsController>
export type fetchStatisticsPerDay = ReturnType<typeof useCommandStatisticsController>['getStatisticsPerDay']
export type fetchStatisticsPerUser = ReturnType<typeof useCommandStatisticsController>['getStatisticsPerUser']
