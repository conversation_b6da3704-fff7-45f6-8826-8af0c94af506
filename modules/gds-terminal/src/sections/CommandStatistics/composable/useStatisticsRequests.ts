import { useTerminalHttpClient } from '~modules/gds-terminal/src/composables/useTerminalHttpClient'
import type { DateRange } from '@/lib/core/helper/DateHelper'

export type TerminalStatisticsDayData = {
    day_at: number,
    total_count: number,
}

export type TerminalStatisticsUserData = {
    agent_pk: PrimaryKey,
    command_count: number,
    custom_command_count: number,
    wpniall_count: number,
    wpniallx_count: number,
    wpniallh_count: number,
    wpncall_count: number,
    wpncallh_count: number,
    wpnch_count: number,
    pnr_count: number,
    pq_count: number,
}

export type CustomCommandStatistics = 'wpniall' | 'wpniallx' | 'wpncall' | 'wpniallh' | 'wpncallh' | 'wpnch' | 'pnr' | 'pq_creation' | 'regular_commands'

// statistics SDK
export function useStatisticsRequests() {
    const { http } = useTerminalHttpClient()

    const getStatisticsPerDay = async (option: {agent_pks: PrimaryKey[], commands: CustomCommandStatistics[] | undefined, dateRange: DateRange, utc_offset: number, group_per_hours?: boolean}) => {
        return http<TerminalStatisticsDayData[]>(`/statistics/perDay`, {
            method: 'POST',
            body: {
                agent_pks: option.agent_pks,
                commands: option.commands,
                from: option.dateRange.start.unixTimestamp(),
                to: option.dateRange.end.unixTimestamp(),
                group_per_hours: option.group_per_hours,
                utc_offset: option.utc_offset,
            },
        })
    }

    const getStatisticsPerUser = async (option: {agent_pks: PrimaryKey[], dateRange: DateRange}) => {
        return http<TerminalStatisticsUserData[]>(`/statistics/listPerUser`, {
            method: 'POST',
            body: {
                agent_pks: option.agent_pks,
                from: option.dateRange.start.unixTimestamp(),
                to: option.dateRange.end.unixTimestamp(),
            },
        })
    }

    return {
        getStatisticsPerUser,
        getStatisticsPerDay,
    }
}
