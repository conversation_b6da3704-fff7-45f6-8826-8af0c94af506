<template>
    <div class="card--bordered rounded-lg">
        <div class="card__header">
            <div class="card__title w-full flex justify-between">
                <div class="flex flex-col">
                    <div class="font-semibold">
                        Commands entered
                    </div>
                    <div class="text-xs">
                        <span class="text-secondary">Selected period: </span>
                        <span v-if="searchTags.dateRangeSearchTag.values?.[0]">
                            {{ formatRange(moveDateRangeToTimeZone(searchTags.dateRangeSearchTag.getDateRangeValue(searchTags.dateRangeSearchTag.values[0]), useAppTimezone().value), 'MMMM d, yyyy') }}</span>
                    </div>
                </div>

                <div class="flex gap-6">
                    <div class="button-group">
                        <AppButton
                            :class="{ '--primary': rangeState === 'thisYear' }"
                            @click="updateRangeState('thisYear')"
                        >
                            Year
                        </AppButton>
                        <AppButton
                            :class="{ '--primary': rangeState === 'thisMonth' }"
                            @click="updateRangeState('thisMonth')"
                        >
                            Month
                        </AppButton>
                        <AppButton
                            :class="{ '--primary': rangeState === 'thisWeek' }"
                            @click="updateRangeState('thisWeek')"
                        >
                            Week
                        </AppButton>
                        <AppButton
                            :class="{ '--primary': rangeState === 'today' }"
                            @click="updateRangeState('today')"
                        >
                            Day
                        </AppButton>
                        <AppButton
                            :class="{ '--primary': !rangeState }"
                            @click="updateRangeState(undefined)"
                        >
                            Custom
                        </AppButton>
                    </div>

                    <Component
                        :is="searchTags.dateRangeSearchTag.component"
                        teleport
                        :tag="searchTags.dateRangeSearchTag"
                        :model-value="searchTags.dateRangeSearchTag.isMultiple ? searchTags.dateRangeSearchTag.values : searchTags.dateRangeSearchTag.values?.[0]"
                        @update:model-value="applyUpdate(searchTags.dateRangeSearchTag, $event)"
                    />
                </div>
            </div>

            <div class="flex gap-4">
                <Component
                    :is="searchTags.commandsSelectSearchTag.component"
                    class="w-44"
                    :tag="searchTags.commandsSelectSearchTag"
                    :model-value="searchTags.commandsSelectSearchTag.isMultiple ? searchTags.commandsSelectSearchTag.values : searchTags.commandsSelectSearchTag.values?.[0]"
                    @update:model-value="applyUpdate(searchTags.commandsSelectSearchTag, $event)"
                />

                <Component
                    :is="searchTags.agentOrTeamSearchTag.component"
                    class="w-44"
                    :tag="searchTags.agentOrTeamSearchTag"
                    :model-value="searchTags.agentOrTeamSearchTag.isMultiple ? searchTags.agentOrTeamSearchTag.values : searchTags.agentOrTeamSearchTag.values?.[0]"
                    @update:model-value="applyUpdate(searchTags.agentOrTeamSearchTag, $event)"
                />
            </div>
        </div>
        <div class="card__body pb-0">
            <SuspenseManual :loading="suspense.silentLoading">
                <template #default>
                    <div class="h-[240px]">
                        <LineChart v-bind="chartProps" />
                    </div>
                </template>
                <template #fallback>
                    <PlaceholderBlock class="!w-full h-[240px]" />
                </template>
            </SuspenseManual>
        </div>
    </div>
</template>

<script setup lang="ts">
import type AgentOrTeamSearchTag from '~/lib/Search/Tag/AgentOrTeamSearchTag'
import type SelectSearchTag from '~/lib/Search/Tag/SelectSearchTag'
import type DateRangeSearchTag from '~/lib/Search/Tag/DateRangeSearchTag'
import { ensureArray } from '~/lib/Helper/ArrayHelper'
import type { DateRangeName } from '@/lib/core/helper/DateHelper'
import { moveDateRangeToTimeZone } from '@/lib/core/helper/DateHelper'
import { formatRange } from '@/lib/core/helper/DateHelper'
import { useAppTimezone } from '~/composables/useAppTimezone'
import type {
    fetchStatisticsPerDay,
} from '~modules/gds-terminal/src/sections/CommandStatistics/composable/useCommandStatisticsController'
import type {
    TerminalStatisticsDayData,
} from '~modules/gds-terminal/src/sections/CommandStatistics/composable/useStatisticsRequests'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import type { DatetimeLineDataset } from '@tmg/chart'
import { defineChartProps } from '@tmg/chart'
import { LineChart } from '@tmg/chart'
import { useLineChartTheme } from '~/composables/ChartThemes/useLineChartTheme'

const props = defineProps<{
    getStatisticsPerDay: fetchStatisticsPerDay,
    searchTags: {
        agentOrTeamSearchTag: AgentOrTeamSearchTag
        commandsSelectSearchTag: SelectSearchTag
        dateRangeSearchTag: DateRangeSearchTag
    }
}>()

const list = ref<TerminalStatisticsDayData[]>([])

const dataset = computed<DatetimeLineDataset<unknown>>(() => {
    return {
        type: 'datetime-line',
        points: list.value.map((item) => ({
            value: item.total_count,
            timestamp: item.day_at,
        })) ?? [],
    }
})

const { theme } = useLineChartTheme()

const chartProps = computed(() => {
    return defineChartProps({
        datasetsConfigs: [
            {
                dataset: dataset.value,
            },
        ],
        gradient: true,
        smoothing: true,
        tooltipMapper: (pointData) => `Commands: ${pointData.value}`,
        maxTicksLimit: 6,
        theme: theme.value,
        minValue: {
            y: 0,
        },
    })
})

const rangeState = ref<DateRangeName | undefined>()

updateRangeState('thisWeek')

const suspense = useSuspensableComponent(async () => {
    list.value = await props.getStatisticsPerDay()
})

watch([() => props.searchTags.commandsSelectSearchTag.values, () => props.searchTags.agentOrTeamSearchTag.values, () => props.searchTags.dateRangeSearchTag.values], async () => {
    await suspense.fetch()
})

function updateRangeState(value: DateRangeName | undefined) {
    rangeState.value = value

    if (value) {
        applyUpdate(props.searchTags.dateRangeSearchTag, value, true)
    }
}

function applyUpdate(searchTag: AgentOrTeamSearchTag | SelectSearchTag | DateRangeSearchTag, value: any,  skipSideEffect?: boolean) {
    if (!searchTag) {
        return
    }

    if (searchTag.constructor.name === 'DateRangeSearchTag' && !skipSideEffect) {
        rangeState.value = undefined
    }

    if (!searchTag.isMultiple) {
        value = [value]
    }

    searchTag.setValues(ensureArray(value).filter((v) => v !== undefined))
}
</script>
