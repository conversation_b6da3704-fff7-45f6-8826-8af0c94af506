.hotkey {
    @apply cursor-pointer rounded-[4px] flex gap-2 items-center py-2 px-2 leading-[14px] h-[28px] max-w-[140px] transition duration-100;
    @apply bg-secondary-50 border border-transparent;
    @apply dark:bg-secondary-900;

    &:hover:not(:focus) {
        @apply bg-secondary-100;
        @apply dark:bg-secondary-950;
    }

    &:hover .hotkey__key{
        @apply bg-secondary-50;
        @apply dark:bg-secondary-800;
    }

    &:active {
        @apply bg-primary-50 text-primary border-primary-200 border;
        @apply dark:bg-secondary-900 dark:border-primary;

    }

    &:active .hotkey__key {
        @apply border-primary-200;
        @apply dark:border-primary;
    }

    &__key {
        @apply border rounded-[4px] px-2 py-0.5 text-3xs;
    }

    &__title {
        @apply text-2xs whitespace-nowrap truncate;
    }
}
