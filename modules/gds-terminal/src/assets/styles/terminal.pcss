@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&family=Fira+Code:wght@300..700&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap');

:root {
    --terminal-background: '';
    --terminal-foreground: '';
    --terminal-commandBar-background-color: '';
    --terminal-commandBar-text-color: '';
    --terminal-cursor: '';
    --terminal-command-text-color: '';
    --terminal-color-01: '';
    --terminal-color-02: '';
    --terminal-color-03: '';
    --terminal-color-04: '';
    --terminal-color-05: '';
    --terminal-color-09: '';
}


.terminal {
    --terminal-font-family: 'Fira Code', 'Source Code Pro', 'Monaco', 'Inconsolata', 'Consolas';
    --terminal-font-size: 14px;
    --terminal-line-height: 18px;
    --terminal-padding: 0.5rem;

    position: relative;
    line-height: var(--terminal-line-height);
    font-family: var(--terminal-font-family), monospace;
    font-size: var(--terminal-font-size);

    padding: var(--terminal-padding) 0;
    cursor: text;

    &:not(#undefined){
        background: var(--terminal-background);
        color: var(--terminal-foreground);
    }

    &__loading {
        padding: 0 var(--terminal-padding);
    }

    &__input {
        .input-field__input {
            color: var(--terminal-commandBar-text-color);
            background-color: var(--terminal-commandBar-background-color);
        }
    }

    &--font-size {
        &-xs {
            --terminal-font-size: 12px;
            --terminal-line-height: 16px;
        }

        &-sm {
            --terminal-font-size: 14px;
            --terminal-line-height: 18px;
        }

        &-md {
            --terminal-font-size: 16px;
            --terminal-line-height: 20px;
        }

        &-lg {
            --terminal-font-size: 18px;
            --terminal-line-height: 24px;
        }

        &-xl {
            --terminal-font-size: 20px;
            --terminal-line-height: 28px;
        }
    }

    &--focused {
        outline: 2px solid #3B82F6;
    }

    &__error {
        background-color: var(--terminal-color-02);
        color: var(--terminal-color-01);
        padding: 6px;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
    }

    &__viewport {
        width: 100%;
        height: 100%;
        position: relative;

        &__scrollable {
            position: absolute;
            overflow-y: auto;
            width: 100%;
            height: 100%;
            outline: none;
            padding: 0 var(--terminal-padding);

            /** Hide scrollbar */
            -ms-overflow-style: none;
            scrollbar-width: none;

            &::-webkit-scrollbar {
                display: none;
            }
        }
    }

    &__input {
        display: flex;

        &--hidden {
            opacity: 0;
            pointer-events: none;
        }
    }

    &__textarea {
        background: inherit;
        color: inherit;
        font-size: inherit;
        font-family: inherit;
        line-height: inherit;
        padding: 0;
        outline: none;
        white-space: pre;
        word-wrap: normal;
        cursor: text;
        overflow: auto;
        border: none;
        resize: none;
        flex-grow: 1;
        height: var(--terminal-line-height);
        caret-color: var(--terminal-cursor);

        &::selection {
            background: var(--terminal-cursor);
            color: var(--terminal-background);
        }

        -ms-overflow-style: none;
        scrollbar-width: none;

        &::-webkit-scrollbar {
            display: none;
        }
    }

    &__error-text {
        color: var(--terminal-color-02);
    }

    --terminal-char-width: 0;
    --terminal-char-height: 0;
    --terminal-caret-line: 0;
    --terminal-caret-col: 0;
    --terminal-virtual-lines: 0;

    &__caret {
        position: absolute;
        width: var(--terminal-char-width);
        height: var(--terminal-line-height);
        background-color: var(--terminal-cursor);
        margin-left: 2px;
        animation: terminal-caret-blink 1s steps(1) infinite;
        top: calc(var(--terminal-padding) + var(--terminal-char-height) * (var(--terminal-caret-line) - 1));
        left: calc(var(--terminal-padding) + var(--terminal-char-width) * (var(--terminal-caret-col) - 1));
    }

    &__char-test {
        display: inline-block; /* @todo Change to inline */
        pointer-events: none;
        visibility: hidden;
        line-height: var(--terminal-line-height);
    }

    &__debug-trailing-character {
        width: 0;
        position: relative;
        animation: infinite pulse 1s;

        &::after {
            content: ' ';
            position: absolute;
            background-color: var(--terminal-color-02);
            height: var(--terminal-char-height);
            top: 0;
            left: 100%;
        }
    }

    &__badge {
        position: absolute;
        top: 4px;
        right: 4px;
    }

    &__line-numbers {
        position: absolute;
        top: 0;
        left: 0;
        width: 30px;
        height: 100%;

        &__item {
            color: var(--terminal-color-09);
        }

        & + .terminal__output {
            padding-left: 30px;
        }
    }

    /**
     * Deltas
     * ================================
     */

    &-delta__command-input {
        color: var(--terminal-command-text-color)
    }

    &-delta__text {
        &--red {
            color: var(--terminal-color-02);
        }

        &--green {
            color: var(--terminal-color-03);
        }

        &--green-dark {
            color: #6ad161;
        }

        &--yellow {
            color: var(--terminal-color-04);
        }

        &--blue {
            color: var(--terminal-color-05);
        }

        &--gray {
            color: var(--terminal-color-09);
        }

        &--bold {
            font-weight: bold;
        }
    }

    &__table-container {
        overflow-x: auto;
        padding-bottom: calc(0.5 * var(--terminal-line-height));
        margin-bottom: calc(0.5 * var(--terminal-line-height));
    }

    &__table {
        width: 100%;
        text-align: left;

        --td-padding: var(--terminal-char-width);

        tr {
            position: relative;
        }

        tr::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            border-bottom: 1px dashed #fff !important;
            opacity: 0.7;
        }

        th, td {
            padding: 0 calc(0.75 * var(--td-padding));
            white-space: nowrap;
            height: calc(var(--terminal-line-height) * 3);
        }

        tbody {
            tr {
                &:hover {
                    background-color: #ffffff10;
                }
            }
        }
    }
}

@keyframes terminal-caret-blink {
    50% {
        opacity: 0;
    }
}
