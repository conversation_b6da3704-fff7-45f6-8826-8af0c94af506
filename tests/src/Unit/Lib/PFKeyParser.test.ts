import { describe, expect, it } from 'vitest'
import type { RawPfKeyEntry } from '~modules/gds-terminal/src/composables/useTerminalPFKeyParser'
import { useTerminalPFKeysParser } from '~modules/gds-terminal/src/composables/useTerminalPFKeyParser'

const rawDataFromTerminal =
    `
#PF Key File
#Mon, 16 Jun 2025 15:29:48 GMT+0300

pfkey.0.label=SnapCodes
pfkey.0.desc=
pfkey.0.command=WPNC\u0081IBTM01\u0083EWPNC\u0081IAGG18\u0083EWPNC\u0081IGEN01\u0083EWPNC\u0081IGAL19\u0083EWPNC\u0081IGEN02\u0083EWPNC\u0081IBMU01\u0083EWPNC\u0081IBAC12\u0083EWPNC\u0081IPPK20\u0083EWPNC\u0081IFCS01\u0083EWPNC\u0081IIGY21\u0083EWPNC\u0081IEFG01\u0083EWPNC\u0081IHTE21\u0083EWPNC\u0081ISOF01\u0083EWPNC\u0081ISTE01\u0083EWPNC\u0081ISTE03\u0083EWPNC\u0081ICHI01\u0083EWPNC\u0081IBTM01\u0083EWPNC\u0081IDOM01\u0083EWPNC\u0081ICLA30\u0083EWPNC\u0081IUPG01\u0083EWPNC\u0081ISIT01\u0083E

pfkey.1.label=PNR U9XF
pfkey.1.desc=
pfkey.1.command=7TAW/\u0083EDK0010020051\u0083EPE\<EMAIL>\u0081\u0083E98558551221\u0099W-ADDRESS\u0083E6SETH\u0083E5.TBC\u0083E5.S*SA7U\u0083EER\u0083E*A\u0083EER\u0083EER\u0083EIR\u0083EQP/J98K50/035\u0083EER

pfkey.2.label=D0TJ
pfkey.2.desc=
pfkey.2.command=7TAW/\u0083EDK0851027\u0083E9MIA8558551221\u0099W-ADDRESS\u0083EPE\<EMAIL>\u0081\u0083E5.S*AN0851027\u0083E5.S*SAAG0851027\u0083E5H-SETH\u0083E6SETH\u0083EER\u0083EER\u0083E

pfkey.3.label=J98K
pfkey.3.desc=
pfkey.3.command=7TAW/\u0083E9MIA8558551221\u0099W-ADDRESS\u0083EPE\<EMAIL>\u0081\u0083E5H-SETH\u0083E6SETH\u0083EER\u0083EER\u0083EIR\u0083EQP/J98K50/035\u0083EER

pfkey.4.label=UE07/2GAC/KN6G
pfkey.4.desc=
pfkey.4.command=7TAW/\u0083EDK370046\u0083E9MIA8558551221\u0099W-ADDRESS\u0083EPE\<EMAIL>\u0081\u0083E5H-SETH\u0083E6SETH\u0083EER\u0083EER\u0083EIR\u0083EQP/J98K50/035\u0083EER

pfkey.5.label=O77I
pfkey.5.desc=
pfkey.5.command=7TAW/\u0083EDK8558551221\u0083EPE\<EMAIL>\u0081\u0083E98558551221\u0083ESETH\u0083E5.TBC\u0083EER\u0083E*A\u0083EER\u0083EER\u0083EIR\u0083EQP/J98K50/035\u0083E

pfkey.6.label=SW04/I8F4
pfkey.6.desc=
pfkey.6.command=7TAW/\u0083EDK8558551221\u0083EPE\<EMAIL>\u0081\u0083E98558551221\u0083ESETH\u0083E5.TBC\u0083EER\u0083E*A\u0083EER\u0083EER\u0083EIR\u0083EQP/J98K50/035\u0083E\\n

pfkey.7.label=V60H
pfkey.7.desc=
pfkey.7.command=7TAW/\u0083EDK0010020051\u0083EPE\<EMAIL>\u0081\u0083E98558551221\u0083ESETH\u0083E5.TBC\u0083EER\u0083E*A\u0083EER\u0083EER\u0083EIR\u0083EQP/J98K50/035\u0083E
    `

describe('useTerminalPFKeysParser', () => {
    const { parsePfFile, generatePfFileContent, decodeSabreUnicode, encodeToSabreUnicode } = useTerminalPFKeysParser()

    it('Parses a PF key file from Sabre Terminal correctly', () => {
        const entries = parsePfFile(rawDataFromTerminal)

        expect(Array.isArray(entries)).toBe(true)
        expect(entries.length).toBeGreaterThan(0)

        const first = entries[0]

        expect(first.keys).toEqual(['F1'])
        expect(first.title).toBe('SnapCodes')
        expect(first.description).toBe('')
        expect(first.command).toContain('WPNC')
        expect(first.command).toContain('¥')
    })

    it('Skips commented and blank lines', () => {
        const sample = [
            '# this is a comment',
            '',
            'pfkey.0.label=SkipTest',
            'pfkey.0.desc=Desc',
            'pfkey.0.command=CMD',
            '',
            '# end',
        ].join('\n')

        const entries = parsePfFile(sample)
        expect(entries).toHaveLength(1)
        expect(entries[0].title).toBe('SkipTest')
    })

    it('Parses non-sequential indices and maps them to correct F-keys', () => {
        const sample = [
            'pfkey.5.label=Index5',
            'pfkey.5.desc=X',
            'pfkey.5.command=CMD5',
            'pfkey.2.label=Index2',
            'pfkey.2.desc=Y',
            'pfkey.2.command=CMD2',
        ].join('\n')
        const entries = parsePfFile(sample)
        const idx5 = entries.find(e => e.keys.includes('F6'))
        const idx2 = entries.find(e => e.keys.includes('F3'))

        expect(idx5).toBeDefined()
        expect(idx5!.title).toBe('Index5')

        expect(idx2).toBeDefined()
        expect(idx2!.title).toBe('Index2')
    })

    it('Parses multi-line command entries correctly', () => {
        const multiline = [
            'pfkey.2.label=MultiLine',
            'pfkey.2.desc=Test multi-line',
            'pfkey.2.command=LINE1',
            'CONTINUED',
            'pfkey.3.label=Next',
            'pfkey.3.desc=',
            'pfkey.3.command=SINGLE',
        ].join('\n')

        const entries = parsePfFile(multiline)

        const entry = entries.find(e => e.keys.includes('F3'))

        expect(entry).toBeDefined()
        expect(entry!.title).toBe('MultiLine')
        expect(entry!.description).toBe('Test multi-line')
        expect(entry!.command).toBe('LINE1CONTINUED')

        const single = entries.find(e => e.keys.includes('F4'))

        expect(single).toBeDefined()
        expect(single!.command).toBe('SINGLE')
    })

    it('Handles entries missing command field gracefully', () => {
        const sample = [
            'pfkey.0.label=NoCmd',
            'pfkey.0.desc=OnlyDesc',
        ].join('\n')

        const entries = parsePfFile(sample)

        expect(entries).toHaveLength(0)
    })

    it('Decodes Sabre Unicode sequences in parsed content', () => {
        const seq = 'HELLO\\u0081WORLD'

        const sample = [
            'pfkey.0.label=U',
            'pfkey.0.desc=',
            `pfkey.0.command=${seq}`,
        ].join('\n')

        const entries = parsePfFile(sample)
        expect(entries[0].command).toBe('HELLO¥WORLD')
    })

    it('Encodes and decodes Sabre Unicode characters correctly via API', () => {
        const original = 'A¥B^C‡'
        const encoded = encodeToSabreUnicode(original)

        expect(encoded).toContain('\\u0081')
        expect(encoded).toContain('\\u0083')

        const decoded = decodeSabreUnicode(encoded)
        expect(decoded).toBe(original)
    })

    it('Generates PF key file content with correct ordering and header', () => {
        const pfKeys: RawPfKeyEntry[] = [
            { index: 1, label: 'L1', desc: 'D1', command: 'C1' },
            { index: 0, label: 'L0', desc: '', command: 'C0' },
        ]
        const content = generatePfFileContent(pfKeys)
        const lines = content.split(/\r?\n/)

        // Header presence
        expect(lines[0]).toMatch(/^#PF Key File$/)

        // Order: index 1 then index 0 entries
        const idx1Pos = lines.findIndex(l => l.startsWith('pfkey.1.label'))
        const idx0Pos = lines.findIndex(l => l.startsWith('pfkey.0.label'))
        expect(idx1Pos).toBeLessThan(idx0Pos)
    })

    it('Import-export retaining original entries', () => {
        const pfKeys: RawPfKeyEntry[] = [
            { index: 0, label: 'Round', desc: 'Trip', command: 'CMD123' },
            { index: 1, label: 'Chars', desc: '', command: 'A¥B^C' },
        ]
        const content = generatePfFileContent(pfKeys)
        const parsed = parsePfFile(content)

        const normalized = parsed.map(e => ({
            index: parseInt(e.keys[0].slice(1), 10) - 1,
            label: e.title,
            desc: e.description,
            command: e.command,
        }))

        expect(normalized).toEqual(pfKeys)
    })

    it('Export empty command', () => {
        const pfKeys: RawPfKeyEntry[] = [
            { index: 0, label: 'Round', desc: 'Trip', command: '' },
        ]
        const content = generatePfFileContent(pfKeys)
        const parsed = parsePfFile(content)

        expect(parsed).toHaveLength(0)
    })
})
